<fieldset class="table-search-fieldset">
    <legend>分会管理中心</legend>
    <div style="margin: 20px;">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #FF5722; color: white;">
                        <i class="layui-icon layui-icon-group"></i> 分会长数量
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #FF5722;">
                            <?php echo $stats['president_count']; ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            当前在职
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #FFB800; color: white;">
                        <i class="layui-icon layui-icon-notice"></i> 待审核申请
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #FFB800;">
                            <?php echo $stats['pending_applications']; ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            需要处理
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="layui-row layui-col-space15" style="margin-top: 20px;">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-group"></i> 分会长管理
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <a href="/branchpresident/applications" class="layui-btn layui-btn-fluid layui-btn-normal">
                                    <i class="layui-icon layui-icon-form"></i> 申请审核
                                    <?php if($stats['pending_applications'] > 0): ?>
                                    <span class="layui-badge layui-bg-orange"><?php echo $stats['pending_applications']; ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="layui-col-md6">
                                <a href="/branchpresident/branches" class="layui-btn layui-btn-fluid">
                                    <i class="layui-icon layui-icon-home"></i> 分会管理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="layui-card" style="margin-top: 20px;">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-search"></i> 分会搜索
            </div>
            <div class="layui-card-body">
                <form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="post" id="search">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">分会名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="branch_name" value="<?php if(!empty($_REQUEST['branch_name']))echo $_REQUEST['branch_name']; ?>" placeholder="请输入分会名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</fieldset>

<fieldset class="table-search-fieldset">
    <legend>分会列表</legend>
    <div style="margin: 10px 10px 10px 10px">
        <table class="layui-table" lay-even lay-skin="nob">
            <colgroup>
                <col width="80">
                <col width="200">
                <col width="150">
                <col width="120">
                <col width="150">
                <col width="120">
                <col width="150">
                <col width="200">
            </colgroup>
            <thead>
                <tr>
                    <th>分会ID</th>
                    <th>分会名称</th>
                    <th>分会地区</th>
                    <th>分会长</th>
                    <th>联系电话</th>
                    <th>成员数量</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(!empty($data)): ?>
                    <?php foreach($data as $row): ?>
                    <tr>
                        <td><?php echo $row['branch_id']; ?></td>
                        <td><?php echo htmlspecialchars($row['branch_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['branch_location'] ?: '未设置'); ?></td>
                        <td><?php echo htmlspecialchars($row['leader_nickname'] ?: '未设置'); ?></td>
                        <td><?php echo htmlspecialchars($row['leader_mobile'] ?: '未设置'); ?></td>
                        <td>
                            <span class="layui-badge layui-bg-blue"><?php echo $row['member_count']; ?></span>
                        </td>
                        <td><?php echo $row['created_at'] ?: '未知'; ?></td>
                        <td>
                            <a class="layui-btn layui-btn-xs" href="javascript:void(0)" onclick="viewBranchDetail(<?php echo $row['branch_id']; ?>)">查看详情</a>
                            <a class="layui-btn layui-btn-xs layui-btn-normal" href="/user/index?branch_id=<?php echo $row['branch_id']; ?>">成员列表</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" style="text-align: center; color: #999;">暂无数据</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <?php echo pageRender(); ?>
    </div>
</fieldset>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 搜索表单提交
    form.on('submit(search)', function(data){
        var params = [];
        for(var key in data.field) {
            if(data.field[key]) {
                params.push(key + '=' + encodeURIComponent(data.field[key]));
            }
        }
        window.location.href = '/branchpresident/branches?' + params.join('&');
        return false;
    });
    
    // 查看分会详情
    window.viewBranchDetail = function(branchId) {
        layer.open({
            type: 2,
            title: '分会详情',
            content: '/branchpresident/branch_detail?id=' + branchId,
            area: ['80%', '80%'],
            maxmin: true
        });
    };
});
</script>

<style>
.layui-table td {
    vertical-align: middle;
}

.layui-card {
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    border-radius: 6px;
}

.layui-card-header {
    border-radius: 6px 6px 0 0;
    font-weight: bold;
}

.layui-btn-fluid {
    width: 100%;
    text-align: center;
}

.layui-badge {
    position: relative;
    top: -2px;
    margin-left: 5px;
}

/* 统计卡片动画效果 */
.layui-card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,.1);
}

/* 按钮悬停效果 */
.layui-btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}
</style>
