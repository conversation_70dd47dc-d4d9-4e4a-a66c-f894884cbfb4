import { store } from "@/store";
import { getCurrentInstance } from "vue";
import { userlogin, userget_user_info, getAddr } from "@/api";

/**
 @@param {getWindowHeight} getWindowHeight 获取视口高度
 */
export const getWindowHeight = () => {
  const res = uni.$u.sys();
  return res.windowHeight;
};

/**
 @@param {getListHeight} getListHeight 获取元素信息
 */
export const getListHeight = async (e = "list") => {
  const { proxy } = getCurrentInstance();
  return await proxy.$u.getRect("." + e);
};

/**
 @@param {setListHeight} setListHeight 设置元素高度
 */
export const setListHeight = async (e = "list") => {
  const { proxy } = getCurrentInstance();
  const res = await proxy.$u.getRect("." + e);
  return getWindowHeight() - res.top;
};

/**
 @@param {getImgInfo} getImgInfo 获取图片信息
 */
export const getImgInfo = async (e) => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: e,
      success: (res) => resolve(res),
      fail: (err) => reject(err)
    });
  });
};

/**
 @@param {copy} copy 复制
 */
export const copy = (e) =>
  uni.setClipboardData({
    data: e + "",
    success: function () {
      uni.$u.toast("复制成功");
    }
  });

/**
 @@param {mergeList} mergeList 改变数组内相同元素并合并
 */
export const mergeList = (arr, newArr, name) => {
  arr.map((val) => {
    let newName = newArr.find((i) => i.name == val.name);
    if (!newName) {
      newArr.push({
        name: val.name,
        val: [val]
      });
    } else {
      newName.val.push(val);
    }
  });
};

/**
 @@param {BaseToUrl} BaseToUrl Base64转url
 */
export const BaseToUrl = (base64) => {
  var arr = base64.split(",");
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  for (var i = 0; i < n; i++) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  var url = URL || webkitURL;
  return url.createObjectURL(
    new Blob([u8arr], {
      type: mime
    })
  );
};

/**
 @@param {navto} navto 跳转页面
 */
export const navto = (e, type = "nav") => {
  switch (type) {
    case "nav":
      uni.navigateTo({
        url: e
      });
      break;
    case "tab":
      uni.switchTab({
        url: e
      });
      break;
    case "red":
      uni.redirectTo({
        url: e
      });
      break;
    case "rel":
      uni.reLaunch({
        url: e
      });
      break;
  }
};

/**
 @@param {back} back 返回上一级页面
 */
export const back = (e) => {
  if (e?.tip) uni.$u.toast(e.tip);
  setTimeout(() => {
    uni.navigateBack({
      delta: e?.num || 1
    });
  }, e?.time || 3000);
};

/**
 @@param {download} download 下载文件
 */
export const download = (e) => {
  uni.downloadFile({
    url: e,
    success: (res) => {
      //下载成功
      if (res.statusCode === 200) {
        uni.getFileSystemManager().saveFile({
          tempFilePath: res.tempFilePath,
          success(res) {
            uni.showLoading({
              title: "正在打开"
            });
            uni.openDocument({
              filePath: res.savedFilePath,
              showMenu: true, //是否可以分享
              success: (res) => {
                uni.hideLoading();
              },
              fail: (e) => {
                uni.hideLoading();
                uni.$u.toast("打开失败");
              }
            });
          }
        });
      }
    },
    fail: (e) => {
      console.log(e, "文件下载失败");
      uni.showToast({
        title: "文件下载失败",
        icon: "error"
      });
    }
  });
};

/**
 @@param {getItem} getItem 根据对应值获取数组内的状态
 */
export const getItem = (arr, e) => arr[e];
/**


/**
 @@param {getDifferTime} getDifferTime 获取相差多少时间
 */
export const getDifferTime = (endTime, startTime = Date.now()) => {
  if ((startTime + "").length <= 10) startTime = startTime * 1000;
  if ((endTime + "").length <= 10) endTime = endTime * 1000;
  let date1 = new Date(startTime); //开始时间
  let date2 = new Date(endTime); //结束时间
  let date3 = date2.getTime() - date1.getTime(); //时间差的毫秒数
  //计算出相差天数
  let d = Math.floor(date3 / (24 * 3600 * 1000));
  //计算出小时数
  let leave1 = date3 % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
  let h = Math.floor(leave1 / (3600 * 1000));
  //计算相差分钟数
  let leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
  let m = Math.floor(leave2 / (60 * 1000));
  //计算相差秒数
  let leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
  let s = Math.round(leave3 / 1000);
  return {
    d,
    h,
    m,
    s
  };
};

/**
 *
 @@param {callPhone} callPhone 打电话
 */
export const callPhone = (e) =>
  uni.makePhoneCall({
    phoneNumber: e,
    success: (res) => {},
    fail: (res) => {
      uni.$u.toast("联系失败");
    }
  });

/**
 *
 @@param {pay} pay 支付
 */
export const pay = (e) => {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: e.provider,
      // #ifdef MP-WEIXIN
      timeStamp: e.timeStamp.toString(),
      nonceStr: e.nonceStr,
      package: e.package,
      signType: e.signType,
      paySign: e.paySign,
      // #endif
      // #ifdef MP-TOUTIAO
      orderInfo: {
        order_id: e.order_id,
        order_token: e.order_token
      },
      service: 5,
      // #endif
      // #ifdef MP-KUAISHOU
      orderInfo: {
        order_id: e.order_id,
        order_token: e.order_token
      },
      // #endif
      success: function (res) {
        resolve(res);
      },
      fail: function (err) {
        resolve(err);
      }
    });
  });
};

/**
 *
 @@param {resetList} resetList 重置列表
 */
// export const resetList = (e) => {
//   e.form ? e.form : form
// }

/**
 *
 @@param {getAge} getAge 通过生日获取年龄
 */
export const getAge = (val) => {
  let currentYear = new Date().getFullYear(); //当前的年份
  let calculationYear = new Date(val).getFullYear(); //计算的年份
  const wholeTime = currentYear + val.substring(4); //周岁时间
  const calculationAge = currentYear - calculationYear; //按照年份计算的年龄
  //判断是否过了生日
  if (new Date().getTime() > new Date(wholeTime).getTime()) {
    return calculationAge;
  } else {
    return calculationAge - 1;
  }
};

/**
 *
 @@param {login} login 登录
 */
export const login = (e) => {
  uni.login({
    provider: "weixin",
    success: async (loginRes) => {
      // 🆕 新增：尝试获取用户当前城市信息
      let userCity = '';
      try {
        // 获取用户位置
        const locationRes = await new Promise((resolve, reject) => {
          uni.getLocation({
            type: 'gcj02',
            success: resolve,
            fail: reject
          });
        });

        // 通过高德API获取城市信息
        if (locationRes && locationRes.latitude && locationRes.longitude) {
          const addrRes = await getAddr({
            latitude: locationRes.latitude,
            longitude: locationRes.longitude
          });

          if (addrRes && addrRes.status == 1) {
            const add = addrRes.regeocode.addressComponent;
            userCity = uni.$u.test.isEmpty(add.city) ? add.province : add.city;
          }
        }
      } catch (error) {
        console.log('获取城市信息失败，将使用空值注册:', error);
        // 获取城市信息失败不影响登录流程
      }

      const res = await userlogin({
        code: loginRes.code,
        pid: store().$state.pid,
        city: userCity // 🆕 新增：传递城市信息
      });
      if (res.status === "ok") e.options ? e.fun(e.options) : e.fun();
      else uni.$u.toast("登录失败");
    }
  });
};

/**
 *
 @@param {getUserInfo} getUserInfo 获取用户信息并存入pinia
 */
export const getUserInfo = async (fun) => {
  try {
    const res = await userget_user_info();
    console.log('获取用户信息响应:', res);

    if (res && res.status === "ok" && res.data) {
      store().changeUserInfo(res.data);
      console.log('用户信息已更新到store:', res.data);
    } else if (res && res.status === "relogin") {
      // 处理认证失败的情况
      console.log('用户认证失败，清理登录状态:', res.msg);

      try {
        // 动态导入auth模块避免循环依赖
        const { handleAuthFailure } = await import('@/utils/auth');
        // 使用统一的认证失败处理机制
        handleAuthFailure(res, 'getUserInfo');
      } catch (error) {
        console.error('处理认证失败时出错:', error);
        // 备用处理：直接清理本地状态
        try {
          const { logout } = await import('@/utils/auth');
          logout();
        } catch (logoutError) {
          console.error('备用登出处理失败:', logoutError);
        }
      }
    } else {
      console.log('获取用户信息失败:', res);
    }

    if (fun && typeof fun === 'function') {
      fun();
    }
  } catch (error) {
    console.error('获取用户信息异常:', error);
  }
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isUserLoggedIn = () => {
  const userInfo = store().$state.userInfo;
  return !!(userInfo?.uid && userInfo?.token);
};

/**
 * 安全调用需要登录的API
 * @param {Function} apiCall API调用函数
 * @param {string} errorMessage 未登录时的错误提示
 * @returns {Promise} API调用结果或错误
 */
export const safeApiCall = async (apiCall, errorMessage = '请先登录后再操作') => {
  if (!isUserLoggedIn()) {
    console.log('用户未登录，跳过API调用');
    return {
      status: 'relogin',
      msg: errorMessage
    };
  }

  try {
    return await apiCall();
  } catch (error) {
    console.error('API调用失败:', error);
    return {
      status: 'error',
      msg: error.message || '操作失败，请重试'
    };
  }
};

/**
 *
 @@param {pxToRpx} pxToRpx px转rpx
 */
// 导入新的系统信息API
import { pxToRpx as newPxToRpx, getWindowInfo } from './systemInfo.js';

export const pxToRpx = (px) => {
  try {
    // 使用新的API替换方案
    return newPxToRpx(px);
  } catch (error) {
    console.warn('pxToRpx转换失败，使用默认值:', error);
    // 使用默认屏幕宽度 375px
    return (750 * Number.parseInt(px)) / 375;
  }
};

export const timeSetResult = () => (1713024000000 > new Date() * 1 ? true : false);
