<?php
namespace controller;
use core\Controller;
use core\Db;
class Uploadimg extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	public function index(){
		responseType("json");
		if(!empty($_REQUEST['path']) && is_string($_REQUEST['path']) && check($_REQUEST['path'],"azAZ09_")){
			$path = trim($_REQUEST['path']);
		}else{
			$path = "news";
		}
		if(!empty($_FILES['image'])){
			$url = Upload()->move($_FILES['image'],$path);
		}else if(!empty($_FILES['video'])){
			$url = Upload()->move($_FILES['video'],$path);
		}else if(!empty($_FILES['file'])){
			$url = Upload()->move($_FILES['file'],$path);
		}else{
			return ['errno'=>1,"message"=>"上传失败[名称不存在]"];
		}
		if(!empty($url)){
			return ['errno'=>0,"data"=>['url'=>$url]];
		}else{
			$error_arr = \core\Upload::getError();
			if(count($error_arr) > 0){
				$msg = end($error_arr);
			}else{
				$msg = "未知错误";
			}
			return ['errno'=>1,"message"=>"上传失败[{$msg}]"];
		}
	}
	
	public function get_info($format=1){
		responseType("json");
		if(!empty($_REQUEST['path']) && is_string($_REQUEST['path']) && check($_REQUEST['path'],"azAZ09_")){
			$path = trim($_REQUEST['path']);
		}else{
			$path = "news";
		}
		if(!empty($_FILES['image'])){
			$file = $_FILES['image'];
		}else if(!empty($_FILES['video'])){
			$file = $_FILES['video'];
		}else if(!empty($_FILES['file'])){
			$file = $_FILES['file'];
		}else{
			return ['errno'=>1,"message"=>"上传失败[名称不存在]"];
		}
		$url = Upload()->move($file,$path);
		if(!empty($url)){
			$name = $file['name'];
			$type = $file['type'];
			$name_arr = explode(".",$name);
			$ext = strtolower(end($name_arr));
			if(!empty($format)){
				$size = format_file_size($file['size']);
			}else{
				$size = $file['size'];
			}
			$info = [
				"type"=>$type,
				"ext"=>$ext,
				"name"=>$name,
				"size"=>$size,
				"url"=>$url,
			];
			return ['errno'=>0,"data"=>$info];
		}else{
			$error_arr = \core\Upload::getError();
			if(count($error_arr) > 0){
				$msg = end($error_arr);
			}else{
				$msg = "未知错误";
			}
			return ['errno'=>1,"message"=>"上传失败[{$msg}]"];
		}
	}

	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
