<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 支付
*/
class Pay extends Controller{

	public function __construct(){
		parent::__construct();
	}

	//校验商城订单信息
	static private function goods_order_verify($order_id,$uid=0){
		dbConn();
		$order_info = Db()->table("goods_order")->select("goods_info,money,status")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
		if(empty($order_info)){
			return ["status"=>"error","msg"=>"订单信息未找到"];
		}else if($order_info['status'] != 0){
			return ["status"=>"error","msg"=>"订单状态异常"];
		}
		$goods_data = json_decode($order_info['goods_info'],true);//[{"goods_id":1,"guige_id":1,"num":2,"money":2.4,"guige_info":{},"goods_name":"ddd"}]
		if(empty($goods_data)){
			return ["status"=>"error","msg"=>"商品信息错误1"];
		}
		//
		$user_info = Db()->table("user")->select("uid,is_huiyuan")->where("uid={$uid}")->fetch();
		if(empty($user_info)){
			return ["status"=>"error","msg"=>"用户信息未找到"];
		}
		//
		$zong_money = 0;
		foreach($goods_data as $row){
			if(
				!isset($row['goods_id']) ||
				!isset($row['guige_id']) ||
				!isset($row['num']) ||
				!isset($row['money']) ||
				!check($row['goods_id'],"intgt0") ||
				!check($row['guige_id'],"intgt0") ||
				!check($row['num'],"intgt0") ||
				!is_numeric($row['money']) ||
				bccomp($row['money'],"0.00",2) < 0 ||
				count($row) !== 6
			){
				return ["status"=>"error","msg"=>"商品信息错误2"];
			}
			$goods_id = (int)$row['goods_id'];
			$guige_id = (int)$row['guige_id'];
			$num = (int)$row['num'];
			$money = number_format($row['money'],2,".","");
			//
			$goods_info = Db()->table("goods")->select("id,name,status")->where("id={$goods_id} AND status=1")->fetch();
			if(empty($goods_info)){
				return ["status"=>"error","msg"=>"商品信息不存在或已下架:{$goods_id}"];
			}
			//
			$guige_info = Db()->table("goods_guige")->select("price,kucun,is_shiyong")->where("id={$guige_id} AND goods_id={$goods_id}")->fetch();
			if(empty($guige_info)){
				return ["status"=>"error","msg"=>"该商品规格不存在或已下架:{$guige_id}"];
			}
			//
			if($user_info['is_huiyuan'] == 1 && $guige_info['is_shiyong'] == 1){
				$money_count = 0;
			}else{
				$money_count = number_format($guige_info['price'] * $num,2,".","");
			}
			if(bccomp($money_count,$money,2) !== 0){
				return ["status"=>"error","msg"=>"商品价格已调整:{$guige_id}"];
			}
			//
			$zong_money += $money_count;
		}
		$zong_money = number_format($zong_money,2,".","");
		if(bccomp($zong_money,$order_info['money'],2) !== 0){
			return ["status"=>"error","msg"=>"商品金额异常:{$order_id}"];
		}
		return ["status"=>"ok"];
	}

	//支付后商城商品增加销量
	static private function add_sell_num($order_id){
		dbConn();
		$goods_info = Db()->table("goods_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->getColumn("goods_info");
		if(empty($goods_info)){
			return ["status"=>"error","msg"=>"订单信息未找到"];
		}
		$goods_data = json_decode($goods_info,true);//[{"goods_id":1,"guige_id":1,"num":2,"money":2.4,"guige_info":{},"goods_name":"ddd"}]
		if(empty($goods_data)){
			return ["status"=>"error","msg"=>"商品信息错误1"];
		}
		foreach($goods_data as $row){
			if(
				!isset($row['goods_id']) ||
				!isset($row['guige_id']) ||
				!isset($row['num']) ||
				!isset($row['money']) ||
				!check($row['goods_id'],"intgt0") ||
				!check($row['guige_id'],"intgt0") ||
				!check($row['num'],"intgt0") ||
				!is_numeric($row['money']) ||
				bccomp($row['money'],"0.00",2) < 0 ||
				count($row) !== 6
			){
				continue;
			}
			$goods_id = (int)$row['goods_id'];
			$guige_id = (int)$row['guige_id'];
			$num = (int)$row['num'];
			//
			$sql = "UPDATE `goods` SET `sell_num`=`sell_num` + {$num} WHERE id={$goods_id}";
			Db()->_exec($sql);
			$sql = "UPDATE `goods_guige` SET `sell_num`=`sell_num` + {$num} WHERE id={$guige_id}";
			Db()->_exec($sql);
			//
		}
		return ["status"=>"ok"];
	}

	/*
	* @apiName 余额支付
	* @method yue_pay
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param type string 类型:1=商城支付,2=活动报名,3=购买会员,4=大礼包支付
	* @param order_id string 订单编号
	* @param money string 金额
	* @return {"status":"ok","msg":"余额支付成功"}
	*/
	public function yue_pay($uid,$token,$type,$order_id,$money){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($order_id) ||
			!is_numeric($money) ||
			$money <= 0 ||
			!check($type,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误1"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$type = (int)$type;
		$money = number_format($money,2,".","");
		dbConn();
		//
		$user_info = Db()->table("user")->select("uid,money,p_uid")->where("uid={$uid}")->fetch();
		if(empty($user_info)){
			return ["status"=>"error","msg"=>"获取用户信息失败"];
		}
		//验证订单
		if($type == 1){
			$res = self::goods_order_verify($order_id,$uid);
			if($res['status'] != "ok"){
				return $res;
			}
			$order_info = Db()->table("goods_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$zhangdan_type = 11;
			$table_name = "goods_order";
		}else if($type == 2){
			$order_info = Db()->table("huodong_baoming_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$zhangdan_type = 8;
			$table_name = "huodong_baoming_order";
		}else if($type == 3){
			$order_info = Db()->table("user_huiyuan_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$zhangdan_type = 12;
			$table_name = "user_huiyuan_order";
		}else if($type == 4){
			$order_info = Db()->table("dalibao_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$zhangdan_type = 14;
			$table_name = "dalibao_order";
		}else{
			return ["status"=>"error","msg"=>"参数错误2"];
		}
		if(empty($order_info) || $order_info['status'] != 0 || bccomp($order_info['money'],$money,2) !== 0){
			return ["status"=>"error","msg"=>"订单状态异常2"];
		}
		//
		if(bccomp($order_info['money'],$user_info['money'],2) > 0){
			return ["status"=>"error","msg"=>"余额不足"];
		}
		//
		Db::begin();
		try{
			//
			$sql = "UPDATE `user` SET `money`=`money`-{$order_info['money']} WHERE uid={$uid}";
			$rowCount = Db()->_exec($sql);
			if(empty($rowCount)){
				throw new \Exception("扣款失败");
			}
			//
			$shengyu = Db()->table("user")->where("uid={$uid}")->getColumn("money",0);
			$zhangdan = [
				"uid"=>$uid,
				"money"=>$order_info['money'],
				"type"=>$zhangdan_type,
				"shengyu"=>$shengyu,
				"msg"=>"订单编号:{$order_id}",
			];
			Db()->table("user_zhangdan")->insert($zhangdan);
			//
			$sql = "UPDATE `{$table_name}` SET `status`=1 WHERE order_id=:order_id AND status=0";
			$rowCount = Db()->_exec($sql,[":order_id"=>$order_id]);
			if(empty($rowCount)){
				throw new \Exception("订单不存在或已处理2");
			}
			$sql = "UPDATE `{$table_name}` SET `pay_time`='".DATETIME."',`pay_type`=2 WHERE order_id=:order_id";
			$rowCount = Db()->_exec($sql,[":order_id"=>$order_id]);
			//
			if($table_name == "goods_order"){
				self::add_sell_num($order_id);
			}else if($table_name == "huodong_baoming_order"){
				// 检查活动是否存在且报名人数未满
				$huodong_check = Db()->table("huodong")->select("id, num, baoming_num")->where("id={$order_info['huodong_id']}")->fetch();
				if(!empty($huodong_check) && $huodong_check['baoming_num'] < $huodong_check['num']) {
					$sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`+1 WHERE id={$order_info['huodong_id']} AND baoming_num < num"; // 增加乐观锁检查
					$update_huodong_count = Db::_exec($sql);
					if(empty($update_huodong_count)){
						// 如果更新失败（可能并发导致人数已满），记录异常但不中断支付确认流程
						$this->exception_log("微信支付通知：更新活动报名人数失败（可能已满或并发） order_id: {$order_id}, huodong_id: {$order_info['huodong_id']}");
					}
				} else {
					// 活动不存在或人数已满，记录异常
					$this->exception_log("微信支付通知：活动不存在或报名人数已满 order_id: {$order_id}, huodong_id: {$order_info['huodong_id']}");
				}
			}else if($table_name == "user_huiyuan_order" || $table_name == "dalibao_order"){
				//更新会员信息
				$huiyuan_days = $this->get_config("huiyuan_days");
				$huiyuan_end_time = date("Y-m-d 23:59:59",strtotime("+{$huiyuan_days} days"));
				$sql = "UPDATE `user` SET is_huiyuan=1,huiyuan_end_time='{$huiyuan_end_time}' WHERE uid={$order_info['uid']}";
				Db()->_exec($sql);
				//
				// 注意：已删除总消费字段，不再需要更新user表的统计字段

				// 添加会员初始积分
				$current_points = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("points");
				if($current_points == 0) { // 只有积分为0时才给初始积分，避免重复给予
					$initial_points = 100;
					$sql = "UPDATE `user` SET `points`=`points`+{$initial_points} WHERE uid={$order_info['uid']}";
					Db::_exec($sql);

					// 获取更新后的积分余额
					$points_balance = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("points");

					// 插入积分记录
					$points_log_data = [
						"uid" => $order_info['uid'],
						"points_change" => $initial_points,
						"points_balance" => $points_balance,
						"source_type" => "member_init",
						"source_id" => null,
						"description" => "开通会员初始积分奖励"
					];
					Db()->table("user_points_log")->insert($points_log_data);
				}
				//
			}else{
				throw new \Exception("未知订单类型：{$table_name} - {$order_id}");
			}
			Db::commit();
			return ["status"=>"ok","msg"=>"余额支付成功"];
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙,请稍后再试"];
		}

	}

	/*
	* @apiName 微信支付下单
	* @method weixin_pay
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param type string 类型:1=商城支付,2=活动报名,3=购买会员,4=充值,5=大礼包支付
	* @param order_id string 订单编号
	* @param money string 金额
	* @return {"status":"ok","prepay_id":"wx0116525740157731b210090a822cc80000"}
	*/
	public function weixin_pay($uid,$token,$type,$order_id,$money,$pay_type="jsapi"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($order_id) ||
			!is_numeric($money) ||
			$money <= 0 ||
			!check($type,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误1"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$type = (int)$type;
		$money = number_format($money,2,".","");
		dbConn();
		//验证订单
		if($type == 1){
			$res = self::goods_order_verify($order_id,$uid);
			if($res['status'] != "ok"){
				return $res;
			}
			$order_info = Db()->table("goods_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$description = $order_info['goods_info_title'];
			$attach = "goods_order";
		}else if($type == 2){
			$order_info = Db()->table("huodong_baoming_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$description = "活动报名";
			$attach = "huodong_baoming_order";
		}else if($type == 3){
			$order_info = Db()->table("user_huiyuan_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$description = "购买会员";
			$attach = "user_huiyuan_order";
		}else if($type == 4){
			$order_info = Db()->table("user_chongzhi_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$description = "充值";
			$attach = "user_chongzhi_order";
		}else if($type == 5){
			$order_info = Db()->table("dalibao_order")->where("order_id=:order_id")->prepareParam([":order_id"=>$order_id])->fetch();
			$description = "大礼包支付";
			$attach = "dalibao_order";
		}else{
			return ["status"=>"error","msg"=>"参数错误2"];
		}
		if(empty($order_info) || $order_info['status'] != 0 || bccomp($order_info['money'],$money,2) !== 0){
			return ["status"=>"error","msg"=>"订单状态异常"];
		}
		//
		$money_fen = intval($order_info['money'] * 100);
		//
		if($pay_type == "jsapi"){
			$openid = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("openid");
			if(empty($openid)){
				return ["status"=>"error","msg"=>"获取openid失败"];
			}
			$res = \model\Wechat::jsapi($money_fen,$openid,$order_id,$description,$attach);

			if($res['status'] != "ok" || !isset($res['prepay_id'])){
				$msg = isset($res['msg']) ? ":" . $res['msg'] : "";
				return ["status"=>"error","msg"=>"微信jsapi支付下单失败:{$msg}"];
			}
			return ["status"=>"ok","prepay_id"=>$res['prepay_id']];
		}else if($pay_type == "h5"){
			$res = \model\Wechat::h5($money_fen,$order_id,$description,$attach);
			if($res['status'] != "ok" || !isset($res['url'])){
				$msg = isset($res['msg']) ? ":" . $res['msg'] : "";
				return ["status"=>"error","msg"=>"微信h5支付下单失败:{$msg}"];
			}
			return ["status"=>"ok","data"=>$res['url']];
		}else if($pay_type == "app"){
			$res = \model\Wechat::app($money_fen,$order_id,$description,$attach);
			if($res['status'] != "ok" || !isset($res['prepay_id'])){
				$msg = isset($res['msg']) ? ":" . $res['msg'] : "";
				return ["status"=>"error","msg"=>"微信app支付下单失败:{$msg}"];
			}
			return ["status"=>"ok","prepay_id"=>$res['prepay_id']];
		}
		return ["status"=>"error","msg"=>"微信支付下单失败"];
	}

	/*
	* @apiName 获取微信支付调起支付签名V3
	* @method get_weixinpay_sign
	* @POST
	* @param prepay_id string 预下单编号
	* @return {"timeStamp":1692752801,"nonceStr":"xnlfmeljzkgjsrq6tk1insgwzsv5e36l","package":"prepay_id=fdsafdsaf","signType":"RSA","paySign":"HLcpcOiTbGSJ0kFM2cyLMxsI\/04K1iuRC8Q3pztkPxsh1mQxJH93zNz7QZfNR9NIP31PjdKucB5SCaIYShPf\/+C1hHeAVqDy9kKOX3v1\/D4tdEBndV9kMqwb\/3wiMjcgSUoS5WnQQBn0QayL6\/eT9Cg3sUpNUwYGHcM1XEAfZ9RfOdHoW0zuhTpP5RTU119f9JL5omsp0Odi0gQ1igO2P81Jo7whL1EqWvtV4xjjiRz24a5lxEdUSboehZV4bawBpmDs2Xdc547z0I\/w6GTYONXNoQrf8MgutC+8nAeAS\/QLSVgIdpAgMxOHSJwLNfE14PwVs+jqoiR4x52qrmQKNg=="}
	*/

	public function get_weixinpay_sign($prepay_id){
		return \model\Wechat::getPaySign($prepay_id);
	}

	//微信支付通知
	public function weixinpay_notify(){
		//
		$input = file_get_contents("php://input");
		file_put_contents("weixin_pay.log",date("Y-m-d H:i:s") . " : " . $input . "\r\n",FILE_APPEND);
		//
		$res = file_get_contents("php://input");
		$result = json_decode($res, true);
		if(empty($result) || !isset($result['event_type']) || $result['event_type'] != "TRANSACTION.SUCCESS"){
			header("HTTP/1.0 500");
			echo json_encode(["code"=>"FAIL","message"=>"数据解析错误"]);
			exit;
		}
		//
		$data = \model\Wechat::get_notify($res);
		//
		if(empty($data) || strtoupper($data['trade_state']) != "SUCCESS"){
			header("HTTP/1.0 500");
			echo json_encode(["code"=>"FAIL","message"=>"验证签名或解密失败"]);
			exit;
		}
		//
		$transaction_id = $data['transaction_id'];//微信平台交易编号
		$out_trade_no = $data['out_trade_no'];//商品订单号
		$attach = $data['attach'];//自定义数据
		$total = $data['amount']['total'];//订单总金额 分
		$amount = $data['amount']['payer_total'];//用户支付金额 分
		$openid = $data['payer']['openid'];//用户openid
		//
		dbConn();

		// 🔧 P0修复：添加支付回调处理日志和重复处理检查
		$total_fee = $data['amount']['payer_total']; // 用户支付金额（分）
		$GLOBALS['current_transaction_id'] = $transaction_id; // 设置全局变量供日志使用

		$this->log_payment_callback($out_trade_no, $attach, $total_fee, 0, "开始处理支付回调");

		// 检查是否重复处理
		if($this->is_payment_already_processed($out_trade_no, $attach)) {
			$this->log_payment_callback($out_trade_no, $attach, $total_fee, 1, "订单已处理，跳过重复处理");
			header("HTTP/1.0 200");
			exit;
		}

		Db()->begin();
		try{
			if($attach == "goods_order"){
				$order_info = Db()->table("goods_order")->where("order_id=:order_id AND status=0")->prepareParam([":order_id"=>$out_trade_no])->fetch();
			}else if($attach == "huodong_baoming_order"){
				$order_info = Db()->table("huodong_baoming_order")->where("order_id=:order_id AND status=0")->prepareParam([":order_id"=>$out_trade_no])->fetch();
			}else if($attach == "user_huiyuan_order"){
				$order_info = Db()->table("user_huiyuan_order")->where("order_id=:order_id AND status=0")->prepareParam([":order_id"=>$out_trade_no])->fetch();
			}else if($attach == "user_chongzhi_order"){
				$order_info = Db()->table("user_chongzhi_order")->where("order_id=:order_id AND status=0")->prepareParam([":order_id"=>$out_trade_no])->fetch();
			}else if($attach == "dalibao_order"){
				$order_info = Db()->table("dalibao_order")->where("order_id=:order_id AND status=0")->prepareParam([":order_id"=>$out_trade_no])->fetch();
			}else{
				throw new \Exception("未知订单类型：{$attach} - {$out_trade_no}");
			}
			if(empty($order_info)){
				throw new \Exception("订单不存在或已处理1");
			}
			//
			if(bccomp(number_format($amount/100,2,".",""),$order_info['money'],2) !== 0){
				throw new \Exception("订单金额不对应");
			}
			//
			$sql = "UPDATE `{$attach}` SET `status`=1 WHERE order_id=:order_id AND status=0";
			$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
			if(empty($rowCount)){
				throw new \Exception("订单不存在或已处理2");
			}
			//
			if($attach == "goods_order"){
				$sql = "UPDATE `{$attach}` SET `pay_time`='".DATETIME."',`pay_type`=1,`transaction_id`='{$transaction_id}' WHERE order_id=:order_id";
				$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
				self::add_sell_num($out_trade_no);
				// 注意：已删除总消费字段，不再需要更新user表的统计字段
			}else if($attach == "huodong_baoming_order"){
				$sql = "UPDATE `{$attach}` SET `pay_time`='".DATETIME."',`pay_type`=1 WHERE order_id=:order_id";
				$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
				//
				// 🔧 P0修复：使用行锁检查活动是否存在且报名人数未满
				$huodong_check = Db()->table("huodong")
					->select("id, num, baoming_num")
					->where("id={$order_info['huodong_id']}")
					->forUpdate() // 添加行锁，防止并发问题
					->fetch();

				if(!empty($huodong_check) && $huodong_check['baoming_num'] < $huodong_check['num']) {
					// 使用参数化查询更新报名人数
					$sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`+1 WHERE id=:huodong_id AND baoming_num < num"; // 保留乐观锁检查
					$update_huodong_count = Db::_exec($sql, [":huodong_id" => $order_info['huodong_id']]);
					if(empty($update_huodong_count)){
						// 如果更新失败（可能并发导致人数已满），记录异常但不中断支付确认流程
						$this->exception_log("微信支付通知：更新活动报名人数失败（可能已满或并发） order_id: {$out_trade_no}, huodong_id: {$order_info['huodong_id']}");
					}
				} else {
					// 活动不存在或人数已满，记录异常
					$this->exception_log("微信支付通知：活动不存在或报名人数已满 order_id: {$out_trade_no}, huodong_id: {$order_info['huodong_id']}");
				}

				// 获取活动信息并创建报名成功通知
				$huodong_info = Db()->table("huodong")->select("name")->where("id={$order_info['huodong_id']}")->fetch();
				if (!empty($huodong_info)) {
					$this->create_notification($order_info['uid'], "activity_registration", "活动报名成功", "您已成功报名活动：{$huodong_info['name']}", $order_info['huodong_id']);
				}

				// 🆕 新增：处理活动发布方收入分配
				$this->settle_activity_publisher_income($order_info);

				// 注意：已删除总消费字段，不再需要更新user表的统计字段
			}else if($attach == "user_huiyuan_order" || $attach == "dalibao_order"){
				//
				$sql = "UPDATE `{$attach}` SET `pay_time`='".DATETIME."',`pay_type`=1 WHERE order_id=:order_id";
				$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
				//更新会员信息
				$huiyuan_days = $this->get_config("huiyuan_days");
				$huiyuan_end_time = date("Y-m-d 23:59:59",strtotime("+{$huiyuan_days} days"));
				$sql = "UPDATE `user` SET is_huiyuan=1,huiyuan_end_time='{$huiyuan_end_time}' WHERE uid={$order_info['uid']}";
				Db()->_exec($sql);
				// 注意：已删除总消费字段，不再需要更新user表的统计字段

				// 处理会员佣金（仅针对会员订单）
				if($attach == "user_huiyuan_order") {
					// 调用User类的佣金结算方法（已包含运营佣金处理）
					$userController = new \controller\User();
					$userController->settle_member_commission($order_info);
				}

				// 添加会员初始积分
				$current_points = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("points");
				if($current_points == 0) { // 只有积分为0时才给初始积分，避免重复给予
					$initial_points = 100;
					$sql = "UPDATE `user` SET `points`=`points`+{$initial_points} WHERE uid={$order_info['uid']}";
					Db::_exec($sql);

					// 获取更新后的积分余额
					$points_balance = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("points");

					// 插入积分记录
					$points_log_data = [
						"uid" => $order_info['uid'],
						"points_change" => $initial_points,
						"points_balance" => $points_balance,
						"source_type" => "member_init",
						"source_id" => null,
						"description" => "开通会员初始积分奖励"
					];
					Db()->table("user_points_log")->insert($points_log_data);
				}
				//
			}else if($attach == "user_chongzhi_order"){
				//
				$sql = "UPDATE `{$attach}` SET `pay_time`='".DATETIME."' WHERE order_id=:order_id";
				$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
				//
				$money = $order_info['money'];
				$user_chongzhi = Db()->table("user_chongzhi")->fetchAll();
				foreach($user_chongzhi as $v){
					if(bccomp($money,$v['money'],2) == 0){
						$money = $v['daozhang_money'];
						break;
					}
				}
				$sql = "UPDATE `user` SET `money`=`money`+{$money} WHERE uid={$order_info['uid']}";
				$rowCount = Db()->_exec($sql);
				if(empty($rowCount)){
					throw new \Exception("微信充值到账余额失败：{$out_trade_no}");
				}
				//
				$shengyu = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("money",0);
				$zhangdan = [
					"uid"=>$order_info['uid'],
					"money"=>$money,
					"type"=>1,
					"shengyu"=>$shengyu,
					"msg"=>"微信充值：订单号{$out_trade_no}",
				];
				Db()->table("user_zhangdan")->insert($zhangdan);
				//
			}else{
				throw new \Exception("未知订单类型：{$attach} - {$out_trade_no}");
			}
			//
			// 🔧 P0修复：记录支付回调处理成功日志
			$this->log_payment_callback($out_trade_no, $attach, $total_fee, 1, "支付回调处理成功");

			Db()->commit();
		}catch(\Exception $e){
			// 🔧 P0修复：记录支付回调处理失败日志
			$this->log_payment_callback($out_trade_no, $attach, $total_fee, 2, "支付回调处理失败：" . $e->getMessage());

			Db()->rollback();
			$this->exception_log($e->getMessage());
			header("HTTP/1.0 500");
			echo json_encode(["code"=>"FAIL","message"=>$e->getMessage()]);
			exit;
		}
		//
		header("HTTP/1.0 200");
		exit;
	}

	//微信支付-退款通知
	public function weixinpay_tuikuan_notify(){
		$input = file_get_contents("php://input");
		file_put_contents("weixin_pay.log",date("Y-m-d H:i:s") . " : " . $input . "\r\n",FILE_APPEND);
		//
		$res = file_get_contents("php://input");
		$result = json_decode($res, true);
		if(empty($result) || !isset($result['event_type']) || stripos($result['event_type'],"REFUND") === false){
			header("HTTP/1.0 500");
			echo json_encode(["code"=>"FAIL","message"=>"数据解析错误"]);
			exit;
		}
		//
		$data = \model\Wechat::get_notify($res);
		//
		if(empty($data) || !isset($data['refund_status'])){
			header("HTTP/1.0 500");
			echo json_encode(["code"=>"FAIL","message"=>"验证签名或解密失败"]);
			exit;
		}
		//
		$transaction_id = $data['transaction_id'];//微信支付订单号
		$refund_id = $data['refund_id'];//微信支付退款单号
		$out_trade_no = $data['out_trade_no'];//商户订单号
		$out_refund_no = $data['out_refund_no'];//商户退款单号
		$amount = $data['amount']['payer_refund'];//退款给用户的金额 单位：分
		$refund_status = $data['refund_status'];//枚举值：SUCCESS：退款成功 CLOSED：退款关闭 ABNORMAL：退款异常
		//
		dbConn();
		Db()->begin();
		try{
			//
			$order_tables = [
				["name"=>"goods_order","before_status"=>5,"success"=>6,"failed"=>7],
				["name"=>"huodong_baoming_order","before_status"=>3,"success"=>4,"failed"=>5],
				["name"=>"user_chongzhi_order","before_status"=>3,"success"=>4,"failed"=>5],
				["name"=>"user_huiyuan_order","before_status"=>3,"success"=>4,"failed"=>5],
			];
			//
			$tuikuan_money = number_format($amount/100,2,".","");
			//
			$table_name = "";
			foreach($order_tables as $table){
				$order_info = Db()->table($table['name'])->where("order_id=:order_id AND status={$table['before_status']}")->prepareParam([":order_id"=>$out_trade_no])->fetch();
				if(!empty($order_info)){
					if($refund_status == "SUCCESS"){
						if($table['name'] == "goods_order"){
							$sql = "UPDATE `{$table['name']}` SET `tuikuan_res_time`='".DATETIME."',`status`={$table['success']},`tuikuan_money`={$tuikuan_money} WHERE order_id=:order_id AND status={$table['before_status']}";
						}else{
							$sql = "UPDATE `{$table['name']}` SET `status`={$table['success']} WHERE order_id=:order_id AND status={$table['before_status']}";
						}
					}else if($refund_status == "FAILED" || $refund_status == "CLOSED" || $refund_status == "ABNORMAL"){
						if($table['name'] == "goods_order"){
							$sql = "UPDATE `{$table['name']}` SET `tuikuan_res_time`='".DATETIME."',`status`={$table['failed']} WHERE order_id=:order_id AND status={$table['before_status']}";
						}else{
							$sql = "UPDATE `{$table['name']}` SET `status`={$table['failed']} WHERE order_id=:order_id AND status={$table['before_status']}";
						}
					}
					$rowCount = Db()->_exec($sql,[":order_id"=>$out_trade_no]);
					if(empty($rowCount)){
						throw new \Exception("微信退款通知事件更新订单状态失败：{$out_trade_no} | {$refund_status} | {$amount}");
					}
					break;
				}
			}
			//
			Db()->commit();
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			header("HTTP/1.0 500");
			return ["code"=>"FAIL","message"=>$e->getMessage()];
		}
		//
		header("HTTP/1.0 200");
		exit;
	}

	public function _empty(){
		return ["status"=>"error","msg"=>"URL error"];
	}

	function __destruct(){

	}

	/*
	 * 创建通知的私有方法
	 * @param int $uid 用户ID，如果为0则创建全局通知
	 * @param string $type 通知类型
	 * @param string $title 通知标题
	 * @param string $content 通知内容
	 * @param int $related_id 关联ID
	 */
	private function create_notification($uid, $type, $title, $content, $related_id = null) {
		try {
			$is_global = ($uid == 0) ? 1 : 0;
			$data = [
				"uid" => $uid,
				"type" => ":type",
				"title" => ":title",
				"content" => ":content",
				"related_id" => $related_id,
				"is_read" => 0,
				"is_global" => $is_global
			];
			$prepareParam = [
				":type" => htmlspecialchars($type),
				":title" => htmlspecialchars($title),
				":content" => htmlspecialchars($content)
			];

			Db()->table("user_notifications")
				->prepareParam($prepareParam)
				->insert($data);

		} catch (\Exception $e) {
			$this->exception_log("创建通知失败：" . $e->getMessage());
		}
	}

	/**
	 * 结算活动发布方收入
	 * @param array $order_info 订单信息
	 */
	private function settle_activity_publisher_income($order_info) {
		try {
			// 获取活动信息
			$activity = Db()->table("huodong")
				->select("id, uid, name, pay_type")
				->where("id={$order_info['huodong_id']}")
				->fetch();

			if (empty($activity)) {
				$this->exception_log("活动收入分配失败：活动不存在，订单：{$order_info['order_id']}");
				return;
			}

			// 只处理线上支付的活动
			if ($activity['pay_type'] != 1) {
				return; // 线下支付不需要分配收入
			}

			// 检查是否已经分配过收入
			$existing = Db()->table("activity_income_log")
				->where("order_id='{$order_info['order_id']}'")
				->fetch();

			if (!empty($existing)) {
				$this->exception_log("活动收入重复分配，跳过：{$order_info['order_id']}");
				return;
			}

			// 获取平台抽成比例
			$platform_fee_rate = (float)$this->get_activity_config("activity_platform_fee_rate", "10.00");
			$total_amount = (float)$order_info['money'];

			// 计算平台抽成和发布方收入
			$platform_fee = $total_amount * $platform_fee_rate / 100;
			$publisher_income = $total_amount - $platform_fee;

			// 插入收入记录
			$income_data = [
				"activity_id" => $order_info['huodong_id'],
				"publisher_uid" => $activity['uid'],
				"order_id" => $order_info['order_id'],
				"total_amount" => $total_amount,
				"platform_fee_rate" => $platform_fee_rate,
				"platform_fee" => $platform_fee,
				"publisher_income" => $publisher_income,
				"status" => 0, // 待结算
				"time" => DATETIME,
				"remark" => "活动报名收入，活动：{$activity['name']}"
			];

			$income_id = Db()->table("activity_income_log")->insert($income_data);

			if (!$income_id) {
				throw new \Exception("活动收入记录插入失败");
			}

			$this->exception_log("活动收入分配成功：订单{$order_info['order_id']}，收入{$publisher_income}元");

		} catch (\Exception $e) {
			$this->exception_log("活动收入分配异常：{$e->getMessage()}，订单：{$order_info['order_id']}");
		}
	}

	/**
	 * 获取活动配置项值
	 * @param string $name 配置项名称
	 * @param string $default 默认值
	 * @return string 配置项值
	 */
	private function get_activity_config($name, $default = "") {
		$config = Db()->table("config")->select("val")->where("name='{$name}'")->fetch();
		return $config ? $config['val'] : $default;
	}

	/**
	 * 🔧 P0修复：记录支付回调处理日志
	 * @param string $order_id 订单号
	 * @param string $order_type 订单类型
	 * @param float $amount 支付金额
	 * @param int $status 处理状态：0=处理中,1=成功,2=失败
	 * @param string $message 处理信息
	 */
	private function log_payment_callback($order_id, $order_type, $amount, $status, $message = "") {
		try {
			$log_data = [
				"order_id" => $order_id,
				"order_type" => $order_type,
				"amount" => number_format($amount / 100, 2, ".", ""),
				"status" => $status,
				"error_message" => $message,
				"transaction_id" => isset($GLOBALS['current_transaction_id']) ? $GLOBALS['current_transaction_id'] : null
			];
			Db()->table("payment_callback_log")->insert($log_data);
		} catch (\Exception $e) {
			$this->exception_log("记录支付回调日志失败：" . $e->getMessage());
		}
	}

	/**
	 * 🔧 P0修复：检查支付是否已处理
	 * @param string $order_id 订单号
	 * @param string $order_type 订单类型
	 * @return bool 是否已处理
	 */
	private function is_payment_already_processed($order_id, $order_type) {
		try {
			$existing_log = Db()->table("payment_callback_log")
				->where("order_id=:order_id AND order_type=:order_type AND status=1")
				->prepareParam([
					":order_id" => $order_id,
					":order_type" => $order_type
				])
				->fetch();
			return !empty($existing_log);
		} catch (\Exception $e) {
			$this->exception_log("检查支付处理状态失败：" . $e->getMessage());
			return false;
		}
	}

}
