/**
 * 公共工具函数
 * 提供可复用的业务逻辑和错误处理
 */

/**
 * 统一的错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 * @param {boolean} showToast - 是否显示错误提示
 * @param {string} defaultMessage - 默认错误消息
 */
export const handleCommonError = (error, context = '', showToast = true, defaultMessage = '操作失败，请稍后重试') => {
  console.error(`${context}错误:`, error);
  
  if (showToast) {
    let message = defaultMessage;
    
    // 根据错误类型提供更友好的提示
    if (error.message) {
      if (error.message.includes('network') || error.message.includes('timeout')) {
        message = '网络连接异常，请检查网络后重试';
      } else if (error.message.includes('permission')) {
        message = '权限不足，请检查相关权限设置';
      } else if (error.message.includes('not found') || error.message.includes('404')) {
        message = '请求的资源不存在';
      }
    }
    
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  return {
    error: true,
    message: error.message || defaultMessage,
    context
  };
};

/**
 * 统一的加载状态管理
 * @param {Object} loadingRef - loading状态的ref
 * @param {Function} asyncFunction - 异步函数
 * @param {string} context - 上下文信息
 */
export const withLoading = async (loadingRef, asyncFunction, context = '') => {
  try {
    if (loadingRef) loadingRef.value = true;
    const result = await asyncFunction();
    return result;
  } catch (error) {
    return handleCommonError(error, context);
  } finally {
    if (loadingRef) loadingRef.value = false;
  }
};

/**
 * 统一的数据验证函数
 * @param {any} data - 要验证的数据
 * @param {string} fieldName - 字段名称
 * @param {Object} rules - 验证规则
 */
export const validateData = (data, fieldName, rules = {}) => {
  const errors = [];
  
  // 必填验证
  if (rules.required && (!data || data === '')) {
    errors.push(`${fieldName}不能为空`);
  }
  
  // 长度验证
  if (rules.minLength && data && data.length < rules.minLength) {
    errors.push(`${fieldName}长度不能少于${rules.minLength}个字符`);
  }
  
  if (rules.maxLength && data && data.length > rules.maxLength) {
    errors.push(`${fieldName}长度不能超过${rules.maxLength}个字符`);
  }
  
  // 格式验证
  if (rules.pattern && data && !rules.pattern.test(data)) {
    errors.push(`${fieldName}格式不正确`);
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 统一的重试机制
 * @param {Function} asyncFunction - 要重试的异步函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 */
export const withRetry = async (asyncFunction, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await asyncFunction();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw error;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
  
  throw lastError;
};

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 */
export const debounce = (func, wait = 300) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 格式化日期
 * @param {Date|string} date - 日期
 * @param {string} format - 格式字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
};

/**
 * 检查是否为空值
 * @param {any} value - 要检查的值
 */
export const isEmpty = (value) => {
  if (value === null || value === undefined) {
    return true;
  }
  
  if (typeof value === 'string') {
    return value.trim() === '';
  }
  
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
};

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 */
export const generateId = (prefix = 'id') => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 条件日志输出（只在开发环境输出）
 * @param {string} level - 日志级别 (log, warn, error)
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
export const devLog = (level = 'log', message, data) => {
  if (process.env.NODE_ENV === 'development') {
    if (data !== undefined) {
      console[level](message, data);
    } else {
      console[level](message);
    }
  }
};
