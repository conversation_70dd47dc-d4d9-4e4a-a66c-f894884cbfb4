<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userdel_addr, userset_default_addr, userget_addr_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto, getListHeight } from "@/utils";

const gapHeight = ref(0);
const list = ref([]);
const type = ref("");
// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
const radiovalue = ref("");

onLoad(async (e) => {
  if (e.type) type.value = e.type;
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});
onShow(async () => {
  getList();
});

const getList = async () => {
  radiovalue.value = "";
  const res = await userget_addr_list();
  if (res.status === "ok") list.value = res.data;
  if (res.data)
    res.data.map((val) => {
      if (val.is_default === 1) radiovalue.value = val.id;
    });
};

const radioChange = async (id) => {
  const res = await userset_default_addr({ id });
  uni.$u.toast(res.msg);
};
const del = async (val, i) => {
  const res = await userdel_addr({ ids: val.id });
  if (res.status === "ok") getList();
};
const back = (e) => {
  if (type.value === "select") {
    store().changeAddr(e);
    uni.navigateBack();
  }
};
</script>
<template>
  <view class="page">
    <!-- <myTitle title="收货地址"></myTitle> -->
    <u-radio-group v-model="radiovalue" placement="column">
      <view class="mb20 w b6f" v-for="(val, i) in list" :key="i" @click="back(val)">
        <view class="item py30">
          <view class="df aie mb20 px30 fb">
            <view class="mr20 x32"> {{ val.username }} </view>
            <view class="x28">{{ val.mobile }} </view>
          </view>
          <view class="px30 x28">
            {{ val?.sheng }}-{{ val?.shi }}-{{ val?.qu }}-{{ val?.addr }}
          </view>
          <u-line color="#F5F2ED" length="750rpx" margin="38rpx 0 18rpx"></u-line>
          <view class="df aic jcsb px30">
            <u-radio
              :customStyle="{ marginBottom: '8px' }"
              active-color="#ef662d"
              :label-color="radiovalue == val.id ? '#ef662d' : '#8A857C'"
              :label="radiovalue == val.id ? '已设为默认' : '设为默认'"
              :name="val.id"
              @change="radioChange"
            >
            </u-radio>
            <view class="df aic">
              <view
                class="mr30"
                @click.stop="
                  navto(
                    `/pages/bundle/common/addAddress?info=${JSON.stringify({
                      ...val,
                      edit: true,
                    })}`
                  )
                "
              >
                <u-icon
                  :name="`${store().$state.url}editAddress.png`"
                  size="28rpx"
                  label="编辑"
                  label-color="#8A857C"
                  label-pos="right"
                  :stop="true"
                ></u-icon>
              </view>
              <u-icon
                :name="`${store().$state.url}delAddress.png`"
                size="28rpx"
                label="删除"
                label-color="#8A857C"
                label-pos="right"
                :stop="true"
                @click="del(val, i)"
              ></u-icon>
            </view>
          </view>
        </view>
      </view>
    </u-radio-group>
    <u-gap :height="gapHeight"></u-gap>
    <view class="pfx bottom0 bottomBox b6f">
      <u-button
        color="#FAD000"
        text="添加新地址"
        :customStyle="{
          width: '750rpx',
          height: '98rpx',
          color: '#000',
          fontSize: '34rpx',
        }"
        @click="navto(`/pages/bundle/common/addAddress`)"
      ></u-button>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
