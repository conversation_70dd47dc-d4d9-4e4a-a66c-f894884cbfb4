<?php
namespace model;
use core\Db;
//阿里云短信
class Alisms{
	
	static private $AccessKeyId = "";
	static private $AccessKeySecret = "";
	static private $SignName = "";
	static private $endpoint = "dysmsapi.aliyuncs.com";
	static private $commonParams = [
		"Action"=>"",
		"Version"=>"2017-05-25",
		"Format"=>"JSON",
		"AccessKeyId"=>"",
		"SignatureNonce"=>"",
		"Timestamp"=>"",
		"SignatureMethod"=>"HMAC-SHA1",
		"SignatureVersion"=>"1.0",
		"Signature"=>"",
	];
	
	public function __construct(){
		
	}
	
    //模板发送
    static public function send($PhoneNumbers,$TemplateParam=[],$TemplateCode=""){//
		$data = [
			"PhoneNumbers"=>$PhoneNumbers,//支持对多个手机号码发送短信，手机号码之间以半角逗号（,）分隔。上限为1000个手机号码
			"SignName"=>self::$SignName,//短信签名名称。
			"TemplateCode"=>$TemplateCode,//短信模板Code。
		];
		if(!empty($TemplateParam)){
			$TemplateParam = is_array($TemplateParam) ? $TemplateParam : ["code"=>$TemplateParam];
			$data['TemplateParam'] = json_encode($TemplateParam,JSON_UNESCAPED_UNICODE);//短信模板变量对应的实际值。支持传入多个参数。{"name":"张三","number":"1390000****"}
		}
		(self::$commonParams)['Action'] = "SendSms";
		self::getRequestSign($data);
		$url = "https://" . self::$endpoint . "/?" . http_build_query(self::$commonParams);
		$res = curl($url,$data);
		$data = json_decode($res['body'],true);
		if(isset($data['Code']) && $data['Code'] == "OK"){
			return ["status"=>"ok","msg"=>"发送成功"];
		}
		$msg = isset($data['Code']) ? $data['Code'] : "操作失败";
		$msg .= isset($data['Message']) ? ":【".$data['Message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
    }
	
	//获取签名后
    static public  function getRequestSign($params,$method="POST"){
		//
		$SignatureNonce = makeCode(16,true);
		$Timestamp = date("Y-m-d\TH:i:s\Z",time()-8*60*60);
		(self::$commonParams)['SignatureNonce'] = $SignatureNonce;
		(self::$commonParams)['Timestamp'] = $Timestamp;
		(self::$commonParams)['AccessKeyId'] = self::$AccessKeyId;
		//
		$params = array_merge($params,self::$commonParams);
        ksort($params);
		$sign_str = "";
        foreach ($params as $k => $v) {
			if($k == "Signature"){
				continue;
			}else{
				$sign_str .= $k . "=" . urlencode($v) . "&";
			}
        }
		$sign_str = substr($sign_str,0,-1);
		$sign_str = urlencode($sign_str);
		$sign_str = $method . "&" . urlencode("/") . "&" . $sign_str;
		$key = self::$AccessKeySecret . "&";
		$Signature = base64_encode(hash_hmac('sha1',$sign_str,$key,true));
		(self::$commonParams)['Signature'] = $Signature;
        return $Signature;
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
