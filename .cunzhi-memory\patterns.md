# 常用模式和最佳实践

- 分会管理系统架构规范：user_branch表存储分会基本信息不存储成员列表，分会长任命需同步更新user_branch表和变更记录表，分会长变更时自动通知分会成员，用户角色变更需实时同步分会信息和权限，支持针对特定分会的通知功能，分会长查询逻辑需处理空值情况显示友好提示
- 用户分会分配规则：分会长/城市分会长/场地第三方邀请用户分配到邀请人分会，自主注册/普通用户邀请/role_type=5邀请使用智能分配（优先同城分会长，无同城则不分配），role_type=3需要创建分会，计数器存储在user_branch表的assignment_counter字段而非配置表
- 数据库查询代码模式：使用Db::_fetch($sql, [$param])进行参数化查询，feeds表type字段只支持'feed'和'diary'两种类型，个人中心页面空数据时不显示"获取数据失败"等错误提示，只在真正网络错误时显示提示，服务器返回空数据(status=empty或res=n)时静默处理不显示错误
