<?php
namespace controller;
use core\Controller;
use core\Db;
class Rchat extends Controller{

	public function __construct(){
		parent::__construct();
	}

	public function index($channel="rchat",$nickname=""){
		if(empty($channel)){
			$channel = "rchat";
		}
		if(empty($nickname)){
			$nickname = "匿名:".makeCode(6,true);
		}
		assign("channel",$channel);
		assign("nickname",$nickname);
		return view(["rchat/index"]);
	}
	
	public function __destruct(){

	}
}
