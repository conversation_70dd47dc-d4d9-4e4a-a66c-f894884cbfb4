<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { goodsget_goods_pingjia } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto, getItem } from "@/utils";

const tabList = ref([
  {
    name: "好评",
  },
  {
    name: "中评",
  },
  {
    name: "差评",
  },
]);
const form = ref({
  goods_id: null,
  guige_id: null,
  star_nums: "4,5",
  has_img: 1,
});
const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");
const urls = ref([
  "https://cdn.uviewui.com/uview/album/1.jpg",
  "https://cdn.uviewui.com/uview/album/2.jpg",
  "https://cdn.uviewui.com/uview/album/3.jpg",
  "https://cdn.uviewui.com/uview/album/4.jpg",
  "https://cdn.uviewui.com/uview/album/5.jpg",
  "https://cdn.uviewui.com/uview/album/6.jpg",
  "https://cdn.uviewui.com/uview/album/7.jpg",
  "https://cdn.uviewui.com/uview/album/8.jpg",
  "https://cdn.uviewui.com/uview/album/9.jpg",
  "https://cdn.uviewui.com/uview/album/10.jpg",
]);

onLoad((e) => {
  form.value.goods_id = e.goods_id;
  form.value.guige_id = e.guige_id;
});
onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  goodsget_goods_pingjia({ ...form.value, page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.list || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};
const changeTabs = (e) => {
  switch (+e.index) {
    case 0:
      form.value.star_nums = "4,5";
    case 1:
      form.value.star_nums = "3";
    case 2:
      form.value.star_nums = "1,2";
  }
  getMescroll().resetUpScroll(true);
};
</script>
<template>
  <view class="page">
    <u-tabs
      :list="tabList"
      :active-style="{
        borderRadius: '26rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        fontSize: '28rpx',
        color: '#333333',
      }"
      :inactiveStyle="{
        fontSize: '24rpx',
        color: '#999999',
      }"
      :itemStyle="{
        padding: '20rpx 30rpx',
      }"
      lineWidth="0"
      @click="changeTabs"
    ></u-tabs>
    <view class="px30">
      <mescroll-uni
        class="list"
        :height="height"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
      >
        <view class="py10 df ais borderBottom" v-for="(val, i) in goods" :key="i">
          <u-avatar size="50" :src="val.user?.avatar" mode="aspectFill"></u-avatar>
          <view class="ml20">
            <u-rate
              size="20"
              active-color="#EF6227"
              :value="val.star_num"
              :readonly="true"
            ></u-rate>
            <u-text margin="10rpx 0" size="30rpx" :text="val.user?.nickname"></u-text>
            <u-text
              margin="10rpx 0"
              size="22rpx"
              color="#aaa"
              :text="val.contents"
            ></u-text>
            <u-album
              v-if="val.imgs_url"
              :urls="val.imgs_url"
              multipleSize="170rpx"
              max-count="3"
            ></u-album>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
