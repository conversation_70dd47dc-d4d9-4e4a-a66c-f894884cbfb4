<?php
namespace core;
class Config{
	static public  $config = [];
	static protected  $databaseConfig = [];
	public function __construct(){


	}
	static public function init(){
		$path =  BASE_PATH . "config";
		$files = scandir($path);
		foreach ($files as $file) {
			if ($file == '.' || $file == '..' || is_dir($file) || $file[0] == ".")continue;
			$file = substr(strtolower($file),0,-4);
			//数据库配置
			if(substr($file,0,9) == "database_" && strlen($file)>9){
				//dump(BASE_PATH . "config" . DS . $file);
				self::$databaseConfig[substr($file,9)] = include BASE_PATH . "config/" . $file . ".php";
			}if(substr($file,0,7) == "config_" && strlen($file)>7){
				self::$config[substr($file,7)] = include  BASE_PATH . "config/" . $file . ".php";
			}
		}
	}
	static public function get($key){
		$paramArr = explode(".", $key);
		$src = self::$config;
		$i = 0;
		while(true){
			if($i == count($paramArr) - 1){
				if(isset($src[$paramArr[$i]])){
					return $src[$paramArr[$i]];
				}else{
					return null;
				}				
			}else{
				if(isset($src[$paramArr[$i]])){
					$src = $src[$paramArr[$i]];
				}else{
					return null;
				}
				$i ++;
			}
		}
	}

	static public function getDatabaseConfig(){
		return self::$databaseConfig;
	}
	static public function getAll(){
		return ['config'=>self::$config,'databaseConfig'=>self::$databaseConfig];
	}
	public function __destruct(){

	}
}
