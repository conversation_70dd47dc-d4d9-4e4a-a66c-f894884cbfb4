<?php
namespace model;
use core\Db;
use AlibabaCloud\SDK\ViapiUtils\ViapiUtils;
//阿里云人脸人体
/*
非上海地域oss需上传到临时上海地域oss，转换地址，依赖 composer require alibabacloud/viapi-utils
composer 7.2测试正常，php8.2失败，必要时先用php7 composer,再修改为使用php8的代码
1、\vendor\alibabacloud\viapi-tool\src\ViapiTool->getStreamFromNet ,file_get_contents 改为 curl 方式获取资源；
2、\vendor\alibabacloud\tea-oss-utils\src\OSSUtils.php,197行php8.2环境报错，兼容修改为：$signatureVersion = $signatureVersion ? strtolower($signatureVersion) : "";
*/
class Alifacebody{
	
	static private $AccessKeyId = "LTAI5tNZZWdy7Tbp7JT2ftDY";
	static private $AccessKeySecret = "******************************";
	static private $endpoint = "facebody.cn-shanghai.aliyuncs.com";
	static private $commonParams = [
		"Action"=>"",
		"Version"=>"2019-12-30",
		"Format"=>"JSON",
		"AccessKeyId"=>"",
		"SignatureNonce"=>"",
		"Timestamp"=>"",
		"SignatureMethod"=>"HMAC-SHA1",
		"SignatureVersion"=>"1.0",
		"Signature"=>"",
	];
	
	public function __construct(){
		
	}
	
	//人脸属性识别
	static public function recognizeFace ($img_url,$Age="true",$Gender="true",$Hat="true",$Glass="true",$Beauty="true",$Expression="true",$Mask="true",$Quality="true",$MaxFaceNumber="1"){
        //非上海地域oss需上传到临时上海地域oss，转换地址
		//$img_url = "https://viapi-test-bj.oss-cn-beijing.aliyuncs.com/viapi-3.0domepic/ocr/RecognizeBankCard/yhk1.jpg";
		$img_url = ViapiUtils::upload(self::$AccessKeyId, self::$AccessKeySecret, $img_url);
		//
		$data = [
			"ImageURL"=>$img_url,//图片链接（长度不超2048，不支持base64）
			"Age"=>$Age,//是否需要返回人脸年龄结果，取值true或false，默认false
			"Gender"=>$Gender,//是否需要返回人脸性别结果，取值true或false，默认false
			"Hat"=>$Hat,//是否需要返回人脸帽子结果，取值true或false，默认false
			"Glass"=>$Glass,//是否需要返回人脸眼镜结果，取值true或false，默认false
			"Beauty"=>$Beauty,//是否需要返回人脸颜值结果，取值true或false，默认false
			"Expression"=>$Expression,//是否需要返回人脸表情结果，取值true或false，默认false
			"Mask"=>$Mask,//是否需要返回人脸口罩结果，取值true或false，默认false
			"Quality"=>$Quality,//是否需要返回人脸质量，取值true或false，默认false
			"MaxFaceNumber"=>$MaxFaceNumber,//图片中人脸的最大返回数量，取值范围1~10，默认为1。若想返回多个人脸检测结果，请正确设置。默认按返回参数FaceProbabilityList进行降序排列
		];
		(self::$commonParams)['Action'] = "RecognizeFace";
		self::getRequestSign($data,"POST");
		//$url = "https://" . self::$endpoint . "/?" . http_build_query(array_merge($data,self::$commonParams));
		$url = "https://" . self::$endpoint;
		//dump($url);
		$res = curl($url,array_merge($data,self::$commonParams));
		$result = json_decode($res['body'],true);
		//dump($res);exit;
		if(isset($result['Data'])){
			return ["status"=>"ok","data"=>$result['Data']];
		}
		$msg = isset($result['Code']) ? $result['Code'] : "操作失败";
		$msg .= isset($result['Message']) ? ":【".$result['Message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//获取签名
    static private function getRequestSign($params,$method="POST"){
		//
		$SignatureNonce = makeCode(16,true);
		$Timestamp = date("Y-m-d\TH:i:s\Z",time()-8*60*60);
		(self::$commonParams)['SignatureNonce'] = $SignatureNonce;
		(self::$commonParams)['Timestamp'] = $Timestamp;
		(self::$commonParams)['AccessKeyId'] = self::$AccessKeyId;
		//
		$params = array_merge($params,self::$commonParams);
        ksort($params);
		$sign_str = "";
        foreach ($params as $k => $v) {
			if($k == "Signature"){
				continue;
			}else{
				$sign_str .= $k . "=" . urlencode($v) . "&";
			}
        }
		$sign_str = substr($sign_str,0,-1);
		$sign_str = urlencode($sign_str);
		$sign_str = $method . "&" . urlencode("/") . "&" . $sign_str;
		$key = self::$AccessKeySecret . "&";
		$Signature = base64_encode(hash_hmac('sha1',$sign_str,$key,true));
		(self::$commonParams)['Signature'] = $Signature;
        return $Signature;
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
