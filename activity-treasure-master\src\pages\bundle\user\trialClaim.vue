<script setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { userget_trial_info, userclaim_trial_member, sendNotification } from "@/api";
import { store } from "@/store";
import { navto, login, getUserInfo } from "@/utils";
import { requireLogin } from "@/utils/auth";

const shareCode = ref('');
const trialInfo = ref(null);
const loading = ref(false);
const claiming = ref(false);
const claimed = ref(false);

onLoad((options) => {
  if (options.code) {
    shareCode.value = options.code;
    getTrialInfo();
  } else {
    uni.$u.toast('分享链接无效');
    setTimeout(() => {
      uni.navigateBack();
    }, 2000);
  }
});

// 获取分享信息
const getTrialInfo = async () => {
  loading.value = true;

  try {
    console.log('开始获取分享信息，分享码:', shareCode.value);

    const res = await userget_trial_info({
      share_code: shareCode.value
    });

    console.log('获取分享信息响应:', res);

    if (res?.status === 'ok') {
      trialInfo.value = res.data;
      console.log('分享信息获取成功:', trialInfo.value);
    } else {
      console.error('获取分享信息失败:', res);
      uni.$u.toast(res?.msg || '获取分享信息失败');
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    }
  } catch (error) {
    console.error('获取分享信息异常:', error);
    uni.$u.toast('获取分享信息失败');
    setTimeout(() => {
      uni.navigateBack();
    }, 2000);
  } finally {
    loading.value = false;
  }
};

// 领取体验会员
const claimTrialMember = async () => {
  // 使用统一的登录校验，但保持特殊的重试逻辑
  if (!requireLogin('', '请先登录后再领取体验会员')) {
    return;
  }

  if (claiming.value) return;

  claiming.value = true;

  try {
    console.log('开始领取体验会员，参数:', {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token?.substring(0, 8) + '...',
      share_code: shareCode.value
    });

    const res = await userclaim_trial_member({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      share_code: shareCode.value
    });

    console.log('领取体验会员响应:', res);

    if (res?.status === 'ok') {
      claimed.value = true;
      uni.$u.toast('领取成功！');

      // 通知逻辑已在后端处理，无需前端重复发送

      // 更新用户信息
      getUserInfo();

      // 3秒后跳转到首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index'
        });
      }, 3000);
    } else if (res?.status === 'relogin') {
      console.error('登录信息验证失败，需要重新登录');
      uni.$u.toast('登录信息已过期，请重新登录');
      // 清除登录信息并跳转到登录页面
      store().$patch({
        userInfo: {}
      });
      setTimeout(() => {
        login({
          fun: () => {
            getUserInfo();
            setTimeout(() => {
              claimTrialMember();
            }, 1000);
          }
        });
      }, 1500);
    } else {
      console.error('领取失败:', res);
      uni.$u.toast(res?.msg || '领取失败');
    }
  } catch (error) {
    console.error('领取异常:', error);
    uni.$u.toast('领取失败，请重试');
  } finally {
    claiming.value = false;
  }
};

// 格式化时间
const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 跳转到登录页面
const goToLogin = () => {
  navto('/pages/bundle/common/login');
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index'
  });
};
</script>

<template>
  <view class="page">
    <!-- 简约标题栏 -->
    <view class="header">
      <view class="back-btn" @click="goToHome">
        <u-icon name="arrow-left" color="#333" size="20"></u-icon>
      </view>
      <text class="header-title">会员体验券</text>
      <view class="placeholder"></view>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-icon">
          <u-loading-icon mode="circle" color="#333" size="40"></u-loading-icon>
        </view>
        <text class="loading-text">获取信息中...</text>
      </view>
    </view>

    <!-- 成功领取状态 -->
    <view v-else-if="claimed" class="success-container">
      <view class="success-content">
        <view class="success-icon">
          <u-icon name="checkmark-circle" color="#4CAF50" size="60"></u-icon>
        </view>
        <text class="success-title">领取成功</text>
        <text class="success-desc">您已获得 {{ trialInfo?.trial_days }} 天会员体验</text>
        <view class="success-btn" @click="goToHome">
          <text class="btn-text">立即体验</text>
        </view>
      </view>
    </view>
    
    <!-- 主要内容 -->
    <view v-else-if="trialInfo" class="main-content">
      <!-- 体验券卡片 -->
      <view class="voucher-card">
        <view class="voucher-header">
          <view class="gift-icon">
            <u-icon name="present" color="#666" size="24"></u-icon>
          </view>
          <text class="voucher-title">30天会员体验券</text>
        </view>

        <view class="voucher-content">
          <text class="voucher-desc">{{ trialInfo.sharer_nickname || '好友' }} 赠送给您</text>
          <view class="voucher-value">
            <text class="value-text">{{ trialInfo.trial_days }}</text>
            <text class="value-unit">天</text>
          </view>
        </view>

        <view class="voucher-footer">
          <text class="expire-text">有效期至 {{ formatDate(trialInfo.expire_time) }}</text>
        </view>
      </view>

      <!-- 权益说明 -->
      <view class="benefits-card">
        <text class="benefits-title">体验权益</text>
        <view class="benefits-grid">
          <view class="benefit-item">
            <u-icon name="checkmark" color="#333" size="16"></u-icon>
            <text class="benefit-text">无限制发布活动</text>
          </view>
          <view class="benefit-item">
            <u-icon name="checkmark" color="#333" size="16"></u-icon>
            <text class="benefit-text">专属客服支持</text>
          </view>
          <view class="benefit-item">
            <u-icon name="checkmark" color="#333" size="16"></u-icon>
            <text class="benefit-text">高级数据分析</text>
          </view>
          <view class="benefit-item">
            <u-icon name="checkmark" color="#333" size="16"></u-icon>
            <text class="benefit-text">优先推荐位置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view v-if="trialInfo" class="bottom-actions">
      <view class="action-btn primary" @click="store().$state.userInfo?.uid ? claimTrialMember() : goToLogin()">
        <u-loading-icon v-if="claiming" mode="circle" color="#fff" size="16"></u-loading-icon>
        <text class="btn-text">{{ claiming ? '领取中...' : (store().$state.userInfo?.uid ? '立即领取' : '登录后领取') }}</text>
      </view>
      <view class="action-btn secondary" @click="goToHome">
        <text class="btn-text">稍后再说</text>
      </view>
    </view>

    <!-- 使用说明 -->
    <view v-if="trialInfo" class="usage-tips">
      <text class="tips-text">• 每个体验券仅限一人领取</text>
      <text class="tips-text">• 仅限未成为过会员的用户领取</text>
      <text class="tips-text">• 体验期结束后可选择续费正式会员</text>
    </view>
  </view>
</template>

<style lang="less">
.page {
  min-height: 100vh;
  background: #FAFAFA;
  display: flex;
  flex-direction: column;
}

// 标题栏
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #FFFFFF;
  border-bottom: 1rpx solid #F0F0F0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F8F8F8;
  transition: all 0.3s ease;

  &:active {
    background: #E8E8E8;
    transform: scale(0.95);
  }
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

// 加载状态
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

// 成功状态
.success-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.success-icon {
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.success-btn {
  background: #333;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  transition: all 0.3s ease;

  &:active {
    background: #555;
    transform: scale(0.98);
  }
}

.btn-text {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

// 主要内容
.main-content {
  flex: 1;
  padding: 40rpx 30rpx 0;
}

// 体验券卡片
.voucher-card {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #F0F0F0;
}

.voucher-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.gift-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: #F8F8F8;
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.voucher-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.voucher-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.voucher-desc {
  font-size: 28rpx;
  color: #666;
}

.voucher-value {
  display: flex;
  align-items: baseline;
}

.value-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.value-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.voucher-footer {
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.expire-text {
  font-size: 24rpx;
  color: #999;
}

// 权益卡片
.benefits-card {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #F0F0F0;
}

.benefits-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
}

.benefit-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 12rpx;
}

// 底部操作区
.bottom-actions {
  padding: 30rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #F0F0F0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;

  &.primary {
    background: #333;
    color: #FFFFFF;

    &:active {
      background: #555;
      transform: scale(0.98);
    }
  }

  &.secondary {
    background: #F8F8F8;
    color: #666;

    &:active {
      background: #E8E8E8;
      transform: scale(0.98);
    }
  }
}

.btn-text {
  margin-left: 8rpx;
}

// 使用说明
.usage-tips {
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}
</style>
