<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_yongjin_log } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
import { getItem } from "@/utils";

const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");
const current = ref(0);

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_yongjin_log({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    0: '待结算',
    1: '可提取',
    2: '提现中',
    3: '已提现',
    5: '已冻结'
  };
  return statusMap[status] || '未知';
};

// 状态类型映射（用于标签颜色）
const getStatusType = (status) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'primary',
    3: 'info',
    5: 'error'
  };
  return typeMap[status] || 'default';
};

// 时间信息显示
const getTimeText = (item) => {
  if (item.status === 1 && item.available_time) {
    return `可提取时间：${item.available_time}`;
  } else if (item.status === 3 && item.withdraw_time) {
    return `提现时间：${item.withdraw_time}`;
  } else if (item.status === 0) {
    return '等待结算中...';
  } else if (item.status === 2) {
    return '提现审核中...';
  }
  return '';
};
</script>
<template>
  <view class="page">
    <mescroll-uni
      class="list"
      :height="height"
      :up="{
        page: {
          num: 0,
          size: 20,
          time: null,
        },
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <view class="p30 df aic borderBottom" v-for="(val, i) in goods" :key="i">
        <u-avatar size="110rpx" mode="aspectFill" :src="val.user.avatar"></u-avatar>
        <view class="ml20 df fdc f1">
          <view class="df f1 aic">
            <text style="color: #414141" class="df aic jcc mr10 x30 fb">
              {{ val.user.nickname }}
            </text>
            <u-text
              size="24rpx"
              color="#414141"
              :text="`ID：${val.user.uid}`"
              word-wrap="anywhere"
            >
            </u-text>
            <!-- 新增：佣金状态标签 -->
            <u-tag
              :text="getStatusText(val.status)"
              :type="getStatusType(val.status)"
              size="mini"
              style="margin-left: 10rpx;"
            ></u-tag>
          </view>
          <u-text
            color="#333"
            size="22rpx"
            lines="1"
            :text="getItem(['会员佣金', '活动佣金', '商品佣金'], val.type - 1)"
          ></u-text>
          <u-text color="#999" size="22rpx" :text="val.time" word-wrap="normal"></u-text>
          <!-- 新增：状态时间信息 -->
          <u-text
            v-if="getTimeText(val)"
            color="#999"
            size="20rpx"
            :text="getTimeText(val)"
            style="margin-top: 4rpx;"
          ></u-text>
        </view>
        <view>
          <u-text
            mode="price"
            align="right"
            color="#FF2A00"
            size="32rpx"
            :text="val.money"
          ></u-text>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less"></style>
