/**
 * 统一错误处理工具
 * 提供用户友好的错误提示和错误上报功能
 */

// 错误类型映射
const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  PERMISSION: 'permission',
  SYSTEM: 'system',
  USER: 'user'
};

// 用户友好的错误消息映射
const ERROR_MESSAGES = {
  // 网络错误
  'network_timeout': '网络连接超时，请检查网络后重试',
  'network_error': '网络连接异常，请稍后重试',
  'server_error': '服务器暂时无法响应，请稍后重试',
  
  // 验证错误
  'required_field': '请填写必填信息',
  'invalid_format': '格式不正确，请检查输入',
  'invalid_phone': '请输入正确的手机号码',
  'invalid_email': '请输入正确的邮箱地址',
  
  // 权限错误
  'permission_denied': '权限不足，请联系管理员',
  'login_required': '请先登录后再操作',
  'member_required': '该功能需要会员权限',
  
  // 文件错误
  'file_too_large': '文件过大，请选择小于10MB的文件',
  'file_format_error': '文件格式不支持，请选择图片文件',
  'upload_failed': '上传失败，请重新尝试',
  
  // 系统错误
  'system_error': '系统异常，请稍后重试',
  'data_error': '数据异常，请刷新页面重试',
  
  // 用户操作错误
  'operation_failed': '操作失败，请重试',
  'duplicate_operation': '请勿重复操作',
  'operation_timeout': '操作超时，请重试'
};

// 错误级别
const ERROR_LEVELS = {
  INFO: 'info',
  WARNING: 'warning', 
  ERROR: 'error',
  CRITICAL: 'critical'
};

/**
 * 统一错误处理类
 */
class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 50;
    this.reportUrl = 'https://api.linqingkeji.com/error/report';
  }

  /**
   * 处理错误
   * @param {Error|string} error - 错误对象或错误消息
   * @param {string} type - 错误类型
   * @param {string} level - 错误级别
   * @param {Object} context - 错误上下文
   */
  handle(error, type = ERROR_TYPES.SYSTEM, level = ERROR_LEVELS.ERROR, context = {}) {
    const errorInfo = this.formatError(error, type, level, context);
    
    // 显示用户友好提示
    this.showUserFriendlyMessage(errorInfo);
    
    // 记录错误
    this.logError(errorInfo);
    
    // 上报错误（非敏感错误）
    if (level === ERROR_LEVELS.ERROR || level === ERROR_LEVELS.CRITICAL) {
      this.reportError(errorInfo);
    }
    
    return errorInfo;
  }

  /**
   * 格式化错误信息
   */
  formatError(error, type, level, context) {
    const timestamp = new Date().toISOString();
    const errorMessage = typeof error === 'string' ? error : error.message;
    const stack = error.stack || '';
    
    return {
      id: this.generateErrorId(),
      timestamp,
      type,
      level,
      message: errorMessage,
      stack,
      context: {
        page: this.getCurrentPage(),
        userAgent: this.getUserAgent(),
        ...context
      }
    };
  }

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyMessage(errorInfo) {
    const friendlyMessage = this.getFriendlyMessage(errorInfo.message);
    const icon = this.getIconByLevel(errorInfo.level);
    
    uni.showToast({
      title: friendlyMessage,
      icon: icon,
      duration: 3000,
      mask: false
    });
  }

  /**
   * 获取用户友好的错误消息
   */
  getFriendlyMessage(errorMessage) {
    // 尝试从映射表中找到友好消息
    for (const [key, message] of Object.entries(ERROR_MESSAGES)) {
      if (errorMessage.includes(key) || errorMessage.toLowerCase().includes(key)) {
        return message;
      }
    }
    
    // 如果没有找到映射，返回通用消息
    if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      return ERROR_MESSAGES.network_error;
    }
    
    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
      return ERROR_MESSAGES.permission_denied;
    }
    
    return ERROR_MESSAGES.system_error;
  }

  /**
   * 根据错误级别获取图标
   */
  getIconByLevel(level) {
    switch (level) {
      case ERROR_LEVELS.INFO:
        return 'success';
      case ERROR_LEVELS.WARNING:
        return 'none';
      case ERROR_LEVELS.ERROR:
      case ERROR_LEVELS.CRITICAL:
      default:
        return 'none';
    }
  }

  /**
   * 记录错误到本地
   */
  logError(errorInfo) {
    // 添加到错误队列
    this.errorQueue.push(errorInfo);
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
    
    // 保存到本地存储
    try {
      uni.setStorageSync('error_logs', this.errorQueue);
    } catch (e) {
      console.error('保存错误日志失败:', e);
    }
    
    // 开发环境下输出到控制台
    if (process.env.NODE_ENV === 'development') {
      console.error('错误详情:', errorInfo);
    }
  }

  /**
   * 上报错误到服务器
   */
  async reportError(errorInfo) {
    try {
      // 只上报关键信息，保护用户隐私
      const reportData = {
        id: errorInfo.id,
        timestamp: errorInfo.timestamp,
        type: errorInfo.type,
        level: errorInfo.level,
        message: errorInfo.message,
        page: errorInfo.context.page,
        userAgent: errorInfo.context.userAgent
      };
      
      // 异步上报，不阻塞用户操作
      setTimeout(async () => {
        try {
          await uni.request({
            url: this.reportUrl,
            method: 'POST',
            data: reportData,
            timeout: 5000
          });
        } catch (e) {
          // 上报失败不影响用户体验
          console.warn('错误上报失败:', e);
        }
      }, 1000);
      
    } catch (e) {
      console.warn('错误上报异常:', e);
    }
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前页面路径
   */
  getCurrentPage() {
    try {
      const pages = getCurrentPages();
      return pages.length > 0 ? pages[pages.length - 1].route : 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /**
   * 获取用户代理信息（修复已废弃API）
   */
  getUserAgent() {
    try {
      // 优先使用新的API
      let deviceInfo, appInfo;

      try {
        deviceInfo = uni.getDeviceInfo ? uni.getDeviceInfo() : null;
        appInfo = uni.getAppBaseInfo ? uni.getAppBaseInfo() : null;
      } catch (newApiError) {
        console.warn('新API调用失败，使用降级方案:', newApiError);
        deviceInfo = null;
        appInfo = null;
      }

      if (deviceInfo && appInfo) {
        const platform = deviceInfo.platform || 'unknown';
        const system = deviceInfo.system || 'unknown';
        const version = appInfo.version || 'unknown';
        return `${platform} ${system} ${version}`;
      }

      // 使用新API替换方案
      try {
        const { getSystemInfo } = require('./systemInfo.js');
        const systemInfo = getSystemInfo();
        const platform = systemInfo.platform || 'unknown';
        const system = systemInfo.system || 'unknown';
        const version = systemInfo.version || 'unknown';
        return `${platform} ${system} ${version}`;
      } catch (newApiError) {
        console.warn('新API获取系统信息失败，使用默认值:', newApiError);
        return 'unknown unknown unknown';
      }
    } catch (e) {
      console.warn('获取用户代理信息失败:', e);
      return 'unknown';
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {},
      byLevel: {},
      recent: this.errorQueue.slice(-10)
    };
    
    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1;
    });
    
    return stats;
  }

  /**
   * 清理错误日志
   */
  clearErrorLogs() {
    this.errorQueue = [];
    try {
      uni.removeStorageSync('error_logs');
    } catch (e) {
      console.error('清理错误日志失败:', e);
    }
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 导出便捷方法
export const handleError = (error, type, level, context) => {
  return errorHandler.handle(error, type, level, context);
};

export const handleNetworkError = (error, context) => {
  return errorHandler.handle(error, ERROR_TYPES.NETWORK, ERROR_LEVELS.ERROR, context);
};

export const handleValidationError = (error, context) => {
  return errorHandler.handle(error, ERROR_TYPES.VALIDATION, ERROR_LEVELS.WARNING, context);
};

export const handlePermissionError = (error, context) => {
  return errorHandler.handle(error, ERROR_TYPES.PERMISSION, ERROR_LEVELS.ERROR, context);
};

export const getErrorStats = () => {
  return errorHandler.getErrorStats();
};

export const clearErrorLogs = () => {
  return errorHandler.clearErrorLogs();
};

export { ERROR_TYPES, ERROR_LEVELS, ERROR_MESSAGES };
export default errorHandler;
