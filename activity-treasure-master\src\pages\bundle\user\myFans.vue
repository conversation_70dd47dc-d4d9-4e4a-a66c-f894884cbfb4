<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_fans_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, navto, callPhone } from "@/utils";
import { getAuthParams, isLoggedIn, safeApiCall } from "@/utils/auth";

const goods = ref([]);
// 调用mescroll的hook
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调
const upCallback = async (mescroll) => {
  if (!isLoggedIn()) {
    uni.$u.toast("请先登录");
    mescroll.endErr();
    return;
  }

  try {
    const res = await safeApiCall(
      () => userget_fans_list({
        ...getAuthParams(),
        page: mescroll.num,
        page_size: mescroll.size
      }),
      '获取粉丝列表'
    );

    if (res && res.status === "ok") {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    } else if (res && res.status === "empty") {
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);
    } else {
      uni.$u.toast(res?.msg || "获取粉丝列表失败");
      mescroll.endErr();
    }
  } catch (error) {
    console.error("获取粉丝列表错误:", error);
    uni.$u.toast("网络错误，请稍后重试");
    mescroll.endErr();
  }
};

// 查看粉丝详情
const viewFanDetail = (fan) => {
  navto(`/pages/bundle/msg/personage?to_uid=${fan.fan_user.uid}`);
};
</script>

<template>
  <view class="fans-page">
    <!-- 标题栏 -->
    <view class="fans-header">
      <myTitle
        bg-color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        height="200rpx"
        title="我的粉丝"
        :titleStyle="{
          color: '#ffffff',
          fontWeight: '600',
          fontSize: '36rpx'
        }"
      ></myTitle>
    </view>

    <!-- 粉丝列表 -->
    <view class="fans-container">
      <mescroll-uni
        class="fans-list"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
        :height="height"
      >
        <!-- 空状态 -->
        <view v-if="goods.length === 0" class="empty-state">
          <u-icon name="account" color="#ccc" size="120rpx"></u-icon>
          <u-text color="#999" size="28rpx" text="暂无粉丝" margin="20rpx 0 0 0"></u-text>
        </view>

        <!-- 粉丝列表 -->
        <view v-else>
          <view
            class="fan-item"
            v-for="(val, i) in goods"
            :key="i"
            @click="viewFanDetail(val)"
          >
            <view class="fan-content">
              <!-- 头像 -->
              <view class="fan-avatar">
                <u-avatar
                  size="96rpx"
                  :src="val.fan_user?.avatar"
                  mode="aspectFill"
                  shape="circle"
                ></u-avatar>
              </view>

              <!-- 用户信息 -->
              <view class="fan-info">
                <view class="fan-name">
                  <u-text size="32rpx" bold color="#333" :text="val.fan_user?.nickname"></u-text>
                </view>
                <view class="fan-desc">
                  <u-text
                    size="24rpx"
                    color="#666"
                    lines="1"
                    :text="val.fan_user?.gexingqianming || '这个用户很神秘，什么都没有留下~'"
                  ></u-text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="fan-action">
                <u-icon name="arrow-right" color="#999" size="32rpx"></u-icon>
              </view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
/* 页面容器 */
.fans-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
}

/* 标题栏 */
.fans-header {
  position: relative;
  z-index: 10;
}

/* 粉丝容器 */
.fans-container {
  padding: 30rpx;
  margin-top: 20rpx;
}

/* 粉丝列表 */
.fans-list {
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

/* 粉丝项 */
.fan-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
  transition: all 0.3s ease;
}

.fan-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.fan-content {
  display: flex;
  align-items: center;
}

/* 头像 */
.fan-avatar {
  margin-right: 24rpx;
}

.fan-avatar .u-avatar {
  border: 3rpx solid rgba(106, 192, 134, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.1);
}

/* 用户信息 */
.fan-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.fan-name {
  margin-bottom: 4rpx;
}

.fan-desc {
  opacity: 0.8;
}

/* 操作按钮 */
.fan-action {
  padding-left: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .fans-container {
    padding: 20rpx;
  }

  .fan-item {
    padding: 24rpx;
  }

  .fan-avatar {
    margin-right: 20rpx;
  }
}

/* 动画效果 */
.fan-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
