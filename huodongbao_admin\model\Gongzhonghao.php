<?php
namespace model;
use core\Controller;
use core\Db;

//微信公众号
class Gongzhonghao extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	//授权 scope : snsapi_base,snsapi_userinfo
	public function snsapi_index($scope="snsapi_userinfo"){
		$redirect_uri = urlencode(HTTP_HOST . url("author/{$scope}"));
		$weixin_gongzhonghao_appid = config("app.weixin_gongzhonghao_appid");
		$url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$weixin_gongzhonghao_appid}&redirect_uri={$redirect_uri}&response_type=code&scope={$scope}&state=STATE#wechat_redirect";
		//echo $url;exit;
		header("location:{$url}");
		exit;
	}
	
	//获取用户信息
	public function snsapi_userinfo($code=""){
		if(empty($code)){
			echo "获取code失败";
			exit;
		}
		$wexin_gongzhonghao_appid = config("app.weixin_gongzhonghao_appid");
		$weixin_gongzhonghao_appsecret = config("app.weixin_gongzhonghao_appsecret");
		$url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$wexin_gongzhonghao_appid}&secret={$weixin_gongzhonghao_appsecret}&code={$code}&grant_type=authorization_code";
		$res = curl($url);
		$data = json_decode($res['body'],true);
		if(!isset($data['access_token'])){
			echo "获取access_token失败";
			exit;
		}
		//dump($data);
		//
		$access_token = $data['access_token'];//单个用户的授权access_token
		$expires_in = $data['expires_in'];//7200
		$refresh_token = $data['refresh_token'];//7200
		$openid = $data['openid'];
		$unionid = isset($data['unionid']) ? $data['unionid'] : "";//绑定开放平台才会有
		//
		if(!empty($unionid)){
			dbConn();
			$author = Db()->table("author")->select("author_id,token")->where("wechat_unionid=:wechat_unionid")->prepareParam([":wechat_unionid"=>$unionid])->fetch();
			if(!empty($author)){
				header("location:".HTTP_HOST."/pages/login/login?token={$data['token']}&atuhor_id={$data['author_id']}");
				exit;
			}
		}
		//
		$url = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}&lang=zh_CN";
		$res = curl($url);
		$data = json_decode($res['body'],true);
		if(!isset($data['openid'])){
			echo "获取用户信息失败";
			exit;
		}
		//
		$openid = $data['openid'];
		$nickname = $data['nickname'];
		$sex = $data['sex'];
		$province = $data['province'];
		$city = $data['city'];
		$country = $data['country'];
		$headimgurl = $data['headimgurl'];
		$privilege = $data['privilege'];
		$unionid = isset($data['unionid']) ? $data['unionid'] : "-";//绑定开放平台才会有
		//
		//dump($data);exit;
		//带着获取到的信息重定向
		header("location:https://author.jieao8.cn/pages/register/register?snsapi_userinfo=".base64_encode(json_encode($data)));
		exit;
	}
	
	//只获取openid
	public function snsapi_base($code=""){
		if(empty($code)){
			echo "获取code失败";
			exit;
		}
		$wexin_gongzhonghao_appid = config("app.weixin_gongzhonghao_appid");
		$weixin_gongzhonghao_appsecret = config("app.weixin_gongzhonghao_appsecret");
		$url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$wexin_gongzhonghao_appid}&secret={$weixin_gongzhonghao_appsecret}&code={$code}&grant_type=authorization_code";
		$res = curl($url);
		dump($res);
		$data = json_decode($res['body'],true);
		if(!isset($data['access_token'])){
			echo "获取access_token失败";
			exit;
		}
		dump($data);exit;
		//
		$access_token = $data['access_token'];//单个用户的授权access_token
		$expires_in = $data['expires_in'];//7200
		$refresh_token = $data['refresh_token'];//7200
		$openid = $data['openid'];
		//
	}
	
	
	//刷新access_token（如果需要）用户access_token
	public function refresh_token($refresh_token=""){
		if(empty($refresh_token)){
			echo "请传入refresh_token";
			exit;
		}
		$wexin_gongzhonghao_appid = config("app.wexin_gongzhonghao_appid");
		$url = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid={$wexin_gongzhonghao_appid}&grant_type=refresh_token&refresh_token={$refresh_token}";
		$res = curl($url);
		$data = json_decode($res['body'],true);
		if(!isset($data['access_token'])){
			echo "error";
			exit;
		}
		//
		$access_token = $data['access_token'];
		$expires_in = $data['expires_in'];//7200
		$refresh_token = $data['refresh_token'];//7200
		$openid = $data['openid'];
		//
		return $data;
	}

	//获取应用AccessToken
	static public function getAccessToken($is_online=false){
		//
		if(empty($is_online)){
			$access_token = \core\Cache::getCache("gongzhonghao_app_access_token");
			if(!empty($access_token))return $access_token;
		}
		//
		$appid = config("app.weixin_gongzhonghao_appid");
		$appsecret = config("app.weixin_gongzhonghao_appsecret");
		$url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
		$res = curl($url);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['access_token']) && isset($data['expires_in'])){
			//\core\Cache::setCache("gongzhonghao_app_access_token",$data['access_token'],$data['expires_in'] - 10);
			return $data['access_token'];
		}else{
			return false;
		}
	}

	//新增其他类型永久素材
	//type 图片（image）、语音（voice）、视频（video）和缩略图（thumb）
	//上传视频素材时需要POST另一个表单，id为description，包含素材的描述信息，内容格式为JSON
	static public function add_material($file,$type="image",$title="",$description=""){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={$access_token}&type={$type}";
		$file_path = realpath($file);
		$data = [
			"media" => new \CURLFile($file_path),
		];
		if($type == "video"){
			$data['description'] = json_encode(["title"=>$title,"introduction"=>$description]);
		}
		//
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_SAFE_UPLOAD, true);
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST,2);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		$response = curl_exec($curl);
		curl_close($curl);
		//
		$data = json_decode($response,true);
		if(!isset($data['url']) || empty($data['url'])){
			return ["status"=>"error","msg"=>"上传失败"];
		}
		$media_id = $data['media_id'];
		$url = $data['url'];
		return ["status"=>"ok","data"=>$data];
	}
	
	//上传图文消息内的图片获取URL
	static public function upload_img($file){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={$access_token}";
		$file_path = realpath($file);
		$data = [
			"media" => new \CURLFile($file_path),
		];
		//
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_SAFE_UPLOAD, true);
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST,2);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		$response = curl_exec($curl);
		curl_close($curl);
		//
		$data = json_decode($response,true);
		if(!isset($data['url']) || empty($data['url'])){
			return ["status"=>"error","msg"=>"上传失败"];
		}
		return ["status"=>"ok","url"=>$data['url']];
	}
	
	//创建公众号文章草稿
	static public function add_caogao($title,$content,$thumb_media_id,$author,$digest){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token={$access_token}";
		$data = [
			"articles"=>[
				[
				"title"=>$title,
				"content"=>$content,
				"thumb_media_id"=>$thumb_media_id,
				]
			]
		];
		if(!empty($author)){
			$data['articles'][0]['author'] = $author;
		}
		if(!empty($digest)){
			$data['articles'][0]['digest'] = $digest;
		}
		$data = json_encode($data,JSON_UNESCAPED_UNICODE);
		$header = [
			'Content-Type: application/json; charset=utf-8'
		];
		//
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_TIMEOUT, 120);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_HTTPHEADER,$header);	
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		$res = curl_exec($curl);
		curl_close($curl);	
		//
		$data = json_decode($res,true);
		if(empty($data['media_id'])){
			return ["status"=>"error","msg"=>"公众号提交草稿失败"];
		}
		return ["status"=>"ok","media_id"=>$data['media_id']];
	}
	
	//发布公众号文章任务
	static public function pub_wenzhang($media_id){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={$access_token}";
		$data = [
			"media_id"=>$media_id,
		];
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		if(!isset($data['errcode']) || $data['errcode'] != "0" || empty($data['publish_id'])){
			return ["status"=>"error","msg"=>"公众号发布任务失败"];
		}
		return ["status"=>"ok","publish_id"=>$data['publish_id']];
	}
	
	//查询公众号文章发布任务状态
	static public function pub_wenzhang_status($publish_id){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/freepublish/get?access_token={$access_token}";
		$data = [
			"publish_id"=>$publish_id,
		];
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		if(empty($data['publish_id']) || !isset($data['publish_status'])){
			return ["status"=>"error","msg"=>"获取发布任务状态失败"];
		}
		if($data['publish_status'] == 0 && isset($data['article_detail']['item'][0]['article_url'])){
			return ["status"=>"ok","publish_status"=>$data['publish_status'],"article_url"=>$data['article_detail']['item'][0]['article_url']];
		}else{
			return ["status"=>"ok","publish_status"=>$data['publish_status']];
		}
	}
	
	//发送模板消息
	static public function send_template_message($openid,$template_id,$data_params=[],$redirect_uri="",$miniprogram = []){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取公众号access_token失败"];
			}
		}
		$url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token={$access_token}";
		$data_params_format = [];
		foreach($data_params as $k=>$v){
			$data_params_format[$k]['value'] = $v;
		}
		$data = [
			"touser"=>$openid,
			"template_id"=>$template_id,
			"data"=>$data_params_format,
		];
		if(!empty($redirect_uri)){
			$data['page'] = $redirect_uri;
		}
		if(!empty($miniprogram) && isset($miniprogram['appid']) && isset($miniprogram['pagepath'])){
			$data['miniprogram'] = ["appid"=>$miniprogram['appid'],"pagepath"=>$miniprogram['pagepath']];
		}
		$res = curl($url,$data,true);dump($res);
		$data = json_decode($res['body'],true);
		if(empty($data) && isset($data['errcode']) && $data['errcode'] == 0 && isset($data['errmsg']) && $data['errmsg'] == "ok" ){
			return ["status"=>"ok","msg"=>"发送成功"];
		}
		return ["status"=>"error","msg"=>"发送失败"];
	}
	
	public function _empty(){
		return ["status"=>"error","msg"=>"URL error"];
	}

	function __destruct(){

	}
}
