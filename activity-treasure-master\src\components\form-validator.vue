<template>
  <view class="form-validator">
    <!-- 表单字段 -->
    <slot 
      :validate="validateField" 
      :errors="errors" 
      :isValid="isValid"
      :clearError="clearFieldError"
    />
    
    <!-- 错误提示区域 -->
    <view v-if="showErrors && hasErrors" class="error-summary">
      <view 
        v-for="(error, field) in errors" 
        :key="field" 
        class="error-item"
      >
        <u-icon name="warning" size="24rpx" color="#ff4757" />
        <text class="error-text">{{ error }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
// 导入验证函数，使用相对路径避免循环依赖
const validateData = (data, fieldName, rules = {}) => {
  const errors = [];

  // 必填验证
  if (rules.required && (!data || data === '')) {
    errors.push(`${fieldName}不能为空`);
  }

  // 长度验证
  if (rules.minLength && data && data.length < rules.minLength) {
    errors.push(`${fieldName}长度不能少于${rules.minLength}个字符`);
  }

  if (rules.maxLength && data && data.length > rules.maxLength) {
    errors.push(`${fieldName}长度不能超过${rules.maxLength}个字符`);
  }

  // 格式验证
  if (rules.pattern && data && !rules.pattern.test(data)) {
    errors.push(`${fieldName}格式不正确`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// Props定义
const props = defineProps({
  // 验证规则
  rules: {
    type: Object,
    default: () => ({})
  },
  
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({})
  },
  
  // 是否显示错误提示
  showErrors: {
    type: Boolean,
    default: true
  },
  
  // 是否实时验证
  realTimeValidation: {
    type: Boolean,
    default: true
  },
  
  // 验证触发时机
  trigger: {
    type: String,
    default: 'blur' // blur, change, submit
    // 移除validator避免小程序兼容性问题
  }
});

// Emits定义
const emit = defineEmits(['update:modelValue', 'validate', 'error']);

// 状态管理
const errors = ref({});
const touched = ref({});

// 计算属性
const isValid = computed(() => {
  return Object.keys(errors.value).length === 0;
});

const hasErrors = computed(() => {
  return Object.keys(errors.value).length > 0;
});

// 验证单个字段
const validateField = (fieldName, value, customRules = null) => {
  const rules = customRules || props.rules[fieldName];
  
  if (!rules) {
    return { valid: true, errors: [] };
  }
  
  // 标记字段已被触摸
  touched.value[fieldName] = true;
  
  // 执行验证
  const result = validateData(value, fieldName, rules);
  
  // 更新错误状态
  if (result.valid) {
    delete errors.value[fieldName];
  } else {
    errors.value[fieldName] = result.errors[0]; // 只显示第一个错误
  }
  
  // 触发验证事件
  emit('validate', {
    field: fieldName,
    value,
    valid: result.valid,
    errors: result.errors
  });
  
  if (!result.valid) {
    emit('error', {
      field: fieldName,
      errors: result.errors
    });
  }
  
  return result;
};

// 验证所有字段
const validateAll = () => {
  const allErrors = {};
  let allValid = true;
  
  Object.keys(props.rules).forEach(fieldName => {
    const value = props.modelValue[fieldName];
    const result = validateField(fieldName, value);
    
    if (!result.valid) {
      allErrors[fieldName] = result.errors[0];
      allValid = false;
    }
  });
  
  errors.value = allErrors;
  return allValid;
};

// 清除字段错误
const clearFieldError = (fieldName) => {
  delete errors.value[fieldName];
  delete touched.value[fieldName];
};

// 清除所有错误
const clearAllErrors = () => {
  errors.value = {};
  touched.value = {};
};

// 获取字段错误
const getFieldError = (fieldName) => {
  return errors.value[fieldName] || null;
};

// 检查字段是否有错误
const hasFieldError = (fieldName) => {
  return !!errors.value[fieldName];
};

// 检查字段是否被触摸
const isFieldTouched = (fieldName) => {
  return !!touched.value[fieldName];
};

// 监听表单数据变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    if (props.realTimeValidation && props.trigger === 'change') {
      // 只验证发生变化的字段
      Object.keys(newValue).forEach(fieldName => {
        if (newValue[fieldName] !== oldValue?.[fieldName] && touched.value[fieldName]) {
          validateField(fieldName, newValue[fieldName]);
        }
      });
    }
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  validateAll,
  validateField,
  clearFieldError,
  clearAllErrors,
  getFieldError,
  hasFieldError,
  isFieldTouched,
  isValid,
  errors
});

// 预定义验证规则
const commonRules = {
  required: {
    required: true
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  phone: {
    required: true,
    pattern: /^1[3-9]\d{9}$/
  },
  password: {
    required: true,
    minLength: 6,
    maxLength: 20
  },
  username: {
    required: true,
    minLength: 2,
    maxLength: 20,
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/
  },
  url: {
    pattern: /^https?:\/\/.+/
  },
  number: {
    pattern: /^\d+$/
  },
  decimal: {
    pattern: /^\d+(\.\d+)?$/
  }
};

// 提供常用验证规则
const getCommonRule = (ruleName) => {
  return commonRules[ruleName] || {};
};

// 暴露常用规则
defineExpose({
  ...defineExpose(),
  getCommonRule,
  commonRules
});
</script>

<style lang="scss" scoped>
.form-validator {
  width: 100%;
}

.error-summary {
  margin-top: var(--spacing-md, 20rpx);
  padding: var(--spacing-md, 20rpx);
  background: rgba(255, 71, 87, 0.05);
  border-radius: var(--radius-md, 16rpx);
  border-left: 4rpx solid #ff4757;
}

.error-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm, 16rpx);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.error-text {
  margin-left: var(--spacing-sm, 16rpx);
  font-size: var(--font-size-sm, 24rpx);
  color: #ff4757;
  line-height: 1.4;
}
</style>
