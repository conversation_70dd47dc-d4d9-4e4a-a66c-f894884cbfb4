<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  userget_other_user_info,
  userguanzhu_add,
  userguanzhu_del,
  userguanzhu_check,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import myLine from "@/components/myLine.vue";
import { store } from "@/store";
import { getListHeight, callPhone, getAge } from "@/utils";
import { requireLogin } from "@/utils/auth";

const gapHeight = ref(0);
const info = ref({});
const userActivities = ref({
  created: [], // 发起的活动
  joined: [],  // 报名的活动
  dynamics: [], // 发布的动态
  diaries: []  // 发布的日记
});
const activeTab = ref(0); // 当前选中的标签页

onLoad(async (e) => {
  const uid = e?.to_uid || e?.uid;
  if (uid) {
    const res = await userget_other_user_info({ to_uid: uid });
    if (res.status === "ok") {
      const guanzhuRes = await userguanzhu_check({ to_uid: uid });
      info.value = { ...res.data, is_guanzhu: guanzhuRes.data };
      // 获取用户相关数据
      await getUserActivities(uid);
    } else uni.$u.toast(res.msg);
  }
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});

// 关注
const follow = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再关注')) {
    return;
  }

  const api = info.value.is_guanzhu ? userguanzhu_del : userguanzhu_add;
  const res = await api({ to_uid: info.value.uid });
  if (res.status === "ok") info.value.is_guanzhu = info.value.is_guanzhu ? 0 : 1;
  uni.$u.toast(res.msg);
};

// 获取用户活动数据
const getUserActivities = async (uid) => {
  try {
    // 调用带详细参数的API
    const res = await userget_other_user_info({
      to_uid: uid,
      include_details: 1
    });

    if (res.status === "ok" && res.data) {
      userActivities.value = {
        created: res.data.created_activities || [],
        joined: res.data.joined_activities || [],
        dynamics: res.data.published_feeds || []
      };
    } else {
      console.error('获取用户活动数据失败:', res.msg);
      userActivities.value = {
        created: [],
        joined: [],
        dynamics: []
      };
    }
  } catch (error) {
    console.error('获取用户活动数据失败:', error);
    userActivities.value = {
      created: [],
      joined: [],
      dynamics: []
    };
  }
};

// 切换标签页
const switchTab = (index) => {
  activeTab.value = index;
};

// 跳转到活动详情
const goToActivity = (activityId) => {
  uni.navigateTo({
    url: `/pages/bundle/index/activeInfo?id=${activityId}`
  });
};

// 跳转到动态详情
const goToFeedDetail = (feedId) => {
  uni.navigateTo({
    url: `/pages/bundle/world/feed/detail?feedId=${feedId}`
  });
};

// 跳转到日记详情
const goToDiaryDetail = (diaryId) => {
  uni.navigateTo({
    url: `/pages/bundle/world/diary/detail?diaryId=${diaryId}`
  });
};
</script>
<template>
  <view class="page personage-page">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="个人信息"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    <view class="user-info-container">
      <view class="user-card">
        <view class="user-header">
          <u-avatar size="120rpx" :src="info.avatar" mode="aspectFill" class="user-avatar"></u-avatar>
          <view class="user-details">
            <view class="user-name-section">
              <u-text size="32rpx" bold :text="info.nickname" class="user-name"></u-text>
              <u-button
                :text="info.is_guanzhu ? '已关注' : '关注'"
                shape="circle"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :customStyle="{
                  width: '120rpx',
                  height: '48rpx',
                  fontSize: '24rpx',
                  color: '#ffffff',
                  marginLeft: '20rpx'
                }"
                @click="follow"
              ></u-button>
            </view>
            <view class="user-tags">
              <view class="tag-item gender-tag">
                <u-icon
                  color="#fff"
                  :name="info?.sex == 0 ? 'question' : info?.sex == 1 ? 'man' : 'woman'"
                  size="12"
                  :label="info?.sex == 0 ? '未知' : info?.sex == 1 ? '男' : '女'"
                  labelSize="18rpx"
                  labelColor="#fff"
                  top="2rpx"
                ></u-icon>
              </view>
              <view class="tag-item age-tag">
                <u-text
                  color="#fff"
                  size="18rpx"
                  :text="info.birthday ? `${getAge(info.birthday)}岁` : '未知'"
                ></u-text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 个性签名 -->
      <view class="signature-section" v-if="info.gexingqianming">
        <view class="signature-content">
          <u-text size="28rpx" :text="info.gexingqianming" color="#666666"></u-text>
        </view>
      </view>

      <!-- 标签 -->
      <view class="labels-section" v-if="info.labels && info.labels.length > 0">
        <view class="section-title">
          <text class="title-text">标签</text>
        </view>
        <view class="labels-container">
          <view
            class="label-item"
            v-for="(val, i) in info.labels"
            :key="i"
          >
            {{ val.label }}
          </view>
        </view>
      </view>

      <!-- 活动和动态展示区域 -->
      <view class="content-tabs-section">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: activeTab === 0 }"
            @click="switchTab(0)"
          >
            发起的活动
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 1 }"
            @click="switchTab(1)"
          >
            报名的活动
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 2 }"
            @click="switchTab(2)"
          >
            发布的动态
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 3 }"
            @click="switchTab(3)"
          >
            发布的日记
          </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
          <!-- 发起的活动 -->
          <view v-if="activeTab === 0" class="activity-list">
            <view v-if="userActivities.created.length === 0" class="empty-state">
              <u-text size="28rpx" color="#999" text="暂无发起的活动"></u-text>
            </view>
            <view
              v-else
              v-for="activity in userActivities.created"
              :key="activity.id"
              class="activity-item"
              @click="goToActivity(activity.id)"
            >
              <image class="activity-image" :src="activity.img_url" mode="aspectFill"></image>
              <view class="activity-info">
                <u-text size="30rpx" bold :text="activity.name" class="activity-title"></u-text>
                <u-text size="24rpx" color="#666" :text="activity.start_time" class="activity-time"></u-text>
              </view>
            </view>
          </view>

          <!-- 报名的活动 -->
          <view v-if="activeTab === 1" class="activity-list">
            <view v-if="userActivities.joined.length === 0" class="empty-state">
              <u-text size="28rpx" color="#999" text="暂无报名的活动"></u-text>
            </view>
            <view
              v-else
              v-for="activity in userActivities.joined"
              :key="activity.id"
              class="activity-item"
              @click="goToActivity(activity.id)"
            >
              <image class="activity-image" :src="activity.img_url" mode="aspectFill"></image>
              <view class="activity-info">
                <u-text size="30rpx" bold :text="activity.name" class="activity-title"></u-text>
                <u-text size="24rpx" color="#666" :text="activity.start_time" class="activity-time"></u-text>
              </view>
            </view>
          </view>

          <!-- 发布的动态 -->
          <view v-if="activeTab === 2" class="dynamics-list">
            <view v-if="userActivities.dynamics.length === 0" class="empty-state">
              <u-text size="28rpx" color="#999" text="暂无发布的动态"></u-text>
            </view>
            <view
              v-else
              v-for="dynamic in userActivities.dynamics"
              :key="dynamic.id"
              class="dynamic-item"
              @click="goToFeedDetail(dynamic.id)"
            >
              <view class="dynamic-content-wrapper">
                <u-text size="28rpx" :text="dynamic.content" class="dynamic-content"></u-text>

                <!-- 动态图片 -->
                <view v-if="dynamic.images && dynamic.images.length > 0" class="dynamic-images">
                  <image
                    v-for="(img, index) in dynamic.images.slice(0, 3)"
                    :key="index"
                    :src="img"
                    class="dynamic-image"
                    mode="aspectFill"
                  ></image>
                  <view v-if="dynamic.images.length > 3" class="more-images">
                    <u-text size="20rpx" color="#999" :text="`+${dynamic.images.length - 3}`"></u-text>
                  </view>
                </view>

                <!-- 动态信息 -->
                <view class="dynamic-meta">
                  <u-text size="22rpx" color="#999" :text="dynamic.created_at" class="dynamic-time"></u-text>
                  <view class="dynamic-stats">
                    <view v-if="dynamic.like_count > 0" class="stat-item">
                      <u-icon name="heart" size="20rpx" color="#999"></u-icon>
                      <u-text size="20rpx" color="#999" :text="dynamic.like_count"></u-text>
                    </view>
                    <view v-if="dynamic.comment_count > 0" class="stat-item">
                      <u-icon name="chat" size="20rpx" color="#999"></u-icon>
                      <u-text size="20rpx" color="#999" :text="dynamic.comment_count"></u-text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 发布的日记 -->
          <view v-if="activeTab === 3" class="diaries-list">
            <view v-if="userActivities.diaries && userActivities.diaries.length === 0" class="empty-state">
              <u-text size="28rpx" color="#999" text="暂无发布的日记"></u-text>
            </view>
            <view
              v-else-if="userActivities.diaries"
              v-for="diary in userActivities.diaries"
              :key="diary.id"
              class="diary-item"
              @click="goToDiaryDetail(diary.id)"
            >
              <view class="diary-content-wrapper">
                <u-text size="28rpx" :text="diary.content" class="diary-content"></u-text>

                <!-- 日记图片 -->
                <view v-if="diary.images && diary.images.length > 0" class="diary-images">
                  <image
                    v-for="(img, index) in diary.images.slice(0, 3)"
                    :key="index"
                    :src="img"
                    class="diary-image"
                    mode="aspectFill"
                  ></image>
                  <view v-if="diary.images.length > 3" class="more-images">
                    <u-text size="20rpx" color="#999" :text="`+${diary.images.length - 3}`"></u-text>
                  </view>
                </view>

                <!-- 日记信息 -->
                <view class="diary-meta">
                  <u-text size="22rpx" color="#999" :text="diary.created_at" class="diary-time"></u-text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <u-gap :height="gapHeight"></u-gap>
<!--    <view class="pfx bottom0 w b6f bottomBox">
      <u-button
        color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
        shape="circle"
        text="联系Ta"
        :customStyle="{
          width: '690rpx',
          height: '98rpx',
          color: '#333',
          fontSize: '30rpx',
        }"
        @click="callPhone(info.mobile)"
      ></u-button>
      <u-safe-bottom></u-safe-bottom>
    </view>-->
  </view>
</template>

<style scoped lang="less">
/* 页面样式 */
.personage-page {
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 用户信息容器 */
.user-info-container {
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 690rpx;
  padding: 30rpx;
}

/* 用户卡片 */
.user-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.user-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.user-avatar {
  border: 2rpx solid rgba(106, 192, 134, 0.1);
}

.user-details {
  flex: 1;
}

.user-name-section {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-name {
  color: #333333;
  font-weight: 600;
}

.user-tags {
  display: flex;
  gap: 12rpx;
}

.tag-item {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gender-tag {
  background-color: #6AC086;
}

.age-tag {
  background-color: #88D7A0;
}

/* 个性签名 */
.signature-section {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.signature-content {
  line-height: 1.6;
}

/* 标签部分 */
.labels-section {
  margin-bottom: 30rpx;
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.labels-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.label-item {
  background-color: #f0f0f0;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #666666;
}

/* 内容标签页 */
.content-tabs-section {
  margin-top: 40rpx;
}

/* 标签页样式 */
.tab-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  font-size: 26rpx;
  color: #666666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.tab-item.active {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  color: #ffffff;
  font-weight: 600;
}

/* 内容容器样式 */
.content-container {
  min-height: 400rpx;
}

/* 活动列表样式 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.activity-item {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.activity-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.activity-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-title {
  margin-bottom: 12rpx;
  color: #333333;
}

/* 动态列表样式 */
.dynamics-list, .diaries-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.dynamic-item, .diary-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.dynamic-item:active, .diary-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.dynamic-content-wrapper, .diary-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.dynamic-content, .diary-content {
  line-height: 1.6;
  word-break: break-word;
  color: #333333;
  font-size: 28rpx;
}

.dynamic-images, .diary-images {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.dynamic-image, .diary-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
}

.more-images {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
}

.dynamic-meta, .diary-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dynamic-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx dashed #e0e0e0;
}
</style>
