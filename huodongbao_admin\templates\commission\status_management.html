<fieldset class="table-search-fieldset">
    <legend>佣金状态管理</legend>
    <div style="margin: 10px 10px 10px 10px">
        <form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="get" id="search">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">佣金状态</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value="">全部状态</option>
                            <option value="0" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '0') ? 'selected' : ''; ?>>待结算</option>
                            <option value="1" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '1') ? 'selected' : ''; ?>>可提取</option>
                            <option value="2" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '2') ? 'selected' : ''; ?>>提现中</option>
                            <option value="3" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '3') ? 'selected' : ''; ?>>已提现</option>
                            <option value="4" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '4') ? 'selected' : ''; ?>>已驳回</option>
                            <option value="5" <?php echo (isset($_REQUEST['status']) && $_REQUEST['status'] == '5') ? 'selected' : ''; ?>>已冻结</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">佣金类型</label>
                    <div class="layui-input-inline">
                        <select name="commission_type">
                            <option value="">全部类型</option>
                            <option value="invite" <?php echo (isset($_REQUEST['commission_type']) && $_REQUEST['commission_type'] == 'invite') ? 'selected' : ''; ?>>邀请佣金</option>
                            <option value="operation" <?php echo (isset($_REQUEST['commission_type']) && $_REQUEST['commission_type'] == 'operation') ? 'selected' : ''; ?>>运营佣金</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <a href="/commission/manual_settlement" class="layui-btn layui-btn-warm">手动结算</a>
                </div>
            </div>
        </form>
    </div>
</fieldset>

<div class="layui-form">
    <!-- 批量操作工具栏 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-btn-group">
                <button class="layui-btn layui-btn-sm" onclick="batchUpdateStatus(1)">批量设为可提取</button>
                <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="batchUpdateStatus(5)">批量冻结</button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="batchUpdateStatus(4)">批量驳回</button>
            </div>
        </div>
    </div>
    
    <table class="layui-table">
        <colgroup>
            <col width="50">
            <col width="80">
            <col width="150">
            <col width="120">
            <col width="100">
            <col width="100">
            <col width="120">
            <col width="150">
            <col width="150">
            <col width="200">
        </colgroup>
        <thead>
            <tr>
                <th><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th>ID</th>
                <th>用户信息</th>
                <th>佣金金额</th>
                <th>佣金类型</th>
                <th>当前状态</th>
                <th>结算时间</th>
                <th>可提取时间</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php if(!empty($data)){ ?>
                <?php foreach($data as $k=>$v){ ?>
                <tr>
                    <td><input type="checkbox" name="commission_ids" value="<?php echo $v['id']; ?>" lay-skin="primary"></td>
                    <td><?php echo $v['id']; ?></td>
                    <td>
                        <?php echo htmlspecialchars($v['user_nickname']); ?><br>
                        <small><?php echo $v['user_mobile']; ?></small>
                    </td>
                    <td class="layui-text-em">￥<?php echo number_format($v['money'], 2); ?></td>
                    <td>
                        <?php if($v['commission_type'] == 'operation'): ?>
                            <span class="layui-badge layui-bg-blue">运营佣金</span>
                        <?php else: ?>
                            <span class="layui-badge layui-bg-green">邀请佣金</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php
                        $status_colors = [
                            0 => 'layui-bg-orange',
                            1 => 'layui-bg-green',
                            2 => 'layui-bg-blue',
                            3 => 'layui-bg-gray',
                            4 => 'layui-bg-red',
                            5 => 'layui-bg-black'
                        ];
                        $color_class = $status_colors[$v['status']] ?? 'layui-bg-gray';
                        ?>
                        <span class="layui-badge <?php echo $color_class; ?>"><?php echo $v['status_text']; ?></span>
                    </td>
                    <td><?php echo $v['settlement_time_formatted']; ?></td>
                    <td><?php echo $v['available_time_formatted']; ?></td>
                    <td><?php echo date('Y-m-d H:i:s', strtotime($v['time'])); ?></td>
                    <td class="layui-table-col-special">
                        <div class="layui-table-cell">
                            <?php if($v['status'] == 0): ?>
                                <a onclick="updateSingleStatus(<?php echo $v['id']; ?>, 1);" class="layui-btn layui-btn-normal layui-btn-xs">结算</a>
                            <?php endif; ?>
                            <?php if($v['status'] == 1): ?>
                                <a onclick="updateSingleStatus(<?php echo $v['id']; ?>, 5);" class="layui-btn layui-btn-warm layui-btn-xs">冻结</a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php } ?>
            <?php }else{ ?>
                <tr>
                    <td colspan="10" style="text-align:center;">暂无数据</td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
    <?php echo pageRender(); ?>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 全选
    form.on('checkbox(allChoose)', function(data){
        var child = $(data.elem).parents('table').find('tbody input[type="checkbox"]');
        child.each(function(index, item){
            item.checked = data.elem.checked;
        });
        form.render('checkbox');
    });
    
    // 搜索表单提交
    form.on('submit(search)', function(data){
        return true;
    });
});

// 批量更新状态
function batchUpdateStatus(targetStatus) {
    var checkedIds = [];
    $('input[name="commission_ids"]:checked').each(function(){
        checkedIds.push($(this).val());
    });
    
    if (checkedIds.length === 0) {
        layer.msg('请选择要操作的佣金记录', {icon: 2});
        return;
    }
    
    var statusTexts = {1: '可提取', 4: '驳回', 5: '冻结'};
    var statusText = statusTexts[targetStatus] || '更新';
    
    layer.prompt({
        title: '批量' + statusText + '操作',
        formType: 2,
        value: '',
        area: ['400px', '200px']
    }, function(value, index){
        layer.close(index);
        
        $.post('/commission/status_management', {
            commission_ids: checkedIds.join(','),
            target_status: targetStatus,
            remark: value
        }, function(response) {
            if(response.indexOf('成功') > -1) {
                layer.msg('操作成功', {icon: 1});
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                layer.msg('操作失败', {icon: 2});
            }
        });
    });
}

// 单个状态更新
function updateSingleStatus(commissionId, targetStatus) {
    var statusTexts = {1: '结算', 4: '驳回', 5: '冻结'};
    var statusText = statusTexts[targetStatus] || '更新';
    
    layer.prompt({
        title: statusText + '操作',
        formType: 2,
        value: '',
        area: ['400px', '200px']
    }, function(value, index){
        layer.close(index);
        
        $.post('/commission/status_management', {
            commission_ids: commissionId,
            target_status: targetStatus,
            remark: value
        }, function(response) {
            if(response.indexOf('成功') > -1) {
                layer.msg('操作成功', {icon: 1});
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                layer.msg('操作失败', {icon: 2});
            }
        });
    });
}
</script>
