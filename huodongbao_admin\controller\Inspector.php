<?php
namespace controller;
use core\Controller;
use core\Db;

class Inspector extends Controller{

	private $xss_mark = "script";
	
	private $webshell_files = [];
	private $white_list = [];
	private $webshell_ext = ["php","jsp","asp","aspx"];
	
	public function __construct(){
		parent::__construct();
	}

	public function xss(){
		if(!IS_CLI){
			echo "只支持命令行访问";
			exit;
		}
		$msg = "";
		$exception_log = [];
		dbConn();
		$model = new \core\Model();
		$tables = $model->showTables();
		foreach($tables as $table){
			$columns = $model->table($table)->showColumns();
			foreach($columns as $column=>$info){
				if(
					stripos($info['type'],"char") !== false || 
					stripos($info['type'],"varchar") !== false || 
					stripos($info['type'],"tinytext") !== false || 
					stripos($info['type'],"text") !== false || 
					stripos($info['type'],"mediumtext") !== false || 
					stripos($info['type'],"longtext") !== false || 
					stripos($info['type'],"binary") !== false || 
					stripos($info['type'],"varbinary") !== false || 
					stripos($info['type'],"tinyblob") !== false || 
					stripos($info['type'],"mediumblob") !== false || 
					stripos($info['type'],"blob") !== false || 
					stripos($info['type'],"longblob") !== false
					
				){
					//echo "正在检测：{$table}.{$column}\n";
					$xss_mark_sth = "%{$this->xss_mark}%";
					$check = Db()->table($table)->where("{$column} LIKE :xss_mark_sth")->prepareParam([':xss_mark_sth'=>$xss_mark_sth])->fetch();
					if(!empty($check)){
						$tips = "位置 {$table}.{$column} 可能存在 xss 攻击，请尽快检查";
						$msg .= $tips."\n";
						$exception_log[] = $tips;
					}
				}
			}
		}
		if(!empty($exception_log)){
			foreach($exception_log as $row){
				$this->exception_log($row);
			}
			_log_(DATETIME . " : XSS检测 - \n" . $msg);
			echo $msg;
		}else{
			_log_(DATETIME . " : XSS检测 - 暂无发现xss攻击");
			echo "暂无发现xss攻击\n";
		}
	}
	
	public function webshell($path=""){
		if(!IS_CLI){
			echo "只支持命令行访问";
			exit;
		}
		if(empty($path)){
			$www_dir = dirname(dirname(__DIR__));
			if(is_readable($www_dir)){
				$dirs = scandir($www_dir);
				foreach($dirs as $dir){
					if($dir == "." || $dir == "..")continue;
					$c_dir = $www_dir . DS . $dir. DS . "access";
					if(!file_exists($c_dir) || !is_dir($c_dir) || !is_readable($c_dir))continue;
					$this->white_list[] = $c_dir . DS . "index.php";
					$this->webshell($c_dir);
				}
			}
			if(!empty($this->webshell_files)){
				$msg = "";
				foreach($this->webshell_files as $file){
					$tips = "可疑文件 {$file} ，请尽快检查";
					$msg .= $tips."\n";
					$this->exception_log("可疑文件 {$file} ，请尽快检查");
				}
				_log_(DATETIME . " : WebShell检测 - \n" . $msg);
				echo $msg;				
			}else{
				_log_(DATETIME . " : WebShell检测 - 暂无发现可疑文件");
				echo "暂无发现可疑文件\n";				
			}
		}else{
			$files = scandir($path);
			foreach($files as $file){
				if($file == "." || $file == "..")continue;
				if(is_dir($path . DS . $file)){
					$this->webshell($path . DS . $file);
				}else{
					if(stripos($file,".") === false)continue;
					if(in_array($path . DS . $file,$this->white_list))continue;
					$file_arr = explode(".",$file);
					$ext = end($file_arr);
					if(in_array($ext,$this->webshell_ext)){
						$this->webshell_files[] = $path . DS . $file;
					}
				}
			}				
		}
	}
	
	public function _empty(){
		echo __CLASS__." -> _empty";
	}
	function __destruct(){

	}
}
