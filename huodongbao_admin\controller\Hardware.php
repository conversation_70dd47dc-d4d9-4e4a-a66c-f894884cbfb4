<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 硬件信息
*/
class Hardware extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 实时数据
	* @method index
	*/		
	public function index(){
		if(stripos(strtolower(PHP_OS),"linux") === false){
			if(isAjax()){
				responseType("json");
				$data = ["status"=>"error","msg"=>"只支持Linux系统"];
				return $data;
			}else{
				assign("msg","只支持Linux系统");
				return view(['header','noprivileges','footer']);
			}
		}
		if(isAjax()){
			responseType("json");
			$data = [
				"load_info"=>$this->getLoadInfo(),
				"mem_info"=>$this->getMemInfo(),
				"file_info"=>$this->getFilesystemInfo(),
				"net_info"=>$this->getNet(),
			];
			return ["status"=>"ok","data"=>$data];
		}
		$data['sys_info'] = $this->getSysInfo();
		$data['cpu_info'] = $this->getCpuInfo();
		$data['mem_info'] = $this->getMemInfo();
		$data['load_info'] = $this->getLoadInfo();
		$data['file_info'] = $this->getFilesystemInfo();
		$data['net_info'] = $this->getNet();
		assign("data",$data);
		return view(['header','hardware/index','footer']);
	}
	private function getSysInfo(){
		$info = [];
		$info['os_version'] = shell_exec("cat /etc/redhat-release");
		$info['linux_version'] = shell_exec("uname -r");
		//
		$info['uptime'] = "";
		$uptime = file_get_contents("/proc/uptime");
		$str = explode(" ", trim($uptime));
		$str = trim($str[0]);
		$min = $str / 60;
		$hours = $min / 60;
		$days = floor($hours / 24);
		$hours = floor($hours - ($days * 24));
		$min = floor($min - ($days * 60 * 24) - ($hours * 60));
		if ($days !== 0) $info['uptime'] = $days."天";
		if ($hours !== 0) $info['uptime'] .= $hours."小时";
		$info['uptime'] .= $min."分钟";
		//
		$info['datetime_system'] = shell_exec('date "+%Y-%m-%d %H:%M:%S"');
		$info['datetime_php'] = date("Y-m-d H:i:s");
		
		$info['timezone_system'] = shell_exec('date +"%Z %z"');
		$info['timezone_php'] = date_default_timezone_get();
		$info['timezone_database'] = "";
		
		$info['php_version'] = PHP_VERSION;
		$info['server_version'] = $_SERVER['SERVER_SOFTWARE'];
		//
		try{
			dbConn();
			//
			$sql = "SELECT VERSION() AS database_version";
			$res = Db()->_fetch($sql);
			$info['database_version'] = isset($res['database_version']) ? $res['database_version'] : "-";
			//
			$sql = "SELECT NOW() AS datetime_database";
			$res = Db()->_fetch($sql);
			$info['datetime_database'] = isset($res['datetime_database']) ? $res['datetime_database'] : "-";
			//
			$sql = "show variables like '%time_zone%';";
			$res = Db()->_fetchAll($sql);
			if(!empty($res)){
				foreach($res as $v){
					if($v['Variable_name'] == "time_zone"){
						$info['timezone_database'] = $v['Value'];
						break;
					}
				}
			}
			//
		}catch(\Exception $e){
			
		}
		//
		return $info;
	}	
	
	private function getCpuInfo(){
		$str = file_get_contents("/proc/cpuinfo");
		preg_match_all("/physical\s+id\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $physical);
		preg_match_all("/cpu\s+cores\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $cores);
		preg_match_all("/model\s+name\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $model);
		preg_match_all("/cpu\s+MHz\s{0,}\:+\s{0,}([\d\.]+)[\r\n]+/", $str, $mhz);
		preg_match_all("/cache\s+size\s{0,}\:+\s{0,}([\d\.]+\s{0,}[A-Z]+[\r\n]+)/", $str, $cache);
		preg_match_all("/bogomips\s{0,}\:+\s{0,}([\d\.]+)[\r\n]+/", $str, $bogomips);
		$info = [];
		$info['cpu_num'] = $physical[1][count($physical[1])-1] + 1;
		$info['cpu_cores'] = $cores[1][0];
		$info['cpu_thread_num'] = count($model[1]);
		$info['cpu_model'] = $model[1][0];
		$info['cpu_mhz'] = $mhz[1][0];
		$info['cpu_cache'] = $cache[1][0];
		$info['cpu_bogomips'] = $bogomips[1][0];
		return $info;
	}
	
	private function getMemInfo(){
		$str = file_get_contents("/proc/meminfo");
		preg_match_all("/MemTotal\s{0,}\:+\s{0,}([\d\.]+).+?MemFree\s{0,}\:+\s{0,}([\d\.]+).+?Cached\s{0,}\:+\s{0,}([\d\.]+).+?SwapTotal\s{0,}\:+\s{0,}([\d\.]+).+?SwapFree\s{0,}\:+\s{0,}([\d\.]+)/s", $str, $buf);
		preg_match_all("/Buffers\s{0,}\:+\s{0,}([\d\.]+)/s", $str, $buffers);
		$info = [];
		$info['memTotal'] = round($buf[1][0]/1024, 2);
		$info['memFree'] = round($buf[2][0]/1024, 2);
		$info['memBuffers'] = round($buffers[1][0]/1024, 2);
		$info['memCached'] = round($buf[3][0]/1024, 2);		
		$info['memUsed'] = $info['memTotal'] - $info['memFree'];
		$info['memPercent'] = round($info['memUsed'] / $info['memTotal'] * 100,2);//使用率
		
		$info['memRealUsed'] = $info['memTotal'] - $info['memFree'] - $info['memCached'] - $info['memBuffers'];//真实内存使用
		$info['memRealFree'] = $info['memTotal'] - $info['memRealUsed']; //真实空闲
		$info['memRealPercent'] = round($info['memRealUsed'] / $info['memTotal'] * 100,2);//真实使用率
		
		//
		$info['swapTotal'] = round($buf[4][0]/1024, 2);
		$info['swapFree'] = round($buf[5][0]/1024, 2);
		$info['swapUsed'] = round($info['swapTotal'] - $info['swapFree'], 2);
		$info['swapPercent'] = round($info['swapUsed'] / $info['swapTotal'] * 100,2);			
		//format
		$info['memTotalFormat'] = $this->m2g_format($info['memTotal']);
		$info['memFreeFormat'] = $this->m2g_format($info['memFree']);
		$info['memUsedFormat'] = $this->m2g_format($info['memUsed']);
		$info['memRealFreeFormat'] = $this->m2g_format($info['memRealFree']);
		$info['memRealUsedFormat'] = $this->m2g_format($info['memRealUsed']);
		$info['swapTotalFormat'] = $this->m2g_format($info['swapTotal']);
		$info['swapFreeFormat'] = $this->m2g_format($info['swapFree']);
		$info['swapUsedFormat'] = $this->m2g_format($info['swapUsed']);
		//
		return $info;
	}
	
	private function getLoadInfo(){
		$res = sys_getloadavg();
		if(!is_array($res) || count($res) != 3)return false;
		$data = $this->getCpuNum();
		$num = $data['cpu_num'] * $data['cpu_cores'];
		//$num = $data['cpu_thread_num'];
		$info = ["num"=>$num];
		$info['m_1_single'] = round($res[0] * 100);
		$info['m_5_single'] = round($res[1] * 100);
		$info['m_15_single'] = round($res[2] * 100);
		
		$info['m_1'] = number_format($res[0] / $num * 100,2,".","");
		$info['m_5'] = number_format($res[1] / $num * 100,2,".","");
		$info['m_15'] = number_format($res[2] / $num * 100,2,".","");
		return $info;
	}

	private function getFilesystemInfo(){
		$info = [];
		$info['total'] = number_format(disk_total_space(".")/1024/1024/1024,2,".","");
		$info['free'] = number_format(disk_free_space(".")/1024/1024/1024,2,".","");
		$info['used'] = number_format($info['total'] - $info['free'],2,".","");
		$info['percent'] = number_format($info['used'] / $info['total'] * 100,2,".","");
		//
		$disk_type_str = trim(shell_exec("lsblk -d -o name,rota"));
		$disk_type_res = explode("\n",$disk_type_str);
		$disk_type = "";
		for($i=1;$i<count($disk_type_res);$i++){
			$row = preg_replace("/\s(?=\s)/","\\1",trim($disk_type_res[$i]));
			$arr = explode(" ",$row);
			if($arr[1] == "1"){
				$val = "HDD";
			}else if($arr[1] == "0"){
				$val = "SSD";
			}else{
				$val = "-";
			}
			$disk_type .= $arr[0]." : ".$val . " | "; 
		}
		$info['disk_type'] = strlen($disk_type) > 3 ? substr($disk_type,0,-3) : "-";
		//
		return $info;
	}
	

	private function getCpuNum(){
		$str = file_get_contents("/proc/cpuinfo");
		preg_match_all("/physical\s+id\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $physical);
		preg_match_all("/cpu\s+cores\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $cores);
		preg_match_all("/model\s+name\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $str, $model);
		$data = [];
		$data['cpu_num'] = $physical[1][count($physical[1])-1] + 1;
		$data['cpu_cores'] = $cores[1][0];
		$data['cpu_thread_num'] = count($model[1]);
		return $data;
	}

	private function getNet(){
		$data = [
			"receive"=>"-",
			"receive_format"=>"-",
			"send"=>"-",
			"send_format"=>"-",
		];
		$str = file_get_contents("/proc/net/dev");
		$str = str_ireplace("\r\n","\n",$str);
		$lines = explode("\n",$str);
		foreach($lines as $line){
			$line = preg_replace('/\s+/', ' ', trim($line));
			if(strpos($line,"eth0") !== false){
				$arr = explode(" ",$line);
				$data['receive'] = $arr[1];
				$data['receive_format'] = format_file_size($arr[1]);
				$data['send'] = $arr[9];
				$data['send_format'] = format_file_size($arr[9]);
				return $data;
				break;
			}
		}
		return $data;
	}
	
	private function m2g_format($num_m){
		if($num_m >= 1024){
			return number_format($num_m / 1024,2,".","") . "G";
		}
		return number_format($num_m,2,".","") . "M";
	}
	
	public function _empty(){
		echo "_empty";
	}
	
	function __destruct(){

	}
}
