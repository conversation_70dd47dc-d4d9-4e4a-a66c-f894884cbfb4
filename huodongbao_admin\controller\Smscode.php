<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 短信
*/
class Smscode extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 验证码
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['mobile'])){
			$mobile = "%".$_REQUEST['mobile']."%";
			$where .= " AND mobile LIKE :mobile";
			$prepareParam[":mobile"] = $mobile;
			$pageParams['mobile'] = $_REQUEST['mobile'];
		}
		if(!empty($_REQUEST['type']) && check($_REQUEST['type'],"intgt0")){
			$type = (int)$_REQUEST['type'];
			$where .= " AND type={$type}";
			$pageParams['type'] = $type;
			assign("type",$type);
		}
		//
		$start_date = date("Y-m-d",strtotime("-7 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `send_time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("sms_code")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		$types = ["1"=>"注册/登录","2"=>"重置密码","3"=>"修改资料"];
		assign("data",$data);
		assign("types",$types);
		return view(['header','smscode/index','footer']);
	}
	
	public function _empty(){
		echo "_empty";
	}
	function __destruct(){

	}
}
