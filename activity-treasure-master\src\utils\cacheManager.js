/**
 * 智能缓存协调系统
 * 统一管理前后端缓存，确保数据一致性和性能优化
 */

// 缓存类型定义
const CACHE_TYPES = {
  MEMORY: 'memory',      // 内存缓存
  STORAGE: 'storage',    // 本地存储缓存
  SESSION: 'session',    // 会话缓存
  NETWORK: 'network'     // 网络缓存
};

// 缓存策略定义
const CACHE_STRATEGIES = {
  LRU: 'lru',           // 最近最少使用
  LFU: 'lfu',           // 最少使用频率
  FIFO: 'fifo',         // 先进先出
  TTL: 'ttl'            // 生存时间
};

/**
 * 智能缓存管理器
 */
class CacheManager {
  constructor() {
    this.memoryCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0
    };
    this.maxMemorySize = 50 * 1024 * 1024; // 50MB
    this.currentMemorySize = 0;
    this.cacheVersion = this.getCacheVersion();
    
    // 初始化缓存清理定时器
    this.initCleanupTimer();
  }

  /**
   * 获取缓存版本
   */
  getCacheVersion() {
    try {
      return uni.getStorageSync('cache_version') || '1.0.0';
    } catch (e) {
      return '1.0.0';
    }
  }

  /**
   * 设置缓存版本
   */
  setCacheVersion(version) {
    this.cacheVersion = version;
    try {
      uni.setStorageSync('cache_version', version);
    } catch (e) {
      console.error('设置缓存版本失败:', e);
    }
  }

  /**
   * 智能缓存设置
   */
  set(key, data, options = {}) {
    const {
      ttl = 5 * 60 * 1000,           // 默认5分钟
      type = CACHE_TYPES.MEMORY,      // 默认内存缓存
      strategy = CACHE_STRATEGIES.LRU, // 默认LRU策略
      priority = 1,                   // 缓存优先级
      compress = false,               // 是否压缩
      version = this.cacheVersion     // 缓存版本
    } = options;

    const cacheItem = {
      data,
      timestamp: Date.now(),
      ttl,
      type,
      strategy,
      priority,
      version,
      accessCount: 0,
      lastAccess: Date.now(),
      size: this.calculateSize(data)
    };

    // 压缩数据（如果需要）
    if (compress && typeof data === 'object') {
      try {
        cacheItem.data = JSON.stringify(data);
        cacheItem.compressed = true;
      } catch (e) {
        console.warn('数据压缩失败:', e);
      }
    }

    // 根据缓存类型存储
    switch (type) {
      case CACHE_TYPES.MEMORY:
        this.setMemoryCache(key, cacheItem);
        break;
      case CACHE_TYPES.STORAGE:
        this.setStorageCache(key, cacheItem);
        break;
      case CACHE_TYPES.SESSION:
        this.setSessionCache(key, cacheItem);
        break;
    }

    this.cacheStats.sets++;
    return true;
  }

  /**
   * 智能缓存获取
   */
  get(key, options = {}) {
    const {
      type = CACHE_TYPES.MEMORY,
      fallbackTypes = [CACHE_TYPES.STORAGE, CACHE_TYPES.SESSION]
    } = options;

    let cacheItem = null;

    // 按优先级查找缓存
    const searchTypes = [type, ...fallbackTypes];
    for (const searchType of searchTypes) {
      switch (searchType) {
        case CACHE_TYPES.MEMORY:
          cacheItem = this.getMemoryCache(key);
          break;
        case CACHE_TYPES.STORAGE:
          cacheItem = this.getStorageCache(key);
          break;
        case CACHE_TYPES.SESSION:
          cacheItem = this.getSessionCache(key);
          break;
      }
      if (cacheItem) break;
    }

    if (!cacheItem) {
      this.cacheStats.misses++;
      return null;
    }

    // 检查缓存是否过期
    if (this.isExpired(cacheItem)) {
      this.delete(key, { type: cacheItem.type });
      this.cacheStats.misses++;
      return null;
    }

    // 检查版本兼容性
    if (cacheItem.version !== this.cacheVersion) {
      this.delete(key, { type: cacheItem.type });
      this.cacheStats.misses++;
      return null;
    }

    // 更新访问统计
    cacheItem.accessCount++;
    cacheItem.lastAccess = Date.now();

    // 解压缩数据（如果需要）
    let data = cacheItem.data;
    if (cacheItem.compressed) {
      try {
        data = JSON.parse(cacheItem.data);
      } catch (e) {
        console.warn('数据解压缩失败:', e);
        return null;
      }
    }

    this.cacheStats.hits++;
    return data;
  }

  /**
   * 删除缓存
   */
  delete(key, options = {}) {
    const { type = CACHE_TYPES.MEMORY } = options;

    switch (type) {
      case CACHE_TYPES.MEMORY:
        this.deleteMemoryCache(key);
        break;
      case CACHE_TYPES.STORAGE:
        this.deleteStorageCache(key);
        break;
      case CACHE_TYPES.SESSION:
        this.deleteSessionCache(key);
        break;
    }

    this.cacheStats.deletes++;
    return true;
  }

  /**
   * 内存缓存操作
   */
  setMemoryCache(key, cacheItem) {
    // 检查内存限制
    if (this.currentMemorySize + cacheItem.size > this.maxMemorySize) {
      this.evictMemoryCache();
    }

    this.memoryCache.set(key, cacheItem);
    this.currentMemorySize += cacheItem.size;
  }

  getMemoryCache(key) {
    return this.memoryCache.get(key) || null;
  }

  deleteMemoryCache(key) {
    const cacheItem = this.memoryCache.get(key);
    if (cacheItem) {
      this.currentMemorySize -= cacheItem.size;
      this.memoryCache.delete(key);
    }
  }

  /**
   * 本地存储缓存操作
   */
  setStorageCache(key, cacheItem) {
    try {
      uni.setStorageSync(`cache_${key}`, cacheItem);
    } catch (e) {
      console.error('设置本地存储缓存失败:', e);
    }
  }

  getStorageCache(key) {
    try {
      return uni.getStorageSync(`cache_${key}`) || null;
    } catch (e) {
      console.error('获取本地存储缓存失败:', e);
      return null;
    }
  }

  deleteStorageCache(key) {
    try {
      uni.removeStorageSync(`cache_${key}`);
    } catch (e) {
      console.error('删除本地存储缓存失败:', e);
    }
  }

  /**
   * 会话缓存操作
   */
  setSessionCache(key, cacheItem) {
    if (typeof sessionStorage !== 'undefined') {
      try {
        sessionStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
      } catch (e) {
        console.error('设置会话缓存失败:', e);
      }
    }
  }

  getSessionCache(key) {
    if (typeof sessionStorage !== 'undefined') {
      try {
        const item = sessionStorage.getItem(`cache_${key}`);
        return item ? JSON.parse(item) : null;
      } catch (e) {
        console.error('获取会话缓存失败:', e);
        return null;
      }
    }
    return null;
  }

  deleteSessionCache(key) {
    if (typeof sessionStorage !== 'undefined') {
      try {
        sessionStorage.removeItem(`cache_${key}`);
      } catch (e) {
        console.error('删除会话缓存失败:', e);
      }
    }
  }

  /**
   * 缓存清理策略
   */
  evictMemoryCache() {
    const entries = Array.from(this.memoryCache.entries());
    
    // 按LRU策略排序
    entries.sort((a, b) => a[1].lastAccess - b[1].lastAccess);
    
    // 删除最少使用的缓存项，直到内存使用降到80%
    const targetSize = this.maxMemorySize * 0.8;
    while (this.currentMemorySize > targetSize && entries.length > 0) {
      const [key] = entries.shift();
      this.deleteMemoryCache(key);
    }
  }

  /**
   * 检查缓存是否过期
   */
  isExpired(cacheItem) {
    return Date.now() - cacheItem.timestamp > cacheItem.ttl;
  }

  /**
   * 计算数据大小
   */
  calculateSize(data) {
    try {
      return JSON.stringify(data).length * 2; // 估算字节大小
    } catch (e) {
      return 1024; // 默认1KB
    }
  }

  /**
   * 初始化清理定时器
   */
  initCleanupTimer() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    // 清理内存缓存
    for (const [key, cacheItem] of this.memoryCache.entries()) {
      if (this.isExpired(cacheItem)) {
        this.deleteMemoryCache(key);
      }
    }

    // 清理本地存储缓存
    try {
      const storageInfo = uni.getStorageInfoSync();
      storageInfo.keys.forEach(key => {
        if (key.startsWith('cache_')) {
          const cacheItem = this.getStorageCache(key.replace('cache_', ''));
          if (cacheItem && this.isExpired(cacheItem)) {
            this.deleteStorageCache(key.replace('cache_', ''));
          }
        }
      });
    } catch (e) {
      console.error('清理本地存储缓存失败:', e);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      ...this.cacheStats,
      hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,
      memoryUsage: this.currentMemorySize,
      memoryUsagePercent: (this.currentMemorySize / this.maxMemorySize) * 100,
      cacheCount: this.memoryCache.size
    };
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear();
    this.currentMemorySize = 0;
    
    // 清空本地存储缓存
    try {
      const storageInfo = uni.getStorageInfoSync();
      storageInfo.keys.forEach(key => {
        if (key.startsWith('cache_')) {
          uni.removeStorageSync(key);
        }
      });
    } catch (e) {
      console.error('清空本地存储缓存失败:', e);
    }

    // 重置统计信息
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0
    };
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager();

// 导出便捷方法
export const setCache = (key, data, options) => cacheManager.set(key, data, options);
export const getCache = (key, options) => cacheManager.get(key, options);
export const deleteCache = (key, options) => cacheManager.delete(key, options);
export const getCacheStats = () => cacheManager.getStats();
export const clearCache = () => cacheManager.clear();

export { CACHE_TYPES, CACHE_STRATEGIES };
export default cacheManager;
