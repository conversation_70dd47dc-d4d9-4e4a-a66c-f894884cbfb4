<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 商品管理 
*/
class Goods extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 分类管理
	* @method type
	*/		
	public function type(){
		dbConn();
		if(isAjax()){
			responseType("json");
			if(
				!isset($_POST['type']) || 
				!in_array($_POST['type'],["add","update","del"])
			){
				return ["status"=>"error","msg"=>"参数错误1"];
			}
			$type = trim($_POST['type']);
			if($type == "add"){
				if(empty($_POST['name'])){
					return ["status"=>"error","msg"=>"参数错误2"];
				}
				$name = htmlspecialchars($_POST['name']);
				$data = [
					"name"=>":name",
				];
				$prepareParam = [
					":name"=>$name,
				];
				try{
					$res = Db()->table("goods_type")->prepareParam($prepareParam)->insert($data);
					if($res){
						return ["status"=>"ok","msg"=>"添加成功"];
					}
				}catch(\Exception $e){
					return ["status"=>"error","msg"=>"操作失败1"];
				}
			}else if($type == "update"){
				if(empty($_POST['name']) || !isset($_POST['id']) || !check($_POST['id'],"intgt0")){
					return ["status"=>"error","msg"=>"参数错误3"];
				}
				$id = intval($_POST['id']);
				$name = htmlspecialchars(trim($_POST['name']));
				try{
					$res = Db()->table("goods_type")->where("id={$id}")->prepareParam([":name"=>$name])->update(["name"=>":name"]);
					if($res){
						return ["status"=>"ok","msg"=>"修改成功"];
					}
				}catch(\Exception $e){
					return ["status"=>"error","msg"=>"操作失败2"];
				}
			}else if($type == "del"){
				if(empty($_POST['ids'])){
					return ["status"=>"error","msg"=>"参数错误4"];
				}
				$ids_arr = explode(",",$_POST['ids']);
				foreach($ids_arr as $id){
					if(!check($id,"intgt0")){
						return ["status"=>"error","msg"=>"参数错误5"];
					}
				}
				$ids_str = implode(",",$ids_arr);
				$res = Db()->table("goods_type")->where("id IN ({$ids_str})")->del();
				if($res){
					return ["status"=>"ok","msg"=>"删除成功"];
				}
			}else{
				return ["status"=>"error","msg"=>"参数错误6"];
			}
			return ["status"=>"error","msg"=>"操作失败3"];
		}
		$data = Db()->table("goods_type")->select("id,name")->fetchAll();
		//dump($data);
		//
		assign("data",$data);
		return view(['header','goods/type','footer']);
	}
	/*
	* @name 商品列表
	* @method index
	*/			
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(isset($_REQUEST['id']) && check($_REQUEST['id'],"intgt0")){
			$id = (int)$_REQUEST['id'];
			$where .= " AND id={$id}";
			$pageParams['id'] = $_REQUEST['id'];
		}
		if(!empty($_REQUEST['name'])){
			$name = "%".$_REQUEST['name']."%";
			$where .= " AND name LIKE :name";
			$prepareParam[":name"] = $name;
			$pageParams['name'] = $_REQUEST['name'];
		}
		if(isset($_REQUEST['type_id']) && check($_REQUEST['type_id'],"intgt0")){
			$type_id = (int)$_REQUEST['type_id'];
			$where .= " AND type_id={$type_id}";
			$pageParams['type_id'] = $_REQUEST['type_id'];
			assign("type_id",$type_id);
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $_REQUEST['status'];
			assign("status",$status);
		}
		if(isset($_REQUEST['is_tuijian']) && in_array($_REQUEST['is_tuijian'],[0,1])){
			$is_tuijian = (int)$_REQUEST['is_tuijian'];
			$where .= " AND is_tuijian={$is_tuijian}";
			$pageParams['is_tuijian'] = $_REQUEST['is_tuijian'];
			assign("is_tuijian",$is_tuijian);
		}
		if(isset($_REQUEST['is_shiyong']) && in_array($_REQUEST['is_shiyong'],[0,1])){
			$is_tuijian = (int)$_REQUEST['is_tuijian'];
			$where .= " AND is_tuijian={$is_tuijian}";
			$pageParams['is_tuijian'] = $_REQUEST['is_tuijian'];
			assign("is_tuijian",$is_tuijian);
		}
		$data = Db()->table("goods")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		foreach($data as &$row){
			$row['type_name'] = Db()->table("goods_type")->where("id={$row['type_id']}")->getColumn("name");
		}
		assign("data",$data);
		//dump($data);exit;
		//
		$status_arr = [0=>"下架",1=>"上架"];
		assign("status_arr",$status_arr);
		//
		$type_arr = Db()->table("goods_type")->fetchAll();
		$type_arr = array_column($type_arr,"name","id");
		assign("type_arr",$type_arr);
		//
		return view(['header','goods/index','footer']);
	}
	/*
	* @name 商品上下架
	* @method shangxiajia
	*/			
	public function shangxiajia($ac,$ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		if($ac == "shangjia"){
			$sql = "UPDATE `goods` SET `status`=1 WHERE id IN ({$ids_str})";
		}else if($ac == "xiajia"){
			$sql = "UPDATE `goods` SET `status`=0 WHERE id IN ({$ids_str})";
		}else{
			return ["status"=>"error","msg"=>"参数错误3"];
		}
		Db()->begin();
		try{
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量修改商品上下架信息:{$ids_str}|{$ac}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}
	/*
	* @name 商品推荐
	* @method tuijian
	*/			
	public function tuijian($ac,$ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		if($ac == "tuijian"){
			$sql = "UPDATE `goods` SET `is_tuijian`=1 WHERE id IN ({$ids_str})";
		}else if($ac == "cancel_tuijian"){
			$sql = "UPDATE `goods` SET `is_tuijian`=0 WHERE id IN ({$ids_str})";
		}else{
			return ["status"=>"error","msg"=>"参数错误3"];
		}
		Db()->begin();
		try{
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量修改商品推荐信息:{$ids_str}|{$ac}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}
	/*
	* @name 商品删除
	* @method del
	*/			
	public function del($ac,$ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		//
		$goods_data = Db()->table("goods")->select("id,img_url")->where("id IN ({$ids_str})")->fetchAll();
		if(count($goods_data) == 0){
			return ["status"=>"error","msg"=>"商品信息不存在"];
		}
		//
		Db()->begin();
		try{
			$sql = "DELETE FROM `goods` WHERE id IN ({$ids_str})";
			$rowCount = Db()->_exec($sql);
			if(!empty($rowCount)){
				//
				$goods_imgs_data = Db()->table("goods_imgs")->select("id,img_url")->where("goods_id IN ({$ids_str})")->fetchAll();
				//
				Db()->table("goods_guige")->where("goods_id IN ({$ids_str})")->del();
				Db()->table("goods_imgs")->where("goods_id IN ({$ids_str})")->del();
				Db()->table("goods_car")->where("goods_id IN ({$ids_str})")->del();
				//
			}
			$this->root_log("批量删除商品信息:{$ids_str}");
			Db()->commit();
			//
			if(isset($goods_imgs_data) && !empty($goods_imgs_data)){
				foreach($goods_imgs_data as $row){
					\core\Upload::delFile($row['img_url']);
				}
			}
			foreach($goods_data as $row){
				\core\Upload::delFile($row['img_url']);
			}
			//
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}
	/*
	* @name 商品添加
	* @method add
	*/			
	public function add(){
		dbConn();
		if(
			!empty($_POST['name']) && 
			!empty($_POST['jieshao']) && 
			!empty($_POST['guige_param']) && 
			!empty($_POST['guige_row']) && 
			!empty($_POST['type_id']) && 
			isset($_POST['price']) && 
			isset($_POST['status']) && 
			isset($_POST['sell_num']) && 
			isset($_POST['is_shiyong']) && 
			isset($_POST['yongjin_bili']) && 
			check($_POST['type_id'],"intgt0") &&
			check($_POST['status'],"integt0") &&
			check($_POST['sell_num'],"integt0") &&
			check($_POST['yongjin_bili'],"integt0") &&
			is_numeric($_POST['price']) &&
			$_POST['price'] >= 0 &&
			in_array($_POST['is_shiyong'],[0,1]) && 
			in_array($_POST['is_tuijian'],[0,1]) &&
			$_POST['yongjin_bili'] < 100
		){
			$name = htmlspecialchars($_POST['name']);
			$jieshao = htmlspecialchars($_POST['jieshao']);
			$guige_param = json_decode(htmlspecialchars_decode($_POST['guige_param']),true);
			$guige_row = json_decode(htmlspecialchars_decode($_POST['guige_row']),true);
			$type_id = (int)$_POST['type_id'];
			$status = (int)$_POST['status'];
			$sell_num = (int)$_POST['sell_num'];
			$is_tuijian = (int)$_POST['is_tuijian'];
			$is_shiyong = (int)$_POST['is_shiyong'];
			$yongjin_bili = (int)$_POST['yongjin_bili'];
			$price = number_format($_POST['price'],2,".","");
			//
			Db()->begin();
			try{
				if(empty($guige_param) || empty($guige_row)){
					throw new \Exception("规格参数错误");
				}
				//
				$guige_keys = array_keys($guige_param);
				$arr = [];
				foreach($guige_param as $v){
					$arr[] = $v;
				}
				$zuhe = self::zuhe($arr);
				foreach($zuhe as $k=>$v){
					$arr = explode("-",$v);
					$str = "";
					for($i=0;$i<count($arr);$i++){
						$str .= $guige_keys[$i] . "===" . $arr[$i] . ":::";
					}
					$zuhe[$k] = mb_substr($str,0,-3);
				}
				//dump($zuhe);
				//dump($guige_row);
				foreach($guige_row as &$row_){
					$guige_row_arr = explode(":::",$row_['guige_data']);
					$guige_obj = [];
					foreach($guige_row_arr as $v){
						$guige_name_val = explode("===",$v);
						$guige_obj[$guige_name_val[0]] = $guige_name_val[1];
					}
					$row_['guige'] = $guige_obj;
					$row_['guige_json'] = json_encode($guige_obj,JSON_UNESCAPED_UNICODE);
					//unset($row_['guige_data']);
					if(!in_array($row_['guige_data'],$zuhe)){
						throw new \Exception("规格参数错误：{$row_['guige_data']}");
					}
					if(
						!isset($row_['name']) ||
						empty($row_['name']) ||
						!isset($row_['price']) ||
						!is_numeric($row_['price']) ||
						$row_['price'] < 0 ||
						!isset($row_['kucun']) ||
						!check($row_['kucun'],"integt0")
					){
						throw new \Exception("规格参数错误1：{$row_['guige_data']}");
					}
				}
				if(count($guige_row) !== count($zuhe)){
					throw new \Exception("规格参数错误2:".count($zuhe)."|".count($guige_row));
				}
				$guige_param_json = json_encode($guige_param,JSON_UNESCAPED_UNICODE);
				//dump($guige_row);exit;
				//
				$data = [
					"name"=>":name",
					"jieshao"=>":jieshao",
					"type_id"=>$type_id,
					"price"=>$price,
					"status"=>$status,
					"sell_num"=>$sell_num,
					"is_tuijian"=>$is_tuijian,
					"is_shiyong"=>$is_shiyong,
					"yongjin_bili"=>$yongjin_bili,
					"guige"=>":guige",
				];
				$prepareParam = [
					":name"=>$name,
					":jieshao"=>$jieshao,
					":guige"=>$guige_param_json,
				];
				//
				if(!empty($_POST['img']) && check($_POST['img'],"url")){
					$data['img_url'] = htmlspecialchars($_POST['img']);
				}
				//
				$res = Db()->table("goods")->prepareParam($prepareParam)->insert($data);
				$id = Db()->insertId();
				//dump($guige_row);
				foreach($guige_row as $row){
					$sql = "INSERT INTO `goods_guige` (`goods_id`,`name`,`guige`,`price`,`kucun`,`is_shiyong`) VALUES ({$id},:name,:guige,{$row['price']},{$row['kucun']},{$is_shiyong})";
					Db()->_exec($sql,[":name"=>$row['name'],":guige"=>$row['guige_json']]);
				}
				//
				$this->root_log("添加商品信息,id:{$id}");
				Db()->commit();
				assign("alertTpl",true);
				assign("alertMsg","添加成功");
			}catch(\Exception $e){
				Db()->rollback();
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		//
		$status_arr = [0=>"下架",1=>"上架"];
		assign("status_arr",$status_arr);
		//
		$type_arr = Db()->table("goods_type")->fetchAll();
		$type_arr = array_column($type_arr,"name","id");
		assign("type_arr",$type_arr);
		//
		return view(['header','goods/add','footer']);
	}
	
	/*
	* @name 商品编辑
	* @method edit
	*/			
	public function edit(){
		dbConn();
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("goods/index"));
			exit;
		}
		$id = (int)$_REQUEST['id'];
		$goods_info = Db()->table("goods")->where("id={$id}")->fetch();
		if(empty($goods_info)){
			header("location:".url("goods/index"));
			exit;			
		}
		if(
			!empty($_POST['name']) && 
			!empty($_POST['jieshao']) && 
			!empty($_POST['guige_param']) && 
			!empty($_POST['guige_row']) && 
			!empty($_POST['type_id']) && 
			isset($_POST['price']) && 
			isset($_POST['status']) && 
			isset($_POST['sell_num']) && 
			check($_POST['type_id'],"intgt0") &&
			check($_POST['status'],"integt0") &&
			check($_POST['sell_num'],"integt0") &&
			is_numeric($_POST['price']) &&
			$_POST['price'] >= 0 &&
			in_array($_POST['is_shiyong'],[0,1]) &&
			in_array($_POST['is_tuijian'],[0,1]) &&
			isset($_POST['yongjin_bili']) && 
			check($_POST['yongjin_bili'],"integt0") &&
			$_POST['yongjin_bili'] < 100
		){
			$name = htmlspecialchars($_POST['name']);
			$jieshao = htmlspecialchars($_POST['jieshao']);
			$guige_param = json_decode(htmlspecialchars_decode($_POST['guige_param']),true);
			$guige_row = json_decode(htmlspecialchars_decode($_POST['guige_row']),true);
			$type_id = (int)$_POST['type_id'];
			$status = (int)$_POST['status'];
			$is_tuijian = (int)$_POST['is_tuijian'];
			$is_shiyong = (int)$_POST['is_shiyong'];
			$sell_num = (int)$_POST['sell_num'];
			$yongjin_bili = (int)$_POST['yongjin_bili'];
			$price = number_format($_POST['price'],2,".","");
			//
			Db()->begin();
			try{
				if(empty($guige_param) || empty($guige_row)){
					throw new \Exception("规格参数错误");
				}
				//
				$guige_keys = array_keys($guige_param);
				$arr = [];
				foreach($guige_param as $v){
					$arr[] = $v;
				}
				$zuhe = self::zuhe($arr);
				foreach($zuhe as $k=>$v){
					$arr = explode("-",$v);
					$str = "";
					for($i=0;$i<count($arr);$i++){
						$str .= $guige_keys[$i] . "===" . $arr[$i] . ":::";
					}
					$zuhe[$k] = mb_substr($str,0,-3);
				}
				//dump($zuhe);
				//dump($guige_row);
				foreach($guige_row as &$row_){
					$guige_row_arr = explode(":::",$row_['guige_data']);
					$guige_obj = [];
					foreach($guige_row_arr as $v){
						$guige_name_val = explode("===",$v);
						$guige_obj[$guige_name_val[0]] = $guige_name_val[1];
					}
					$row_['guige'] = $guige_obj;
					$row_['guige_json'] = json_encode($guige_obj,JSON_UNESCAPED_UNICODE);
					//unset($row_['guige_data']);
					if(!in_array($row_['guige_data'],$zuhe)){
						throw new \Exception("规格参数错误：{$row_['guige_data']}");
					}
					if(
						!isset($row_['name']) ||
						empty($row_['name']) ||
						!isset($row_['price']) ||
						!is_numeric($row_['price']) ||
						$row_['price'] < 0 ||
						!isset($row_['kucun']) ||
						!check($row_['kucun'],"integt0")
					){
						throw new \Exception("规格参数错误1：{$row_['guige_data']}");
					}
				}
				if(count($guige_row) !== count($zuhe)){
					throw new \Exception("规格参数错误2:".count($zuhe)."|".count($guige_row));
				}
				$guige_param_json = json_encode($guige_param,JSON_UNESCAPED_UNICODE);
				//dump($guige_row);exit;
				//
				$guige_data_res = Db()->table("goods_guige")->select("id,guige")->where("goods_id={$id}")->fetchAll();
				if(!empty($guige_data_res)){
					foreach($guige_data_res as $row){
						$guige = json_decode($row['guige'],true);
						$guige_check = "";
						foreach($guige as $k=>$v){
							$guige_check .= "{$k}==={$v}:::";
						}
						$guige_check = mb_substr($guige_check,0,-3);
						if(!in_array($guige_check,$zuhe)){
							//
							$delete_goods_imgs = Db()->table("goods_imgs")->select("id,img_url")->where("guige_id={$row['id']}")->fetchAll();
							//
							Db()->table("goods_guige")->where("id={$row['id']}")->del();
							Db()->table("goods_imgs")->where("guige_id={$row['id']}")->del();
							Db()->table("goods_car")->where("guige_id={$row['id']}")->del();
							//
						}
					}
				}
				//
				//dump($guige_row);
				foreach($guige_row as $row){
					$sql = "INSERT INTO `goods_guige` (`goods_id`,`name`,`guige`,`price`,`kucun`,`is_shiyong`) VALUES ({$id},:name,:guige,{$row['price']},{$row['kucun']},{$is_shiyong})";
					$sql .= " ON DUPLICATE KEY UPDATE name=:name,guige=:guige,price={$row['price']},kucun={$row['kucun']},is_shiyong={$is_shiyong}";
					Db()->_exec($sql,[":name"=>$row['name'],":guige"=>$row['guige_json']]);
				}
				//
				$data = [
					"name"=>":name",
					"jieshao"=>":jieshao",
					"type_id"=>$type_id,
					"price"=>$price,
					"status"=>$status,
					"sell_num"=>$sell_num,
					"is_tuijian"=>$is_tuijian,
					"is_shiyong"=>$is_shiyong,
					"yongjin_bili"=>$yongjin_bili,
					"guige"=>":guige",
				];
				$prepareParam = [
					":name"=>$name,
					":jieshao"=>$jieshao,
					":guige"=>$guige_param_json,
				];
				//
				if(!empty($_POST['img']) && $_POST['img'] != $goods_info['img_url']){
					$data['img_url'] = htmlspecialchars($_POST['img']);
					\core\Upload::delFile($goods_info['img_url']);
				}
				//
				$res = Db()->table("goods")->where("id={$id}")->prepareParam($prepareParam)->update($data);
				$this->root_log("编辑商品信息,id:{$id}");
				Db()->commit();
				if(isset($delete_goods_imgs) && !empty($delete_goods_imgs)){
					foreach($delete_goods_imgs as $img){
						\core\Upload::delFile($img['img_url']);		
					}					
				}
				$params = $_GET;
				unset($params['id']);
				header("location:".url("goods/index",$params));
				exit;
			}catch(\Exception $e){
				Db()->rollback();
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$data = Db()->table("goods")->where("id={$id}")->fetch();
		if(empty($data)){
			header("location:".url("goods/index"));
			exit;			
		}
		$data['jieshao'] = Db()->quote(htmlspecialchars_decode($data['jieshao']));
		$data['guige_row'] = Db()->table("goods_guige")->where("goods_id={$id}")->fetchAll();
		assign("data",$data);;
		//
		$status_arr = [0=>"下架",1=>"上架"];
		assign("status_arr",$status_arr);
		
		$type_arr = Db()->table("goods_type")->fetchAll();
		$type_arr = array_column($type_arr,"name","id");
		assign("type_arr",$type_arr);
		//
		return view(['header','goods/edit','footer']);
	}
	/*
	* @name 图片列表
	* @method imgs
	*/			
	public function imgs($id){
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("goods/index"));
			exit;
		}
		dbConn();
		$id = (int)$_REQUEST['id'];
		$goods_info = Db()->table("goods")->where("id={$id}")->fetch();
		if(empty($goods_info)){
			header("location:".url("goods/index",["id"=>$id]));
			exit;
		}
		
		assign("goods_info",$goods_info);
		//
		$guige_data = Db()->table("goods_guige")->select("id,name,guige")->where("goods_id={$goods_info['id']}")->fetchAll();
		assign("guige_data",$guige_data);
		$guige_id_name = array_column($guige_data,"name","id");
		assign("guige_id_name",$guige_id_name);
		//
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 100;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "goods_id={$goods_info['id']}";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size,"id"=>$id];
		if(!empty($_REQUEST['guige_id']) && check($_REQUEST['guige_id'],"intgt0")){
			$guige_id = (int)$_REQUEST['guige_id'];
			$where .= " AND guige_id={$guige_id}";
			$pageParams['guige_id'] = $_REQUEST['guige_id'];
			assign("guige_id",$guige_id);
		}
		$data = Db()->table("goods_imgs")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		assign("data",$data);
		//dump($data);exit;
		//
		if(isAjax()){
			responseType("json");
			return ["data"=>$data,"pages"=>ceil(\core\Page::$count / \core\Page::$pageSize)];
		}
		//
		return view(['header','goods/imgs','footer']);
	}
	
	/*
	* @name 图片添加
	* @method imgs_add
	*/			
	public function imgs_add($goods_id,$guige_id){
		responseType("json");
		if(
			!check($goods_id,"intgt0") || 
			!check($guige_id,"integt0") || 
			!isset($_FILES['image']) ||
			$_FILES['image']['error'] != 0
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		dbConn();
		$goods_id = (int)$goods_id;
		$guige_id = (int)$guige_id;
		$goods_info = Db()->table("goods")->select("id,name")->where("id={$goods_id}")->fetch();
		if(empty($goods_info)){
			return ["status"=>"error","msg"=>"商品不存在"];
		}
		if($guige_id > 0){
			$guige_info = Db()->table("goods_guige")->select("id,name")->where("id={$guige_id} AND goods_id={$goods_id}")->fetch();
			if(empty($guige_info)){
				return ["status"=>"error","msg"=>"商品规格不存在"];
			}			
		}
		$img_url = Upload()->move($_FILES['image'],"goods_imgs");
		if(!empty($img_url)){
			$data = [
				"goods_id"=>$goods_id,
				"guige_id"=>$guige_id,
				"img_url"=>":img_url",
			];
			$prepareParam = [
				":img_url"=>$img_url
			];
			$res = Db()->table("goods_imgs")->prepareParam($prepareParam)->insert($data);
			if(!empty($res)){
				$this->root_log("上传商品图片：{$goods_info['name']}");
				return ["status"=>"ok","msg"=>"上传成功【{$_FILES['image']['name']}】"];		
			}else{
				return ["status"=>"error","msg"=>"操作失败【{$_FILES['image']['name']}】"];
			}
		}else{
			return ["status"=>"error","msg"=>"上传图片失败【{$_FILES['image']['name']}】"];
		}
	}
	/*
	* @name 图片删除
	* @method imgs_del
	*/			
	public function imgs_del($ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		$imgs_data = Db()->table("goods_imgs")->select("id,img_url")->where("id IN ({$ids_str})")->fetchAll();
		if(empty($imgs_data)){
			return ["status"=>"error","msg"=>"参数错误3"];
		}
		Db()->begin();
		try{
			$sql = "DELETE FROM `goods_imgs` WHERE id IN ({$ids_str})";
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量删除商品图片信息:{$ids_str}");
			Db()->commit();
			if(!empty($rowCount)){
				foreach($imgs_data as $img){
					\core\Upload::delFile($img['img_url']);		
				}
			}
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}	
	}
	/*
	* @name 订单列表
	* @method orders
	*/
	public function orders(){
		dbConn();
		//
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(isset($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		if(!empty($_REQUEST['order_id'])){
			$order_id = trim($_REQUEST['order_id']);
			$where .= " AND order_id=:order_id";
			$prepareParam[":order_id"]=$order_id;
			$pageParams['order_id'] = $_REQUEST['order_id'];
		}
		if(!empty($_REQUEST['transaction_id'])){
			$transaction_id = trim($_REQUEST['transaction_id']);
			$where .= " AND transaction_id=:transaction_id";
			$prepareParam[":transaction_id"]=$transaction_id;
			$pageParams['transaction_id'] = $_REQUEST['transaction_id'];
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $_REQUEST['status'];
			assign("status",$status);
		}
		if(isset($_REQUEST['pay_type']) && check($_REQUEST['pay_type'],"integt0")){
			$pay_type = (int)$_REQUEST['pay_type'];
			$where .= " AND pay_type={$pay_type}";
			$pageParams['pay_type'] = $_REQUEST['pay_type'];
			assign("pay_type",$pay_type);
		}
		if(isset($_REQUEST['goods_id']) && check($_REQUEST['goods_id'],"intgt0")){
			$goods_id = (int)$_REQUEST['goods_id'];
			$where .= " AND goods_ids LIKE '%|{$goods_id}|%'";
			$pageParams['goods_id'] = $_REQUEST['goods_id'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-3 month"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `create_time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$status_arr = [0=>"未支付",1=>"已支付",2=>"已发货",3=>"已完成",4=>"已取消",5=>"退款中",6=>"退款成功",7=>"退款失败"];
		assign("status_arr",$status_arr);
		//
		$data = Db()->table("goods_order")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		foreach($data as &$row){
			$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$row['uid']} LIMIT 1";
			$res = Db()->_fetch($sql);
			$row["user"] = $res ? $res : [];
			//
			$goods_info = json_decode($row['goods_info'],true);
			$row['goods_info'] = $goods_info;
			//
			$addr_info = [];
			if($row['addr_id'] > 0){
				$addr_info = Db()->table("user_addr")->where("id={$row['addr_id']}")->fetch();
			}
			$row['addr_info'] = $addr_info ?: [];
			//
			$wuliu_info = [];
			if($row['wuliu_id'] > 0){
				$wuliu_info = Db()->table("wuliu")->where("id={$row['wuliu_id']}")->fetch();
			}
			$row['wuliu_info'] = $wuliu_info ?: [];
			//
		}
		assign("data",$data);
		//dump($data);exit;
		$wuliu_arr = Db()->table("wuliu")->select("id,name")->fetchAll();
		assign("wuliu_arr",$wuliu_arr);
		//
		$pay_type_arr = [1=>"微信",2=>"余额",3=>"其他"];
		assign("pay_type_arr",$pay_type_arr);
		//
		return view(['header','goods/orders','footer']);
	}
	/*
	* @name 取消订单
	* @method cancel
	*/	
	public function cancel($ids){
		dbConn();
		responseType("json");
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);
		//
		Db()->begin();
		try{
			$sql = "UPDATE `goods_order` SET `status`=4,cancel_time='".DATETIME."' WHERE id IN ({$ids}) AND status IN (0,1,2)";
			$rowCount = Db()->_exec($sql);
			if(empty($rowCount)){
				throw new \Exception("操作失败或影响数为零");
			}
			$this->root_log("取消订单,id:{$ids}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功，更改数量：".$rowCount];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 订单发货
	* @method fahuo
	*/	
	public function fahuo($ids,$wuliu_id,$wuliu_danhao){
		dbConn();
		responseType("json");
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);
		if(!check($wuliu_id,"intgt0")){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$wuliu_id = (int)$wuliu_id;
		$wuliu_danhao = htmlspecialchars($wuliu_danhao);
		$wuliu_check = Db()->table("wuliu")->where("id={$wuliu_id}")->getColumn("name");
		if(empty($wuliu_check)){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		//
		$goods_auto_queren_days = $this->get_config("goods_auto_queren_days");
		if(empty($goods_auto_queren_days) || !check($goods_auto_queren_days,"intgt0")){
			return ["status"=>"error","msg"=>"获取系统配置参数错误"];
		}
		//
		Db()->begin();
		try{
			$auto_queren_time = date("Y-m-d H:i:s",strtotime("+{$goods_auto_queren_days} days"));
			$sql = "UPDATE `goods_order` SET `status`=2,fahuo_time='".DATETIME."',wuliu_id={$wuliu_id},wuliu_danhao=:wuliu_danhao,auto_queren_time='{$auto_queren_time}' WHERE id IN ({$ids}) AND status IN (1)";
			$rowCount = Db()->_exec($sql,[":wuliu_danhao"=>$wuliu_danhao]);
			if(empty($rowCount)){
				throw new \Exception("操作失败或影响数为零");
			}
			$this->root_log("订单发货,id:{$ids}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功，更改数量：".$rowCount];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	
	/*
	* @name 退款
	* @method tuikuan
	*/	
	public function tuikuan($id,$tukuan_money,$tuikuan_yuanyin){
		dbConn();
		responseType("json");
		if(
			!check($id,"intgt0") ||
			empty($tukuan_money) ||
			!is_numeric($tukuan_money) ||
			$tukuan_money <= 0 ||
			empty($tuikuan_yuanyin)
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = (int)$id;
		$tukuan_money = number_format($tukuan_money,2,".","");
		$tuikuan_yuanyin = trim($tuikuan_yuanyin);
		$goods_order = Db()->table("goods_order")->where("id={$id}")->fetch();
		if(empty($goods_order))return ["status"=>"error","msg"=>"订单未找到"];
		if(!in_array($goods_order['status'],[1,2,3])){
			return ["status"=>"error","msg"=>"仅支持已支付、已发货、已确认的订单退款"];
		}
		if(bccomp($tukuan_money,$goods_order['money'],2) > 0){
			return ["status"=>"error","msg"=>"退款金额不能超过支付金额"];
		}
		$money_fen = (int)($tukuan_money * 100);
		$total_fen = (int)($goods_order['money'] * 100);
		$order_id = $goods_order['order_id'];
		//
		$pay_type = $goods_order['pay_type'];
		//
		Db()->begin();
		try{
			if($pay_type == 1){
				$res = \model\Wechat::tuikuan($money_fen,$total_fen,$order_id,$tuikuan_yuanyin);
				if($res['status'] != "ok"){
					throw new \Exception("微信支付退款受理失败:".$res['msg']);
				}
				if($res['data'] == "success"){
					$sql = "UPDATE `goods_order` SET `status`=6,`tuikuan_money`={$tukuan_money},`tuikuan_time`='".DATETIME."',`tuikuan_res_time`='".DATETIME."' WHERE `order_id`={$order_id}";
					Db()->_exec($sql);
				}else{
					$sql = "UPDATE `goods_order` SET `status`=5,`tuikuan_money`={$tukuan_money},`tuikuan_time`='".DATETIME."' WHERE `order_id`={$order_id}";
					Db()->_exec($sql);
				}
			}else if($pay_type == 2){
				//
				$sql = "UPDATE `goods_order` SET `status`=6,`tuikuan_money`={$tukuan_money},`tuikuan_time`='".DATETIME."',`tuikuan_res_time`='".DATETIME."' WHERE `order_id`={$order_id} AND status IN (1,2,3)";
				$rowCount = Db()->_exec($sql);
				if(empty($rowCount)){
					throw new \Exception("更改订单信息失败");
				}
				//
				$sql = "UPDATE `user` SET money=money+{$tukuan_money} WHERE uid={$goods_order['uid']}";
				$rowCount = Db::_exec($sql);
				if(empty($rowCount)){
					throw new \Exception("余额支付退款失败");
				}
				//
				$shengyu = Db()->table("user")->where("uid={$goods_order['uid']}")->getColumn("money",0);
				$zhangdan = [
					"uid"=>$goods_order['uid'],
					"money"=>$tukuan_money,
					"type"=>13,
					"shengyu"=>$shengyu,
					"msg"=>"商品退款：{$order_id}",
				];
				Db()->table("user_zhangdan")->insert($zhangdan);
				//
			}else{
				throw new \Exception("未知支付类型");
			}
			$this->root_log("订单退款:{$order_id}|{$tukuan_money}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 退款获取订单金额
	* @method get_order_money
	*/	
	public function get_order_money($id){
		dbConn();
		responseType("json");
		if(!check($id,"intgt0")){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = (int)$_POST['id'];
		$money = Db()->table("goods_order")->where("id={$id}")->getColumn("money");
		if(empty($money)){
			return ["status"=>"error","msg"=>"金额未找到"];
		}else{
			return ["status"=>"ok","data"=>$money];
		}
	}		
	/*
	* @name 评价列表
	* @method pingjia
	*/			
	public function pingjia(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(isset($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		if(isset($_REQUEST['goods_id']) && check($_REQUEST['goods_id'],"intgt0")){
			$goods_id = (int)$_REQUEST['goods_id'];
			$where .= " AND goods_id={$goods_id}";
			$pageParams['goods_id'] = $_REQUEST['goods_id'];
		}
		if(isset($_REQUEST['guige_id']) && check($_REQUEST['guige_id'],"intgt0")){
			$guige_id = (int)$_REQUEST['guige_id'];
			$where .= " AND guige_id={$guige_id}";
			$pageParams['guige_id'] = $_REQUEST['guige_id'];
		}
		if(isset($_REQUEST['star_num']) && check($_REQUEST['star_num'],"intgt0")){
			$star_num = (int)$_REQUEST['star_num'];
			$where .= " AND star_num={$star_num}";
			$pageParams['star_num'] = $_REQUEST['star_num'];
			assign("star_num",$star_num);
		}
		if(!empty($_REQUEST['order_id'])){
			$where .= " AND order_id=:order_id";
			$prepareParam[":order_id"] = $_REQUEST['order_id'];
			$pageParams['order_id'] = $_REQUEST['order_id'];
		}
		if(isset($_REQUEST['has_img']) && in_array($_REQUEST['has_img'],[0,1])){
			$has_img = intval($_REQUEST['has_img']);
			if($has_img == 1){
				$where .= " AND imgs_url IS NOT NULL";
			}else if($has_img == 0){
				$where .= " AND imgs_url IS NULL";
			}
			$pageParams['has_img'] = $_REQUEST['has_img'];
			assign("has_img",$has_img);
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND contents LIKE :keyword";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("goods_pingjia")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		foreach($data as &$row){
			$row['user'] = Db()->table("user")->select("uid,nickname")->where("uid={$row['uid']}")->fetch();
			$row['goods_name'] = Db()->table("goods")->where("id={$row['goods_id']}")->getColumn("name","-");
			$row['guige_name'] = Db()->table("goods_guige")->where("id={$row['guige_id']}")->getColumn("name","-");
		}
		assign("data",$data);
		//dump($data);exit;
		//
		return view(['header','goods/pingjia','footer']);
	}
	/*
	* @name 评价删除
	* @method pingjia_del
	*/			
	public function pingjia_del($ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		$pingjia_data = Db()->table("goods_pingjia")->select("id,imgs_url")->where("id IN ({$ids_str})")->fetchAll();
		if(empty($pingjia_data)){
			return ["status"=>"error","msg"=>"参数错误3"];
		}
		Db()->begin();
		try{
			$sql = "DELETE FROM `goods_pingjia` WHERE id IN ({$ids_str})";
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量删除评价信息:{$ids_str}");
			Db()->commit();
			foreach($pingjia_data as $row){
				if(!empty($row['imgs_url'])){
					$imgs = explode("|",trim($row['imgs_url'],"|"));
					if(!empty($imgs)){
						foreach($imgs as $img){
							\core\Upload::delFile($img);
						}
					}
				}
			}
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	//组合[["a","b","c"],["x","y"]...]
	static private function zuhe($arr){
		$res = [];
		foreach($arr as $a){
			if(empty($res)){
				$res = $a;
			}else{
				$list = [];
				foreach ($res as $v) {
					foreach ($a as $value) {
						$list[] = $v.'-'.$value;     
					}
				}
				$res = $list;
			}
		}
		return $res;
	}
	
	static public function get_depth($array){
		$max_depth = 1;
		foreach($array as $value){
			if(is_array($value)){
				$depth = self::get_depth($value) + 1;
				if($depth > $max_depth){
					$max_depth = $depth;
				}
			}
		}
		return $max_depth;
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
