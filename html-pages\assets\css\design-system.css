/* 微信小程序HTML复刻 - 设计系统 */

/* CSS变量系统 - 基于优化设计文档的设计令牌 */
:root {
  /* 主色系 - 根据设计文档优化 */
  --color-primary: #5CB85C;           /* 优化后的主色调，增强活力 */
  --color-primary-light: #7BC97B;     /* 浅色变体 */
  --color-primary-lighter: #E8F5E8;   /* 最浅背景色 */
  --color-primary-dark: #4A9A4A;      /* 深色变体 */

  /* 辅助色系 */
  --color-secondary: #6C757D;         /* 辅助色 */
  --color-accent: #FF6B6B;            /* 强调色 */
  --color-warning: #FFC107;           /* 警告色 */
  --color-success: #28A745;           /* 成功色 */
  --color-error: #DC3545;             /* 错误色 */
  --color-info: #17A2B8;              /* 信息色 */

  /* 完善的中性色阶 */
  --color-gray-50: #F8F9FA;           /* 最浅背景 */
  --color-gray-100: #E9ECEF;          /* 浅背景 */
  --color-gray-200: #DEE2E6;          /* 边框色 */
  --color-gray-300: #CED4DA;          /* 分割线 */
  --color-gray-400: #6C757D;          /* 辅助文字 */
  --color-gray-500: #495057;          /* 次要文字 */
  --color-gray-600: #343A40;          /* 主要文字 */
  --color-gray-700: #212529;          /* 标题文字 */

  /* 语义化颜色别名 */
  --color-background: var(--color-gray-50);
  --color-surface: #ffffff;
  --color-text-primary: var(--color-gray-700);
  --color-text-secondary: var(--color-gray-500);
  --color-text-tertiary: var(--color-gray-400);
  --color-border: var(--color-gray-200);
  --color-border-light: var(--color-gray-100);
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* 间距系统 (rpx转换为rem, 1rpx ≈ 0.0267rem) */
  --spacing-xs: 0.21rem;   /* 8rpx */
  --spacing-sm: 0.43rem;   /* 16rpx */
  --spacing-md: 0.64rem;   /* 24rpx */
  --spacing-lg: 0.85rem;   /* 32rpx */
  --spacing-xl: 1.28rem;   /* 48rpx */
  --spacing-xxl: 1.6rem;   /* 60rpx */

  /* 圆角系统 */
  --radius-xs: 0.21rem;    /* 8rpx */
  --radius-sm: 0.32rem;    /* 12rpx */
  --radius-md: 0.43rem;    /* 16rpx */
  --radius-lg: 0.64rem;    /* 24rpx */
  --radius-xl: 0.85rem;    /* 32rpx */
  --radius-round: 50%;

  /* 优化后的字体系统 - 根据设计文档层级优化 */
  --font-size-xs: 0.59rem;   /* 22rpx - 时间标签 */
  --font-size-sm: 0.64rem;   /* 24rpx - 辅助信息 */
  --font-size-md: 0.75rem;   /* 28rpx - 正文内容 */
  --font-size-lg: 0.85rem;   /* 32rpx - 卡片标题 */
  --font-size-xl: 0.96rem;   /* 36rpx - 页面标题 */
  --font-size-xxl: 1.07rem;  /* 40rpx - 主标题 */

  /* 字体权重系统 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 优化后的行高系统 */
  --line-height-tight: 1.3;    /* 标题使用 */
  --line-height-normal: 1.5;   /* 一般文本 */
  --line-height-relaxed: 1.6;  /* 正文内容 */

  /* 字间距系统 */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;

  /* 阴影系统 */
  --shadow-sm: 0 0.05rem 0.21rem rgba(0, 0, 0, 0.05);
  --shadow-md: 0 0.11rem 0.43rem rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 0.21rem 0.64rem rgba(0, 0, 0, 0.15);
  --shadow-card: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.05);

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 层级系统 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal: 1040;
  --z-index-popover: 1050;
  --z-index-tooltip: 1060;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px; /* 基准字体大小 */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局工具类 */
.container {
  max-width: 750px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.page {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Flexbox工具类 */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }

/* 定位工具类 */
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-xs); }
.pl-2 { padding-left: var(--spacing-sm); }
.pl-3 { padding-left: var(--spacing-md); }
.pl-4 { padding-left: var(--spacing-lg); }
.pl-5 { padding-left: var(--spacing-xl); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-xs); }
.pr-2 { padding-right: var(--spacing-sm); }
.pr-3 { padding-right: var(--spacing-md); }
.pr-4 { padding-right: var(--spacing-lg); }
.pr-5 { padding-right: var(--spacing-xl); }

/* 文本工具类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-xxl { font-size: var(--font-size-xxl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

/* 优化后的文本层级系统 - 根据设计文档 */
.text-hierarchy-h1 {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
  letter-spacing: var(--letter-spacing-tight);
}

.text-hierarchy-h2 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-xs);
}

.text-hierarchy-body {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-wide);
}

.text-hierarchy-caption {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  opacity: 0.8;
  line-height: var(--line-height-normal);
}

/* 背景色工具类 */
.bg-primary { background-color: var(--color-primary); }
.bg-surface { background-color: var(--color-surface); }
.bg-background { background-color: var(--color-background); }

/* 圆角工具类 */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-round); }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-card { box-shadow: var(--shadow-card); }

/* 过渡动画工具类 */
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* 文本截断工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

/* 隐藏/显示工具类 */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
}

@media (min-width: 769px) {
  .d-lg-none { display: none; }
  .d-lg-block { display: block; }
}
