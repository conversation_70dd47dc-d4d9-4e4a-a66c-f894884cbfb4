<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 用户管理
*/
class User extends Controller{

	public function __construct(){
		parent::__construct();
	}

	/*
	* @apiName 用户注册/登录
	* @method login
	* @POST
	* @param code string 微信登录code
	* @param pid string 邀请人编号
	* @param city string 用户城市（可选，用于新用户注册）
	* @return {"status":"ok","data":{"uid":2,"mobile":"15800000000","avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","sex":2,"money":"0.00","token":"7723cdc68268ccb1a77cd95c6a3097ee","birthday":"1960-10-10","gexingqianming":"个性签名","is_huiyuan":0,"reg_time":"2023-11-17 16:23:53","labels":[{"id":1,"label":"90后"},{"id":2,"label":"音乐"}],"imgs":[{"id":1,"img_url":"http:\/\/127.0.0.1\/test.jpg"},{"id":2,"img_url":"http:\/\/127.0.0.1\/test.jpg"}]}}
	*/
	public function login($code,$pid=0){
		if(
			empty($code)
		)return ["status"=>"error","msg"=>"参数错误"];
		
		try {
			$info = \model\Wechat::getOpenid($code);
			
			if(
				empty($info) ||
				!isset($info['openid']) ||
				!isset($info['unionid'])
			){
				// 记录错误详情到日志
				$this->exception_log("登录失败: 获取OpenID/UnionID失败，返回信息: " . json_encode($info));
				return ["status"=>"error","msg"=>"获取信息失败"];
			}
			
			$openid = $info['openid'];
			$unionid = $info['unionid'];
			
			dbConn();
			$user_info = Db()->table("user")->select("uid,is_dongjie")->where("unionid=:unionid")->prepareParam([":unionid"=>$unionid])->fetch();
			
			if(!empty($user_info)){
				// 已有用户
				if($user_info['is_dongjie'] != 0){
					return ["status"=>"error","msg"=>"账户不可用"];
				}
				
				$user_info = self::_get_user_info($user_info['uid']);
				if(empty($user_info)){
					$this->exception_log("登录失败: 获取用户信息失败，用户ID: " . $user_info['uid']);
					return ["status"=>"error","msg"=>"获取用户信息失败"];
				}
				
				$token = md5(makeCode(32,true));
				// 设置最后活动时间为当前时间
				$last_active_time = date('Y-m-d H:i:s');
				Db()->table("user")->where("uid={$user_info['uid']}")->update(["token"=>$token, "last_active_time"=>$last_active_time]);
				$user_info['token'] = $token;
				
				$this->user_log($user_info['uid'],"登录成功");
				
				return ["status"=>"ok","data"=>$user_info];
			}
			
			// 新用户注册
			$p_uid = 0;
			$branch_id = null;
			if(check($pid,"intgt0")){
				$pid = intval($pid);
				$check = Db()->table("user")->select("uid,role_type,branch_id")->where("uid={$pid}")->fetch();
				if(!empty($check)){
					$p_uid = $check['uid'];

					// 分会长分配逻辑
					if($check['role_type'] == '1'){
						// 如果分享者是分会长，新用户直接分配到该分会长的分会
						$branch_id = $check['branch_id'];
					} else if(!empty($check['branch_id'])){
						// 如果分享者是普通用户但有分会，新用户分配到同一分会
						$branch_id = $check['branch_id'];
					}
				}
			}

			// 如果没有通过分享分配分会，分配到默认分会（总会）
			if($branch_id === null){
				$branch_id = 0; // 总会ID
			}

			// 🆕 新增：获取用户城市信息
			$user_city = '';
			if (!empty($_POST['city'])) {
				$user_city = trim($_POST['city']);
				// 简单的城市名称验证和清理
				if (mb_strlen($user_city) > 50) {
					$user_city = mb_substr($user_city, 0, 50);
				}
			}

			$token = md5(makeCode(32,true));
			// 设置最后活动时间为当前时间
			$last_active_time = date('Y-m-d H:i:s');
			$user_default_head_img = Db()->table("img_config")->where("mark='user_default_head_img'")->getColumn("img_url");
			$user_default_head_img = $user_default_head_img ?: "";

			Db()->begin();
			try{
				$user = [
					"nickname"=>"",
					"token"=>$token,
					"last_active_time"=>$last_active_time,
					"avatar"=>"",
					"p_uid"=>$p_uid,
					"branch_id"=>$branch_id,
					"city"=>":city",
					"openid"=>":openid",
					"unionid"=>":unionid",
					"reg_ip"=>":ip",
				];
				$prepareParam = [
					":city"=>$user_city,
					":openid"=>$openid,
					":unionid"=>$unionid,
					":ip"=>IP,
				];
				if(!Db()->table("user")->prepareParam($prepareParam)->insert($user)){
					throw new \Exception("插入用户信息失败");
				}
				
				$uid = Db()->insertId();
				$this->user_log($uid,"注册并登录成功");

				// 🆕 新增：为新用户分配分会（如果需要）
				$this->assignUserToBranch($uid, $p_uid > 0 ? $p_uid : null);

				Db()->commit();
				$user_info = self::_get_user_info($uid);
				if(empty($user_info)){
					$this->exception_log("注册失败: 新用户插入成功但获取用户信息失败，用户ID: " . $uid);
					return ["status"=>"error","msg"=>"获取用户信息失败"];
				}

				return ["status"=>"ok","data"=>$user_info];
				
			}catch(\Exception $e){
				Db()->rollback();
				$this->exception_log("注册失败: " . $e->getMessage());
				return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
			}
		} catch(\Exception $e) {
			// 捕获整个登录过程中的任何异常
			$this->exception_log("登录过程异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
			return ["status"=>"error","msg"=>"登录失败，请稍后重试"];
		}
	}

	/*
	* @apiName 获取用户信息
	* @method get_user_info
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","data":{"uid":2,"mobile":"15800000000","avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","sex":2,"money":"0.00","token":"7723cdc68268ccb1a77cd95c6a3097ee","birthday":"1960-10-10","gexingqianming":"个性签名","is_huiyuan":0,"reg_time":"2023-11-17 16:23:53","labels":[{"id":1,"label":"90后"},{"id":2,"label":"音乐"}],"imgs":[{"id":1,"img_url":"http:\/\/127.0.0.1\/test.jpg"},{"id":2,"img_url":"http:\/\/127.0.0.1\/test.jpg"}]}}
	*/
	public function get_user_info($uid,$token){
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$user_info = self::_get_user_info($uid);
		if(empty($user_info)){
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
		return ["status"=>"ok","data"=>$user_info];
	}

	private static function _get_user_info($uid,$ext_column=""){
		dbConn();
		$uid = (int)$uid;
		$user_info = Db()->table("user")->select("uid,mobile,avatar,nickname,sex,money,points,token,birthday,gexingqianming,city,is_huiyuan,huiyuan_end_time,role_type,branch_id,reg_time{$ext_column}")->where("uid={$uid}")->fetch();
		if(!empty($user_info)){
			$labels = Db()->table("user_label")->select("id,label")->where("uid={$user_info['uid']}")->fetchAll();
			$user_info['labels'] = $labels;
			$imgs = Db()->table("user_img")->select("id,img_url")->where("uid={$uid}")->fetchAll();
			$user_info['imgs'] = $imgs ?: [];
			// 确保积分字段存在且为整数
			$user_info['points'] = (int)($user_info['points'] ?? 0);

			// 🔴 修复：获取分会信息（优化版）
			if ($user_info['role_type'] == '1') { // 分会长
				// 查找用户管理的分会信息
				$branch_info = Db()->table("user_branch")
					->select("branch_id,branch_name,branch_location,branch_leader_mobile,branch_leader_qr_image")
					->where("branch_leader=:uid")
					->prepareParam([":uid" => $uid])
					->fetch();

				if (!empty($branch_info)) {
					$user_info['branch_id'] = $branch_info['branch_id'];
					$user_info['branch_name'] = $branch_info['branch_name'];
					$user_info['branch_location'] = $branch_info['branch_location'];
					$user_info['branch_leader_mobile'] = $branch_info['branch_leader_mobile'];
					$user_info['branch_leader_qr_image'] = $branch_info['branch_leader_qr_image'];

					// 同步更新用户表的branch_id字段
					if (empty($user_info['branch_id']) || $user_info['branch_id'] != $branch_info['branch_id']) {
						Db()->table("user")
							->where("uid=:uid")
							->prepareParam([":uid" => $uid])
							->update(["branch_id" => $branch_info['branch_id']]);
					}
				} else {
					// 🔧 优化：分会长但没有找到对应分会，显示友好提示
					$user_info['branch_name'] = '当前分会暂无分会长';
					$user_info['branch_location'] = '';
					$user_info['branch_leader_mobile'] = '';
					$user_info['branch_leader_qr_image'] = '';
				}
			} else if (!empty($user_info['branch_id'])) {
				// 普通用户，根据branch_id获取分会信息
				$branch_info = Db()->table("user_branch")
					->select("branch_name,branch_location,branch_leader,branch_leader_mobile,branch_leader_qr_image")
					->where("branch_id=:branch_id")
					->prepareParam([":branch_id" => $user_info['branch_id']])
					->fetch();

				if (!empty($branch_info)) {
					$user_info['branch_name'] = $branch_info['branch_name'];
					$user_info['branch_location'] = $branch_info['branch_location'];

					// 🔧 优化：检查分会长是否存在
					if (empty($branch_info['branch_leader'])) {
						$user_info['branch_leader_status'] = '当前分会暂无分会长';
					} else {
						// 获取分会长信息
						$leader_info = Db()->table("user")
							->select("nickname")
							->where("uid=:uid")
							->prepareParam([":uid" => $branch_info['branch_leader']])
							->fetch();

						$user_info['branch_leader_name'] = $leader_info['nickname'] ?? '未知分会长';
						$user_info['branch_leader_mobile'] = $branch_info['branch_leader_mobile'];
						$user_info['branch_leader_qr_image'] = $branch_info['branch_leader_qr_image'];
					}
				} else {
					$user_info['branch_name'] = '未知分会';
					$user_info['branch_location'] = '';
				}
			} else {
				// 没有分会信息的用户
				$user_info['branch_name'] = '';
				$user_info['branch_location'] = '';
			}
		}
		return $user_info;
	}

	/*
	* @apiName 更新手机号
	* @method update_mobile
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param code string 微信手机号code
	* @return {"status":"ok","msg":"修改成功"}
	*/
	public function update_mobile($uid,$token,$code){
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$mobile = \model\Wechat::getPhoneNumber($code);
		if(empty($mobile)){
			return ["status"=>"error","msg"=>"获取手机号失败"];
		}
		$data = [
			"mobile"=>":mobile"
		];
		$prepareParam = [
			":mobile"=>$mobile
		];
		dbConn();
		try{
			$res = Db()->table("user")->where("uid={$uid}")->prepareParam($prepareParam)->update($data);
			if($res){
				$this->user_log($uid,"修改手机号：{$mobile}");
			}
			return ["status"=>"ok","msg"=>"修改成功", "data"=>$mobile];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 手动更新手机号
	* @method update_mobile_manual
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param mobile string 手机号
	* @return {"status":"ok","msg":"修改成功"}
	*/
	public function update_mobile_manual($uid,$token,$mobile){
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];

		// 验证手机号格式
		if(empty($mobile) || !check($mobile,"mobile")){
			return ["status"=>"error","msg"=>"请输入正确的手机号"];
		}

		$uid = (int)$uid;
		$mobile = htmlspecialchars($mobile);

		dbConn();

		// 检查手机号是否已被其他用户使用
		$existing_user = Db()->table("user")
			->where("mobile=:mobile AND uid!=:uid")
			->prepareParam([":mobile"=>$mobile, ":uid"=>$uid])
			->fetch();

		if(!empty($existing_user)){
			return ["status"=>"error","msg"=>"该手机号已被其他用户绑定"];
		}

		$data = [
			"mobile"=>":mobile"
		];
		$prepareParam = [
			":mobile"=>$mobile
		];

		try{
			$res = Db()->table("user")->where("uid={$uid}")->prepareParam($prepareParam)->update($data);
			if($res){
				$this->user_log($uid,"手动修改手机号：{$mobile}");
			}
			return ["status"=>"ok","msg"=>"手机号绑定成功", "data"=>$mobile];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 修改资料
	* @method update
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param avatar string 头像地址(可选)
	* @param nickname string 昵称(可选)
	* @param sex string 性别:0=未知,1=男,2=女(可选)
	* @param birthday string 出生日期(可选)
	* @param gexingqianming	 string 个性签名(可选)
	* @return {"status":"ok","msg":"修改成功"}
	*/
	public function update($uid,$token){
		if(!$this->auth($uid,$token)) {
            return ["status"=>"relogin","msg"=>"登录信息验证失败"];
        }
		$uid = (int)$uid;
		$data = [];
		$prepareParam = [];
		if(!empty($_POST['avatar']) && check($_POST['avatar'],"url")){
			$data['avatar'] = ":avatar";
			$prepareParam[':avatar'] = htmlspecialchars($_POST['avatar']);
		}
		if(!empty($_POST['sex']) && in_array($_POST['sex'],[1,2])){
			$data['sex'] = intval($_POST['sex']);
		}
		if(!empty($_POST['nickname'])){
			$data['nickname'] = ":nickname";
			$prepareParam[':nickname'] = htmlspecialchars($_POST['nickname']);
		}
		if(!empty($_POST['birthday'])){
			$data['birthday'] = ":birthday";
			$prepareParam[':birthday'] = date("Y-m-d",strtotime($_POST['birthday']));
		}
		if(!empty($_POST['gexingqianming'])){
			$data['gexingqianming'] = ":gexingqianming";
			$prepareParam[':gexingqianming'] = htmlspecialchars($_POST['gexingqianming']);
		}
		// 🆕 新增：支持更新城市信息
		if(!empty($_POST['city'])){
			$city = trim($_POST['city']);
			if (mb_strlen($city) <= 50) {
				$data['city'] = ":city";
				$prepareParam[':city'] = htmlspecialchars($city);
			}
		}
		if(empty($data))return ["status"=>"error","msg"=>"参数错误"];
		dbConn();
		try{
			$res = Db()->table("user")->where("uid={$uid}")->prepareParam($prepareParam)->update($data);
			if($res){
				$this->user_log($uid,"修改资料");
			}
			return ["status"=>"ok","msg"=>"修改成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 添加标签
	* @method add_label
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param label string 标签
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function add_label($uid,$token,$label){
		if(
			empty($token) ||
			empty($label) ||
			!check($uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$data = [
			"uid"=>$uid,
			"label"=>":label"
		];
		$prepareParam = [
			":label"=>htmlspecialchars($label),
		];
		dbConn();
		try{
			$res = Db()->table("user_label")->prepareParam($prepareParam)->insert($data);
			$insertId = Db()->insertId();
			$this->user_log($uid,"添加标签:{$label}");
			return ["status"=>"ok","msg"=>"操作成功","data"=>$insertId];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 删除标签
	* @method del_label
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param id string 标签编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function del_label($uid,$token,$id){
		if(
			empty($token) ||
			!check($id,"intgt0") ||
			!check($uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$id = (int)$id;
		dbConn();
		try{
			$res = Db()->table("user_label")->where("id={$id} AND uid={$uid}")->del();
			if(empty($res)){
				throw new \Exception("操作失败");
			}
			$this->user_log($uid,"删除标签:{$id}");
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 添加资料照片
	* @method add_img
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param img_url string 图片地址
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function add_img($uid,$token,$img_url){
		if(
			empty($token) ||
			!check($img_url,"url") ||
			!check($uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$data = [
			"uid"=>$uid,
			"img_url"=>":img_url"
		];
		$prepareParam = [
			":img_url"=>htmlspecialchars($img_url),
		];
		dbConn();
		try{
			$res = Db()->table("user_img")->prepareParam($prepareParam)->insert($data);
			$insertId = Db()->insertId();
			//
			$img_num = Db()->table("user_img")->where("uid={$uid}")->count();
			Db()->table("user")->where("uid={$uid}")->update(["img_num"=>$img_num]);
			//
			$this->user_log($uid,"添加资料照片:{$img_url}");
			return ["status"=>"ok","msg"=>"操作成功","data"=>$insertId];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 删除资料照片
	* @method del_img
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param ids string 资料照片编号,多个英文逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function del_img($uid,$token,$ids){
		if(
			empty($token) ||
			empty($ids) ||
			!check($uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		$ids_arr = explode(",",$ids);
		foreach($ids_arr as $v){
			if(!check($v,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$ids_str = implode(",",$ids_arr);
		$uid = (int)$uid;
		dbConn();
		//
		$data = Db()->table("user_img")->where("id IN ({$ids_str}) AND uid={$uid}")->fetchAll();
		if(count($data) !== count($ids_arr)){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		//
		Db::begin();
		try{
			$rowCount = Db()->table("user_img")->where("id IN ({$ids_str}) AND uid={$uid}")->del();
			if(empty($rowCount)){
				throw new \Exception("操作失败");
			}
			//
			foreach($data as $row){
				\core\Upload::delFile($row['img_url']);
			}
			//
			$this->user_log($uid,"删除资料照片:{$rowCount}个");
			//
			$img_num = Db()->table("user_img")->where("uid={$uid}")->count();
			Db()->table("user")->where("uid={$uid}")->update(["img_num"=>$img_num]);
			//
			Db::commit();
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 添加收货地址
	* @method add_addr
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param sheng_id string 省编号
	* @param shi_id string 市编号
	* @param qu_id string 区/县编号
	* @param addr string 地址
	* @param username string 收货人
	* @param mobile string 手机号
	* @param is_default string 是否默认地址默认0
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function add_addr($uid,$token,$sheng_id,$shi_id,$qu_id,$addr,$username,$mobile,$is_default=0){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($sheng_id) ||
			empty($shi_id) ||
			empty($qu_id) ||
			!check($sheng_id,"intgt0") ||
			!check($shi_id,"intgt0") ||
			!check($qu_id,"intgt0") ||
			empty($addr) ||
			empty($username) ||
			!check($mobile,"mobile") ||
			!in_array($is_default,[0,1])
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$sheng_id = (int)$sheng_id;
		$shi_id = (int)$shi_id;
		$qu_id = (int)$qu_id;
		$is_default = (int)$is_default;
		dbConn();
		$sheng = Db()->table("china")->where("id={$sheng_id} AND deep=1")->getColumn("name");
		if(empty($sheng)){
			return ["status"=>"error","msg"=>"获取省份信息失败"];
		}
		$shi = Db()->table("china")->where("id={$shi_id} AND pid={$sheng_id} AND deep=2")->getColumn("name");
		if(empty($shi)){
			return ["status"=>"error","msg"=>"获取市信息失败"];
		}
		$qu = Db()->table("china")->where("id={$qu_id} AND pid={$shi_id} AND deep=3")->getColumn("name");
		if(empty($qu)){
			return ["status"=>"error","msg"=>"获取县区信息失败"];
		}
		$data = [
			"uid"=>$uid,
			"sheng_id"=>$sheng_id,
			"shi_id"=>$shi_id,
			"qu_id"=>$qu_id,
			"sheng"=>":sheng",
			"shi"=>":shi",
			"qu"=>":qu",
			"addr"=>":addr",
			"username"=>":username",
			"mobile"=>":mobile",
		];
		$prepareParam = [
			":sheng"=>htmlspecialchars($sheng),
			":shi"=>htmlspecialchars($shi),
			":qu"=>htmlspecialchars($qu),
			":addr"=>htmlspecialchars(mb_substr($addr,0,100)),
			":username"=>htmlspecialchars(mb_substr($username,0,15)),
			":mobile"=>htmlspecialchars(mb_substr($mobile,0,11)),
		];
		Db()->begin();
		try{
			if($is_default == 1){
				$data['is_default'] = 1;
				Db()->table("user_addr")->where("uid={$uid}")->update(["is_default"=>0]);
			}
			$res = Db()->table("user_addr")->prepareParam($prepareParam)->insert($data);
			$id = Db()->insertId();
			$this->user_log($uid,"添加收货地址：{$id}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"添加成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 删除收货地址
	* @method del_addr
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param ids string 收货地址编号,多个英文逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function del_addr($uid,$token,$ids){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($ids)
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$ids_arr = explode(",",$ids);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误:{$id}"];
			}
		}
		dbConn();
		try{
			$res = Db()->table("user_addr")->where("id IN ({$ids}) AND uid={$uid}")->update(["is_del"=>1]);
			if($res){
				return ["status"=>"ok","msg"=>"操作成功"];
			}
			$this->user_log($uid,"删除收货地址：{$ids}");
			throw new \Exception("资源不存在-{$uid}-{$ids}");
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 设置默认收货地址
	* @method set_default_addr
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param id string 收货地址编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function set_default_addr($uid,$token,$id){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($id) ||
			!check($id,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$id = (int)$id;
		dbConn();
		Db()->begin();
		try{
			Db()->table("user_addr")->where("uid={$uid}")->update(["is_default"=>0]);
			$res = Db()->table("user_addr")->where("id={$id} AND uid={$uid} AND is_del=0")->update(["is_default"=>1]);
			if(empty($res)){
				throw new \Exception("操作失败");
			}
			$this->user_log($uid,"设置默认收货地址：{$id}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取收货地址列表
	* @method get_addr_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","data":[{"id":2,"uid":2,"sheng":"安徽省","shi":"铜陵市","qu":"铜官区","sheng_id":1012,"shi_id":1059,"qu_id":1060,"addr":"某某街道某小区","mobile":"15811111111","username":"张三","is_default":1},{"id":1,"uid":2,"sheng":"安徽省","shi":"铜陵市","qu":"铜官区","sheng_id":1012,"shi_id":1059,"qu_id":1060,"addr":"某某街道某小区","mobile":"15811111111","username":"张三","is_default":0}]}
	*/
	public function get_addr_list($uid,$token){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$where = "`uid`={$uid} AND is_del=0";
		$data = Db()->table("user_addr")
            ->select("id,uid,sheng,shi,qu,sheng_id,shi_id,qu_id,addr,mobile,username,is_default")
            ->where($where)
            ->order("id DESC")
            ->fetchAll();

		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		return ["status"=>"ok","data"=>$data];
	}
	/*
	* @apiName 添加收款账号信息
	* @method bank_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param bank_name string 银行名称
	* @param bank_num string 银行卡号
	* @param username string 收款姓名
	* @param is_default string 是否默认,默认0
	* @param sms_code string 短信验证码(可选)
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function bank_add($uid,$token,$bank_name,$bank_num,$username,$is_default=0,$sms_code=""){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			//strlen($sms_code) != 6 ||
			empty($bank_name) ||
			empty($bank_num) ||
			empty($username)
		)return ["status"=>"error","msg"=>"参数错误"];
		$uid = (int)$uid;
		if(!$this->auth($uid,$token)){
			return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		}
		$bank_name = mb_substr(htmlspecialchars($bank_name),0,50);
		$bank_num = mb_substr(htmlspecialchars($bank_num),0,30);
		$username = mb_substr(htmlspecialchars($username),0,20);

		$data = [
			"uid"=>$uid,
			"bank_name"=>":bank_name",
			"bank_num"=>":bank_num",
			"username"=>":username",
		];
		$prepareParam = [
			":bank_name"=>$bank_name,
			":bank_num"=>$bank_num,
			":username"=>$username,
		];
		dbConn();

        $mobile = Db()->table("user")
            ->where("uid={$uid}")
            ->getColumn("mobile");

        $check_sms_code = Db()->table("sms_code")
            ->where("mobile=:mobile AND type=3 AND deadline>'".DATETIME."'")
            ->prepareParam([":mobile"=>$mobile])
            ->order("id DESC")
            ->getColumn("code");

		if(empty($check_sms_code) || $check_sms_code != $sms_code){
			//return ["status"=>"error","msg"=>"短信验证码错误"];
		}
		try{
			if($is_default == 1){
				$data['is_default'] = 1;
				Db()->table("user_bank")->where("uid={$uid}")->update(["is_default"=>0]);
			}
			if(!Db()->table("user_bank")->prepareParam($prepareParam)->insert($data)){
				throw new \Exception("插入信息失败");
			}
			$bank_id = Db()->insertId();
			//
			$this->user_log($uid,"添加收款账号信息【{$username} - {$bank_id}】");
			//
			return ["status"=>"ok","msg"=>"操作成功","data"=>$bank_id];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 删除收款账号信息
	* @method bank_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param ids string 收款账户编号多个逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function bank_del($uid,$token,$ids){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		)return ["status"=>"error","msg"=>"参数错误"];
		$uid = (int)$uid;
		$bank_ids_arr = explode(",",$ids);
		foreach($bank_ids_arr as $bank_id){
			if(!check($bank_id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		$ids = implode(",",$bank_ids_arr);
		if(!$this->auth($uid,$token)){
			return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		}
		dbConn();
		try{
			if(!Db()->table("user_bank")->where("id IN ({$ids}) AND uid={$uid} AND is_del=0")->update(["is_del"=>1])){
				throw new \Exception("操作失败");
			}
			//
			$this->user_log($uid,"删除收款账号信息【{$ids}】");
			//
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取收款账号信息
	* @method bank_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","data":[{"id":1,"bank_name":"工商银行","bank_num":"*************","username":"张三","is_default":0},{"id":2,"bank_name":"支付宝","bank_num":"***********","username":"张三","is_default":0}]}
	*/
	public function bank_list($uid,$token){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		)return ["status"=>"error","msg"=>"参数错误"];
		$uid = (int)$uid;
		if(!$this->auth($uid,$token)){
			return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		}
		dbConn();
		$data = Db()->table("user_bank")->select("id,bank_name,bank_num,username,is_default")->where("uid={$uid} AND is_del=0")->fetchAll();
		if(empty($data)){
			return ["status"=>"empty","data"=>[]];
		}
		return ["status"=>"ok","data"=>$data];
	}
	/*
	* @apiName 设置默认收款地址
	* @method set_default_bank
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param id string 收款编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function set_default_bank($uid,$token,$id){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($id) ||
			!check($id,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$id = (int)$id;
		dbConn();
		Db()->begin();
		try{
			Db()->table("user_bank")->where("uid={$uid}")->update(["is_default"=>0]);
			$res = Db()->table("user_bank")->where("id={$id} AND uid={$uid} AND is_del=0")->update(["is_default"=>1]);
			if(empty($res)){
				throw new \Exception("操作失败");
			}
			$this->user_log($uid,"设置默认收款地址：{$id}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 提现
	* @method tixian
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param bank_id string 收款编号
	* @param money string 金额
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function tixian($uid,$token,$bank_id,$money){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($bank_id) ||
			!check($bank_id,"intgt0") ||
			!is_numeric($money) ||
			$money <= 0
		)return ["status"=>"error","msg"=>"参数错误"];

		$uid = (int)$uid;
		$bank_id = (int)$bank_id;
		$money = number_format($money,2,".","");

        if(!$this->auth($uid,$token)){
			return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		}

		dbConn();
		$config = $this->get_config(["min_tixian_money","tixian_fee"]);

		if(
			!isset($config['min_tixian_money']) ||
			!isset($config['tixian_fee']) ||
			!is_numeric($config['min_tixian_money']) ||
			!is_numeric($config['tixian_fee']) ||
			$config['min_tixian_money'] <= 0 ||
			$config['tixian_fee'] < 0 ||
			$config['tixian_fee'] >= 100
		){
			return ["status"=>"error",
                "msg"=>"获取系统配置信息失败"];
		}
		$min_tixian_money = number_format($config['min_tixian_money'],
            2,".","");
		$tixian_fee = number_format($config['tixian_fee']/100,
            4,".","");

		if(bccomp($money,$min_tixian_money,2) < 0){
			return ["status"=>"error",
                "msg"=>"最少提现金额：{$min_tixian_money}"];
		}

        $has_money = Db()->table("user")->where("uid={$uid}")->getColumn("money");

        if(bccomp($has_money,$money,2) < 0){
			return ["status"=>"error",
                "msg"=>"余额不足"];
		}
		$bank_info = Db()
            ->table("user_bank")
            ->where("id={$bank_id} AND uid={$uid} AND is_del=0")
            ->fetch();

        if(empty($bank_info)){
			return ["status"=>"error",
                "msg"=>"收款地址不存在"];
		}

		$daozhang_money = number_format($money - $money * $tixian_fee,
            2,".","");

        if(bccomp($daozhang_money,"0.00",2) <= 0){
			return ["status"=>"error",
                "msg"=>"系统繁忙，请稍后再试"];
		}

		Db()->begin();
		try{
			//
			$sql = "UPDATE `user` SET `money`=`money`-{$money} WHERE uid={$uid}";
			$res = Db()->_exec($sql);
			if(empty($res)){
				throw new \Exception("扣款失败");
			}
			//
			$beizhu = "{$daozhang_money} = {$money} - {$money} * {$config['tixian_fee']}%";
			//
			$data = [
				"uid"=>$uid,
				"money"=>$money,
				"daozhang_money"=>$daozhang_money,
				"bank_id"=>$bank_id,
				"beizhu"=>$beizhu,
			];
			$res = Db()->table("user_tixian")->insert($data);
			if(empty($res)){
				throw new \Exception("插入提现订单失败");
			}
			$tixian_id = Db()->insertId();
			//
			$shengyu = Db()->table("user")->where("uid={$uid}")->getColumn("money");
			$zhangdan = [
				"uid"=>$uid,
				"money"=>$money,
				"type"=>6,
				"shengyu"=>$shengyu,
				"msg"=>"提现编号:{$tixian_id},{$beizhu}",
			];
			Db()->table("user_zhangdan")->insert($zhangdan);
			//
			$this->user_log($uid,"申请提现【{$money} - {$tixian_id}】");
			//
			Db()->commit();
			return ["status"=>"ok",
                "msg"=>"操作成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error",
                "msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 获取提现记录
	* @method get_tixian_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param status string 	状态:0=未处理,1=已通过,2=已拒绝
	* @return {"status":"ok","data":[{"id":1,"uid":2,"money":"10.00","daozhang_money":"9.49","time":"2023-11-17 16:59:30","status":0,"bank_info":{"bank_name":"工商银行","bank_num":"*************","username":"张三"}}],"count":1}
	*/
	public function get_tixian_list($uid,$token,$page=1,$page_size=20,$status="ALL"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		if(!empty($status) && check($status,"integt0")){
			$status = (int)$status;
			$where .= " AND `status`={$status}";
		}
		$data = Db()->table("user_tixian")->select("id,uid,money,daozhang_money,bank_id,time,status")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			$row['bank_info'] = Db()->table("user_bank")->select("bank_name,bank_num,username")->where("id={$row['bank_id']}")->fetch();
			unset($row['bank_id']);
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}
	/*
	* @apiName 获取佣金
	* @method get_yongjin_log
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型:1=会员佣金,2=活动佣金,3=商品佣金,4=运营佣金
	* @param commission_type string 佣金类型:invite=邀请佣金,operation=运营佣金,ALL=全部
	* @return {"status":"ok","data":[{"id":35,"money":"3.79","type":3,"order_id":"20240125170318698022","time":"2024-01-25 17:06:41","commission_type":"invite","user":{"uid":25,"avatar":"https:\/\/api.huodongbaobao.com\/upload\/api\/2024_01_22\/8121d7565119faf04f601c925a0f7387.jpg","nickname":"null"}}],"count":10}
	*/
	public function get_yongjin_log($uid,$token,$page=1,$page_size=20,$type="ALL",$commission_type="ALL"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		if(!empty($type) && check($type,"integt0")){
			$type = (int)$type;
			$where .= " AND `type`={$type}";
		}
		if(!empty($commission_type) && $commission_type != "ALL"){
			if(in_array($commission_type, ["invite", "operation"])){
				$where .= " AND `commission_type`='{$commission_type}'";
			}
		}
		$data = Db()->table("user_yongjin_log")->select("id,money,type,order_id,time,commission_type,branch_id")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			$row['user'] = new \stdClass();
			// 设置默认佣金类型（兼容旧数据）
			if(empty($row['commission_type'])){
				$row['commission_type'] = 'invite';
			}

			if(!empty($row['order_id'])){
				$table_name = "";
				if($row['type'] == 1){//会员佣金
					$table_name = "user_huiyuan_order";
				}else if($row['type'] == 2){//活动佣金
					$table_name = "huodong_baoming_order";
				}else if($row['type'] == 3){//商品佣金
					$table_name = "goods_order";
				}else if($row['type'] == 4){//运营佣金
					// 运营佣金不需要查询关联用户，直接跳过
					continue;
				}

				if(!empty($table_name)){
					$uid = Db()->table($table_name)->where("order_id=:order_id")->prepareParam([":order_id"=>$row['order_id']])->getColumn("uid",0);
					if(!empty($uid)){
						$user = Db()->table("user")->select("uid,avatar,nickname")->where("uid={$uid}")->fetch();
						if($user){
							$row['user'] = $user;
						}
					}
				}
			}
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}

	/*
	* @apiName 获取充值记录
	* @method get_chongzhi_log
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param status string 状态:0=未支付,1=已支付,2=已取消
	* @return {"status":"ok","data":[{"id":1,"order_id":"24324324","money":"10.00","time":"2023-11-17 17:04:36","pay_time":null,"status":0}],"count":1}
	*/
	public function get_chongzhi_log($uid,$token,$page=1,$page_size=20,$status="ALL"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		if(check($status,"integt0")){
			$status = (int)$status;
			$where .= " AND `status`={$status}";
		}
		$data = Db()->table("user_chongzhi_order")->select("id,order_id,money,time,pay_time,status")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}

	/*
	* @apiName 获取分享记录
	* @method get_fenxiang_log
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型:1=其他,2=活动,2=商品
	* @return {"status":"ok","data":[{"id":3,"type":3,"item_id":1,"time":"2023-11-17 17:06:31","item_info":{"id":1,"name":"商品1","price":"10.00","img_url":"http:\/\/127.0.0.1\/test.jpg"}},{"id":2,"type":2,"item_id":1,"time":"2023-11-17 17:06:31","item_info":{"id":1,"name":"活动1","title":"活动标题","img_url":"http:\/\/127.0.0.1\/test.jpg","date":"2023-11-30","start_time":"08:00:00","end_time":"10:00:00","baoming_start_time":"2023-11-17 17:11:18","baoming_end_time":"2023-11-17 17:11:18","num":10}},{"id":1,"type":1,"item_id":1,"time":"2023-11-17 17:06:20","item_info":{}}],"count":3}
	*/
	public function get_fenxiang_log($uid,$token,$page=1,$page_size=20,$type="ALL"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		if(check($type,"integt0")){
			$type = (int)$type;
			$where .= " AND `type`={$type}";
		}
		$data = Db()->table("user_fenxiang_log")->select("id,type,item_id,time")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			$item_info = new \stdClass();
			if(!empty($row['item_id'])){
				if($row['type'] == 2){
					$item_info = Db()->table("huodong")->select("id,name,title,img_url,date,start_time,end_time,baoming_start_time,baoming_end_time,num")->where("id={$row['item_id']}")->fetch();
				}else if($row['type'] == 3){
					$item_info = Db()->table("goods")->select("id,name,price,img_url")->where("id={$row['item_id']}")->fetch();
				}
				$item_info = $item_info ?: new \stdClass();
			}
			$row['item_info'] = $item_info;
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}
	/*
	* @apiName 分享事件上传
	* @method fenxiang_event
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param type string 类型:1=会员,2=活动,3=商品
	* @param item_id string 类型内容编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function fenxiang_event($uid,$token,$type,$item_id=0){
		if(
			empty($token) ||
			!check($uid,"intgt0") ||
			!in_array($type,[1,2,3]) ||
			!check($item_id,"integt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$type = (int)$type;
		$item_id = (int)$item_id;
		$data = [
			"uid"=>$uid,
			"type"=>$type,
			"item_id"=>$item_id,
		];
		$prepareParam = [
		];
		dbConn();
		try{
			$sql = "INSERT INTO `user_fenxiang_log` (uid,type,item_id) VALUES ({$uid},{$type},{$item_id}) ";
			$sql .= " ON DUPLICATE KEY UPDATE `time`='".date("Y-m-d H:i:s")."'";
			$res = Db::_exec($sql);
			$this->user_log($uid,"分享:{$type}");
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取对账单
	* @method get_zhangdan
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型:1=充值,2=活动费用结算,3=活动佣金结算,4=商品佣金结算,5=会员佣金结算,6=提现申请,7=提现驳回,8=活动费用支付,9=活动费用退还,10=抽奖中奖,11=商品支付,12=会员支付,13=商品退款,14=大礼包支付,15=大礼包退款
	* @return {"status":"ok","data":[{"id":1,"money":"10.00","shengyu":"9990.00","type":6,"time":"2023-11-17 16:59:30"}],"count":1}
	*/
	public function get_zhangdan($uid,$token,$page=1,$page_size=20,$type="ALL"){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		if(!empty($type) && check($type,"integt0")){
			$type = (int)$type;
			$where .= " AND `type`={$type}";
		}
		$data = Db()->table("user_zhangdan")->select("id,money,shengyu,type,time")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}
	/*
	* @apiName 充值下单
	* @method add_chongzhi_order
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param money string 金额
	* @return {"status":"ok","msg":"操作成功","order_id":"20231117171914966171","money":"10.00"}
	*/
	public function add_chongzhi_order($uid,$token,$money){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			!is_numeric($money) ||
			$money <= 0
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$money = number_format($money,2,".","");
		dbConn();
		$min_chongzhi_money = $this->get_config("min_chongzhi_money");
		if(empty($min_chongzhi_money)){
			return ["status"=>"error","msg"=>"获取系统配置失败"];
		}
		if(bccomp($min_chongzhi_money,$money,2) > 0){
			return ["status"=>"error","msg"=>"最低充值金额：{$min_chongzhi_money}"];
		}
		$order_id = date("YmdHis").makeCode(6);
		$data = [
			"order_id"=>$order_id,
			"uid"=>$uid,
			"money"=>$money,
		];
		$prepareParam = [];
		dbConn();
		try{
			//
			Db()->table("user_chongzhi_order")->prepareParam($prepareParam)->insert($data);
			$insert_id = Db()->insertId();
			//
			$this->user_log($uid,"创建充值订单【{$insert_id}】");
			//
			return ["status"=>"ok","msg"=>"操作成功","order_id"=>$order_id,"money"=>$money];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 购买会员下单
	* @method add_huiyuan_order
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","msg":"操作成功","order_id":"20231117172120001855","money":"500"}
	*/
	public function add_huiyuan_order($uid,$token){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		dbConn();
		$huiyuan_price = $this->get_config("huiyuan_price");
		if(empty($huiyuan_price)){
			return ["status"=>"error","msg"=>"获取系统配置失败"];
		}
		$order_id = date("YmdHis").makeCode(6);
		$data = [
			"order_id"=>$order_id,
			"uid"=>$uid,
			"money"=>$huiyuan_price,
		];
		//佣金计算
		$p_uid = Db()->table("user")->where("uid={$uid}")->getColumn("p_uid");
		if(!empty($p_uid)){
			// 检查是否为首次成为会员（在创建订单时预先检查）
			$is_first_time = $this->is_first_time_member($uid);
			$this->exception_log("add_huiyuan_order: uid={$uid}, 邀请人={$p_uid}, 是否首次成为会员={$is_first_time}");

			// 获取推荐人的邀请佣金配置
			$commission_config = $this->get_commission_config($p_uid, 'invite');
			if ($commission_config === false) {
				return ["status"=>"error","msg"=>"获取佣金配置失败"];
			}

			// 计算佣金金额
			$commission_amount = 0;
			if ($commission_config['calculation_method'] === 'fixed') {
				$commission_amount = $commission_config['base_amount'];
			} else if ($commission_config['calculation_method'] === 'percentage') {
				$commission_amount = $huiyuan_price * $commission_config['percentage_rate'] / 100;

				// 应用最小值和最大值限制
				if ($commission_config['min_amount'] > 0) {
					$commission_amount = max($commission_amount, $commission_config['min_amount']);
				}
				if ($commission_config['max_amount'] > 0) {
					$commission_amount = min($commission_amount, $commission_config['max_amount']);
				}
			}

			$commission_amount = number_format($commission_amount, 2, ".", "");

			// 获取结算延迟天数
			$jiesuan_days = $commission_config['available_delay_days'] ?? 7;

			// 无论是否首次成为会员，都在订单中记录佣金信息，但在结算时才判断是否实际发放
			$data['yongjin_money_1'] = $commission_amount;
			$data['yongjin_uid_1'] = $p_uid;
			$data['jiesuan_status'] = 0;
			$data['jiesuan_time'] = date("Y-m-d H:i:s",strtotime("+{$jiesuan_days} days"));

			$this->exception_log("add_huiyuan_order: 订单佣金信息设置完成 uid={$uid}, 佣金={$commission_amount}, 邀请人={$p_uid}");
		}
		//
		$prepareParam = [];
		dbConn();
		try{
			//
			Db()->table("user_huiyuan_order")->prepareParam($prepareParam)->insert($data);
			$insert_id = Db()->insertId();
			//
			$this->user_log($uid,"创建购买会员订单【{$order_id}】");
			//
			return ["status"=>"ok","msg"=>"操作成功","order_id"=>$order_id,"money"=>$huiyuan_price];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 添加关注
	* @method guanzhu_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param to_uid string 被关注用户编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function guanzhu_add($uid,$token,$to_uid){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			!check($to_uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$to_uid = (int)$to_uid;
		if($to_uid == $uid){
			return ["status"=>"error","msg"=>"不能自己关注自己"];
		}
		dbConn();

		$to_user = Db()->table("user")->select("uid")->where("uid={$to_uid}")->fetch();
		if(empty($to_user)){
			return ["status"=>"error","msg"=>"被关注用户不存在"];
		}

		$check = Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$to_uid}")->fetch();
		if(!empty($check)){
			return ["status"=>"ok","msg"=>"操作成功"];
		}

		$data = [
			"uid"=>$uid,
			"to_uid"=>$to_uid,
		];
		$prepareParam = [

		];
		dbConn();

		try{
			Db()->table("user_guanzhu")->prepareParam($prepareParam)->insert($data);
			//
			$this->user_log($uid,"添加关注【{$to_uid}】");
			//
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 取消关注
	* @method guanzhu_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param to_uid string 被关注用户编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
	public function guanzhu_del($uid,$token,$to_uid){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			!check($to_uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		$uid = (int)$uid;
		$to_uid = (int)$to_uid;
		dbConn();
		$check = Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$to_uid}")->fetch();
		if(empty($check)){
			return ["status"=>"ok","msg"=>"操作成功"];
		}
		try{
			Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$to_uid}")->del();
			//
			$this->user_log($uid,"取消关注【{$to_uid}】");
			//
			return ["status"=>"ok","msg"=>"操作成功"];
		}catch(\Exception $e){
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}
	/*
	* @apiName 检测关注
	* @method guanzhu_check
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param to_uid string 被关注用户编号
	* @return {"status":"ok","data":1}
	*/
	public function guanzhu_check($uid,$token,$to_uid){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			!check($to_uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$to_uid = (int)$to_uid;
		dbConn();
		$check = Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$to_uid}")->fetch();
		if(!empty($check)){
			return ["status"=>"ok","data"=>1];
		}else{
			return ["status"=>"ok","data"=>0];
		}
	}
	/*
	* @apiName 获取关注列表
	* @method get_guanzhu_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":3,"uid":2,"to_uid":1,"time":"2023-11-17 17:23:29","to_user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","mobile":"15800000000","is_huiyuan":0}}],"count":1}
	*/
	public function get_guanzhu_list($uid,$token,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "uid={$uid}";
		$data = Db()->table("user_guanzhu")->select("id,uid,to_uid,time")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			$to_user = Db()->table("user")->select("uid,nickname,avatar,mobile,is_huiyuan,gexingqianming")->where("uid={$row['to_uid']} AND is_dongjie=0")->fetch();
			$row['to_user'] = $to_user ?: new \stdClass();
			//
			$row['huodong_faqi'] = Db()->table("huodong")->where("uid={$row['to_uid']} AND status=1")->count();
			$row['huodong_canyu'] = Db()->table("huodong_baoming_order")->where("uid={$row['to_uid']} AND status=1")->count();
			//
			$imgs_url = Db()->table("user_img")->where("uid={$row['to_uid']}")->fetchAll();
			$row['imgs_url'] = $imgs_url ?: [];
			//
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}
	/*
	* @apiName 获取社交用户列表
	* @method get_shejiao_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","sex":2,"birthday":"1960-10-10","gexingqianming":"个性签名","is_huiyuan":0,"imgs":["http:\/\/127.0.0.1\/test.jpg","http:\/\/127.0.0.1\/test.jpg"],"is_guanzhu":0}],"count":1}
	*/
	public function get_shejiao_list($uid,$token,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "img_num>0 AND is_dongjie=0 AND uid!={$uid}";
		$order = "is_huiyuan DESC,open_times DESC,uid DESC";
		$data = Db()->table("user")->select("uid,avatar,nickname,sex,mobile,birthday,gexingqianming,is_huiyuan")->where($where)->order($order)->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			//
			$is_guanzhu = Db()->table("user_guanzhu")->select("id")->where("uid={$uid} AND to_uid={$row['uid']}")->fetch();
			$row['is_guanzhu'] = $is_guanzhu ? 1 : 0;
			//
			$row['huodong_faqi'] = Db()->table("huodong")->where("uid={$row['uid']} AND status=1")->count();
			$row['huodong_canyu'] = Db()->table("huodong_baoming_order")->where("uid={$row['uid']} AND status=1")->count();
			//
			$imgs_url = Db()->table("user_img")->where("uid={$row['uid']}")->fetchAll();
			$row['imgs_url'] = $imgs_url ?: [];
			//
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}
	/*
	* @apiName 获取他人信息资料
	* @method get_other_user_info
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param to_uid string 他人用户编号
	* @param include_details string 是否包含详细活动和动态数据，默认0
	* @return {"status":"ok","data":{"uid":2,"mobile":"15800000000","avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","sex":2,"birthday":"1960-10-10","gexingqianming":"个性签名","is_huiyuan":0,"reg_time":"2023-11-17 16:23:53","labels":[{"id":1,"label":"90后"},{"id":2,"label":"音乐"}],"imgs":["http:\/\/127.0.0.1\/test.jpg","http:\/\/127.0.0.1\/test.jpg"],"created_activities":[],"joined_activities":[],"published_feeds":[]}}
	*/
	public function get_other_user_info($uid,$token,$to_uid,$include_details=0){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($to_uid) ||
			!check($to_uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		dbConn();
		$uid = (int)$uid;
		$to_uid = (int)$to_uid;
		$include_details = (int)$include_details;

		try {
			$user_info = Db()->table("user")->select("uid,mobile,avatar,nickname,sex,birthday,gexingqianming,is_huiyuan,reg_time")->where("uid={$to_uid} AND is_dongjie=0")->fetch();
			if(empty($user_info)){
				return ["status"=>"empty","msg"=>"资源未找到"];
			}
			$labels = Db()->table("user_label")->select("id,label")->where("uid={$user_info['uid']}")->fetchAll();
			$user_info['labels'] = $labels ?: [];
			$imgs = Db()->table("user_img")->select("id,img_url")->where("uid={$to_uid}")->fetchAll();
			$user_info['imgs'] = $imgs ? array_column($imgs,"img_url") : [];
			$user_info['huodong_faqi'] = Db()->table("huodong")->where("uid={$to_uid} AND status=1")->count();
			$user_info['huodong_canyu'] = Db()->table("huodong_baoming_order")->where("uid={$to_uid} AND status=1")->count();

			// 如果需要详细数据
			if($include_details == 1){
				// 获取用户发起的活动列表（最近10条）
				// 修复：明确指定WHERE条件为该用户ID，并确保活动状态有效
				$sql = "SELECT id,name,img_url,start_time,end_time,money,member_money,is_online,addr,status 
					FROM `huodong` 
					WHERE uid={$to_uid} AND status=1 
					ORDER BY id DESC LIMIT 10";
				$created_activities = Db::_fetchAll($sql);
				$user_info['created_activities'] = $created_activities ?: [];

				// 获取用户报名的活动列表（最近10条）
				// 修复：明确指定JOIN条件和WHERE条件，确保数据一致性
				$sql = "SELECT ho.huodong_id, ho.pay_time, h.name, h.img_url, h.start_time, h.end_time, h.money, h.member_money, h.is_online, h.addr, h.status 
					FROM `huodong_baoming_order` ho 
					LEFT JOIN `huodong` h ON ho.huodong_id = h.id 
					WHERE ho.uid={$to_uid} AND ho.status=1 AND h.status=1 
					ORDER BY ho.id DESC LIMIT 10";
				$joined_activities = Db::_fetchAll($sql);
				$user_info['joined_activities'] = $joined_activities ?: [];

				// 获取用户发布的动态列表（最近10条）
				// 修复：明确指定WHERE条件为该用户ID
				$sql = "SELECT id,content,images_json,location,tags,like_count,comment_count,created_at 
					FROM `feeds` 
					WHERE user_id={$to_uid} AND status='published' 
					ORDER BY id DESC LIMIT 10";
				$published_feeds = Db::_fetchAll($sql);

				// 处理动态数据
				if($published_feeds){
					foreach($published_feeds as &$feed){
						$feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
						if(json_last_error() !== JSON_ERROR_NONE) $feed['images'] = [];
						unset($feed['images_json']);
					}
				}
				$user_info['published_feeds'] = $published_feeds ?: [];
			} else {
				// 不包含详细数据时，设置为空数组
				$user_info['created_activities'] = [];
				$user_info['joined_activities'] = [];
				$user_info['published_feeds'] = [];
			}

			Db()->table("user_access")->insert(["uid"=>$uid,"to_uid"=>$to_uid]);
			$sql = "UPDATE `user` SET open_times=open_times+1 WHERE uid={$to_uid}";
			Db::_exec($sql);
			
			return ["status"=>"ok","data"=>$user_info];
		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"获取用户信息失败"];
		}
	}

	/*
	* @apiName 获取账户待结算状态
	* @method get_daijiesuan_status
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","data":{"yue":"8068.00","huodong_daijiesuan":"492.00","huiyuan_yongjin_daijiesuan":"0.00","huodong_yongjin_daijiesuan":"0.00","goods_yongjin_daijiesuan":"0.00"}}
	*/
	public function get_daijiesuan_status($uid,$token){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		dbConn();
		$uid = (int)$uid;
		//可提现金额（所有status=1的佣金总和）
		$yue_sql = "SELECT SUM(money) as total_available FROM user_yongjin_log WHERE uid={$uid} AND status=1";
		$yue_res = Db::_fetch($yue_sql);
		$yue = $yue_res && isset($yue_res['total_available']) ? $yue_res['total_available'] : 0;
		$yue = number_format($yue, 2, ".", "");
	
		//会员佣金待结算
		$sql = "SELECT SUM(`yongjin_money_1`) AS user_huiyuan_order_yongjin_money_1 FROM `user_huiyuan_order` WHERE yongjin_uid_1={$uid} AND jiesuan_status=0 AND status=1";
		$res = Db::_fetch($sql);
		$user_huiyuan_order_yongjin_money_1 = $res && isset($res['user_huiyuan_order_yongjin_money_1']) ? $res['user_huiyuan_order_yongjin_money_1'] : 0;

		$sql = "SELECT SUM(`yongjin_money_2`) AS user_huiyuan_order_yongjin_money_2 FROM `user_huiyuan_order` WHERE yongjin_uid_2={$uid} AND jiesuan_status=0 AND status=1";
		$res = Db::_fetch($sql);
		$user_huiyuan_order_yongjin_money_2 = $res && isset($res['user_huiyuan_order_yongjin_money_2']) ? $res['user_huiyuan_order_yongjin_money_2'] : 0;

		$huiyuan_yongjin_daijiesuan = number_format($user_huiyuan_order_yongjin_money_1 + $user_huiyuan_order_yongjin_money_2 ,2,".","");

		//邀请佣金待结算（按用户要求的SQL）
		$invite_yongjin_sql = "SELECT SUM(money) as invite_pending FROM user_yongjin_log WHERE uid={$uid} AND commission_type='invite' AND status=0";
		$invite_res = Db::_fetch($invite_yongjin_sql);
		$invite_yongjin_daijiesuan = $invite_res && isset($invite_res['invite_pending']) ? $invite_res['invite_pending'] : 0;
		$invite_yongjin_daijiesuan = number_format($invite_yongjin_daijiesuan, 2, ".", "");

		//运营佣金待结算（按用户要求的SQL）
		$operation_yongjin_sql = "SELECT SUM(money) as operation_pending FROM user_yongjin_log WHERE uid={$uid} AND commission_type='operation' AND status=0";
		$operation_res = Db::_fetch($operation_yongjin_sql);
		$operation_yongjin_daijiesuan = $operation_res && isset($operation_res['operation_pending']) ? $operation_res['operation_pending'] : 0;
		$operation_yongjin_daijiesuan = number_format($operation_yongjin_daijiesuan, 2, ".", "");
		
		$data = [
			"yue" => $yue, // 可提现金额（所有status=1的佣金总和）
			"invite_yongjin_daijiesuan" => $invite_yongjin_daijiesuan, // 邀请佣金待结算
			"operation_yongjin_daijiesuan" => $operation_yongjin_daijiesuan // 运营佣金待结算
		];
		return ["status"=>"ok","data"=>$data];
	}

	/*
	* @apiName 获取下级用户
	* @method get_xiaji_user
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param to_uid string 查看谁的下级，可以是自己或者一级下级编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","sex":2,"is_huiyuan":0}]}
	*/
	public function get_xiaji_user($uid,$token,$to_uid,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($to_uid) ||
			!check($to_uid,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		dbConn();
		$page = (int)$page;
		$page_size = (int)$page_size;
		$uid = (int)$uid;
		$to_uid = (int)$to_uid;
		if($to_uid !== $uid){
			$p_uid = Db()->table("user")->where("uid={$to_uid}")->getColumn("p_uid");
			if(empty($p_uid) || $p_uid != $uid){
				return ["status"=>"error","msg"=>"权限受限"];
			}
		}
		$data = Db()->table("user")->select("uid,avatar,nickname,sex,is_huiyuan")->where("p_uid={$to_uid}")->order("uid DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		//
		return ["status"=>"ok","data"=>$data];
	}



	/*
	* 检查用户是否首次成为会员
	* @param int $uid 用户ID
	* @return bool true=首次成为会员, false=续费会员
	*/
	private function is_first_time_member($uid) {
		try {
			// 检查user_huiyuan_order表中是否有历史支付成功的会员订单
			$history_orders = Db()->table("user_huiyuan_order")
				->where("uid={$uid} AND status=1")
				->count();

			$this->exception_log("is_first_time_member: uid={$uid}, 历史会员订单数={$history_orders}");

			// 如果历史订单数量大于1，说明不是首次成为会员
			return $history_orders <= 1;

		} catch (\Exception $e) {
			$this->exception_log("is_first_time_member错误: " . $e->getMessage());
			// 出错时默认认为是首次成为会员，避免影响正常业务
			return true;
		}
	}

	/*
	* 插入会员佣金记录
	*/
	/**
	 * Settles member commission for an order, including invitation and operation commissions.
	 * 
	 * This private method handles the commission settlement process for a member order. It:
	 * - Updates the order's settlement status
	 * - Checks if invitation commission should be given (only for first-time members)
	 * - Logs commission-related operations
	 * - Handles database transactions with rollback on failure
	 * 
	 * @param array $order_info Order information containing commission details
	 * @throws \Exception If commission record insertion fails or other errors occur
	 */
	private function settle_member_commission($order_info){
		$yongjin_uid_1 = $order_info['yongjin_uid_1'];

		Db::begin();
		try{
			$sql = "UPDATE `user_huiyuan_order` SET `jiesuan_status`=1 WHERE id={$order_info['id']} AND jiesuan_status=0";
			$rowCount = Db::_exec($sql);

			// 检查是否需要发放邀请佣金
			$should_give_invite_commission = false;
			$invite_commission_amount = 0;

			if(!empty($rowCount) && $yongjin_uid_1 > 0){
				// 检查是否为首次成为会员
				$is_first_time = $this->is_first_time_member($order_info['uid']);
				$this->exception_log("settle_member_commission: uid={$order_info['uid']}, 订单ID={$order_info['order_id']}, 是否首次成为会员={$is_first_time}");

				if($is_first_time) {
					// 获取邀请佣金配置
					$invite_commission_config = $this->get_commission_config($yongjin_uid_1, 'invite');
					if ($invite_commission_config !== false) {
						// 动态计算邀请佣金金额
						$invite_commission_amount = $this->calculate_commission_amount($invite_commission_config, $order_info);

						if ($invite_commission_amount > 0) {
							$should_give_invite_commission = true;
							$this->exception_log("settle_member_commission: 首次成为会员，发放邀请佣金 uid={$order_info['uid']}, 邀请人={$yongjin_uid_1}, 佣金={$invite_commission_amount}");
						} else {
							$this->exception_log("settle_member_commission: 邀请佣金计算结果为0，跳过发放 uid={$order_info['uid']}, 邀请人={$yongjin_uid_1}");
						}
					} else {
						$this->exception_log("settle_member_commission: 邀请佣金配置获取失败，跳过发放 uid={$order_info['uid']}, 邀请人={$yongjin_uid_1}");
					}
				} else {
					$this->exception_log("settle_member_commission: 续费会员，不发放邀请佣金 uid={$order_info['uid']}, 邀请人={$yongjin_uid_1}");
				}
			}

			if($should_give_invite_commission){
				// 插入邀请佣金记录到user_yongjin_log表（使用新的佣金状态管理）
				$invite_yongjin_log = [
					"uid" => $yongjin_uid_1,
					"type" => 1, // 邀请佣金类型
					"money" => $invite_commission_amount,
					"order_id" => $order_info['order_id'],
					"commission_type" => "invite",
					"status" => 0, // 待结算状态
					"time" => DATETIME, // 添加时间字段
					"remark" => "邀请佣金，关联会员订单：{$order_info['order_id']}"
				];

				$invite_yongjin_id = Db()->table("user_yongjin_log")->insert($invite_yongjin_log);

				if (!$invite_yongjin_id) {
					throw new \Exception("邀请佣金记录插入失败");
				}

				$this->exception_log("settle_member_commission: 邀请佣金记录插入成功，ID={$invite_yongjin_id}，金额={$invite_commission_amount}");
			}

			// 🆕 修改：运营佣金分配逻辑增强
			// 1. 如果有邀请人且是分会长，给邀请人分配运营佣金
			if($yongjin_uid_1 > 0) {
				$this->exception_log("settle_member_commission: 检查运营佣金 uid={$order_info['uid']}, 邀请人={$yongjin_uid_1}, 订单={$order_info['order_id']}");
				$this->insert_operation_commission_for_president($yongjin_uid_1, $order_info['order_id']);
			}

			// 2. 🆕 新增：如果是系统分配的用户，给其所属分会的分会长分配运营佣金
			$this->handle_system_assigned_user_operation_commission($order_info);

			Db::commit();
		}catch(\Exception $e){
			Db::rollback();
			throw $e;
		}
	}

	/**
	 * 🆕 新增：处理系统分配用户的运营佣金
	 * @param array $order_info 会员订单信息
	 */
	private function handle_system_assigned_user_operation_commission($order_info) {
		try {
			// 获取用户信息
			$user_info = Db()->table("user")
				->select("id, branch_id, assignment_type")
				->where("id={$order_info['uid']}")
				->fetch();

			if (!$user_info) {
				$this->exception_log("系统分配用户运营佣金：用户不存在，订单：{$order_info['order_id']}");
				return;
			}

			// 只处理系统分配的用户
			if ($user_info['assignment_type'] !== 'system') {
				return; // 非系统分配用户，不处理运营佣金
			}

			// 检查用户是否有分会
			if (empty($user_info['branch_id'])) {
				$this->exception_log("系统分配用户运营佣金：用户无分会信息，用户ID：{$order_info['uid']}");
				return;
			}

			// 获取分会的分会长
			$branch_president = Db()->table("user")
				->select("id")
				->where("role_type=1 AND branch_id={$user_info['branch_id']}")
				->fetch();

			if (!$branch_president) {
				$this->exception_log("系统分配用户运营佣金：找不到分会长，分会ID：{$user_info['branch_id']}");
				return;
			}

			// 调用现有的运营佣金分配方法，但使用特殊标记
			$this->insert_operation_commission_for_president_with_source($branch_president['id'], $order_info['order_id'], 'system_assigned');

		} catch (\Exception $e) {
			$this->exception_log("系统分配用户运营佣金异常：{$e->getMessage()}，订单：{$order_info['order_id']}");
		}
	}

	/**
	 * 🆕 新增：为分会长插入运营佣金记录（带来源标记）
	 * @param int $uid 分会长ID
	 * @param string $order_id 订单ID
	 * @param string $source 来源标记：'normal'=正常邀请，'system_assigned'=系统分配
	 */
	private function insert_operation_commission_for_president_with_source($uid, $order_id, $source = 'normal') {
		// 🟡 修复：增强分会长身份验证
		$sql = "SELECT u.role_type, u.branch_id, u.nickname, u.is_dongjie, b.status as branch_status
				FROM user u
				LEFT JOIN branch b ON u.branch_id = b.id
				WHERE u.uid = {$uid}";
		$user = Db::_fetch($sql);

		if (empty($user) || $user['role_type'] != 1) {
			return; // 不是分会长，跳过
		}

		// 检查分会长是否被冻结
		if ($user['is_dongjie'] == 1) {
			$this->user_log($uid, "分会长账户已被冻结，无法发放运营佣金。订单：{$order_id}");
			return; // 分会长被冻结，跳过
		}

		// 检查分会状态是否有效
		if (!empty($user['branch_status']) && $user['branch_status'] != 1) {
			$this->user_log($uid, "分会状态无效，无法发放运营佣金。订单：{$order_id}，分会状态：{$user['branch_status']}");
			return; // 分会状态无效，跳过
		}

		// 🔴 修复：添加重复检查机制（基于原始会员订单ID精确匹配）
		$existing_record = Db()->table("user_yongjin_log")
			->select("id")
			->where("uid={$uid} AND commission_type='operation' AND remark LIKE '%{$order_id}%'")
			->fetch();

		if (!empty($existing_record)) {
			$this->user_log($uid, "运营佣金记录已存在，跳过重复插入。订单：{$order_id}");
			return; // 已存在相同记录，跳过
		}

		// 生成运营佣金订单号
		$operation_order_id = "OPERATION_" . date('Y-m') . "_" . $uid . "_" . time();

		// 获取运营佣金配置
		$commission_config = $this->get_commission_config($uid, 'operation');
		if ($commission_config === false) {
			$this->user_log($uid, "运营佣金配置获取失败，无法发放佣金。订单：{$order_id}");
			return; // 没有配置，跳过
		}

		// 🟡 修复：使用统一的佣金计算方法
		$order_info_for_calculation = [
			'order_id' => $order_id,
			'money' => null // 运营佣金计算时会从数据库获取实际金额
		];

		$commission = $this->calculate_commission_amount($commission_config, $order_info_for_calculation);

		if ($commission <= 0) {
			$this->user_log($uid, "运营佣金计算结果为0或无效，跳过发放。订单：{$order_id}");
			return;
		}

		// 生成运营佣金订单号
		$operation_order_id = "OPERATION_" . date('Y-m') . "_" . $uid . "_" . time();

		// 🔴 修复：添加完整的数据库事务处理
		try {
			Db::begin();

			// 🆕 修改：根据来源设置不同的备注
			$remark = $source === 'system_assigned'
				? "系统分配用户运营佣金，关联会员订单：{$order_id}"
				: "分会长运营佣金，关联会员订单：{$order_id}";

			// 插入运营佣金记录
			$yongjin_log = [
				"uid" => $uid,
				"type" => 4, // 运营佣金类型
				"money" => $commission,
				"order_id" => $operation_order_id,
				"commission_type" => "operation",
				"branch_id" => $user['branch_id'],
				"status" => 0, // 待结算状态
				"time" => DATETIME, // 添加时间字段
				"remark" => $remark
			];

			$yongjin_id = Db()->table("user_yongjin_log")->insert($yongjin_log);

			if (!$yongjin_id) {
				throw new \Exception("佣金记录插入失败");
			}
			Db::commit();
			// 记录成功日志
			$this->user_log($uid, "获得运营佣金 {$commission} 元，关联会员订单：{$order_id}，佣金记录ID：{$yongjin_id}");

		} catch (\Exception $e) {
			Db::rollback();
			$this->exception_log("运营佣金插入失败：用户ID={$uid}, 订单={$order_id}, 错误=" . $e->getMessage());
			$this->user_log($uid, "运营佣金发放失败：{$e->getMessage()}，关联会员订单：{$order_id}");
		}
	}

	/*
	 * 为分会长插入运营佣金记录（原方法保持兼容性）
	 */
	private function insert_operation_commission_for_president($uid, $order_id) {
		// 调用新的带来源标记的方法，默认为正常邀请
		$this->insert_operation_commission_for_president_with_source($uid, $order_id, 'normal');
	}

	/**
	 * 🔧 P1修复：增强版佣金金额计算方法
	 * @param array $commission_config 佣金配置
	 * @param array $order_info 订单信息
	 * @return float 计算出的佣金金额
	 */
	private function calculate_commission_amount($commission_config, $order_info) {
		$commission = 0;
		$order_id = $order_info['order_id'] ?? 'unknown';

		// 验证配置字段的完整性
		if (empty($commission_config['calculation_method'])) {
			$this->exception_log("佣金配置缺少计算方法，订单：{$order_id}");
			return 0;
		}

		// 记录佣金计算开始
		$this->log_commission_calculation($order_id, $commission_config, $order_info, 'start');

		try {
			if ($commission_config['calculation_method'] === 'fixed') {
				// 固定金额计算
				if (empty($commission_config['base_amount']) || !is_numeric($commission_config['base_amount'])) {
					throw new \Exception("固定佣金配置缺少有效金额");
				}
				$commission = (float)$commission_config['base_amount'];

			} else if ($commission_config['calculation_method'] === 'percentage') {
				// 百分比计算
				if (empty($commission_config['percentage_rate']) || !is_numeric($commission_config['percentage_rate'])) {
					throw new \Exception("百分比佣金配置缺少有效比例");
				}

				// 获取订单金额作为计算基数
				$base_amount = $this->get_order_base_amount($order_info);
				if ($base_amount <= 0) {
					throw new \Exception("无法获取有效的订单金额作为计算基数");
				}

				$commission = $base_amount * (float)$commission_config['percentage_rate'] / 100;

				// 应用最小值和最大值限制
				$commission = $this->apply_commission_limits($commission, $commission_config);

			} else {
				throw new \Exception("不支持的佣金计算方式：{$commission_config['calculation_method']}");
			}

			// 确保佣金金额为正数
			$commission = max(0, $commission);

			// 记录佣金计算成功
			$this->log_commission_calculation($order_id, $commission_config, $order_info, 'success', $commission);

			return $commission;

		} catch (\Exception $e) {
			$error_msg = $e->getMessage();
			$this->exception_log("佣金计算失败，订单：{$order_id}，错误：{$error_msg}");

			// 记录佣金计算失败
			$this->log_commission_calculation($order_id, $commission_config, $order_info, 'error', 0, $error_msg);

			return 0;
		}
	}

	/**
	 * 🔧 P1修复：获取订单基础金额
	 * @param array $order_info 订单信息
	 * @return float 基础金额
	 */
	private function get_order_base_amount($order_info) {
		// 优先使用订单信息中的金额
		if (!empty($order_info['money']) && is_numeric($order_info['money'])) {
			return (float)$order_info['money'];
		}

		// 如果订单信息中没有金额，尝试从数据库获取
		if (!empty($order_info['order_id'])) {
			$actual_order_amount = $this->get_member_order_amount($order_info['order_id']);
			if ($actual_order_amount > 0) {
				return $actual_order_amount;
			}
		}

		// 最后使用默认会员价格
		return (float)$this->get_config("huiyuan_price", "500.00");
	}

	/**
	 * 🔧 P1修复：应用佣金限制
	 * @param float $commission 计算出的佣金
	 * @param array $commission_config 佣金配置
	 * @return float 应用限制后的佣金
	 */
	private function apply_commission_limits($commission, $commission_config) {
		// 应用最小值限制
		if (!empty($commission_config['min_amount']) && is_numeric($commission_config['min_amount']) && $commission_config['min_amount'] > 0) {
			$commission = max($commission, (float)$commission_config['min_amount']);
		}

		// 应用最大值限制
		if (!empty($commission_config['max_amount']) && is_numeric($commission_config['max_amount']) && $commission_config['max_amount'] > 0) {
			$commission = min($commission, (float)$commission_config['max_amount']);
		}

		return $commission;
	}

	/**
	 * 🔧 P1修复：记录佣金计算日志
	 * @param string $order_id 订单号
	 * @param array $commission_config 佣金配置
	 * @param array $order_info 订单信息
	 * @param string $status 计算状态：start|success|error
	 * @param float $calculated_amount 计算结果
	 * @param string $error_message 错误信息
	 */
	private function log_commission_calculation($order_id, $commission_config, $order_info, $status, $calculated_amount = 0, $error_message = '') {
		try {
			$log_data = [
				"order_id" => $order_id,
				"user_id" => $order_info['uid'] ?? 0,
				"commission_type" => $commission_config['commission_type'] ?? 'unknown',
				"config_used" => json_encode($commission_config),
				"calculation_method" => $commission_config['calculation_method'] ?? 'unknown',
				"base_amount" => $commission_config['base_amount'] ?? null,
				"calculated_amount" => $calculated_amount,
				"status" => $status,
				"error_message" => $error_message
			];

			// 这里不使用实际表名，因为表可能还不存在，需要先创建
			// 实际使用时，应该先创建commission_calculation_log表
			// Db()->table("commission_calculation_log")->insert($log_data);
		} catch (\Exception $e) {
			$this->exception_log("记录佣金计算日志失败：" . $e->getMessage());
		}
	}

	/*
	 * 获取会员订单实际金额
	 */
	private function get_member_order_amount($order_id) {
		try {
			// 从会员订单表中获取实际支付金额
			$order = Db()->table("user_huiyuan_order")
				->select("money")
				->where("order_id=:order_id")
				->prepareParam([":order_id" => $order_id])
				->fetch();

			return !empty($order) ? (float)$order['money'] : 0;
		} catch (\Exception $e) {
			$this->exception_log("获取会员订单金额失败：订单={$order_id}, 错误=" . $e->getMessage());
			return 0;
		}
	}





	/*
	* @apiName 获取粉丝列表
	* @method get_fans_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":3,"uid":1,"to_uid":2,"time":"2023-11-17 17:23:29","fan_user":{"uid":1,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","mobile":"15800000000","is_huiyuan":0}}],"count":1}
	*/
	public function get_fans_list($uid,$token,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$data = Db()->table("user_guanzhu")->where("to_uid={$uid}")->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		foreach($data as &$row){
			$fan_user = Db()->table("user")->select("uid,nickname,avatar,mobile,is_huiyuan,gexingqianming")->where("uid={$row['uid']} AND is_dongjie=0")->fetch();
			$row['fan_user'] = $fan_user ?: new \stdClass();
		}
		return ["status"=>"ok","data"=>$data,"count"=>\core\Page::$count];
	}

	/*
	* @apiName 获取用户点赞内容
	* @method get_user_likes
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":{"activities":[],"feeds":[],"cards":[]},"count":1}
	*/
	public function get_user_likes($uid,$token,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;

		// 添加开始日志
		$this->exception_log($uid, "开始获取用户点赞内容，页码：{$page}，每页：{$page_size}");

		dbConn();

		$result = ["activities"=>[],"feeds"=>[],"cards"=>[]];

		try {
			// 获取点赞的活动
			$this->exception_log($uid, "开始查询点赞的活动");
			$activity_likes = Db()->table("huodong_zan")->where("uid={$uid}")->order("id DESC")->page($page,$page_size);
			if(!empty($activity_likes)){
				$this->exception_log($uid, "找到点赞活动记录：" . count($activity_likes) . "条");
				foreach($activity_likes as &$like){
					$activity = Db()->table("huodong")->select("id,name,img_url,start_time,address")->where("id={$like['huodong_id']} AND status=1")->fetch();
					if($activity){
						$like['activity'] = $activity;
						$result['activities'][] = $like;
					}
				}
				$this->exception_log($uid, "有效点赞活动：" . count($result['activities']) . "条");
			} else {
				$this->exception_log($uid, "未找到点赞的活动");
			}

			// 获取点赞的动态
			$this->exception_log( "开始查询点赞的动态");
			$feed_likes = Db()->table("user_feed_likes")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
			if(!empty($feed_likes)){
				$this->exception_log( "找到点赞动态记录：" . count($feed_likes) . "条");
				foreach($feed_likes as &$like){
					$feed = Db()->table("feeds")->select("id,content,images_json,location,like_count,comment_count,created_at")->where("id={$like['feed_id']}")->fetch();
					if($feed){
						$feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
						unset($feed['images_json']);
						$like['feed'] = $feed;
						$result['feeds'][] = $like;
					}
				}
				$this->exception_log( "有效点赞动态：" . count($result['feeds']) . "条");
			} else {
				$this->exception_log( "未找到点赞的动态");
			}

			// 获取点赞的日卡
			$this->exception_log("开始查询点赞的日卡");
			$card_likes = Db()->table("user_card_likes")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
			if(!empty($card_likes)){
				$this->exception_log("找到点赞日卡记录：" . count($card_likes) . "条");
				foreach($card_likes as &$like){
					$card = Db()->table("daily_cards")->select("id,description,background_image_url,card_date,like_count,comment_count,created_at")->where("id={$like['card_id']}")->fetch();
					if($card){
						// 字段名映射以保持前端兼容性
						$card['content'] = $card['description'];
						$card['image_url'] = $card['background_image_url'];
						$card['date'] = $card['card_date'];
						unset($card['description'], $card['background_image_url'], $card['card_date']);
						$like['card'] = $card;
						$result['cards'][] = $like;
					}
				}
				$this->exception_log( "有效点赞日卡：" . count($result['cards']) . "条");
			} else {
				$this->exception_log( "未找到点赞的日卡");
			}

			$total_count = count($result['activities']) + count($result['feeds']) + count($result['cards']);
			$this->exception_log( "获取用户点赞内容完成，总计：{$total_count}条（活动：" . count($result['activities']) . "，动态：" . count($result['feeds']) . "，日卡：" . count($result['cards']) . "）");

			return ["status"=>"ok","data"=>$result,"count"=>$total_count];

		} catch (\Exception $e) {
			$this->exception_log($uid, "获取用户点赞内容异常：" . $e->getMessage());
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取用户收藏内容
	* @method get_user_favorites
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型筛选,可选值:activity,feed,card,quote
	* @return {"status":"ok","data":{"activities":[],"feeds":[],"cards":[],"quotes":[]},"count":1}
	*/
	public function get_user_favorites($uid,$token,$page=1,$page_size=20,$type=""){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;

		// 添加调试日志
		$this->exception_log("获取用户收藏内容 - 用户ID: {$uid}, 页码: {$page}, 每页: {$page_size}, 类型: {$type}");

		dbConn();

		$result = ["activities"=>[],"feeds"=>[],"cards"=>[],"quotes"=>[]];

		// 根据type参数决定查询哪些类型
		$queryTypes = [];
		if(empty($type)){
			$queryTypes = ['activity','feed','card','quote'];
		} else {
			$queryTypes = [$type];
		}

		$this->exception_log("查询类型: " . implode(',', $queryTypes));

		foreach($queryTypes as $queryType){
			switch($queryType){
				case 'activity':
					// 获取收藏的活动
					$activity_favorites = Db()->table("huodong_shoucang")->where("uid={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($activity_favorites)){
						foreach($activity_favorites as &$favorite){
							$activity = Db()->table("huodong")->select("id,name,img_url,start_time,addr,baoming_num")->where("id={$favorite['huodong_id']} AND status=1")->fetch();
							if($activity){
								// 字段名映射以保持前端兼容性
								$activity['address'] = $activity['addr'];
								unset($activity['addr']);
								$favorite['activity'] = $activity;
								$result['activities'][] = $favorite;
							}
						}
					}
					break;

				case 'feed':
					// 获取收藏的动态
					$this->exception_log("开始查询收藏的动态");
					$feed_favorites = Db()->table("user_favorites")->where("user_id={$uid} AND item_type='feed'")->order("id DESC")->page($page,$page_size);
					$this->exception_log("收藏动态查询结果数量: " . (is_array($feed_favorites) ? count($feed_favorites) : 0));
					if(!empty($feed_favorites)){
						foreach($feed_favorites as &$favorite){
							$feed = Db()->table("feeds")->select("id,content,images_json,location,like_count,comment_count,created_at")->where("id={$favorite['item_id']}")->fetch();
							if($feed){
								$feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
								unset($feed['images_json']);
								$favorite['feed'] = $feed;
								$result['feeds'][] = $favorite;
							}
						}
						$this->exception_log("有效收藏动态数量: " . count($result['feeds']));
					} else {
						$this->exception_log("未找到收藏的动态");
					}
					break;

				case 'card':
					// 获取收藏的日卡
					$card_favorites = Db()->table("user_favorites")->where("user_id={$uid} AND item_type='card'")->order("id DESC")->page($page,$page_size);
					if(!empty($card_favorites)){
						foreach($card_favorites as &$favorite){
							$card = Db()->table("daily_cards")->select("id,description,background_image_url,card_date,like_count,comment_count,created_at")->where("id={$favorite['item_id']}")->fetch();
							if($card){
								// 字段名映射以保持前端兼容性
								$card['content'] = $card['description'];
								$card['image_url'] = $card['background_image_url'];
								$card['date'] = $card['card_date'];
								unset($card['description'], $card['background_image_url'], $card['card_date']);
								$favorite['card'] = $card;
								$result['cards'][] = $favorite;
							}
						}
					}
					break;

				case 'quote':
					// 获取收藏的摘录
					$quote_favorites = Db()->table("user_favorites")->where("user_id={$uid} AND item_type='quote'")->order("id DESC")->page($page,$page_size);
					if(!empty($quote_favorites)){
						foreach($quote_favorites as &$favorite){
							$quote = Db()->table("quotes")->select("id,content,author,source,tags,like_count,comment_count,created_at")->where("id={$favorite['item_id']}")->fetch();
							if($quote){
								$favorite['quote'] = $quote;
								$result['quotes'][] = $favorite;
							}
						}
					}
					break;
			}
		}

		if(empty($result['activities']) && empty($result['feeds']) && empty($result['cards']) && empty($result['quotes'])){
			return ["status"=>"empty","msg"=>"暂无收藏内容"];
		}

		$total_count = count($result['activities']) + count($result['feeds']) + count($result['cards']) + count($result['quotes']);
		return ["status"=>"ok","data"=>$result,"count"=>$total_count];
	}

	/*
	* @apiName 获取用户发布内容
	* @method get_user_published
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型筛选,可选值:activity,feed,quote
	* @return {"status":"ok","data":{"activities":[],"feeds":[],"quotes":[]},"count":1}
	*/
	public function get_user_published($uid,$token,$page=1,$page_size=20,$type=""){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();

		$result = ["activities"=>[],"feeds"=>[],"quotes"=>[]];

		// 根据type参数决定查询哪些类型
		$queryTypes = [];
		if(empty($type)){
			$queryTypes = ['activity','feed','quote'];
		} else {
			$queryTypes = [$type];
		}

		foreach($queryTypes as $queryType){
			switch($queryType){
				case 'activity':
					// 获取发布的活动
					$activities = Db()->table("huodong")->select("id,name,img_url,start_time,addr,baoming_num,status")->where("uid={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($activities)){
						// 字段名映射以保持前端兼容性
						foreach($activities as &$activity){
							$activity['address'] = $activity['addr'];
							unset($activity['addr']);
						}
						$result['activities'] = $activities;
					}
					break;

				case 'feed':
					// 获取发布的动态
					$feeds = Db()->table("feeds")->select("id,content,images_json,location,like_count,comment_count,created_at")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($feeds)){
						foreach($feeds as &$feed){
							$feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
							unset($feed['images_json']);
						}
						$result['feeds'] = $feeds;
					}
					break;

				case 'quote':
					// 获取发布的摘录
					$quotes = Db()->table("quotes")->select("id,content,author,source,tags,like_count,comment_count,created_at")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($quotes)){
						$result['quotes'] = $quotes;
					}
					break;
			}
		}

		if(empty($result['activities']) && empty($result['feeds']) && empty($result['quotes'])){
			return ["status"=>"empty","msg"=>"暂无发布内容"];
		}

		$total_count = count($result['activities']) + count($result['feeds']) + count($result['quotes']);
		return ["status"=>"ok","data"=>$result,"count"=>$total_count];
	}

	/*
	* @apiName 获取用户评论内容
	* @method get_user_comments
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param type string 类型筛选,可选值:activity,feed,card,quote
	* @return {"status":"ok","data":{"activity_comments":[],"feed_comments":[],"card_comments":[],"quote_comments":[]},"count":1}
	*/
	public function get_user_comments($uid,$token,$page=1,$page_size=20,$type=""){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();

		$result = ["activity_comments"=>[],"feed_comments"=>[],"card_comments"=>[],"quote_comments"=>[]];

		// 根据type参数决定查询哪些类型
		$queryTypes = [];
		if(empty($type)){
			$queryTypes = ['activity','feed','card','quote'];
		} else {
			$queryTypes = [$type];
		}

		foreach($queryTypes as $queryType){
			switch($queryType){
				case 'activity':
					// 获取活动评论
					$activity_comments = Db()->table("huodong_pingjia")->where("uid={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($activity_comments)){
						foreach($activity_comments as &$comment){
							$activity = Db()->table("huodong")->select("id,name,img_url")->where("id={$comment['huodong_id']} AND status=1")->fetch();
							if($activity){
								// 截取活动名称用于显示
								$activity['display_content'] = mb_substr($activity['name'], 0, 30) . (mb_strlen($activity['name']) > 30 ? '...' : '');
								$comment['activity'] = $activity;
								$result['activity_comments'][] = $comment;
							}
						}
					}
					break;

				case 'feed':
					// 获取动态评论
					$feed_comments = Db()->table("feed_comments")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($feed_comments)){
						foreach($feed_comments as &$comment){
							$feed = Db()->table("feeds")->select("id,content,images_json")->where("id={$comment['feed_id']}")->fetch();
							if($feed){
								$feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
								// 截取动态内容用于显示
								$feed['display_content'] = mb_substr($feed['content'], 0, 50) . (mb_strlen($feed['content']) > 50 ? '...' : '');
								unset($feed['images_json']);
								$comment['feed'] = $feed;
								$result['feed_comments'][] = $comment;
							}
						}
					}
					break;

				case 'card':
					// 获取日卡评论
					$card_comments = Db()->table("card_comments")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($card_comments)){
						foreach($card_comments as &$comment){
							$card = Db()->table("daily_cards")->select("id,description,background_image_url,card_date")->where("id={$comment['card_id']}")->fetch();
							if($card){
								// 字段名映射以保持前端兼容性
								$card['content'] = $card['description'];
								$card['image_url'] = $card['background_image_url'];
								$card['date'] = $card['card_date'];
								// 截取日卡内容用于显示
								$card['display_content'] = mb_substr($card['content'], 0, 50) . (mb_strlen($card['content']) > 50 ? '...' : '');
								unset($card['description'], $card['background_image_url'], $card['card_date']);
								$comment['card'] = $card;
								$result['card_comments'][] = $comment;
							}
						}
					}
					break;

				case 'quote':
					// 获取摘录评论
					$quote_comments = Db()->table("quote_comments")->where("user_id={$uid}")->order("id DESC")->page($page,$page_size);
					if(!empty($quote_comments)){
						foreach($quote_comments as &$comment){
							$quote = Db()->table("quotes")->select("id,content,author,source")->where("id={$comment['quote_id']}")->fetch();
							if($quote){
								// 截取摘录内容用于显示
								$quote['display_content'] = mb_substr($quote['content'], 0, 50) . (mb_strlen($quote['content']) > 50 ? '...' : '');
								$comment['quote'] = $quote;
								$result['quote_comments'][] = $comment;
							}
						}
					}
					break;
			}
		}

		if(empty($result['activity_comments']) && empty($result['feed_comments']) && empty($result['card_comments']) && empty($result['quote_comments'])){
			return ["status"=>"empty","msg"=>"暂无评论内容"];
		}

		$total_count = count($result['activity_comments']) + count($result['feed_comments']) + count($result['card_comments']) + count($result['quote_comments']);
		return ["status"=>"ok","data"=>$result,"count"=>$total_count];
	}

	/*
	* @apiName 获取用户通知列表
	* @method get_notifications
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @param page int 页码 (默认 1)
	* @param page_size int 每页数量 (默认 10)
	* @return {"status":"ok","data":{"list":[...],"total":N,"unread_count":N}} | {"status":"error","msg":"..."}
	*/
	public function get_notifications($uid, $token, $page = 1, $page_size = 10) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}
		if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
			return ["status" => "error", "msg" => "分页参数错误"];
		}

		$userId = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;

		dbConn();
		try {
			// 🔧 调试：记录方法开始
			$this->exception_log("get_notifications开始：用户ID={$userId}, 页码={$page}, 每页={$page_size}");

			// 🔧 修复：完整的通知查询，支持个人、全局和分会通知
			$start = ($page - 1) * $page_size;

			// 获取用户信息（包含分会ID）
			$this->exception_log("get_notifications步骤1：查询用户信息");
			$user_info = Db::_fetch("SELECT branch_id FROM user WHERE uid = ?", [$userId]);
			$user_branch_id = $user_info['branch_id'] ?? null;
			$this->exception_log("get_notifications步骤1完成：用户分会ID={$user_branch_id}");

			// 构建查询条件
			$this->exception_log("get_notifications步骤2：构建查询条件");
			$where_conditions = [];
			$params = [];

			// 1. 用户专属通知
			$where_conditions[] = "(target_type = 'user' AND uid = ?)";
			$params[] = $userId;

			// 2. 全局通知
			$where_conditions[] = "target_type = 'all'";

			// 3. 分会通知（如果用户有分会）
			if (!empty($user_branch_id)) {
				$where_conditions[] = "(target_type = 'branch' AND branch_id = ?)";
				$params[] = $user_branch_id;
			}

			$where_clause = "(" . implode(" OR ", $where_conditions) . ")";
			$this->exception_log("get_notifications步骤2完成：查询条件={$where_clause}");

			// 获取通知列表
			$this->exception_log("get_notifications步骤3：查询通知列表");
			$notifications_sql = "
				SELECT id, uid, type, title, content, related_id, branch_id, target_type, created_at, is_global, is_read, read_at
				FROM user_notifications
				WHERE {$where_clause}
				ORDER BY created_at DESC
				LIMIT ?, ?
			";
			$all_params = array_merge($params, [$start, $page_size]);
			$this->exception_log("get_notifications步骤3：SQL参数=" . json_encode($all_params));
			$notifications = \core\Db::_fetchAll($notifications_sql, $all_params);
			$this->exception_log("get_notifications步骤3完成：查询到" . count($notifications) . "条通知");

			// 计算总数
			$this->exception_log("get_notifications步骤4：计算总数");
			$total_sql = "
				SELECT COUNT(*) as count
				FROM user_notifications
				WHERE {$where_clause}
			";
			$total_result = \core\Db::_fetch($total_sql, $params);
			$total = $total_result['count'];
			$this->exception_log("get_notifications步骤4完成：总数={$total}");

			// 🔧 修复：计算未读数量，需要考虑全局通知的特殊处理
			$this->exception_log("get_notifications步骤5：计算未读数量");
			$unread_count = 0;

			// 1. 用户专属未读通知
			$this->exception_log("get_notifications步骤5.1：计算用户专属未读");
			$user_unread_sql = "SELECT COUNT(*) as count FROM user_notifications WHERE target_type = 'user' AND uid = ? AND is_read = 0";
			$user_unread_result = \core\Db::_fetch($user_unread_sql, [$userId]);
			$unread_count += $user_unread_result['count'];
			$this->exception_log("get_notifications步骤5.1完成：用户专属未读={$user_unread_result['count']}");

			// 2. 全局通知未读数量（需要检查用户是否已读）
			$this->exception_log("get_notifications步骤5.2：计算全局通知未读");
			$global_notifications_sql = "SELECT id FROM user_notifications WHERE target_type = 'all'";
			$global_notifications = \core\Db::_fetchAll($global_notifications_sql, []);
			$this->exception_log("get_notifications步骤5.2：找到" . count($global_notifications) . "条全局通知");

			foreach ($global_notifications as $global_notif) {
				// 检查用户是否已读此全局通知
				$read_check_sql = "SELECT id FROM user_notifications WHERE uid = ? AND type = 'global_read' AND related_id = ?";
				$read_record = \core\Db::_fetch($read_check_sql, [$userId, $global_notif['id']]);
				if (!$read_record) {
					$unread_count++;
				}
			}
			$this->exception_log("get_notifications步骤5.2完成：全局通知未读计算完成");

			// 3. 分会通知未读数量（如果用户有分会）
			if (!empty($user_branch_id)) {
				$this->exception_log("get_notifications步骤5.3：计算分会通知未读");
				$branch_notifications_sql = "SELECT id FROM user_notifications WHERE target_type = 'branch' AND branch_id = ?";
				$branch_notifications = \core\Db::_fetchAll($branch_notifications_sql, [$user_branch_id]);
				$this->exception_log("get_notifications步骤5.3：找到" . count($branch_notifications) . "条分会通知");

				foreach ($branch_notifications as $branch_notif) {
					// 检查用户是否已读此分会通知
					$read_check_sql = "SELECT id FROM user_notifications WHERE uid = ? AND type = 'branch_read' AND related_id = ?";
					$read_record = \core\Db::_fetch($read_check_sql, [$userId, $branch_notif['id']]);
					if (!$read_record) {
						$unread_count++;
					}
				}
				$this->exception_log("get_notifications步骤5.3完成：分会通知未读计算完成");
			}
			$this->exception_log("get_notifications步骤5完成：总未读数量={$unread_count}");

			$this->exception_log("get_notifications步骤6：准备返回结果");
			if (empty($notifications)) {
				$this->exception_log("get_notifications完成：无通知数据");
				return ["status" => "empty", "msg" => "暂无通知"];
			}

			$this->exception_log("get_notifications完成：成功返回" . count($notifications) . "条通知");
			return ["status" => "ok", "data" => [
				"list" => $notifications,
				"total" => $total,
				"unread_count" => $unread_count
			]];

		} catch (\Exception $e) {
			$this->exception_log("get_notifications异常：" . $e->getMessage() . "，文件：" . $e->getFile() . "，行号：" . $e->getLine());
			return ["status" => "error", "msg" => "获取通知列表失败"];
		}
	}

	/*
	* @apiName 标记通知为已读
	* @method mark_notification_read
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @param notification_id int 通知ID (可选，不传则标记所有为已读)
	* @return {"status":"ok","msg":"操作成功"} | {"status":"error","msg":"..."}
	*/
	public function mark_notification_read($uid, $token, $notification_id = 0) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		$userId = (int)$uid;
		$notificationId = (int)$notification_id;

		dbConn();
		try {
			// 🔧 调试：记录方法开始
			$this->exception_log("mark_notification_read开始：用户ID={$userId}, 通知ID={$notificationId}");

			$data = [
				"is_read" => 1,
				"read_at" => date('Y-m-d H:i:s')
			];

			if ($notificationId > 0) {
				$this->exception_log("mark_notification_read：标记单个通知，ID={$notificationId}");
				// 标记单个通知为已读
				// 获取通知信息以确定类型
				$notification_info = \core\Db::_fetch("SELECT target_type, branch_id FROM user_notifications WHERE id = ?", [$notificationId]);

				if ($notification_info) {
					if ($notification_info['target_type'] == 'user') {
						// 用户专属通知，直接更新
						$res = Db()->table("user_notifications")
							->where("id = {$notificationId} AND uid = {$userId}")
							->update($data);
					} else {
						// 全局或分会通知，创建已读记录
						$read_type = $notification_info['target_type'] == 'all' ? 'global_read' : 'branch_read';
						$user_read_data = [
							'uid' => $userId,
							'type' => $read_type,
							'title' => $notification_info['target_type'] == 'all' ? '全局通知已读标记' : '分会通知已读标记',
							'content' => '用户已读通知ID:' . $notificationId,
							'related_id' => $notificationId,
							'branch_id' => $notification_info['branch_id'],
							'target_type' => 'user',
							'is_read' => 1,
							'is_global' => 0,
							'created_at' => date('Y-m-d H:i:s'),
							'read_at' => date('Y-m-d H:i:s')
						];

						// 检查是否已存在已读记录
						$existing_read = Db::_fetch("SELECT id FROM user_notifications WHERE uid = ? AND type = ? AND related_id = ?",
							[$userId, $read_type, $notificationId]);

						if (!$existing_read) {
							$res = Db()->table("user_notifications")->insert($user_read_data);
						} else {
							$res = true; // 已存在已读记录
						}
					}
				} else {
					$res = false;
				}
			} else {
				// 🔧 修复：标记所有通知为已读，包括全局通知和分会通知
				$user_info = Db::_fetch("SELECT branch_id FROM user WHERE uid = ?", [$userId]);
				$user_branch_id = $user_info['branch_id'] ?? null;

				// 首先标记用户专属通知为已读
				$res1 = Db()->table("user_notifications")
					->where("uid = {$userId} AND target_type = 'user' AND is_read = 0")
					->update($data);

				// 处理全局通知的已读状态
				$global_notifications = Db::_fetchAll("SELECT id FROM user_notifications WHERE target_type = 'all'", []);
				$res2 = true;
				foreach ($global_notifications as $global_notif) {
					$existing_read = Db::_fetch("SELECT id FROM user_notifications WHERE uid = ? AND type = 'global_read' AND related_id = ?",
						[$userId, $global_notif['id']]);

					if (!$existing_read) {
						$user_read_data = [
							'uid' => $userId,
							'type' => 'global_read',
							'title' => '全局通知已读标记',
							'content' => '用户已读全局通知ID:' . $global_notif['id'],
							'related_id' => $global_notif['id'],
							'target_type' => 'user',
							'is_read' => 1,
							'is_global' => 0,
							'created_at' => date('Y-m-d H:i:s'),
							'read_at' => date('Y-m-d H:i:s')
						];
						$insert_result = Db()->table("user_notifications")->insert($user_read_data);
						if (!$insert_result) {
							$res2 = false;
						}
					}
				}

				// 🆕 新增：处理分会通知的已读状态
				$res3 = true;
				if (!empty($user_branch_id)) {
					$branch_notifications = Db::_fetchAll("SELECT id FROM user_notifications WHERE target_type = 'branch' AND branch_id = ?", [$user_branch_id]);
					foreach ($branch_notifications as $branch_notif) {
						$existing_read = Db::_fetch("SELECT id FROM user_notifications WHERE uid = ? AND type = 'branch_read' AND related_id = ?",
							[$userId, $branch_notif['id']]);

						if (!$existing_read) {
							$user_read_data = [
								'uid' => $userId,
								'type' => 'branch_read',
								'title' => '分会通知已读标记',
								'content' => '用户已读分会通知ID:' . $branch_notif['id'],
								'related_id' => $branch_notif['id'],
								'branch_id' => $user_branch_id,
								'target_type' => 'user',
								'is_read' => 1,
								'is_global' => 0,
								'created_at' => date('Y-m-d H:i:s'),
								'read_at' => date('Y-m-d H:i:s')
							];
							$insert_result = Db()->table("user_notifications")->insert($user_read_data);
							if (!$insert_result) {
								$res3 = false;
							}
						}
					}
				}

				$res = $res1 && $res2 && $res3;
			}

			if ($res) {
				$this->user_log($userId, "标记通知已读：" . ($notificationId > 0 ? $notificationId : "全部"));
			}

			return ["status" => "ok", "msg" => "操作成功"];

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "操作失败"];
		}
	}

	/*
	* @apiName 获取未读通知数量
	* @method get_unread_count
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @return {"status":"ok","data":{"unread_count":N}} | {"status":"error","msg":"..."}
	*/
	public function get_unread_count($uid, $token) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		$userId = (int)$uid;

		dbConn();
		try {
			// 🔧 修复：完整的未读数量计算，支持个人、全局和分会通知
			$user_info = \core\Db::_fetch("SELECT branch_id FROM user WHERE uid = ?", [$userId]);
			$user_branch_id = $user_info['branch_id'] ?? null;

			$unread_count = 0;

			// 1. 用户专属未读通知
			$user_unread_sql = "SELECT COUNT(*) as count FROM user_notifications WHERE target_type = 'user' AND uid = ? AND is_read = 0";
			$user_unread_result = \core\Db::_fetch($user_unread_sql, [$userId]);
			$unread_count += $user_unread_result['count'];

			// 2. 全局通知未读数量（需要检查用户是否已读）
			$global_notifications_sql = "SELECT id FROM user_notifications WHERE target_type = 'all'";
			$global_notifications = \core\Db::_fetchAll($global_notifications_sql, []);

			foreach ($global_notifications as $global_notif) {
				$read_check_sql = "SELECT id FROM user_notifications WHERE uid = ? AND type = 'global_read' AND related_id = ?";
				$read_record = \core\Db::_fetch($read_check_sql, [$userId, $global_notif['id']]);
				if (!$read_record) {
					$unread_count++;
				}
			}

			// 3. 分会通知未读数量（如果用户有分会）
			if (!empty($user_branch_id)) {
				$branch_notifications_sql = "SELECT id FROM user_notifications WHERE target_type = 'branch' AND branch_id = ?";
				$branch_notifications = \core\Db::_fetchAll($branch_notifications_sql, [$user_branch_id]);

				foreach ($branch_notifications as $branch_notif) {
					$read_check_sql = "SELECT id FROM user_notifications WHERE uid = ? AND type = 'branch_read' AND related_id = ?";
					$read_record = \core\Db::_fetch($read_check_sql, [$userId, $branch_notif['id']]);
					if (!$read_record) {
						$unread_count++;
					}
				}
			}

			return ["status" => "ok", "data" => ["unread_count" => $unread_count]];

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "获取未读数量失败"];
		}
	}

	/*
	* @apiName 创建全局通知（管理员专用）
	* @method create_global_notification
	* @POST
	* @param uid int 管理员用户ID
	* @param token string token
	* @param type string 通知类型
	* @param title string 通知标题
	* @param content string 通知内容
	* @param related_id int 关联ID（可选）
	* @return {"status":"ok","msg":"创建成功"} | {"status":"error","msg":"..."}
	*/
	public function create_global_notification($uid, $token, $type, $title, $content, $related_id = null) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		// 验证管理员权限
		$user_info = Db()->table("user")->select("role_type")->where("uid={$uid}")->fetch();
		if ($user_info['role_type'] != '0') {
			return ["status" => "error", "msg" => "权限不足，只有管理员可以创建全局通知"];
		}

		if (empty($type) || empty($title) || empty($content)) {
			return ["status" => "error", "msg" => "通知类型、标题和内容不能为空"];
		}

		dbConn();
		try {
			// 创建全局通知，uid设为0表示全局通知
			$this->create_notification(0, $type, $title, $content, $related_id);

			$this->user_log($uid, "创建全局通知：{$title}");
			return ["status" => "ok", "msg" => "全局通知创建成功"];

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "创建全局通知失败"];
		}
	}

	/*
	* @apiName 创建体验会员分享链接
	* @method create_trial_share
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @param trial_days int 体验天数 (默认30天)
	* @return {"status":"ok","data":{"share_code":"...","share_url":"..."}} | {"status":"error","msg":"..."}
	*/
	public function create_trial_share($uid, $token, $trial_days = 30) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		$userId = (int)$uid;
		$trialDays = (int)$trial_days;

		if ($trialDays <= 0 || $trialDays > 365) {
			return ["status" => "error", "msg" => "体验天数参数错误"];
		}

		dbConn();
		try {
			// 检查用户权限 (role_type 0或1)
			$user_role = Db()->table("user")
				->select("role_type")
				->where("uid = {$userId}")
				->fetch();

			if (empty($user_role) || !in_array($user_role['role_type'], ['0', '1'])) {
				return ["status" => "error", "msg" => "无权限创建分享链接"];
			}

			// 生成唯一分享码
			$share_code = md5($userId . time() . makeCode(16, true));
			$expire_time = date('Y-m-d H:i:s', strtotime("+7 days")); // 分享链接7天有效期

			$data = [
				"share_code" => ":share_code",
				"sharer_uid" => $userId,
				"expire_time" => ":expire_time",
				"trial_days" => $trialDays,
				"status" => 0,
				"share_ip" => ":share_ip"
			];
			$prepareParam = [
				":share_code" => $share_code,
				":expire_time" => $expire_time,
				":share_ip" => IP
			];

			$res = Db()->table("trial_member_records")
				->prepareParam($prepareParam)
				->insert($data);

			if ($res) {
				// 获取当前域名，如果没有配置则使用默认值
				$domain = $_SERVER['HTTP_HOST'] ?? 'your-domain.com';
				$protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https' : 'http';
				$share_url = "{$protocol}://{$domain}/pages/bundle/user/trialClaim?code=" . $share_code;

				$this->user_log($userId, "创建体验会员分享链接：{$share_code}");

				return ["status" => "ok", "data" => [
					"share_code" => $share_code,
					"share_url" => $share_url,
					"expire_time" => $expire_time,
					"trial_days" => $trialDays
				]];
			} else {
				return ["status" => "error", "msg" => "创建分享链接失败"];
			}

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取分享链接信息
	* @method get_trial_info
	* @POST
	* @param share_code string 分享码
	* @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."}
	*/
	public function get_trial_info($share_code) {
		if (empty($share_code) || strlen($share_code) != 32) {
			return ["status" => "error", "msg" => "分享码参数错误"];
		}

		dbConn();
		try {
			$fromClause = 'trial_member_records tmr LEFT JOIN user u ON u.uid = tmr.sharer_uid';
			$selectFields = 'tmr.id, tmr.share_code, tmr.sharer_uid, tmr.receiver_uid, tmr.share_time, tmr.receive_time, tmr.expire_time, tmr.status, tmr.trial_days, u.nickname as sharer_nickname, u.avatar as sharer_avatar';

			$record = Db()->table($fromClause)
				->select($selectFields)
				->where('tmr.share_code = :share_code')
				->prepareParam([':share_code' => $share_code])
				->fetch();

			if (empty($record)) {
				return ["status" => "error", "msg" => "分享链接不存在"];
			}

			// 检查是否过期
			if (strtotime($record['expire_time']) < time()) {
				// 更新状态为已过期
				Db()->table("trial_member_records")
					->where("id = {$record['id']}")
					->update(["status" => 2]);
				return ["status" => "error", "msg" => "分享链接已过期"];
			}

			// 检查是否已被领取
			if ($record['status'] == 1) {
				return ["status" => "error", "msg" => "该体验券已被领取"];
			}

			return ["status" => "ok", "data" => $record];

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "获取分享信息失败"];
		}
	}

	/*
	* @apiName 领取体验会员
	* @method claim_trial_member
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @param share_code string 分享码
	* @return {"status":"ok","msg":"领取成功"} | {"status":"error","msg":"..."}
	*/
	public function claim_trial_member($uid, $token, $share_code) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}
		if (empty($share_code) || strlen($share_code) != 32) {
			return ["status" => "error", "msg" => "分享码参数错误"];
		}

		$userId = (int)$uid;

		dbConn();
		Db()->begin();
		try {
			// 检查用户是否曾经是会员
			$user_info = Db()->table("user")
				->select("is_huiyuan, huiyuan_end_time")
				->where("uid = {$userId}")
				->fetch();

			if (empty($user_info)) {
				throw new \Exception("用户信息不存在");
			}

			// 检查是否曾经是会员
			if ($user_info['is_huiyuan'] == 1 || !empty($user_info['huiyuan_end_time'])) {
				throw new \Exception("您已经是会员或曾经是会员，无法领取体验券");
			}

			// 获取分享记录
			$record = Db()->table("trial_member_records")
				->select("id, sharer_uid, expire_time, status, trial_days")
				->where("share_code = :share_code")
				->prepareParam([":share_code" => $share_code])
				->fetch();

			if (empty($record)) {
				throw new \Exception("分享链接不存在");
			}

			// 检查是否过期
			if (strtotime($record['expire_time']) < time()) {
				throw new \Exception("分享链接已过期");
			}

			// 检查是否已被领取
			if ($record['status'] == 1) {
				throw new \Exception("该体验券已被领取");
			}

			// 检查是否是自己分享的
			if ($record['sharer_uid'] == $userId) {
				throw new \Exception("不能领取自己分享的体验券");
			}

			// 更新用户会员状态
			$member_end_time = date('Y-m-d H:i:s', strtotime("+" . $record['trial_days'] . " days"));
			$user_update = [
				"is_huiyuan" => 1,
				"huiyuan_end_time" => ":huiyuan_end_time"
			];
			$user_param = [
				":huiyuan_end_time" => $member_end_time
			];

			$user_res = Db()->table("user")
				->where("uid = {$userId}")
				->prepareParam($user_param)
				->update($user_update);

			if (!$user_res) {
				throw new \Exception("更新用户会员状态失败");
			}

			// 更新分享记录
			$record_update = [
				"receiver_uid" => $userId,
				"receive_time" => date('Y-m-d H:i:s'),
				"status" => 1,
				"receive_ip" => ":receive_ip"
			];
			$record_param = [
				":receive_ip" => IP
			];

			$record_res = Db()->table("trial_member_records")
				->where("id = {$record['id']}")
				->prepareParam($record_param)
				->update($record_update);

			if (!$record_res) {
				throw new \Exception("更新分享记录失败");
			}

			// 创建通知给领取人
			$this->create_notification($userId, "receive_vip", "体验会员领取成功", "恭喜您成功领取{$record['trial_days']}天体验会员，有效期至{$member_end_time}", $record['id']);

			// 创建通知给分享人
			$this->create_notification($record['sharer_uid'], "share_success", "体验券被领取", "您分享的体验会员券已被成功领取", $record['id']);

			$this->user_log($userId, "领取体验会员：{$record['trial_days']}天");
			$this->user_log($record['sharer_uid'], "体验券被领取：用户{$userId}");

			Db()->commit();
			return ["status" => "ok", "msg" => "领取成功，您已获得{$record['trial_days']}天体验会员"];

		} catch (\Exception $e) {
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => $e->getMessage()];
		}
	}

	/*
	* @apiName 获取分享记录
	* @method get_share_records
	* @POST
	* @param uid int 用户ID
	* @param token string token
	* @param page int 页码 (默认 1)
	* @param page_size int 每页数量 (默认 10)
	* @return {"status":"ok","data":{"list":[...],"total":N}} | {"status":"error","msg":"..."}
	*/
	public function get_share_records($uid, $token, $page = 1, $page_size = 10) {
		if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}
		if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
			return ["status" => "error", "msg" => "分页参数错误"];
		}

		$userId = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;

		dbConn();
		try {
			$fromClause = 'trial_member_records tmr LEFT JOIN user u ON u.uid = tmr.receiver_uid';
			$selectFields = 'tmr.id, tmr.share_code, tmr.share_time, tmr.receive_time, tmr.expire_time, tmr.status, tmr.trial_days, u.nickname as receiver_nickname, u.avatar as receiver_avatar';

			$records = Db()->table($fromClause)
				->select($selectFields)
				->where('tmr.sharer_uid = :uid')
				->order('tmr.share_time DESC')
				->prepareParam([':uid' => $userId])
				->page($page, $page_size);

			$total = Db()->table("trial_member_records")
				->where("sharer_uid = :uid")
				->prepareParam([":uid" => $userId])
				->count();

			if (empty($records)) {
				return ["status" => "empty", "msg" => "暂无分享记录"];
			}

			return ["status" => "ok", "data" => [
				"list" => $records,
				"total" => $total
			]];

		} catch (\Exception $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "获取分享记录失败"];
		}
	}

	/*
	* 创建通知的私有方法
	* @param int $uid 用户ID，如果为0则创建全局通知
	* @param string $type 通知类型
	* @param string $title 通知标题
	* @param string $content 通知内容
	* @param int $related_id 关联ID
	* @param int $branch_id 分会ID（分会通知专用）
	*/
	private function create_notification($uid, $type, $title, $content, $related_id = null, $branch_id = null) {
		try {
			// 确定通知目标类型
			$target_type = 'user';
			$is_global = 0;

			if ($uid == 0) {
				$target_type = 'all';
				$is_global = 1;
			} elseif (!empty($branch_id)) {
				$target_type = 'branch';
			}

			$data = [
				"uid" => $uid,
				"type" => ":type",
				"title" => ":title",
				"content" => ":content",
				"related_id" => $related_id,
				"branch_id" => $branch_id,
				"target_type" => $target_type,
				"is_read" => 0,
				"is_global" => $is_global
			];
			$prepareParam = [
				":type" => htmlspecialchars($type),
				":title" => htmlspecialchars($title),
				":content" => htmlspecialchars($content)
			];

			Db()->table("user_notifications")
				->prepareParam($prepareParam)
				->insert($data);

		} catch (\Exception $e) {
			$this->exception_log("创建通知失败：" . $e->getMessage());
		}
	}

	/*
	* 🆕 新增：创建分会通知的公共方法
	* @param int $branch_id 分会ID
	* @param string $type 通知类型
	* @param string $title 通知标题
	* @param string $content 通知内容
	* @param int $related_id 关联ID
	*/
	public function create_branch_notification($branch_id, $type, $title, $content, $related_id = null) {
		if (empty($branch_id) || !check($branch_id, "intgt0")) {
			return ["status" => "error", "msg" => "分会ID参数错误"];
		}

		try {
			$this->create_notification(0, $type, $title, $content, $related_id, $branch_id);
			return ["status" => "ok", "msg" => "分会通知创建成功"];
		} catch (\Exception $e) {
			$this->exception_log("创建分会通知失败：" . $e->getMessage());
			return ["status" => "error", "msg" => "创建分会通知失败"];
		}
	}

	/*
	* @apiName 获取积分记录
	* @method get_points_log
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码，默认1
	* @param page_size string 每页数量，默认20
	* @return {"status":"ok","data":{"list":[{"id":1,"points_change":10,"points_balance":110,"source_type":"activity_checkin","description":"活动签到：测试活动","created_at":"2024-01-01 10:00:00"}],"total":1,"current_points":110}}
	*/
	public function get_points_log($uid, $token, $page = 1, $page_size = 20)
	{
		if (
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32
		) {
			return ["status" => "error", "msg" => "参数错误"];
		}

		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		$uid = (int)$uid;
		$page = max(1, (int)$page);
		$page_size = min(100, max(1, (int)$page_size));
		$offset = ($page - 1) * $page_size;

		dbConn();

		try {
			// 获取积分记录总数
			$total = Db()->table("user_points_log")
				->where("uid={$uid}")
				->count();

			// 如果没有记录，返回空状态
			if ($total === 0) {
				// 获取当前用户积分
				$current_points = (int)Db()->table("user")
					->where("uid={$uid}")
					->getColumn("points");
					
				return [
					"status" => "ok", // 改为ok状态但返回空列表，确保前端能正确处理
					"data" => [
						"list" => [],
						"total" => 0,
						"current_points" => $current_points,
						"page" => $page,
						"page_size" => $page_size
					]
				];
			}

			// 获取积分记录列表
			$list = Db()->table("user_points_log")
				->select("id,points_change,points_balance,source_type,source_id,description,created_at")
				->where("uid={$uid}")
				->orderBy("id DESC")
				->limit($offset, $page_size)
				->fetchAll();

			// 获取当前用户积分
			$current_points = (int)Db()->table("user")
				->where("uid={$uid}")
				->getColumn("points");

			return [
				"status" => "ok",
				"data" => [
					"list" => $list ?: [],
					"total" => $total,
					"current_points" => $current_points,
					"page" => $page,
					"page_size" => $page_size
				]
			];

		} catch (\Exception $e) {
			$this->exception_log("获取积分记录失败: " . $e->getMessage());
			return ["status" => "error", "msg" => "获取积分记录失败"];
		}
	}

	/*
	* @apiName 添加积分记录（内部方法）
	* @method add_points
	* @param uid int 用户ID
	* @param points_change int 积分变动（正数为增加，负数为减少）
	* @param source_type string 积分来源类型
	* @param source_id int 来源ID（可选）
	* @param description string 描述
	* @return bool 是否成功
	*/
	public static function add_points($uid, $points_change, $source_type, $source_id = null, $description = '')
	{
		if (empty($uid) || !is_numeric($points_change) || empty($source_type)) {
			return false;
		}

		$uid = (int)$uid;
		$points_change = (int)$points_change;

		dbConn();

		try {
			Db()->beginTransaction();

			// 更新用户积分
			if ($points_change > 0) {
				// 增加积分
				Db()->table("user")->where("uid={$uid}")->update([
					"points" => "points + {$points_change}"
				]);
			} else {
				// 减少积分，需要检查余额
				$current_points = Db()->table("user")->where("uid={$uid}")->getColumn("points");
				if ($current_points + $points_change < 0) {
					throw new \Exception("积分余额不足");
				}
				Db()->table("user")->where("uid={$uid}")->update([
					"points" => "points + ({$points_change})"
				]);
			}

			// 获取更新后的积分余额
			$points_balance = Db()->table("user")->where("uid={$uid}")->getColumn("points");

			// 插入积分记录
			$log_data = [
				"uid" => $uid,
				"points_change" => $points_change,
				"points_balance" => $points_balance,
				"source_type" => ":source_type",
				"source_id" => $source_id,
				"description" => ":description"
			];

			$prepareParam = [
				":source_type" => $source_type,
				":description" => $description
			];

			Db()->table("user_points_log")
				->prepareParam($prepareParam)
				->insert($log_data);

			Db()->commit();
			return true;

		} catch (\Exception $e) {
			Db()->rollback();
			return false;
		}
	}

	public function _empty(){
		return ["status"=>"error","msg"=>"URL error"];
	}

	function __destruct(){

	}

	/*
	* @apiName 获取待结算会员佣金订单列表
	* @method get_daijiesuan_order_huiyuan_yongjin
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":1,"order_id":"NO.202311171721200018","uid":1,"money":"500.00","pay_time":"2023-11-17 17:21:10","pay_type":1,"status":1,"yongjin_money_1":"100.00","yongjin_uid_1":2,"yongjin_money_2":"0.00","yongjin_uid_2":0,"jiesuan_status":0,"jiesuan_time":"2023-11-27 17:21:10","user":{"uid":1,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","mobile":"15800000000","is_huiyuan":0}}],"count":1}
	*/
	public function get_daijiesuan_order_huiyuan_yongjin($uid,$token,$page=1,$page_size=20){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			empty($page) ||
			!check($page,"intgt0") ||
			empty($page_size) ||
			!check($page_size,"intgt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		$page = (int)$page;
		$page_size = (int)$page_size;
		dbConn();
		$where = "yongjin_uid_1={$uid} AND jiesuan_status=0 AND status=1";
		$data = Db()->table("user_huiyuan_order")->select("*")->where($where)->order("id DESC")->page($page,$page_size);
		if(empty($data)){
			return ["status"=>"empty","msg"=>"暂无数据"];
		}
		$count = Db()->table("user_huiyuan_order")->where($where)->count();
		foreach($data as &$row){
			$row['user'] = $this->_get_user_info($row['uid'],"uid,nickname,avatar,mobile,is_huiyuan");
		}
		return ["status"=>"ok","data"=>$data,"count"=>$count];
	}

	// 🗑️ 已删除：获取佣金统计信息方法（get_commission_stats）- 未被使用，直接删除

	// 🗑️ 已删除：获取佣金详细列表方法（get_commission_details）- 未被使用，直接删除

	/*
	 * @apiName 申请佣金提现（保持现有逻辑兼容）
	 * @method apply_commission_withdraw
	 * @param uid string 用户ID
	 * @param token string 用户token
	 * @param commission_ids string 佣金记录ID列表JSON
	 * @param bank_id int 银行卡ID
	 * @param total_amount decimal 提现总金额
	 */
	public function apply_commission_withdraw($uid, $token, $commission_ids, $bank_id, $total_amount) {
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		$uid = (int)$uid;
		$bank_id = (int)$bank_id;
		$total_amount = number_format($total_amount, 2, ".", "");
		$commission_ids_array = json_decode($commission_ids, true);

		if (empty($commission_ids_array)) {
			return ["status" => "error", "msg" => "请选择要提现的佣金记录"];
		}

		dbConn();
		Db()->begin();

		try {
			// 验证佣金记录（添加行锁防止并发问题）
			$commission_ids_str = implode(',', array_map('intval', $commission_ids_array));

			// 使用FOR UPDATE行锁防止并发提现
			$sql = "SELECT * FROM user_yongjin_log WHERE id IN ({$commission_ids_str}) AND uid={$uid} AND status=1 FOR UPDATE";
			$commissions = Db::_fetchAll($sql);

			if (empty($commissions)) {
				throw new \Exception("没有可提现的佣金记录");
			}

			// 再次验证状态（防止在获取锁之前状态被其他事务修改）
			foreach ($commissions as $commission) {
				if ($commission['status'] != 1) {
					throw new \Exception("佣金记录状态已变更，请刷新后重试");
				}
			}

			// 验证金额
			$actual_total = array_sum(array_column($commissions, 'money'));
			if (abs($actual_total - $total_amount) > 0.01) {
				throw new \Exception("提现金额不匹配");
			}

			// 创建提现申请
			$withdraw_data = [
				"uid" => $uid,
				"money" => $total_amount,
				"daozhang_money" => $total_amount, // 佣金提现暂不收手续费
				"bank_id" => $bank_id,
				"source_type" => "commission",
				"commission_ids" => $commission_ids,
				"status" => 0
			];

			$withdraw_id = Db()->table("user_tixian")->insert($withdraw_data);
			if (!$withdraw_id) {
				throw new \Exception("创建提现申请失败");
			}

			// 更新佣金状态为提现中（使用参数化查询）
			$update_data = [
				'status' => 2,
				'withdraw_apply_id' => $withdraw_id
			];
			Db()->table("user_yongjin_log")
				->where("id IN ({$commission_ids_str}) AND uid=:uid")
				->prepareParam([':uid' => $uid])
				->update($update_data);

			// 记录状态变更日志
			foreach ($commission_ids_array as $commission_id) {
				$log_data = [
					'commission_id' => $commission_id,
					'old_status' => 1,
					'new_status' => 2,
					'operator_id' => $uid,
					'operator_type' => 'user',
					'remark' => "用户申请提现，提现申请ID: {$withdraw_id}"
				];
				Db()->table("commission_status_logs")->insert($log_data);
			}

			// 记录操作日志
			$this->user_log($uid, "申请佣金提现【{$total_amount} - {$withdraw_id}】");

			Db()->commit();
			return ["status" => "ok", "msg" => "佣金提现申请成功"];

		} catch (\Exception $e) {
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => $e->getMessage()];
		}
	}

	// 注意：settle_commission_status方法已迁移到Daemon.php中统一管理
	// 如需手动触发佣金结算，请使用管理后台的手动结算功能

	/*
	 * @name 获取佣金配置
	 * @method get_commission_config
	 * @param uid int 用户ID
	 * @param commission_type string 佣金类型：invite|operation
	 * @return array|false 佣金配置信息
	 */
	/**
	 * 🔧 P1修复：增强版佣金配置获取方法
	 * @param int $uid 用户ID
	 * @param string $commission_type 佣金类型：invite|operation
	 * @return array|false 佣金配置信息
	 */
	private function get_commission_config($uid, $commission_type) {
		dbConn();

		// 使用参数化查询获取用户角色信息
		$user = Db()->table("user")
			->select("role_type, branch_id")
			->where("uid=:uid")
			->prepareParam([":uid" => $uid])
			->fetch();

		if (empty($user)) {
			$this->exception_log("获取佣金配置失败：用户不存在，uid={$uid}");
			return false;
		}

		// 确保role_type是字符串类型
		$role_type = trim($user['role_type'] ?? '2'); // 默认普通用户

		// 记录查询日志
		$this->log_commission_config_lookup($uid, $commission_type, $role_type, $user['branch_id']);

		// 优先级：用户级配置 > 分会级配置 > 角色级配置 > 全局配置
		$config_queries = [];

		// 1. 用户级配置
		$config_queries[] = [
			"where" => "target_type=:target_type1 AND target_id=:target_id1 AND commission_type=:commission_type",
			"params" => [
				":target_type1" => "user",
				":target_id1" => (string)$uid,
				":commission_type" => $commission_type
			]
		];

		// 2. 分会级配置（如果有分会）
		if (!empty($user['branch_id'])) {
			$config_queries[] = [
				"where" => "target_type=:target_type2 AND target_id=:target_id2 AND commission_type=:commission_type",
				"params" => [
					":target_type2" => "branch",
					":target_id2" => (string)$user['branch_id'],
					":commission_type" => $commission_type
				]
			];
		}

		// 3. 角色级配置
		$config_queries[] = [
			"where" => "target_type=:target_type3 AND target_id=:target_id3 AND commission_type=:commission_type",
			"params" => [
				":target_type3" => "role",
				":target_id3" => $role_type,
				":commission_type" => $commission_type
			]
		];

		// 4. 全局配置
		$config_queries[] = [
			"where" => "target_type=:target_type4 AND target_id=:target_id4 AND commission_type=:commission_type",
			"params" => [
				":target_type4" => "global",
				":target_id4" => "0",
				":commission_type" => $commission_type
			]
		];

		// 按优先级查询配置
		foreach ($config_queries as $query) {
			$where = $query["where"] . " AND status=1 AND (effective_date IS NULL OR effective_date <= NOW()) AND (expiry_date IS NULL OR expiry_date >= NOW())";

			$config = Db()->table("commission_configs")
				->select("*")
				->where($where)
				->prepareParam($query["params"])
				->order("priority DESC, created_at DESC")
				->fetch();

			if (!empty($config)) {
				// 记录使用的配置
				$this->log_commission_config_used($uid, $config);
				return $config;
			}
		}

		// 没有找到配置，记录错误并返回false
		$this->exception_log("未找到有效的佣金配置，uid={$uid}, commission_type={$commission_type}");
		return false;
	}

	/**
	 * 🔧 P1修复：记录佣金配置查询日志
	 * @param int $uid 用户ID
	 * @param string $commission_type 佣金类型
	 * @param string $role_type 用户角色
	 * @param int|null $branch_id 分会ID
	 */
	private function log_commission_config_lookup($uid, $commission_type, $role_type, $branch_id) {
		try {
			$log_data = [
				"user_id" => $uid,
				"commission_type" => $commission_type,
				"user_role" => $role_type,
				"branch_id" => $branch_id,
				"lookup_time" => date('Y-m-d H:i:s')
			];

			// 这里不使用实际表名，因为表可能还不存在，需要先创建
			// 实际使用时，应该先创建commission_config_lookup_log表
			// Db()->table("commission_config_lookup_log")->insert($log_data);
		} catch (\Exception $e) {
			$this->exception_log("记录佣金配置查询日志失败：" . $e->getMessage());
		}
	}

	/**
	 * 🔧 P1修复：记录使用的佣金配置
	 * @param int $uid 用户ID
	 * @param array $config 使用的配置
	 */
	private function log_commission_config_used($uid, $config) {
		try {
			$log_data = [
				"user_id" => $uid,
				"config_id" => $config['id'],
				"config_name" => $config['config_name'],
				"target_type" => $config['target_type'],
				"target_id" => $config['target_id'],
				"commission_type" => $config['commission_type'],
				"calculation_method" => $config['calculation_method'],
				"used_time" => date('Y-m-d H:i:s')
			];

			// 这里不使用实际表名，因为表可能还不存在，需要先创建
			// 实际使用时，应该先创建commission_config_used_log表
			// Db()->table("commission_config_used_log")->insert($log_data);
		} catch (\Exception $e) {
			$this->exception_log("记录佣金配置使用日志失败：" . $e->getMessage());
		}
	}

	/**
	 * 🔧 优化：智能分会分配逻辑（新版）
	 * 为特定角色邀请的用户或自主注册用户分配分会
	 * @param int $new_user_id 新用户ID
	 * @param int $inviter_id 邀请人ID（可为空）
	 * @return bool 分配是否成功
	 */
	private function assignUserToBranch($new_user_id, $inviter_id = null) {
		try {
			// 🔧 优化：新的分配规则
			if ($inviter_id) {
				$inviter = Db()->table("user")
					->select("role_type, branch_id")
					->where("uid={$inviter_id}")
					->fetch();

				if ($inviter) {
					$inviter_role = trim($inviter['role_type']);

					// 🆕 新规则：分会长、城市分会长、场地第三方直接分配到邀请人分会
					if (in_array($inviter_role, ['1', '4', '3'])) {
						return $this->assignToInviterBranch($new_user_id, $inviter_id, $inviter);
					}

					// 🆕 新规则：普通用户和场地第三方（不管理分会）邀请需要系统分配
					if (in_array($inviter_role, ['2', '5'])) {
						return $this->systemAssignBranch($new_user_id, $inviter_id, "invite_by_role{$inviter_role}");
					}
				}
			}

			// 🆕 新规则：自主注册用户使用系统分配
			return $this->systemAssignBranch($new_user_id, null, 'auto_register');

		} catch (\Exception $e) {
			$this->exception_log("用户分会分配失败：" . $e->getMessage());
			return false;
		}
	}

	/**
	 * 🆕 新增：分配到邀请人分会
	 * @param int $new_user_id 新用户ID
	 * @param int $inviter_id 邀请人ID
	 * @param array $inviter 邀请人信息
	 * @return bool 分配是否成功
	 */
	private function assignToInviterBranch($new_user_id, $inviter_id, $inviter) {
		$inviter_branch_id = $inviter['branch_id'];
		$inviter_role = trim($inviter['role_type']);

		if (empty($inviter_branch_id)) {
			$this->exception_log("邀请人分会分配失败：邀请人{$inviter_id}（角色{$inviter_role}）没有分会，用户ID：{$new_user_id}");
			return false;
		}

		// 开始数据库事务
		Db::begin();
		try {
			// 更新用户的分会信息
			$update_result = Db()->table("user")
				->where("uid={$new_user_id}")
				->update([
					'branch_id' => $inviter_branch_id,
					'assignment_type' => 'normal',
					'assignment_time' => date('Y-m-d H:i:s'),
					'assigned_by' => "inviter_{$inviter_role}"
				]);

			if ($update_result === false) {
				throw new \Exception("更新用户分会信息失败");
			}

			// 更新分会成员数量
			$this->updateBranchMemberCount($inviter_branch_id);

			// 插入分配记录
			$assignment_log = [
				'user_id' => $new_user_id,
				'branch_id' => $inviter_branch_id,
				'branch_president_id' => $this->getBranchLeaderId($inviter_branch_id),
				'assignment_type' => "invite_by_role{$inviter_role}",
				'inviter_id' => $inviter_id,
				'assignment_algorithm' => 'direct_inviter_branch',
				'assignment_counter' => 0,
				'assignment_time' => date('Y-m-d H:i:s'),
				'status' => 1,
				'remark' => "邀请人分会分配：角色{$inviter_role}邀请，直接分配到邀请人分会{$inviter_branch_id}"
			];

			$log_id = Db()->table("user_assignment_log")->insert($assignment_log);
			if (!$log_id) {
				throw new \Exception("插入分配记录失败");
			}

			Db::commit();
			$this->exception_log("邀请人分会分配成功：用户{$new_user_id}分配到邀请人{$inviter_id}的分会{$inviter_branch_id}");
			return true;

		} catch (\Exception $e) {
			Db::rollback();
			$this->exception_log("邀请人分会分配失败：" . $e->getMessage());
			return false;
		}
	}

	/**
	 * 🆕 新增：系统智能分配分会
	 * @param int $new_user_id 新用户ID
	 * @param int $inviter_id 邀请人ID（可为空）
	 * @param string $assignment_type 分配类型
	 * @return bool 分配是否成功
	 */
	private function systemAssignBranch($new_user_id, $inviter_id, $assignment_type) {
		try {
			// 获取新用户的城市信息
			$new_user_info = Db()->table("user")
				->select("city")
				->where("uid={$new_user_id}")
				->fetch();

			$user_city = $new_user_info['city'] ?? '';

			// 🔧 优化：获取同城分会长列表（包含系统分配统计）
			$same_city_presidents = [];
			if (!empty($user_city)) {
				$same_city_presidents = Db::_fetchAll("
					SELECT ub.branch_id, ub.branch_leader as president_id, ub.system_assigned_count,
					       ub.assignment_counter, ub.created_at, u.city
					FROM user_branch ub
					INNER JOIN user u ON ub.branch_leader = u.uid
					WHERE u.role_type IN ('1', '4')
					AND ub.branch_id > 0
					AND u.city = ?
					ORDER BY ub.system_assigned_count ASC, ub.created_at ASC
				", [$user_city]);
			}

			// 🔧 优化：如果没有同城分会长，尝试分配到总会或其他分会
			if (empty($same_city_presidents)) {
				if (empty($user_city)) {
					// 用户没有城市信息，分配到总会
					$this->exception_log("系统分配：用户{$new_user_id}没有城市信息，分配到总会");
					return $this->assignToDefaultBranch($new_user_id, $assignment_type, "no_city_info");
				} else {
					// 用户有城市信息但没有同城分会长，分配到总会
					$this->exception_log("系统分配：用户{$new_user_id}城市为'{$user_city}'，没有同城分会长，分配到总会");
					return $this->assignToDefaultBranch($new_user_id, $assignment_type, "no_same_city_president");
				}
			}

			// 选择系统分配数量最少的分会长
			$selected_president = $same_city_presidents[0];
			$selected_branch_id = $selected_president['branch_id'];
			$selected_president_id = $selected_president['president_id'];

			// 开始数据库事务
			Db::begin();
			try {
				// 更新用户的分会信息
				$update_result = Db()->table("user")
					->where("uid={$new_user_id}")
					->update([
						'branch_id' => $selected_branch_id,
						'assignment_type' => 'system',
						'assignment_time' => date('Y-m-d H:i:s'),
						'assigned_by' => 'auto_system'
					]);

				if ($update_result === false) {
					throw new \Exception("更新用户分会信息失败");
				}

				// 🔧 优化：更新分会的系统分配计数器和统计
				$this->updateBranchAssignmentStats($selected_branch_id);

				// 插入分配记录
				$assignment_log = [
					'user_id' => $new_user_id,
					'branch_id' => $selected_branch_id,
					'branch_president_id' => $selected_president_id,
					'assignment_type' => $assignment_type,
					'inviter_id' => $inviter_id,
					'assignment_algorithm' => 'city_match_load_balanced',
					'assignment_counter' => $selected_president['assignment_counter'],
					'assignment_time' => date('Y-m-d H:i:s'),
					'status' => 1,
					'remark' => "系统智能分配：同城优先，负载均衡，城市：{$user_city}，当前负载：{$selected_president['system_assigned_count']}"
				];

				$log_id = Db()->table("user_assignment_log")->insert($assignment_log);
				if (!$log_id) {
					throw new \Exception("插入分配记录失败");
				}

				Db::commit();
				$this->exception_log("系统分配成功：用户{$new_user_id}分配到同城分会{$selected_branch_id}，分会长{$selected_president_id}");
				return true;

			} catch (\Exception $e) {
				Db::rollback();
				throw $e;
			}

		} catch (\Exception $e) {
			$this->exception_log("系统分配失败：" . $e->getMessage());
			return false;
		}
	}

	/**
	 * 🆕 新增：获取分会长ID
	 * @param int $branch_id 分会ID
	 * @return int|null 分会长ID
	 */
	private function getBranchLeaderId($branch_id) {
		$branch_info = Db()->table("user_branch")
			->select("branch_leader")
			->where("branch_id={$branch_id}")
			->fetch();

		return $branch_info['branch_leader'] ?? null;
	}

	/**
	 * 🆕 新增：更新分会成员数量
	 * @param int $branch_id 分会ID
	 */
	private function updateBranchMemberCount($branch_id) {
		$member_count = Db()->table("user")
			->where("branch_id={$branch_id}")
			->count();

		Db()->table("user_branch")
			->where("branch_id={$branch_id}")
			->update(['branch_members' => $member_count]);
	}

	/**
	 * 🆕 新增：更新分会分配统计
	 * @param int $branch_id 分会ID
	 */
	private function updateBranchAssignmentStats($branch_id) {
		// 更新系统分配用户数量
		$system_assigned_count = Db()->table("user")
			->where("branch_id={$branch_id} AND assignment_type='system'")
			->count();

		// 更新分配计数器
		$current_counter = Db()->table("user_branch")
			->select("assignment_counter")
			->where("branch_id={$branch_id}")
			->fetch();

		$new_counter = ($current_counter['assignment_counter'] ?? 0) + 1;

		// 更新分会统计
		Db()->table("user_branch")
			->where("branch_id={$branch_id}")
			->update([
				'system_assigned_count' => $system_assigned_count,
				'assignment_counter' => $new_counter
			]);

		// 同时更新总成员数量
		$this->updateBranchMemberCount($branch_id);
	}




	/*
	 * @apiName 更新用户城市信息
	 * @method update_city
	 * @POST
	 * @param uid string 用户编号
	 * @param token string token
	 * @param city string 城市名称
	 * @return {"status":"ok","msg":"更新成功"}
	 */
	public function update_city($uid, $token, $city) {
		// 参数验证
		if (empty($uid) || !check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}

		// 用户认证验证
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		// 城市信息验证
		$city = trim($city);
		if (empty($city) || mb_strlen($city) > 50) {
			return ["status" => "error", "msg" => "城市信息格式错误"];
		}

		try {
			$update_result = Db()->table("user")
				->where("uid={$uid}")
				->update(['city' => $city]);

			if ($update_result !== false) {
				$this->user_log($uid, "更新城市信息：{$city}");
				return ["status" => "ok", "msg" => "城市信息更新成功"];
			} else {
				return ["status" => "error", "msg" => "更新失败"];
			}
		} catch (\Exception $e) {
			$this->exception_log("更新城市信息失败：" . $e->getMessage());
			return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
		}
	}

	/*
	 * @apiName 获取分会长信息
	 * @method get_branch_leader_info
	 * @POST
	 * @param uid string 用户编号
	 * @param token string token
	 * @return {"status":"ok","msg":"获取成功","data":{"branch_name":"分会名称","leader_name":"分会长昵称","leader_qr_image":"微信二维码","leader_mobile":"手机号"}}
	 */
	public function get_branch_leader_info($uid, $token) {
		// 参数验证
		if (empty($uid) || !check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
			return ["status" => "error", "msg" => "参数错误"];
		}

		// 用户认证验证
		if (!$this->auth($uid, $token)) {
			return ["status" => "relogin", "msg" => "登录信息验证失败"];
		}

		try {
			// 获取用户的分会信息
			$user_info = Db::_fetch("
				SELECT branch_id
				FROM user
				WHERE uid = ?
			", [$uid]);

			if (empty($user_info) || empty($user_info['branch_id'])) {
				return [
					"status" => "ok",
					"msg" => "暂无分会信息",
					"data" => [
						"has_branch" => false,
						"message" => "您暂未加入任何分会"
					]
				];
			}

			// 获取分会和分会长信息
			$branch_info = Db::_fetch("
				SELECT
					ub.branch_name,
					ub.branch_leader,
					ub.branch_leader_mobile,
					ub.branch_leader_qr_image,
					u.nickname as leader_name
				FROM user_branch ub
				LEFT JOIN user u ON ub.branch_leader = u.uid
				WHERE ub.branch_id = ?
			", [$user_info['branch_id']]);

			if (empty($branch_info)) {
				return [
					"status" => "ok",
					"msg" => "分会信息不存在",
					"data" => [
						"has_branch" => false,
						"message" => "分会信息异常，请联系管理员"
					]
				];
			}

			// 检查是否有分会长
			if (empty($branch_info['branch_leader'])) {
				return [
					"status" => "ok",
					"msg" => "暂无分会长",
					"data" => [
						"has_branch" => true,
						"branch_name" => $branch_info['branch_name'],
						"has_leader" => false,
						"message" => "当前分会暂无分会长"
					]
				];
			}

			// 返回分会长信息
			return [
				"status" => "ok",
				"msg" => "获取成功",
				"data" => [
					"has_branch" => true,
					"has_leader" => true,
					"branch_name" => $branch_info['branch_name'],
					"leader_name" => $branch_info['leader_name'] ?? '未知分会长',
					"leader_mobile" => $branch_info['branch_leader_mobile'] ?? '',
					"leader_qr_image" => $branch_info['branch_leader_qr_image'] ?? '',
					"has_qr_code" => !empty($branch_info['branch_leader_qr_image'])
				]
			];

		} catch (\Exception $e) {
			$this->exception_log("获取分会长信息失败：" . $e->getMessage());
			return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
		}
	}

	/**
	 * 🆕 新增：分配用户到默认分会（总会）
	 * @param int $user_id 用户ID
	 * @param string $assignment_type 分配类型
	 * @param string $reason 分配原因
	 * @return bool 分配是否成功
	 */
	private function assignToDefaultBranch($user_id, $assignment_type, $reason) {
		try {
			Db::begin();

			// 更新用户的分会信息为总会
			$update_result = Db()->table("user")
				->where("uid={$user_id}")
				->update([
					'branch_id' => 0, // 总会ID
					'assignment_type' => 'system_default',
					'assignment_time' => date('Y-m-d H:i:s'),
					'assigned_by' => 'auto_system'
				]);

			if ($update_result === false) {
				throw new \Exception("更新用户分会信息失败");
			}

			// 插入分配记录
			$assignment_log = [
				'user_id' => $user_id,
				'branch_id' => 0,
				'branch_president_id' => 0,
				'assignment_type' => $assignment_type,
				'inviter_id' => null,
				'assignment_algorithm' => 'default_fallback',
				'assignment_counter' => 0,
				'assignment_time' => date('Y-m-d H:i:s'),
				'status' => 1,
				'remark' => "默认分配到总会：{$reason}"
			];

			$log_id = Db()->table("user_assignment_log")->insert($assignment_log);
			if (!$log_id) {
				throw new \Exception("插入分配记录失败");
			}

			Db::commit();
			$this->exception_log("默认分会分配成功：用户{$user_id}分配到总会，原因：{$reason}");
			return true;

		} catch (\Exception $e) {
			Db::rollback();
			$this->exception_log("默认分会分配失败：" . $e->getMessage());
			return false;
		}
	}

	/**
	 * 🔧 P0-3修复：增强版检查用户是否有活动收入权限
	 * @param int $uid 用户ID
	 * @return bool 是否有权限
	 */
	private function hasActivityIncomePermission($uid) {
		// 使用参数化查询，防止SQL注入
		$user_info = Db()->table("user")
			->select("role_type, is_dongjie")
			->where("uid=:uid")
			->prepareParam([":uid" => $uid])
			->fetch();

		if (!$user_info) {
			$this->user_log($uid, "权限检查失败：用户不存在");
			return false;
		}

		// 检查用户是否被冻结
		if ($user_info['is_dongjie'] != 0) {
			$this->user_log($uid, "权限检查失败：用户已被冻结");
			return false;
		}

		// 处理role_type的varchar类型，确保类型安全比较
		$role_type = trim($user_info['role_type']);

		// 管理员(0)、场地与活动第三方(3)、城市分会长(4)、场地第三方-不管理分会(5) 有活动收入权限
		// 注意：分会长(1)和普通用户(2)无活动收入权限
		$has_permission = in_array($role_type, ['0', '3', '4', '5']);

		// 记录权限检查日志
		$this->log_permission_check($uid, 'activity_income', $has_permission, $role_type);

		return $has_permission;
	}

	/**
	 * 🔧 P0-3修复：记录权限检查日志
	 * @param int $uid 用户ID
	 * @param string $permission_type 权限类型
	 * @param bool $result 检查结果
	 * @param string $role_type 用户角色
	 */
	private function log_permission_check($uid, $permission_type, $result, $role_type) {
		try {
			$log_data = [
				"user_id" => $uid,
				"operation" => "check_permission",
				"permission_type" => $permission_type,
				"result" => $result ? 1 : 0,
				"user_role" => $role_type,
				"user_status" => null,
				"ip_address" => $_SERVER['REMOTE_ADDR'] ?? null,
				"user_agent" => $_SERVER['HTTP_USER_AGENT'] ?? null
			];

			Db()->table("permission_operation_log")->insert($log_data);
		} catch (\Exception $e) {
			$this->exception_log("记录权限检查日志失败：" . $e->getMessage());
		}
	}

	/**
	 * @apiName 获取用户活动收入状态
	 * @method get_activity_income_status
	 * @POST
	 * @param uid string 用户编号
	 * @param token string token
	 * @return {"status":"ok","data":{"available_income":"0.00","pending_income":"0.00","withdrawing_income":"0.00","total_income":"0.00"}}
	 */
	public function get_activity_income_status($uid, $token) {
		if (!$this->auth($uid, $token)) {
			return ["status" => "error", "msg" => "身份验证失败"];
		}

		// 🆕 新增：检查活动收入权限
		if (!$this->hasActivityIncomePermission($uid)) {
			return ["status" => "error", "msg" => "您没有活动收入查看权限"];
		}

		dbConn();

		// 可提取收入 - 使用参数化查询防止SQL注入
		$available_income_sql = "SELECT SUM(publisher_income) as total_available FROM activity_income_log WHERE publisher_uid=? AND status=1";
		$available_income_res = Db::_fetch($available_income_sql, [$uid]);
		$available_income = $available_income_res && isset($available_income_res['total_available']) ? $available_income_res['total_available'] : 0;

		// 待结算收入 - 使用参数化查询防止SQL注入
		$pending_income_sql = "SELECT SUM(publisher_income) as total_pending FROM activity_income_log WHERE publisher_uid=? AND status=0";
		$pending_income_res = Db::_fetch($pending_income_sql, [$uid]);
		$pending_income = $pending_income_res && isset($pending_income_res['total_pending']) ? $pending_income_res['total_pending'] : 0;

		// 提现中收入 - 使用参数化查询防止SQL注入
		$withdrawing_income_sql = "SELECT SUM(publisher_income) as total_withdrawing FROM activity_income_log WHERE publisher_uid=? AND status=2";
		$withdrawing_income_res = Db::_fetch($withdrawing_income_sql, [$uid]);
		$withdrawing_income = $withdrawing_income_res && isset($withdrawing_income_res['total_withdrawing']) ? $withdrawing_income_res['total_withdrawing'] : 0;

		// 总收入统计 - 使用参数化查询防止SQL注入
		$total_income_sql = "SELECT SUM(publisher_income) as total_income FROM activity_income_log WHERE publisher_uid=?";
		$total_income_res = Db::_fetch($total_income_sql, [$uid]);
		$total_income = $total_income_res && isset($total_income_res['total_income']) ? $total_income_res['total_income'] : 0;

		return [
			"status" => "ok",
			"data" => [
				"available_income" => number_format($available_income, 2, ".", ""),
				"pending_income" => number_format($pending_income, 2, ".", ""),
				"withdrawing_income" => number_format($withdrawing_income, 2, ".", ""),
				"total_income" => number_format($total_income, 2, ".", "")
			]
		];
	}

	/**
	 * @apiName 申请活动收入提现
	 * @method apply_activity_income_withdraw
	 * @POST
	 * @param uid string 用户编号
	 * @param token string token
	 * @param bank_id string 银行卡ID
	 * @param money string 提现金额
	 * @return {"status":"ok","msg":"活动收入提现申请成功"}
	 */
	public function apply_activity_income_withdraw($uid, $token, $bank_id, $money) {
		if (!$this->auth($uid, $token)) {
			return ["status" => "error", "msg" => "身份验证失败"];
		}

		// 🆕 新增：检查活动收入权限
		if (!$this->hasActivityIncomePermission($uid)) {
			return ["status" => "error", "msg" => "您没有活动收入提现权限"];
		}

		if (!check($bank_id, "intgt0") || !is_numeric($money) || $money <= 0) {
			return ["status" => "error", "msg" => "参数错误"];
		}

		$money = number_format($money, 2, ".", "");
		$min_withdraw = $this->get_config("activity_income_min_withdraw", "10.00");

		if (bccomp($money, $min_withdraw, 2) < 0) {
			return ["status" => "error", "msg" => "提现金额不能少于{$min_withdraw}元"];
		}

		dbConn();
		Db::begin();

		try {
			// 修复：获取可提现的收入记录，使用参数化查询
			$income_records_sql = "SELECT id, publisher_income FROM activity_income_log WHERE publisher_uid=? AND status=1 ORDER BY time ASC";
			$income_records = Db::_fetchAll($income_records_sql, [$uid]);

			$available_total = array_sum(array_column($income_records, 'publisher_income'));

			if (bccomp($money, $available_total, 2) > 0) {
				throw new \Exception("提现金额超过可提现金额");
			}

			// 按时间顺序扣减收入记录
			$remaining_amount = (float)$money;
			$selected_income_ids = [];

			foreach ($income_records as $record) {
				if ($remaining_amount <= 0) break;

				$selected_income_ids[] = $record['id'];
				$remaining_amount -= (float)$record['publisher_income'];
			}

			$income_ids_str = implode(',', $selected_income_ids);

			// 创建提现申请
			$withdraw_data = [
				"uid" => $uid,
				"money" => $money,
				"daozhang_money" => $money, // 活动收入提现暂不收手续费
				"bank_id" => $bank_id,
				"source_type" => "activity_income",
				"commission_ids" => $income_ids_str,
				"status" => 0,
				"beizhu" => "活动收入提现申请"
			];

			$withdraw_id = Db()->table("user_tixian")->insert($withdraw_data);
			if (!$withdraw_id) {
				throw new \Exception("创建提现申请失败");
			}

			// 修复：更新收入记录状态为提现中，使用参数化查询
			$placeholders = str_repeat('?,', count($selected_income_ids) - 1) . '?';
			$update_sql = "UPDATE activity_income_log SET status=2, withdraw_apply_id=? WHERE id IN ({$placeholders})";
			$update_params = array_merge([$withdraw_id], $selected_income_ids);
			Db::_exec($update_sql, $update_params);

			// 记录操作日志
			$this->user_log($uid, "申请活动收入提现【{$money}元 - {$withdraw_id}】");

			Db::commit();
			return ["status" => "ok", "msg" => "活动收入提现申请成功"];

		} catch (\Exception $e) {
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => $e->getMessage()];
		}
	}

	/**
	 * @apiName 获取活动收入记录列表
	 * @method get_activity_income_list
	 * @POST
	 * @param uid string 用户编号
	 * @param token string token
	 * @param page string 页码,默认1
	 * @param page_size string 每页多少条,默认20
	 * @return {"status":"ok","data":[]}
	 */
	public function get_activity_income_list($uid, $token, $page = 1, $page_size = 20) {
		if (!$this->auth($uid, $token)) {
			return ["status" => "error", "msg" => "身份验证失败"];
		}

		// 🆕 新增：检查活动收入权限
		if (!$this->hasActivityIncomePermission($uid)) {
			return ["status" => "error", "msg" => "您没有活动收入查看权限"];
		}

		dbConn();

		$page = intval($page) > 0 ? intval($page) : 1;
		$page_size = intval($page_size) > 0 ? intval($page_size) : 20;
		$offset = ($page - 1) * $page_size;

		// 修复：使用参数化查询防止SQL注入
		$data_sql = "SELECT id, activity_id, order_id, total_amount, platform_fee, publisher_income, status, time, settlement_time, remark
					FROM activity_income_log
					WHERE publisher_uid=?
					ORDER BY time DESC
					LIMIT ?, ?";
		$data = Db::_fetchAll($data_sql, [$uid, $offset, $page_size]);

		// 补充活动信息
		foreach ($data as &$row) {
			$activity_sql = "SELECT name, img_url FROM huodong WHERE id=?";
			$activity = Db::_fetch($activity_sql, [$row['activity_id']]);

			$row['activity_name'] = $activity['name'] ?? '未知活动';
			$row['activity_img'] = $activity['img_url'] ?? '';
			$row['status_text'] = $this->get_activity_income_status_text($row['status']);
		}

		return ["status" => "ok", "data" => $data];
	}

	/**
	 * 获取活动收入状态文本
	 * @param int $status 状态值
	 * @return string 状态文本
	 */
	private function get_activity_income_status_text($status) {
		$status_texts = [
			0 => '待结算',
			1 => '可提取',
			2 => '提现中',
			3 => '已提现',
			4 => '已驳回',
			5 => '已冻结'
		];
		return $status_texts[$status] ?? '未知状态';
	}

}
