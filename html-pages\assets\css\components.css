/* 微信小程序HTML复刻 - 组件样式库 */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 2.34rem; /* 44rpx */
  min-width: 6.4rem; /* 120rpx */
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.11rem rgba(106, 192, 134, 0.25);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  box-shadow: 0 0.11rem 0.32rem rgba(92, 184, 92, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-0.05rem);
  box-shadow: 0 0.21rem 0.64rem rgba(92, 184, 92, 0.4);
}

.btn-primary:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 0.05rem 0.21rem rgba(92, 184, 92, 0.3);
}

.btn-secondary {
  background: var(--color-surface);
  color: var(--color-text-primary);
  border: 0.05rem solid var(--color-border-light);
}

.btn-secondary:hover {
  background: var(--color-gray-50);
  border-color: var(--color-primary);
}

.btn-ghost {
  background: transparent;
  color: var(--color-primary);
  border: 0.05rem solid var(--color-primary);
}

.btn-ghost:hover {
  background: var(--color-primary-lighter);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 1.87rem; /* 35rpx */
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 2.67rem; /* 50rpx */
}

.btn-round {
  border-radius: var(--radius-round);
}

.btn-block {
  width: 100%;
  display: flex;
}

/* 卡片组件 - 根据设计文档标准化 */
.card {
  background: var(--color-surface);
  border-radius: var(--radius-md);
  box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.11rem;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.card:hover {
  transform: translateY(-0.05rem);
  box-shadow: 0 0.21rem 0.64rem rgba(0, 0, 0, 0.08);
}

.card:hover::before {
  transform: scaleX(1);
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 0.05rem solid var(--color-border-light);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 0.05rem solid var(--color-border-light);
  background: var(--color-gray-50);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.card-text {
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
}

/* 输入框组件 */
.input-group {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 0.05rem solid var(--color-border-light);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  background: var(--color-surface);
  transition: all var(--transition-fast);
  min-height: 2.13rem; /* 40rpx */
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.11rem rgba(92, 184, 92, 0.1);
  transform: translateY(-0.05rem);
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

.input-search {
  padding-left: 2.67rem; /* 为搜索图标留空间 */
  border-radius: var(--radius-round);
}

.input-search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-md);
}

/* 头像组件 */
.avatar {
  display: inline-block;
  border-radius: var(--radius-round);
  overflow: hidden;
  background: var(--color-gray-200);
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-sm {
  width: 1.6rem; /* 30rpx */
  height: 1.6rem;
}

.avatar-md {
  width: 2.13rem; /* 40rpx */
  height: 2.13rem;
}

.avatar-lg {
  width: 2.93rem; /* 55rpx */
  height: 2.93rem;
}

.avatar-xl {
  width: 3.73rem; /* 70rpx */
  height: 3.73rem;
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.13rem 0.27rem; /* 5rpx 10rpx */
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.tag-primary {
  background: var(--color-primary-lighter);
  color: var(--color-primary-dark);
}

.tag-secondary {
  background: var(--color-gray-100);
  color: var(--color-text-secondary);
}

.tag-success {
  background: rgba(40, 167, 69, 0.1);
  color: var(--color-success);
}

.tag-warning {
  background: rgba(255, 193, 7, 0.1);
  color: var(--color-warning);
}

.tag-error {
  background: rgba(220, 53, 69, 0.1);
  color: var(--color-error);
}

/* 分割线组件 */
.divider {
  height: 0.05rem;
  background: var(--color-border-light);
  margin: var(--spacing-md) 0;
  border: none;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: var(--spacing-md) 0;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 0.05rem;
  background: var(--color-border-light);
}

.divider-text span {
  background: var(--color-background);
  padding: 0 var(--spacing-sm);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  position: relative;
  z-index: 1;
}

/* 加载状态组件 */
.loading {
  display: inline-block;
  width: 1.33rem; /* 25rpx */
  height: 1.33rem;
  border: 0.11rem solid var(--color-border-light);
  border-top: 0.11rem solid var(--color-primary);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary);
}

.loading-text {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

/* 空状态组件 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  text-align: center;
}

.empty-state-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.empty-state-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 3.2rem; /* 60rpx */
  height: 3.2rem;
  border-radius: var(--radius-round);
  background: var(--color-primary);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  transition: all var(--transition-normal);
  z-index: var(--z-index-fixed);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 0.32rem 0.85rem rgba(106, 192, 134, 0.4);
}

.fab:active {
  transform: scale(0.95);
}

/* 标签页组件 */
.tabs {
  display: flex;
  background: var(--color-surface);
  border-bottom: 0.05rem solid var(--color-border-light);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
}

.tab-item {
  flex: 1;
  padding: var(--spacing-md);
  text-align: center;
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  border: none;
  background: none;
}

.tab-item:hover {
  color: var(--color-primary);
}

.tab-item.active {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1.07rem; /* 20rpx */
  height: 0.11rem; /* 2rpx */
  background: var(--color-primary);
  border-radius: var(--radius-xs);
}

/* 底部导航栏 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--color-surface);
  border-top: 0.05rem solid var(--color-border-light);
  display: flex;
  z-index: var(--z-index-fixed);
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  text-decoration: none;
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
  min-height: 2.67rem; /* 50rpx */
}

.bottom-nav-item:hover {
  color: var(--color-primary);
}

.bottom-nav-item.active {
  color: var(--color-primary);
}

.bottom-nav-icon {
  font-size: var(--font-size-lg);
  margin-bottom: 0.13rem; /* 2rpx */
}

.bottom-nav-text {
  font-size: var(--font-size-xs);
  line-height: 1;
}

/* 标签页内容切换 */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图片预览组件 */
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.image-preview.show {
  opacity: 1;
  visibility: visible;
}

.image-preview img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.image-preview-close {
  position: absolute;
  top: 20px;
  right: 20px;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  transition: all var(--transition-fast);
}

.image-preview-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* 下拉刷新组件 */
.pull-refresh {
  position: relative;
  overflow: hidden;
}

.pull-refresh-indicator {
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  transition: all var(--transition-normal);
}

.pull-refresh.pulling .pull-refresh-indicator {
  top: 0;
}

/* 骨架屏组件 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
  margin-bottom: 0.5em;
}

.skeleton-text:last-child {
  margin-bottom: 0;
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

/* 模态框组件 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--color-surface);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg);
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: all var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.modal-close {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-xs);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  color: var(--color-text-primary);
  background: var(--color-gray-100);
}

.modal-body {
  margin-bottom: var(--spacing-md);
}

.modal-footer {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* 增强交互设计 - 微动效和状态反馈 */
.interactive-element {
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.interactive-element::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(92, 184, 92, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.interactive-element:active::after {
  width: 200px;
  height: 200px;
}

/* 悬浮效果增强 */
.hover-lift {
  transition: all var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-0.11rem);
  box-shadow: 0 0.32rem 0.85rem rgba(0, 0, 0, 0.1);
}

/* 脉冲动画 */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 渐入动画 */
.fade-in {
  animation: fadeInUp 0.5s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.07rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹性动画 */
.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 加载状态优化 */
.loading-enhanced {
  position: relative;
  overflow: hidden;
}

.loading-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(92, 184, 92, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
