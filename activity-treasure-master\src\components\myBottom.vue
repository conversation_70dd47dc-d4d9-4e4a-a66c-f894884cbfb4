<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import { store } from "@/store";

const props = defineProps({
  b: {
    type: String || Number,
    default: "0"
  }
});


</script>
<template>
  <view class="page">
    <view class="pfx" :style="{ bottom: b + 'rpx' }">
      <slot></slot>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
