<?php
// 启用错误显示
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 配置错误日志文件路径
ini_set('log_errors', 1);
ini_set('error_log', '/home/<USER>/default/huodongbao_admin/error.log');
define("_NOW_",time());
define('IS_CLI', preg_match("/cli/i", php_sapi_name()) ? true : false);
define('DS', DIRECTORY_SEPARATOR);
define("BASE_PATH",dirname(__DIR__) . DS);
define("PI",pi());
//
require(BASE_PATH . "core/func.php");
if(file_exists(BASE_PATH . "vendor/autoload.php")){
	require_once(BASE_PATH . "vendor/autoload.php");
}
//
$ip = get_client_ip();
define("IP",$ip);
//
define("DATETIME",date("Y-m-d H:i:s"));
//
\core\Config::init();
if(!IS_CLI){
	\core\Config::get("sys.error_reporting") ? error_reporting(E_ALL) : error_reporting(0);
	if(\core\Config::get("sys.session"))\core\Session::init();
	define("ASSET_PATH","/access/asset/");
	$protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https') ? "https://" : "http://";
	define("HTTP_HOST",$protocol . $_SERVER["HTTP_HOST"]);
}else{
	// CLI环境下定义默认值，避免未定义警告
	define("ASSET_PATH","/access/asset/");
	define("HTTP_HOST","http://localhost");
}
\core\Route::run();
