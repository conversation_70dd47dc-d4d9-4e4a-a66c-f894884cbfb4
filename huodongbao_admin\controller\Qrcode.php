<?php
namespace controller;
class Qrcode{

	public function __construct(){
		
	}
	
	public function qrcode($contents,$base64_decode=0,$urldecode=0){
		header("Content-Type: image/png");
		if(!empty($base64_decode)){
			$contents = base64_decode($contents);
		}
		if(!empty($urldecode)){
			$contents = urldecode($contents);
		}
		echo \lib\Qrcode::png($contents);
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
