import {
	createApp
} from 'vue';
import App from './App.vue';
import {
	createPinia
} from 'pinia';
import {
	createUnistorage
} from 'pinia-plugin-unistorage'
import uviewPlus from "uview-plus";
import errorHandler, { handleError, ERROR_TYPES, ERROR_LEVELS } from './utils/errorHandler'

// 增强的全局错误处理
const enhancedErrorHandler = (err, vm, info) => {
	console.error('全局捕获的错误:', err);
	console.error('错误组件:', vm);
	console.error('错误详情:', info);

	// 使用新的错误处理系统
	handleError(err, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {
		component: vm?.$options.name || 'Unknown',
		errorInfo: info
	});
};

const app = createApp(App);

// 配置全局错误处理
app.config.errorHandler = enhancedErrorHandler;

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
	if (process.env.NODE_ENV === 'development') {
		console.warn('Vue警告:', msg, trace);
	}
	handleError(msg, ERROR_TYPES.SYSTEM, ERROR_LEVELS.WARNING, {
		component: instance?.$options.name || 'Unknown',
		trace
	});
};



const store = createPinia();
store.use(createUnistorage())
app.use(store).use(uviewPlus).mount('#app');

// 未捕获的Promise错误处理（兼容小程序环境）
if (typeof window !== 'undefined' && window.addEventListener) {
	// H5环境
	window.addEventListener('unhandledrejection', (event) => {
		console.error('未捕获的Promise错误:', event.reason);
		handleError(event.reason, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {
			type: 'unhandledrejection'
		});
		event.preventDefault();
	});
} else {
	// 小程序环境 - 使用uni-app的错误处理
	uni.onError && uni.onError((error) => {
		console.error('小程序错误:', error);
		handleError(error, ERROR_TYPES.SYSTEM, ERROR_LEVELS.ERROR, {
			type: 'miniprogram_error'
		});
	});
}