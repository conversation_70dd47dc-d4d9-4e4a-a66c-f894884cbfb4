<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
<title>video.js</title>
<meta charset="utf-8">
  <link href="video-js.css" rel="stylesheet">

  <!-- If you'd like to support IE8 (for Video.js versions prior to v7) -->
  <script src="videojs-ie8.min.js"></script>
  <script src='video.js'></script>
</head>
<body>
    <video id="my-player" class="video-js" controls>
        <source src="" type="application/x-mpegURL">
        <p class="vjs-no-js">not support</p>
    </video>
    <script type="text/javascript">
        var player = videojs('my-player', {
            width : 1000,
            heigh : 800
        });
    </script>
<!--
nginx加上

location /hls {
	types {
		application/vnd.apple.mpegurl m3u8;
		video/mp2t ts;
	}
	add_header Cache-Control no-cache;
	add_header Access-Control-Allow-Origin *;
}
-->
</body>
</html>