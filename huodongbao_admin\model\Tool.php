<?php
namespace model;
use core\Db;
use core\Model;

class Tool extends Model{
	
	public function __construct(){
		
	}
	
	//格式化时间
	static public function timeFormat($time){
		$diff = time() - strtotime($time);
		if($diff < 60){
			$format = "刚刚";
		}else if($diff < 3600){
			$fenzhong = floor($diff / 60);
			$format = "{$fenzhong}分钟前";
		}else if($diff < 24 * 3600){
			$hours = floor($diff / 3600);
			$format = "{$hours}小时前";			
		}else if($diff < 24 * 3600 * 30){
			$days = floor($diff / (3600 * 24));
			$format = "{$days}天前";					
		}else if($diff < 24 * 3600 * 30 * 12){
			$month = floor($diff / (3600 * 24 * 30));
			$format = "{$month}个月前";					
		}else{
			$years = floor($diff / (3600 * 24 * 30 * 12));
			$format = "{$years}年前";					
		}
		return $format;
	}
	
	static public function qrcodeReader($file){
		try{
			require_once BASE_PATH . "lib/QrReader/QrReader.php";
			$qrcode_reader = new \Zxing\QrReader($file);
			$text = $qrcode_reader->text();
			if(empty($text)){
				return "";
			}
			return $text;
		}catch(\Excetion $e){
			return false;
		}
	}
	public function __destruct(){

	}
}