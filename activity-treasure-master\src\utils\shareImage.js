/**
 * 分享图片生成工具 - 简化版本
 * 用于生成带有水印的分享图片
 */

/**
 * 生成分享图片（带重试机制）
 * @param {Object} options 配置选项
 * @param {String} options.backgroundImage 背景图片URL
 * @param {String} options.content 内容文本
 * @param {String} options.author 作者名称 (可选)
 * @param {String} options.authorAvatar 作者头像URL (可选)
 * @param {String} options.date 日期 (可选)
 * @param {String} options.watermark 水印文本 (默认: "分享自小聚会")
 * @param {Number} options.width 图片宽度 (默认: 750)
 * @param {Number} options.height 图片高度 (默认: 1334)
 * @param {String} options.template 模板类型 (默认: "default")
 * @param {Array} options.images 内容图片数组 (可选)
 * @returns {Promise<String>} 返回生成的图片临时路径
 */
export const generateShareImage = (options) => {
  return generateShareImageWithRetry(options, 0);
};

/**
 * 生成分享图片的内部实现（支持重试）
 * @param {Object} options 配置选项
 * @param {Number} retryCount 当前重试次数
 * @returns {Promise<String>} 返回生成的图片临时路径
 */
const generateShareImageWithRetry = (options, retryCount = 0) => {
  const maxRetries = 3;

  return new Promise(async (resolve, reject) => {
    const {
      backgroundImage,
      content,
      author,
      date,
      watermark = "分享自小聚会",
      width = 750,
      height = 1334,
      template = "default"
    } = options;

    console.log(`开始生成分享图片，参数:`, options, `重试次数: ${retryCount}/${maxRetries}`);

    // 添加超时机制（优化版）
    const timeout = setTimeout(() => {
      console.error(`分享图片生成超时，重试次数: ${retryCount}/${maxRetries}`);
      cleanupCanvas();

      // 如果还有重试次数，则重试
      if (retryCount < maxRetries) {
        console.log(`开始第${retryCount + 1}次重试...`);
        generateShareImageWithRetry(options, retryCount + 1)
          .then(resolve)
          .catch(reject);
      } else {
        reject(new Error('分享图片生成超时，已达到最大重试次数'));
      }
    }, 8000); // 优化为8秒超时

    try {
      // 优先尝试Canvas 2D API，提高兼容性和性能
      let ctx = null;
      let isCanvas2D = false;

      // 尝试获取Canvas 2D上下文
      try {
        const query = uni.createSelectorQuery();
        const canvasNode = await new Promise((resolve, reject) => {
          // 添加延迟确保DOM已渲染
          setTimeout(() => {
            query.select('#share-canvas')
              .fields({ node: true, size: true })
              .exec((res) => {
                console.log('Canvas查询结果:', res);

                if (res && res[0] && res[0].node) {
                  console.log('Canvas节点获取成功');
                  resolve(res[0].node);
                } else {
                  console.error('Canvas节点获取失败，查询结果:', res);
                  reject(new Error('Canvas节点获取失败 - 请确保页面中存在id为share-canvas的canvas元素'));
                }
              });
          }, 200); // 延迟200ms确保DOM渲染完成
        });

        if (canvasNode) {
          ctx = canvasNode.getContext('2d');
          isCanvas2D = true;
          console.log('使用Canvas 2D API生成分享图片');
        }
      } catch (canvas2DError) {
        console.warn('Canvas 2D API不可用，降级到旧版API:', canvas2DError);
      }

      // 降级到旧版Canvas API
      if (!ctx) {
        if (typeof uni.createCanvasContext !== 'function') {
          clearTimeout(timeout);
          console.error('Canvas API完全不可用');
          reject(new Error('Canvas API不可用'));
          return;
        }

        ctx = uni.createCanvasContext('share-canvas');
        isCanvas2D = false;
        console.log('使用旧版Canvas API生成分享图片');
      }

      if (!ctx) {
        clearTimeout(timeout);
        console.error('无法获取Canvas上下文');
        reject(new Error('Canvas上下文不可用'));
        return;
      }

      // 简化绘制，支持Canvas 2D API
      let drawResult;
      try {
        console.log(`使用${isCanvas2D ? 'Canvas 2D' : '旧版Canvas'} API绘制模式`);
        drawResult = drawBasicContent(ctx, options, width, height, isCanvas2D);
      } catch (drawError) {
        clearTimeout(timeout);
        console.error('绘制内容异常:', drawError);
        cleanupCanvas();
        reject(new Error(`绘制内容失败: ${drawError.message}`));
        return;
      }

      if (!drawResult) {
        clearTimeout(timeout);
        console.error('绘制内容失败');
        cleanupCanvas();
        reject(new Error('绘制内容失败'));
        return;
      }

      console.log('开始生成图片文件');

      // 根据Canvas API类型选择不同的图片生成方式
      const generateImage = () => {
        // 检查API可用性
        if (typeof uni.canvasToTempFilePath !== 'function') {
          clearTimeout(timeout);
          console.error('canvasToTempFilePath API不可用');
          cleanupCanvas();
          reject(new Error('canvasToTempFilePath API不可用'));
          return;
        }

        const canvasConfig = {
          canvas: isCanvas2D ? ctx.canvas : undefined,
          canvasId: isCanvas2D ? undefined : 'share-canvas',
          x: 0,
          y: 0,
          width: width,
          height: height,
          destWidth: width,
          destHeight: height,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            clearTimeout(timeout);
            console.log('生成图片成功:', res.tempFilePath);
            cleanupCanvas();

            // 添加临时文件到清理队列（延时清理）
            setTimeout(() => {
              cleanupTempFile(res.tempFilePath);
            }, 300000); // 5分钟后自动清理

            resolve(res.tempFilePath);
          },
          fail: (err) => {
            clearTimeout(timeout);
            console.error(`生成图片失败，重试次数: ${retryCount}/${maxRetries}`, err);
            cleanupCanvas();

            // 如果还有重试次数，则重试
            if (retryCount < maxRetries) {
              console.log(`图片生成失败，开始第${retryCount + 1}次重试...`);
              setTimeout(() => {
                generateShareImageWithRetry(options, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              }, 1000); // 延时1秒后重试
            } else {
              // 提供更详细的错误信息
              const errorMsg = err.errMsg || err.message || JSON.stringify(err);
              reject(new Error(`生成图片失败: ${errorMsg}，已达到最大重试次数`));
            }
          }
        };

        uni.canvasToTempFilePath(canvasConfig);
      };

      // Canvas 2D API直接生成，旧版API需要等待draw完成
      if (isCanvas2D) {
        console.log('Canvas 2D绘制完成，开始转换为图片');
        setTimeout(generateImage, 300); // Canvas 2D API延时更短
      } else {
        // 旧版Canvas API需要调用draw
        ctx.draw(false, () => {
          console.log('旧版Canvas绘制完成，开始转换为图片');
          setTimeout(generateImage, 500); // 旧版API需要更长延时
        });
      }

    } catch (error) {
      clearTimeout(timeout);
      console.error('生成分享图片过程中发生错误:', error);
      cleanupCanvas();
      reject(error);
    }
  });
};

/**
 * 保存图片到相册（增强版）
 * @param {String} filePath 图片路径
 * @returns {Promise}
 */
export const saveImageToAlbum = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: (res) => {
        console.log('图片保存到相册成功');
        // 保存成功后清理临时文件
        cleanupTempFile(filePath);
        resolve(res);
      },
      fail: (error) => {
        console.error('图片保存到相册失败:', error);
        // 保存失败也清理临时文件
        cleanupTempFile(filePath);
        reject(error);
      }
    });
  });
};

/**
 * 清理临时文件
 * @param {String} filePath 文件路径
 */
export const cleanupTempFile = (filePath) => {
  if (!filePath) return;

  try {
    // 延时清理，确保文件使用完毕
    setTimeout(() => {
      uni.getFileSystemManager().unlink({
        filePath: filePath,
        success: () => {
          console.log('临时文件清理成功:', filePath);
        },
        fail: (error) => {
          console.warn('临时文件清理失败:', error);
        }
      });
    }, 5000); // 5秒后清理
  } catch (error) {
    console.warn('临时文件清理异常:', error);
  }
};

/**
 * 清理Canvas资源（增强版）
 */
export const cleanupCanvas = () => {
  try {
    // 清理Canvas 2D API资源
    try {
      const query = uni.createSelectorQuery();
      query.select('#share-canvas')
        .fields({ node: true })
        .exec((res) => {
          if (res && res[0] && res[0].node) {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            if (ctx) {
              // 清空Canvas 2D画布
              ctx.clearRect(0, 0, canvas.width, canvas.height);
              // 重置Canvas尺寸释放内存
              canvas.width = 1;
              canvas.height = 1;
              console.log('Canvas 2D资源清理完成');
            }
          }
        });
    } catch (canvas2DError) {
      console.warn('Canvas 2D清理失败:', canvas2DError);
    }

    // 清理旧版Canvas API资源
    try {
      const ctx = uni.createCanvasContext('share-canvas');
      if (ctx) {
        // 清空画布
        ctx.clearRect(0, 0, 750, 1334);
        ctx.draw();
        console.log('旧版Canvas资源清理完成');
      }
    } catch (oldCanvasError) {
      console.warn('旧版Canvas清理失败:', oldCanvasError);
    }

    // 强制垃圾回收（如果支持）
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      wx.triggerGC();
      console.log('触发垃圾回收');
    }

    console.log('Canvas资源清理完成');
  } catch (error) {
    console.warn('清理Canvas资源失败:', error);
  }
};

/**
 * 基础内容绘制函数（降级处理）
 * @param {CanvasContext} ctx Canvas上下文
 * @param {Object} options 配置选项
 * @param {Number} width 画布宽度
 * @param {Number} height 画布高度
 */
const drawBasicContent = (ctx, options, width, height, isCanvas2D = false) => {
  const { content = "分享内容", author = "作者", date = new Date().toLocaleDateString(), watermark = "MindfulMeetUp" } = options;

  try {
    // Canvas 2D API需要设置画布尺寸
    if (isCanvas2D && ctx.canvas) {
      ctx.canvas.width = width;
      ctx.canvas.height = height;
    }

    // 绘制简化背景（避免复杂渐变，提高性能）
    ctx.fillStyle = '#8EFFFE';
    ctx.fillRect(0, 0, width, height);

    // 绘制白色内容区域
    const cardMargin = 40;
    const cardWidth = width - (cardMargin * 2);
    const cardHeight = height - (cardMargin * 2);

    ctx.fillStyle = '#ffffff';
    ctx.fillRect(cardMargin, cardMargin, cardWidth, cardHeight);

    // 绘制内容文本
    ctx.fillStyle = '#333333';
    ctx.font = '32px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    // 简化的文本绘制（避免复杂换行逻辑）
    const maxLineLength = 18;
    const lines = [];

    // 简单的文本分行处理
    for (let i = 0; i < content.length; i += maxLineLength) {
      lines.push(content.substr(i, maxLineLength));
      if (lines.length >= 8) break; // 最多8行，提高性能
    }

    lines.forEach((line, index) => {
      ctx.fillText(line, cardMargin + 60, cardMargin + 80 + index * 50);
    });

    // 绘制作者信息
    if (author) {
      ctx.fillStyle = '#666666';
      ctx.font = '24px sans-serif';
      ctx.fillText(author, cardMargin + 60, cardMargin + cardHeight - 100);
    }

    // 绘制日期
    if (date) {
      ctx.fillStyle = '#999999';
      ctx.font = '20px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(date, cardMargin + cardWidth - 60, cardMargin + cardHeight - 100);
    }

    // 绘制水印
    if (watermark) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
      ctx.font = '20px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(watermark, width / 2, height - 30);
    }

    return true;
  } catch (error) {
    console.error('基础绘制失败:', error);
    return false;
  }
};

/**
 * 简化的同步绘制分享内容（修复实现）
 * @param {CanvasContext} ctx Canvas上下文
 * @param {Object} options 配置选项
 * @param {Number} width 画布宽度
 * @param {Number} height 画布高度
 */
const drawShareContentSync = (ctx, options, width, height) => {
  const { backgroundImage, content, author, date, watermark, template } = options;

  console.log('开始绘制分享内容（同步模式）', { template, content: content?.substring(0, 50) });

  try {
    // 绘制背景（简化版）
    drawBackgroundSync(ctx, width, height);
    console.log('背景绘制完成');

    // 根据模板绘制内容（简化版）
    switch (template) {
      case 'card':
        drawCardTemplateSync(ctx, { content, author, date, watermark }, width, height);
        console.log('日卡模板绘制完成');
        break;
      case 'dynamic':
        drawDynamicTemplateSync(ctx, { content, author, date, watermark }, width, height);
        console.log('动态模板绘制完成');
        break;
      default:
        drawDefaultTemplateSync(ctx, { content, author, date, watermark }, width, height);
        console.log('默认模板绘制完成');
    }

    console.log('绘制完成（同步模式）');
    return true;
  } catch (error) {
    console.error('绘制过程中发生错误:', error);
    throw error;
  }
};

/**
 * 同步绘制背景
 */
const drawBackgroundSync = (ctx, width, height) => {
  try {
    // 使用渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#8EFFFE');
    gradient.addColorStop(1, '#C6E538');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    return true;
  } catch (error) {
    console.error('绘制背景失败:', error);
    return false;
  }
};



/**
 * 绘制背景
 */
const drawBackground = (ctx, backgroundImage, width, height) => {
  return new Promise((resolve, reject) => {
    if (!backgroundImage) {
      // 如果没有背景图，使用渐变背景
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#8EFFFE');
      gradient.addColorStop(1, '#C6E538');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      resolve();
      return;
    }

    // 获取图片信息并绘制
    uni.getImageInfo({
      src: backgroundImage,
      success: (res) => {
        try {
          ctx.drawImage(res.path, 0, 0, width, height);
          resolve();
        } catch (error) {
          console.error('绘制背景图失败:', error);
          // 降级到渐变背景
          const gradient = ctx.createLinearGradient(0, 0, 0, height);
          gradient.addColorStop(0, '#8EFFFE');
          gradient.addColorStop(1, '#C6E538');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, width, height);
          resolve();
        }
      },
      fail: (error) => {
        console.error('背景图获取失败:', error);
        // 降级到渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#8EFFFE');
        gradient.addColorStop(1, '#C6E538');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        resolve();
      }
    });
  });
};

/**
 * 默认模板绘制
 */
const drawDefaultTemplate = async (ctx, data, width, height) => {
  const { content, author, date, watermark } = data;

  // 绘制白色卡片背景
  const cardMargin = 40;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = height - (cardMargin * 2);
  const cardX = cardMargin;
  const cardY = cardMargin;

  // 绘制白色卡片
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  // 绘制内容文本
  if (content) {
    ctx.fillStyle = '#333333';
    ctx.font = '32px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const contentPadding = 60;
    const maxWidth = cardWidth - (contentPadding * 2);
    const lineHeight = 50;
    const contentY = cardY + 60;

    // 简化的文本换行处理
    const lines = wrapText(ctx, content, maxWidth);
    lines.forEach((line, index) => {
      if (index < 8) { // 限制行数
        ctx.fillText(line, cardX + contentPadding, contentY + index * lineHeight);
      }
    });
  }

  // 绘制作者和日期
  if (author || date) {
    const infoY = cardY + cardHeight - 60;

    if (author) {
      ctx.fillStyle = '#666666';
      ctx.font = '24px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText(author, cardX + 60, infoY);
    }

    if (date) {
      ctx.fillStyle = '#999999';
      ctx.font = '20px sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText(date, cardX + cardWidth - 60, infoY);
    }
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
    ctx.font = '20px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 文本换行处理
 */
const wrapText = (ctx, text, maxWidth) => {
  const lines = [];
  let line = '';

  // 简化的换行逻辑
  for (let i = 0; i < text.length; i++) {
    const testLine = line + text[i];
    const metrics = ctx.measureText(testLine);

    if (metrics.width > maxWidth && line.length > 0) {
      lines.push(line);
      line = text[i];
    } else {
      line = testLine;
    }
  }

  if (line) {
    lines.push(line);
  }

  return lines;
};

/**
 * 日卡模板绘制
 */
const drawCardTemplate = async (ctx, data, width, height) => {
  const { content, author, authorAvatar, date, watermark, backgroundImage } = data;

  console.log('绘制日卡模板，数据:', data);

  // 绘制白色内容卡片
  const cardMargin = 60;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = 400;
  const cardX = cardMargin;
  const cardY = height - cardHeight - 100; // 底部留出空间

  // 绘制卡片背景（圆角矩形）
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
  ctx.shadowBlur = 20;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 4;

  // 简化的圆角矩形
  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  // 重置阴影
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;

  // 绘制用户信息区域
  const userInfoY = cardY + 30;
  const avatarSize = 60;
  const avatarX = cardX + 30;

  // 绘制用户头像
  if (authorAvatar) {
    try {
      await drawUserAvatar(ctx, authorAvatar, avatarX, userInfoY, avatarSize);
    } catch (error) {
      console.error('绘制用户头像失败:', error);
      // 绘制默认头像背景
      ctx.fillStyle = '#E5E5E5';
      ctx.fillRect(avatarX, userInfoY, avatarSize, avatarSize);
    }
  } else {
    // 绘制默认头像背景
    ctx.fillStyle = '#E5E5E5';
    ctx.fillRect(avatarX, userInfoY, avatarSize, avatarSize);
  }

  // 绘制用户名和时间
  const userTextX = avatarX + avatarSize + 20;

  if (author) {
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 28px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(author, userTextX, userInfoY + 5);
  }

  if (date) {
    ctx.fillStyle = '#999999';
    ctx.font = '22px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const formattedDate = formatDate(date);
    ctx.fillText(formattedDate, userTextX, userInfoY + 35);
  }

  // 绘制内容文本
  if (content) {
    const contentY = userInfoY + 100;
    const contentPadding = 30;
    const maxWidth = cardWidth - (contentPadding * 2);

    ctx.fillStyle = '#333333';
    ctx.font = '30px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const lines = wrapText(ctx, content, maxWidth);
    const lineHeight = 45;

    lines.forEach((line, index) => {
      if (index < 5) { // 限制行数
        ctx.fillText(line, cardX + contentPadding, contentY + index * lineHeight);
      }
    });
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 动态模板绘制
 */
const drawDynamicTemplate = async (ctx, data, width, height) => {
  const { content, author, authorAvatar, date, watermark, images } = data;

  console.log('绘制动态模板，数据:', data);

  // 绘制白色内容卡片
  const cardMargin = 40;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = height * 0.6; // 占用60%高度
  const cardX = cardMargin;
  const cardY = (height - cardHeight) / 2; // 居中

  // 绘制卡片背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
  ctx.shadowBlur = 20;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 4;

  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  // 重置阴影
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;

  let currentY = cardY + 30;

  // 绘制用户信息区域
  const avatarSize = 60;
  const avatarX = cardX + 30;

  // 绘制用户头像
  if (authorAvatar) {
    try {
      await drawUserAvatar(ctx, authorAvatar, avatarX, currentY, avatarSize);
    } catch (error) {
      console.error('绘制用户头像失败:', error);
      // 绘制默认头像背景
      ctx.fillStyle = '#E5E5E5';
      ctx.fillRect(avatarX, currentY, avatarSize, avatarSize);
    }
  } else {
    // 绘制默认头像背景
    ctx.fillStyle = '#E5E5E5';
    ctx.fillRect(avatarX, currentY, avatarSize, avatarSize);
  }

  // 绘制用户名和时间
  const userTextX = avatarX + avatarSize + 20;

  if (author) {
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 28px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(author, userTextX, currentY + 5);
  }

  if (date) {
    ctx.fillStyle = '#999999';
    ctx.font = '22px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const formattedDate = formatDate(date);
    ctx.fillText(formattedDate, userTextX, currentY + 35);
  }

  currentY += 100;

  // 绘制内容文本
  if (content) {
    const contentPadding = 30;
    const maxWidth = cardWidth - (contentPadding * 2);

    ctx.fillStyle = '#333333';
    ctx.font = '30px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const lines = wrapText(ctx, content, maxWidth);
    const lineHeight = 45;

    lines.forEach((line, index) => {
      if (index < 6) { // 限制行数
        ctx.fillText(line, cardX + contentPadding, currentY + index * lineHeight);
      }
    });

    currentY += Math.min(lines.length, 6) * lineHeight + 30;
  }

  // 绘制内容图片（如果有）
  if (images && images.length > 0) {
    const imageSize = Math.min(200, (cardWidth - 60) / 2);
    const imageX = cardX + 30;

    try {
      await drawContentImage(ctx, images[0], imageX, currentY, imageSize, imageSize);
    } catch (error) {
      console.error('绘制内容图片失败:', error);
    }
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 绘制用户头像
 */
const drawUserAvatar = (ctx, avatarUrl, x, y, size) => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: avatarUrl,
      success: (res) => {
        try {
          // 绘制圆形头像
          ctx.save();
          ctx.beginPath();
          ctx.arc(x + size/2, y + size/2, size/2, 0, 2 * Math.PI);
          ctx.clip();
          ctx.drawImage(res.path, x, y, size, size);
          ctx.restore();
          resolve();
        } catch (error) {
          console.error('绘制头像失败:', error);
          reject(error);
        }
      },
      fail: (error) => {
        console.error('获取头像失败:', error);
        reject(error);
      }
    });
  });
};

/**
 * 绘制内容图片
 */
const drawContentImage = (ctx, imageUrl, x, y, width, height) => {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imageUrl,
      success: (res) => {
        try {
          // 计算图片缩放比例，保持宽高比
          const imgRatio = res.width / res.height;
          const targetRatio = width / height;

          let drawWidth = width;
          let drawHeight = height;
          let drawX = x;
          let drawY = y;

          if (imgRatio > targetRatio) {
            // 图片更宽，以高度为准
            drawWidth = height * imgRatio;
            drawX = x - (drawWidth - width) / 2;
          } else {
            // 图片更高，以宽度为准
            drawHeight = width / imgRatio;
            drawY = y - (drawHeight - height) / 2;
          }

          // 裁剪区域
          ctx.save();
          ctx.beginPath();
          ctx.rect(x, y, width, height);
          ctx.clip();

          ctx.drawImage(res.path, drawX, drawY, drawWidth, drawHeight);
          ctx.restore();
          resolve();
        } catch (error) {
          console.error('绘制内容图片失败:', error);
          reject(error);
        }
      },
      fail: (error) => {
        console.error('获取内容图片失败:', error);
        reject(error);
      }
    });
  });
};

/**
 * 格式化日期
 */
const formatDate = (dateStr) => {
  try {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // 小于1小时显示分钟
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
    }

    // 小于24小时显示小时
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours}小时前`;
    }

    // 小于7天显示天数
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days}天前`;
    }

    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化失败:', error);
    return '未知时间';
  }
};



/**
 * 简化的默认模板绘制
 */
const drawDefaultTemplateSync = (ctx, data, width, height) => {
  const { content, author, date, watermark } = data;

  // 绘制白色卡片背景
  const cardMargin = 40;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = height - (cardMargin * 2);
  const cardX = cardMargin;
  const cardY = cardMargin;

  // 绘制白色卡片
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  // 绘制内容文本
  if (content) {
    ctx.fillStyle = '#333333';
    ctx.font = '32px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const contentPadding = 60;
    const maxWidth = cardWidth - (contentPadding * 2);
    const lineHeight = 50;
    const contentY = cardY + 60;

    // 简化的文本换行处理
    const lines = wrapText(ctx, content, maxWidth);
    lines.forEach((line, index) => {
      if (index < 8) { // 限制行数
        ctx.fillText(line, cardX + contentPadding, contentY + index * lineHeight);
      }
    });
  }

  // 绘制作者和日期
  if (author || date) {
    const infoY = cardY + cardHeight - 60;

    if (author) {
      ctx.fillStyle = '#666666';
      ctx.font = '24px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText(author, cardX + 60, infoY);
    }

    if (date) {
      ctx.fillStyle = '#999999';
      ctx.font = '20px sans-serif';
      ctx.textAlign = 'right';
      const formattedDate = formatDateSimple(date);
      ctx.fillText(formattedDate, cardX + cardWidth - 60, infoY);
    }
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
    ctx.font = '20px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 简化的日卡模板绘制
 */
const drawCardTemplateSync = (ctx, data, width, height) => {
  const { content, author, date, watermark } = data;

  // 绘制白色内容卡片
  const cardMargin = 60;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = 400;
  const cardX = cardMargin;
  const cardY = height - cardHeight - 100;

  // 绘制卡片背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  // 绘制用户信息区域（简化版，不绘制头像）
  const userInfoY = cardY + 30;

  if (author) {
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 28px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(author, cardX + 30, userInfoY + 5);
  }

  if (date) {
    ctx.fillStyle = '#999999';
    ctx.font = '22px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const formattedDate = formatDateSimple(date);
    ctx.fillText(formattedDate, cardX + 30, userInfoY + 35);
  }

  // 绘制内容文本
  if (content) {
    const contentY = userInfoY + 80;
    const contentPadding = 30;
    const maxWidth = cardWidth - (contentPadding * 2);

    ctx.fillStyle = '#333333';
    ctx.font = '30px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const lines = wrapText(ctx, content, maxWidth);
    const lineHeight = 45;

    lines.forEach((line, index) => {
      if (index < 5) {
        ctx.fillText(line, cardX + contentPadding, contentY + index * lineHeight);
      }
    });
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 简化的动态模板绘制
 */
const drawDynamicTemplateSync = (ctx, data, width, height) => {
  const { content, author, date, watermark } = data;

  // 绘制白色内容卡片
  const cardMargin = 40;
  const cardWidth = width - (cardMargin * 2);
  const cardHeight = height * 0.6;
  const cardX = cardMargin;
  const cardY = (height - cardHeight) / 2;

  // 绘制卡片背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.fillRect(cardX, cardY, cardWidth, cardHeight);

  let currentY = cardY + 30;

  // 绘制用户信息（简化版，不绘制头像）
  if (author) {
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 28px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText(author, cardX + 30, currentY + 5);
  }

  if (date) {
    ctx.fillStyle = '#999999';
    ctx.font = '22px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    const formattedDate = formatDateSimple(date);
    ctx.fillText(formattedDate, cardX + 30, currentY + 35);
  }

  currentY += 80;

  // 绘制内容文本
  if (content) {
    const contentPadding = 30;
    const maxWidth = cardWidth - (contentPadding * 2);

    ctx.fillStyle = '#333333';
    ctx.font = '30px sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const lines = wrapText(ctx, content, maxWidth);
    const lineHeight = 45;

    lines.forEach((line, index) => {
      if (index < 6) {
        ctx.fillText(line, cardX + contentPadding, currentY + index * lineHeight);
      }
    });
  }

  // 绘制水印
  if (watermark) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.font = '24px sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    ctx.fillText(watermark, width / 2, height - 30);
  }
};

/**
 * 简化的日期格式化
 */
const formatDateSimple = (dateStr) => {
  try {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // 小于1小时显示分钟
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
    }

    // 小于24小时显示小时
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours}小时前`;
    }

    // 超过24小时显示日期
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return '未知时间';
  }
};

export default {
  generateShareImage,
  saveImageToAlbum
};
