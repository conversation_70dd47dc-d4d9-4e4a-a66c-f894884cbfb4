<?php
namespace controller;
use core\Controller;
use core\Db;
class Area extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	//获取IP对应地理位置
	public function get_addr_for_ip($ip=""){
		responseType("json");
		if(empty($ip)){
			$ip = IP;
		}else if(!check($ip,"ip")){
			return ["status"=>"error","msg"=>"IP格式错误"];
		}
		$url = "https://whois.pconline.com.cn/ip.jsp?ip=" . $ip;
		//$url = "https://whois.pconline.com.cn/ip.jsp?level=3&ip=" . $ip;
		$header = [
			"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
			"Accept-Language: zh-CN,zh;q=0.9",
			"Cache-Control: max-age=0",
			'Sec-Ch-Ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
			'Sec-Ch-Ua-Mobile: ?0',
			'Sec-Ch-Ua-Platform: "Windows"',
			'Sec-Fetch-Dest: document',
			'Sec-Fetch-Mode: navigate',
			'Sec-Fetch-Site: none',
			'Sec-Fetch-User: ?1',
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Cookie: u=25jw62s8; c=13jw5jrx; u4ad=291jpa13n; pcsuv=1694421271791.a.13982354; pcuvdata=lastAccessTime=1703120780642|visits=3; channel=2",
		];
		$res = curl($url,null,false,$header);
		//dump($res);exit;
		if(empty($res) || !isset($res['body']) || empty($res['body']) || mb_strlen($res['body']) > 40 || check($res['body'],"integt0")){
			return ["status"=>"error","msg"=>"获取失败"];
		}
		$addr = $res['body'];
		$addr = iconv('GB2312', 'UTF-8', $addr);
		$addr = trim($addr);
		return ["status"=>"ok","addr"=>$addr];
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
