/**
 * uni-share组件配置工具
 * 用于统一管理分享配置和逻辑
 */

import uniShare from '@/components/share-popup/uni-share.js';

/**
 * 创建分享配置
 * @param {Object} options 分享选项
 * @param {String} options.title 分享标题
 * @param {String} options.summary 分享描述
 * @param {String} options.href 分享链接
 * @param {String} options.imageUrl 分享图片
 * @param {String} options.type 分享类型
 * @returns {Object} 分享配置对象
 */
export const createShareConfig = (options) => {
  const {
    title = '分享内容',
    summary = '来自MindfulMeetUp & 小聚会',
    href = '',
    imageUrl = '',
    type = 0
  } = options;

  return {
    content: {
      type,
      href,
      title,
      summary,
      imageUrl
    },
    menus: [
      {
        "img": "/static/app-plus/sharemenu/wechatfriend.png",
        "text": "微信好友",
        "share": {
          "provider": "weixin",
          "scene": "WXSceneSession"
        }
      },
      {
        "img": "/static/app-plus/sharemenu/wechatmoments.png",
        "text": "微信朋友圈",
        "share": {
          "provider": "weixin",
          "scene": "WXSceneTimeline"
        }
      },
      {
        "img": "/static/app-plus/sharemenu/copyurl.png",
        "text": "复制链接",
        "share": "copyurl"
      },
      {
        "img": "/static/app-plus/sharemenu/more.png",
        "text": "更多",
        "share": "shareSystem"
      }
    ],
    cancelText: "取消分享"
  };
};

/**
 * 创建日卡分享配置
 * @param {Object} cardData 日卡数据
 * @returns {Object} 分享配置
 */
export const createCardShareConfig = (cardData) => {
  const {
    id,
    description = '',
    background_image_url = '',
    author = '匿名用户'
  } = cardData;

  const title = description.length > 30 
    ? description.substring(0, 30) + '...' 
    : (description || '分享一张精美日卡');

  const summary = `${author}分享了一张日卡：${description || '精美内容'}`;
  
  // #ifdef MP-WEIXIN
  const href = `/pages/bundle/world/card/detail?cardId=${id}`;
  // #endif
  
  // #ifndef MP-WEIXIN
  const href = `https://example.com/card?id=${id}`;
  // #endif

  return createShareConfig({
    title,
    summary,
    href,
    imageUrl: background_image_url,
    type: 0
  });
};

/**
 * 创建动态分享配置
 * @param {Object} feedData 动态数据
 * @returns {Object} 分享配置
 */
export const createFeedShareConfig = (feedData) => {
  const {
    id,
    content = '',
    images = [],
    user = {},
    location = ''
  } = feedData;

  const title = content.length > 30 
    ? content.substring(0, 30) + '...' 
    : (content || '分享一条精彩动态');

  const locationText = location ? ` 📍${location}` : '';
  const summary = `${user.nickname || '匿名用户'}分享了一条动态：${content || '精彩内容'}${locationText}`;
  
  // #ifdef MP-WEIXIN
  const href = `/pages/bundle/world/feed/detail?feedId=${id}`;
  // #endif
  
  // #ifndef MP-WEIXIN
  const href = `https://example.com/feed?id=${id}`;
  // #endif

  const imageUrl = images && images.length > 0 ? images[0] : '';

  return createShareConfig({
    title,
    summary,
    href,
    imageUrl,
    type: 0
  });
};

/**
 * 显示分享菜单
 * @param {Object} shareConfig 分享配置
 * @param {Function} callback 回调函数
 * @returns {Promise}
 */
export const showShareMenu = (shareConfig, callback) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('显示uni-share分享菜单:', shareConfig);
      
      uniShare.show(shareConfig, (result) => {
        console.log('uni-share回调结果:', result);
        
        if (callback) {
          callback(result);
        }
        
        if (result.event === 'clickMenu') {
          resolve({
            success: true,
            event: 'share',
            index: result.index
          });
        } else if (result.event === 'clickMask') {
          resolve({
            success: false,
            event: 'cancel'
          });
        }
      });
    } catch (error) {
      console.error('显示分享菜单失败:', error);
      reject(error);
    }
  });
};

/**
 * 隐藏分享菜单
 */
export const hideShareMenu = () => {
  try {
    uniShare.hide();
  } catch (error) {
    console.warn('隐藏分享菜单失败:', error);
  }
};

/**
 * 检查分享菜单是否显示
 * @returns {Boolean}
 */
export const isShareMenuShow = () => {
  try {
    return uniShare.isShow;
  } catch (error) {
    console.warn('检查分享菜单状态失败:', error);
    return false;
  }
};

/**
 * 微信小程序专用分享处理
 * @param {Object} shareData 分享数据
 * @param {String} type 分享类型：'friend' | 'timeline'
 */
export const handleWechatShare = (shareData, type = 'friend') => {
  // #ifdef MP-WEIXIN
  try {
    if (type === 'timeline') {
      // 朋友圈分享
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
      
      uni.showToast({
        title: '请点击右上角分享到朋友圈',
        icon: 'none'
      });
    } else {
      // 好友分享
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage']
      });
      
      uni.showToast({
        title: '请点击右上角分享给好友',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('微信分享失败:', error);
    uni.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
  // #endif
  
  // #ifndef MP-WEIXIN
  // 非微信小程序环境，使用uni-share
  const shareConfig = createShareConfig({
    title: shareData.title,
    summary: shareData.summary,
    href: shareData.href,
    imageUrl: shareData.imageUrl
  });
  
  showShareMenu(shareConfig).catch(error => {
    console.error('分享失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  });
  // #endif
};

/**
 * 统一的分享处理函数
 * @param {Object} data 分享数据
 * @param {String} dataType 数据类型：'card' | 'feed' | 'custom'
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 错误回调
 */
export const handleShare = async (data, dataType = 'custom', onSuccess, onError) => {
  try {
    let shareConfig;
    
    switch (dataType) {
      case 'card':
        shareConfig = createCardShareConfig(data);
        break;
      case 'feed':
        shareConfig = createFeedShareConfig(data);
        break;
      default:
        shareConfig = createShareConfig(data);
    }
    
    const result = await showShareMenu(shareConfig);
    
    if (result.success) {
      console.log('分享成功:', result);
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
      
      if (onSuccess) {
        onSuccess(result);
      }
    } else {
      console.log('用户取消分享');
    }
  } catch (error) {
    console.error('分享失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
    
    if (onError) {
      onError(error);
    }
  }
}