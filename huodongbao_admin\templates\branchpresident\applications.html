<fieldset class="table-search-fieldset">
    <legend>分会长申请管理</legend>
    <div style="margin: 10px 10px 10px 10px">
        <form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="post" id="search">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">申请状态</label>
                    <div class="layui-input-inline">
                        <select name="status" value="<?php if(isset($status))echo $status; ?>">
                            <option value="all">全部</option>
                            <option value="0">待审核</option>
                            <option value="1">已通过</option>
                            <option value="2">已拒绝</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">申请人</label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_name" value="<?php if(!empty($_REQUEST['user_name']))echo $_REQUEST['user_name']; ?>" placeholder="请输入申请人昵称" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">分会名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="branch_name" value="<?php if(!empty($_REQUEST['branch_name']))echo $_REQUEST['branch_name']; ?>" placeholder="请输入分会名称" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</fieldset>

<fieldset class="table-search-fieldset">
    <legend>申请列表</legend>
    <div style="margin: 10px 10px 10px 10px">
        <table class="layui-table" lay-even lay-skin="nob">
            <colgroup>
                <col width="80">
                <col width="120">
                <col width="150">
                <col width="200">
                <col width="150">
                <col width="120">
                <col width="150">
                <col width="200">
            </colgroup>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>申请人</th>
                    <th>手机号</th>
                    <th>分会名称</th>
                    <th>申请地区</th>
                    <th>申请时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(!empty($data)): ?>
                    <?php foreach($data as $row): ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo htmlspecialchars($row['user_nickname']); ?></td>
                        <td><?php echo htmlspecialchars($row['user_mobile']); ?></td>
                        <td><?php echo htmlspecialchars($row['branch_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['branch_location']); ?></td>
                        <td><?php echo $row['application_time']; ?></td>
                        <td>
                            <?php 
                            switch($row['status']) {
                                case 0:
                                    echo '<span class="layui-badge layui-bg-orange">待审核</span>';
                                    break;
                                case 1:
                                    echo '<span class="layui-badge layui-bg-green">已通过</span>';
                                    break;
                                case 2:
                                    echo '<span class="layui-badge">已拒绝</span>';
                                    break;
                            }
                            ?>
                        </td>
                        <td>
                            <a class="layui-btn layui-btn-xs" href="/branchpresident/application_detail?id=<?php echo $row['id']; ?>">查看详情</a>
                            <?php if($row['status'] == 0): ?>
                            <a class="layui-btn layui-btn-xs layui-btn-normal" href="javascript:void(0)" onclick="reviewApplication(<?php echo $row['id']; ?>, 1)">通过</a>
                            <a class="layui-btn layui-btn-xs layui-btn-danger" href="javascript:void(0)" onclick="reviewApplication(<?php echo $row['id']; ?>, 2)">拒绝</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" style="text-align: center; color: #999;">暂无数据</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <?php echo pageRender(); ?>
    </div>
</fieldset>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 搜索表单提交
    form.on('submit(search)', function(data){
        var params = [];
        for(var key in data.field) {
            if(data.field[key] && data.field[key] !== 'all') {
                params.push(key + '=' + encodeURIComponent(data.field[key]));
            }
        }
        window.location.href = '/branchpresident/applications?' + params.join('&');
        return false;
    });
    
    // 设置选中状态
    <?php if(isset($status)): ?>
    $('select[name="status"]').val('<?php echo $status; ?>');
    <?php endif; ?>
    
    form.render('select');
});

// 审核申请
function reviewApplication(id, status) {
    var title = status == 1 ? '通过申请' : '拒绝申请';
    var content = '<div style="padding: 20px;">';
    content += '<div class="layui-form-item">';
    content += '<label class="layui-form-label">审核意见：</label>';
    content += '<div class="layui-input-block">';
    content += '<textarea id="review_comment" placeholder="请输入审核意见（可选）" class="layui-textarea"></textarea>';
    content += '</div>';
    content += '</div>';
    content += '</div>';
    
    layer.open({
        type: 1,
        title: title,
        content: content,
        area: ['500px', '300px'],
        btn: ['确认', '取消'],
        yes: function(index, layero) {
            var comment = $('#review_comment').val();
            
            // 提交审核
            $.post('/branchpresident/review_application', {
                id: id,
                status: status,
                comment: comment
            }, function(response) {
                layer.close(index);
                if(response.indexOf('成功') > -1) {
                    layer.msg('审核完成', {icon: 1}, function() {
                        window.location.reload();
                    });
                } else {
                    layer.msg('审核失败', {icon: 2});
                }
            });
        }
    });
}
</script>
