<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { goodsshenqing_tuikuan } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { back } from "@/utils";
const form = ref({
  order_id: "",
  msg: "",
});

onLoad((e) => {
  form.value.order_id = e.order_id;
});

const submit = async () => {
  if (form.value.msg) {
    const res = await goodsshenqing_tuikuan(form.value);
    if (res.status === "ok") back({ tip: "提交成功，即将返回", time: 2000 });
    else uni.$u.toast(res.msg)
  }
};
</script>
<template>
  <view class="page p30">
    <u-textarea
      v-model="form.msg"
      placeholder="请输入退款理由~"
      count
      maxlength="200"
    ></u-textarea>
    <u-button
      shape="circle"
      color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
      text="提交"
      :customStyle="{ margin: '50rpx auto 0', color: '#333' }"
      @click="submit"
    ></u-button>
  </view>
</template>

<style scoped lang="less"></style>
