<fieldset class="layui-elem-field layui-field-title">
    <legend>佣金记录详情</legend>
</fieldset>

<div class="layui-form">
    <div class="layui-row layui-col-space15">
        <!-- 基本信息 -->
        <div class="layui-col-md6">
            <fieldset class="layui-elem-field">
                <legend>基本信息</legend>
                <div class="layui-field-box">
                    <table class="layui-table">
                        <tbody>
                            <tr>
                                <td width="120">记录ID：</td>
                                <td><?php echo $record['id']; ?></td>
                            </tr>
                            <tr>
                                <td>用户昵称：</td>
                                <td><?php echo htmlspecialchars($record['user_nickname']); ?></td>
                            </tr>
                            <tr>
                                <td>手机号码：</td>
                                <td><?php echo $record['user_mobile']; ?></td>
                            </tr>
                            <tr>
                                <td>所属分会：</td>
                                <td><?php echo htmlspecialchars($record['branch_name']); ?></td>
                            </tr>
                            <tr>
                                <td>佣金类型：</td>
                                <td>
                                    <?php if($record['commission_type'] == 'operation'): ?>
                                        <span class="layui-badge layui-bg-blue">运营佣金</span>
                                    <?php else: ?>
                                        <span class="layui-badge layui-bg-green">邀请佣金</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>佣金金额：</td>
                                <td class="layui-text-em" style="color: #ff5722; font-weight: bold;">
                                    ￥<?php echo number_format($record['money'], 2); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>创建月份：</td>
                                <td><?php echo date('Y-m', $record['time']); ?></td>
                            </tr>
                            <tr>
                                <td>创建时间：</td>
                                <td><?php echo $record['create_time_formatted']; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>
        
        <!-- 详细信息 -->
        <div class="layui-col-md6">
            <fieldset class="layui-elem-field">
                <legend>详细信息</legend>
                <div class="layui-field-box">
                    <?php if($record['commission_type'] == 'operation'): ?>
                        <!-- 运营佣金详情 -->
                        <table class="layui-table">
                            <tbody>
                                <?php if(!empty($record['activity_info'])): ?>
                                <tr>
                                    <td width="120">关联活动：</td>
                                    <td><?php echo htmlspecialchars($record['activity_info']['name']); ?></td>
                                </tr>
                                <tr>
                                    <td>活动标题：</td>
                                    <td><?php echo htmlspecialchars($record['activity_info']['title']); ?></td>
                                </tr>
                                <tr>
                                    <td>活动时间：</td>
                                    <td><?php echo $record['activity_info']['time']; ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td>佣金说明：</td>
                                    <td>分会长审核活动获得的运营佣金</td>
                                </tr>
                                <tr>
                                    <td>计算方式：</td>
                                    <td>按活动审核数量和设定比例计算</td>
                                </tr>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <!-- 邀请佣金详情 -->
                        <table class="layui-table">
                            <tbody>
                                <tr>
                                    <td width="120">佣金说明：</td>
                                    <td>邀请用户注册获得的推广佣金</td>
                                </tr>
                                <tr>
                                    <td>计算方式：</td>
                                    <td>按邀请用户数量和设定比例计算</td>
                                </tr>
                                <tr>
                                    <td>结算周期：</td>
                                    <td>按月结算</td>
                                </tr>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </fieldset>
        </div>
    </div>
    
    <!-- 备注信息 -->
    <?php if(!empty($record['remark'])): ?>
    <fieldset class="layui-elem-field">
        <legend>备注信息</legend>
        <div class="layui-field-box">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block">
                    <textarea readonly class="layui-textarea"><?php echo htmlspecialchars($record['remark']); ?></textarea>
                </div>
            </div>
        </div>
    </fieldset>
    <?php endif; ?>
    
    <!-- 操作按钮 -->
    <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back();">
            <i class="layui-icon">&#xe65c;</i> 返回列表
        </button>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 页面加载完成提示
    layer.msg('佣金记录详情加载完成', {icon: 1, time: 1000});
});
</script>
