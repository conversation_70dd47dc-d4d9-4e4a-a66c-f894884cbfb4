<script setup>
import { watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app";
import { store } from "@/store";

const props = defineProps({
  popupShow: {
    type: Boolean,
    default: false,
  },
  src: {
    type: String
  },
  ipt: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(["update:popupShow", "submit"]);

const baomingForm = ref({
  lianxi_name: store().$state.userInfo.nickname,
  lianxi_mobile: store().$state.userInfo.mobile,
});
</script>
<template>
  <view class="">
    <u-popup
      :show="popupShow"
      :close-on-click-overlay="true"
      round="30rpx"
      mode="center"
      :safe-area-inset-bottom="false"
      @close="emit('update:popupShow', false)"
    >
      <view class="pt50 pb50 px30 w690 r30">
        <template v-if="ipt">
          <view class="mb50 df aic jcc fb x34"> 报名信息输入框 </view>
          <u-input
            type="text"
            :adjust-position="false"
            border="surround"
            shape="circle"
            placeholder="请输入姓名"
            v-model="baomingForm.lianxi_name"
          ></u-input>
          <u-gap height="50rpx" bg-color=""></u-gap>
          <u-input
            type="number"
            :adjust-position="false"
            border="surround"
            shape="circle"
            placeholder="请输入联系方式"
            v-model="baomingForm.lianxi_mobile"
          ></u-input>
          <u-gap height="50rpx"></u-gap>
          <u-button
            shape="circle"
            color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
            :customStyle="{
              color: '#000',
            }"
            text="提交"
            @click="emit('submit', baomingForm)"
          ></u-button>
        </template>
        <template v-else>
          <view class="df aic jcc mb20">
            <u-image :src="src" width="200rpx" height="200rpx"></u-image>
          </view>
          <u-text
            text="长按识别二维码，以便联系活动发起人"
            align="center"
            bold
            color="#333"
            size="32rpx"
          ></u-text>
        </template>
      </view>
    </u-popup>
  </view>
</template>

<style scoped lang="less"></style>
