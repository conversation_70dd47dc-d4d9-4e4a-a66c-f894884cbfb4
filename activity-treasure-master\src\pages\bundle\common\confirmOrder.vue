<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  goodsadd_order,
  userget_addr_list,
  payget_weixinpay_sign,
  payweixin_pay,
  payyue_pay,
  dalibaoadd_order,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onUnload,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto, pay } from "@/utils";

const address = ref({});
const goodsList = ref([]);
const allMoney = ref(0);
const radiovalue = ref("微信支付");
const radiolist = ref([{ name: "微信支付" }, { name: "余额支付" }]);
const vip = ref(false);

onLoad(async (e) => {
  goodsList.value = store().$state.goodsList;
  goodsList.value.forEach((val) => {
    if (store().$state.userInfo.is_huiyuan && val.is_shiyong) allMoney.value += 0;
    else allMoney.value += val.money * val.num;
  });
  if (e?.vip) vip.value = e.vip;
});
onShow(async () => {
  if (store().$state.addr.id) address.value = store().$state.addr;
  else {
    const res = await userget_addr_list();
    if (res != "n") {
      let addressList = res.data.filter((val) => val.is_default === 1);
      if (addressList.length > 0) address.value = addressList[0];
      else
        uni.showModal({
          title: "提示！",
          content: "还没选择收货地址，快去添加收货地址吧！",
          complete(res) {
            if (res.confirm) navto("/pages/bundle/common/address?type=select");
            else
              uni.switchTab({
                url: "/pages/index",
              });
          },
        });
    } else
      uni.showModal({
        title: "提示！",
        content: "还没选择收货地址，快去添加收货地址吧！",
        complete(res) {
          if (res.confirm) navto("/pages/bundle/common/address?type=select");
          else
            uni.switchTab({
              url: "/pages/index",
            });
        },
      });
    //页面显示时，加载订单信息
  }
});
// 退出页面清空存储的地址
onUnload(() => {
  store().changeAddr({});
});

// 选择支付方式
const groupChange = (e) => {};
// 去支付
const goPay = async (e) => {
  let goods_info = [];
  goodsList.value.map((val) => {
    goods_info.push({
      goods_id: val.goods_id,
      guige_id: val.guige_id,
      num: val.num,
      money: store().$state.userInfo.is_huiyuan && val.is_shiyong ? 0 : val.money,
    });
  });
  const api = vip.value
    ? dalibaoadd_order({
        addr_id: address.value.id,
      })
    : goodsadd_order({
        goods_info: JSON.stringify(goods_info),
        addr_id: address.value.id,
      });
  const res = await api;
  if (res.status === "ok") {
    const obj = { order_id: res.order_id, money: res.pay_money };
    if (res.pay_money * 1 == 0) {
      uni.$u.toast("支付成功，2秒后将返回首页");
      setTimeout(() => uni.switchTab({ url: "/pages/index" }), 2000);
      return;
    } else {
      let payRes;
      if (radiovalue.value === "余额支付") {
        payRes = await payyue_pay({ ...obj, type: vip.value ? 4 : 1 });
        if (payRes?.status === "ok") {
          uni.$u.toast("支付成功，2秒后将返回首页");
          setTimeout(() => uni.switchTab({ url: "/pages/index" }), 2000);
        } else uni.$u.toast(payRes?.msg);
      } else {
        const wxRes = await payweixin_pay({ ...obj, type: vip.value ? 5 : 1 });
        if (wxRes.status === "ok") {
          const signRes = await payget_weixinpay_sign({ prepay_id: wxRes.prepay_id });
          payRes = await pay(signRes);
          if (payRes.errMsg === "requestPayment:ok") {
            uni.$u.toast("支付成功，2秒后将返回首页");
            setTimeout(() => uni.switchTab({ url: "/pages/index" }), 2000);
          } else payRes.msg = "支付失败";
        }
      }
    }
  }
};
</script>
<template>
  <view class="page b6f">
    <view class="df aic p30" @click="navto(`/pages/bundle/common/address`)">
      <view class="df fdc f1">
        <view class="df aic mb20">
          <view class="mr50">
            <u-text color="#2B2B2B" size="28rpx" :text="address.username"></u-text>
          </view>
          <u-text color="#2B2B2B" size="28rpx" :text="address.mobile"></u-text>
        </view>
        <u-text
          color="#7A7A7A"
          size="24rpx"
          :text="address.sheng + address.shi + address.qu + address.addr"
        ></u-text>
      </view>
      <u-icon name="arrow-right" size="44rpx" color="#9d9d9d"></u-icon>
    </view>
    <u-image
      width="750rpx"
      height="4rpx"
      :src="`${store().$state.url}placeBg.png`"
    ></u-image>
    <u-gap height="20rpx" bg-color="#F6F6F6"></u-gap>
    <view class="p30">
      <u-text margin="0 0 30rpx" text="订单商品" color="#333" bold size="30rpx"></u-text>
      <view class="df mb30 r30" v-for="val in goodsList" :key="val.goods_id">
        <u-image width="160rpx" height="160rpx" radius="10rpx" :src="val.img"></u-image>
        <view class="ml20 df fdc jcsa f1">
          <view>
            <u-text size="28rpx" color="#2C2C2C" :text="val.name"></u-text>
          </view>
          <view class="df" :class="[vip ? 'ais' : 'aic']">
            <view>
              <u-text size="22rpx" color="#AAAAAA" text="规格："></u-text>
            </view>
            <template v-if="vip">
              <view>
                <u-text
                  v-for="(value, index) in val.guige"
                  :key="index"
                  size="22rpx"
                  color="#AAAAAA"
                  :text="`${index}:${value},`"
                ></u-text>
              </view>
            </template>
            <template v-else>
              <view v-for="(value, index) in val.guige" :key="index">
                <u-text
                  size="22rpx"
                  color="#AAAAAA"
                  :text="`${index}:${value},`"
                ></u-text>
              </view>
            </template>
          </view>
          <view class="df aic jcsb">
            <u-text
              mode="price"
              color="#FF3333"
              size="28rpx"
              :text="`${$u.priceFormat(
                store().$state.userInfo.is_huiyuan && val.is_shiyong
                  ? 0
                  : (Math.round(val.money * 100) * val.num) / 100,
                2
              )}`"
            ></u-text>
            <u-text align="right" size="#FF3333" :text="`x${val.num}`"></u-text>
          </view>
        </view>
      </view>
    </view>
    <u-gap height="20rpx" bg-color="#F6F6F6"></u-gap>
    <view class="p30">
      <u-cell
        :border="false"
        title="商品："
        title-style="font-size:28rpx;color:#676767"
        :value="`共${1}件商品`"
      ></u-cell>
      <u-cell :border="false" title="合计：" title-style="font-size:28rpx;color:#676767">
        <template #value>
          <u-text
            align="right"
            size="28rpx"
            mode="price"
            color="#FF4444"
            :text="$u.priceFormat(allMoney, 2)"
          ></u-text>
        </template>
      </u-cell>
      <!-- <u-cell
        :border="false"
        title="物流单号："
        title-style="font-size:28rpx;color:#676767"
        :value="123"
      ></u-cell> -->
      <!-- <u-cell
        :border="false"
        title="付款时间："
        title-style="font-size:28rpx;color:#676767"
        :value="123"
      ></u-cell> -->
      <!-- <u-cell
        :border="false"
        title="发货时间："
        title-style="font-size:28rpx;color:#676767"
        :value="123"
      ></u-cell> -->
      <!-- <u-cell
        :border="false"
        title="订单编号："
        title-style="font-size:28rpx;color:#676767"
        :value="123"
      ></u-cell> -->
      <view class="px30">
        <u-radio-group
          v-model="radiovalue"
          placement="column"
          iconPlacement="right"
          @change="groupChange"
        >
          <u-radio
            :customStyle="{ marginBottom: '8px' }"
            v-for="(val, i) in radiolist"
            :key="i"
            :label="val.name"
            :name="val.name"
          >
          </u-radio>
        </u-radio-group>
      </view>
    </view>
    <view class="pfx bottom0 w b6f">
      <view class="df aic jcr">
        <view>
          <u-text align="right" size="28rpx" color="#FF4444" text="应付："></u-text>
        </view>
        <view>
          <u-text
            align="right"
            size="30rpx"
            mode="price"
            color="#FF4444"
            :text="$u.priceFormat(allMoney, 2)"
          ></u-text>
        </view>
        <u-button
          color="#FAD000"
          text="支付"
          :customStyle="{
            margin: '0 0 0 26rpx',
            width: '241rpx',
            height: '96rpx',
            color: '#000',
            fontSize: '32rpx',
          }"
          @click="$u.debounce(goPay, 1000)"
        ></u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
