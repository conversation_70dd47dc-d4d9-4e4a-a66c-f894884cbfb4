/**
 * Painter库配置工具
 * 用于生成分享图片的JSON配置
 */

/**
 * 创建日卡分享图片配置
 * @param {Object} options 配置选项
 * @param {String} options.backgroundImage 背景图片URL
 * @param {String} options.content 内容文本
 * @param {String} options.author 作者名称
 * @param {String} options.authorAvatar 作者头像URL
 * @param {String} options.date 日期
 * @param {String} options.watermark 水印文本
 * @returns {Object} Painter配置对象
 */
export const createCardShareConfig = (options) => {
  console.log('🔧 createCardShareConfig 输入参数:', options);

  const {
    image,
    backgroundImage,
    content = '',
    author = '匿名用户',
    authorAvatar = '',
    date = '',
    watermark = '分享自小聚会'
  } = options;

  // 修复：优先使用 image 字段，然后是 backgroundImage
  const finalBackgroundImage = image || backgroundImage;

  console.log('🔧 解析后的参数:', {
    image,
    backgroundImage,
    finalBackgroundImage,
    content: content?.substring(0, 50) + '...',
    author,
    authorAvatar,
    date,
    watermark
  });

  // 验证背景图片
  if (!finalBackgroundImage) {
    console.warn('⚠️ 警告：背景图片为空，将使用默认图片');
  }

  const config = {
    width: '750rpx',
    height: '1334rpx',
    background: '#f8f9fa',
    views: [
      // 背景图片
      {
        type: 'image',
        url: finalBackgroundImage || '/static/default-card-bg.jpg',
        css: {
          top: '0rpx',
          left: '0rpx',
          width: '750rpx',
          height: '1334rpx',
          mode: 'aspectFill'
        }
      },
      // 背景遮罩
      {
        type: 'rect',
        css: {
          top: '0rpx',
          left: '0rpx',
          width: '750rpx',
          height: '1334rpx',
          color: 'linear-gradient(180deg, rgba(36, 93, 60, 0.3) 0%, rgba(36, 93, 60, 0.6) 100%)'
        }
      },
      // 内容卡片背景
      {
        type: 'rect',
        css: {
          top: '200rpx',
          left: '60rpx',
          width: '630rpx',
          height: '800rpx',
          color: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '24rpx',
          shadow: '0 8rpx 32rpx rgba(36, 93, 60, 0.15)'
        }
      },
      // 作者头像
      authorAvatar ? {
        type: 'image',
        url: authorAvatar,
        css: {
          top: '240rpx',
          left: '100rpx',
          width: '80rpx',
          height: '80rpx',
          borderRadius: '40rpx',
          border: '4rpx solid #ffffff'
        }
      } : null,
      // 作者名称
      {
        type: 'text',
        text: author,
        css: {
          top: authorAvatar ? '250rpx' : '260rpx',
          left: authorAvatar ? '200rpx' : '100rpx',
          fontSize: '32rpx',
          color: '#245D3C',
          fontWeight: '600',
          maxLines: 1,
          width: '400rpx'
        }
      },
      // 日期
      date ? {
        type: 'text',
        text: date,
        css: {
          top: authorAvatar ? '290rpx' : '300rpx',
          left: authorAvatar ? '200rpx' : '100rpx',
          fontSize: '24rpx',
          color: '#73C088',
          maxLines: 1,
          width: '400rpx'
        }
      } : null,
      // 内容文本
      {
        type: 'text',
        text: content,
        css: {
          top: '380rpx',
          left: '100rpx',
          width: '550rpx',
          fontSize: '36rpx',
          color: '#333333',
          lineHeight: '54rpx',
          maxLines: 12,
          textAlign: 'left',
          wordBreak: 'break-all'
        }
      },
      // 水印
      {
        type: 'text',
        text: watermark,
        css: {
          top: '1050rpx',
          left: '100rpx',
          fontSize: '24rpx',
          color: '#A5E1B8',
          maxLines: 1,
          width: '550rpx'
        }
      },
      // 小程序码占位（可选）
      {
        type: 'rect',
        css: {
          top: '1100rpx',
          right: '100rpx',
          width: '120rpx',
          height: '120rpx',
          color: '#ffffff',
          borderRadius: '12rpx',
          borderWidth: '2rpx',
          borderColor: '#E5E5E5',
          borderStyle: 'solid'
        }
      },
      // 小程序码提示文字
      {
        type: 'text',
        text: '扫码查看',
        css: {
          top: '1240rpx',
          right: '100rpx',
          width: '120rpx',
          fontSize: '20rpx',
          color: '#999999',
          textAlign: 'center',
          maxLines: 1
        }
      }
    ].filter(Boolean) // 过滤掉null值
  };

  console.log('✅ createCardShareConfig 配置生成完成:', {
    width: config.width,
    height: config.height,
    viewsCount: config.views.length,
    background: config.background
  });

  return config;
};

/**
 * 创建动态分享图片配置
 * @param {Object} options 配置选项
 * @param {String} options.content 内容文本
 * @param {Array} options.images 图片数组
 * @param {String} options.author 作者名称
 * @param {String} options.authorAvatar 作者头像URL
 * @param {String} options.date 日期
 * @param {String} options.location 位置信息
 * @param {String} options.watermark 水印文本
 * @returns {Object} Painter配置对象
 */
export const createFeedShareConfig = (options) => {
  const {
    content = '',
    images = [],
    author = '匿名用户',
    authorAvatar = '',
    date = '',
    location = '',
    watermark = '分享自小聚会'
  } = options;

  const views = [
    // 背景
    {
      type: 'rect',
      css: {
        top: '0rpx',
        left: '0rpx',
        width: '750rpx',
        height: '1334rpx',
        color: 'linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 100%)'
      }
    },
    // 主内容卡片
    {
      type: 'rect',
      css: {
        top: '60rpx',
        left: '40rpx',
        width: '670rpx',
        height: '1200rpx',
        color: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '24rpx',
        shadow: '0 8rpx 32rpx rgba(36, 93, 60, 0.1)'
      }
    },
    // 用户头像
    authorAvatar ? {
      type: 'image',
      url: authorAvatar,
      css: {
        top: '100rpx',
        left: '80rpx',
        width: '80rpx',
        height: '80rpx',
        borderRadius: '40rpx',
        border: '3rpx solid #ffffff'
      }
    } : null,
    // 用户名称
    {
      type: 'text',
      text: author,
      css: {
        top: '110rpx',
        left: authorAvatar ? '180rpx' : '80rpx',
        fontSize: '32rpx',
        color: '#245D3C',
        fontWeight: '600',
        maxLines: 1,
        width: '400rpx'
      }
    },
    // 发布时间
    date ? {
      type: 'text',
      text: date,
      css: {
        top: '150rpx',
        left: authorAvatar ? '180rpx' : '80rpx',
        fontSize: '24rpx',
        color: '#73C088',
        maxLines: 1,
        width: '400rpx'
      }
    } : null,
    // 位置信息
    location ? {
      type: 'text',
      text: `📍 ${location}`,
      css: {
        top: '180rpx',
        left: authorAvatar ? '180rpx' : '80rpx',
        fontSize: '24rpx',
        color: '#A5E1B8',
        maxLines: 1,
        width: '500rpx'
      }
    } : null
  ];

  // 添加内容文本
  if (content) {
    views.push({
      type: 'text',
      text: content,
      css: {
        top: location ? '240rpx' : (date ? '210rpx' : '180rpx'),
        left: '80rpx',
        width: '590rpx',
        fontSize: '32rpx',
        color: '#333333',
        lineHeight: '48rpx',
        maxLines: 8,
        textAlign: 'left',
        wordBreak: 'break-all'
      }
    });
  }

  // 添加图片（最多显示4张）
  const displayImages = images.slice(0, 4);
  const imageStartTop = content ? 
    (location ? 500 : (date ? 470 : 440)) : 
    (location ? 260 : (date ? 230 : 200));

  if (displayImages.length === 1) {
    // 单张图片，大尺寸显示
    views.push({
      type: 'image',
      url: displayImages[0],
      css: {
        top: `${imageStartTop}rpx`,
        left: '80rpx',
        width: '590rpx',
        height: '400rpx',
        borderRadius: '16rpx',
        mode: 'aspectFill'
      }
    });
  } else if (displayImages.length > 1) {
    // 多张图片，网格显示
    const imageSize = displayImages.length === 2 ? 285 : 190;
    const imageGap = 10;
    
    displayImages.forEach((imageUrl, index) => {
      const row = Math.floor(index / 2);
      const col = index % 2;
      
      views.push({
        type: 'image',
        url: imageUrl,
        css: {
          top: `${imageStartTop + row * (imageSize + imageGap)}rpx`,
          left: `${80 + col * (imageSize + imageGap)}rpx`,
          width: `${imageSize}rpx`,
          height: `${imageSize}rpx`,
          borderRadius: '12rpx',
          mode: 'aspectFill'
        }
      });
    });
  }

  // 添加水印
  views.push({
    type: 'text',
    text: watermark,
    css: {
      bottom: '100rpx',
      left: '80rpx',
      fontSize: '24rpx',
      color: '#A5E1B8',
      maxLines: 1,
      width: '400rpx'
    }
  });

  // 添加小程序码区域
  views.push(
    {
      type: 'rect',
      css: {
        bottom: '80rpx',
        right: '80rpx',
        width: '120rpx',
        height: '120rpx',
        color: '#ffffff',
        borderRadius: '12rpx',
        borderWidth: '2rpx',
        borderColor: '#E5E5E5',
        borderStyle: 'solid'
      }
    },
    {
      type: 'text',
      text: '扫码查看',
      css: {
        bottom: '50rpx',
        right: '80rpx',
        width: '120rpx',
        fontSize: '20rpx',
        color: '#999999',
        textAlign: 'center',
        maxLines: 1
      }
    }
  );

  return {
    width: '750rpx',
    height: '1334rpx',
    background: '#f8f9fa',
    views: views.filter(Boolean)
  };
};

/**
 * 创建默认分享图片配置
 * @param {Object} options 配置选项
 * @returns {Object} Painter配置对象
 */
export const createDefaultShareConfig = (options) => {
  const {
    content = '分享内容',
    author = '匿名用户',
    watermark = '分享自小聚会'
  } = options;

  return {
    width: '750rpx',
    height: '1334rpx',
    background: 'linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 100%)',
    views: [
      // 主标题
      {
        type: 'text',
        text: content,
        css: {
          top: '400rpx',
          left: '75rpx',
          width: '600rpx',
          fontSize: '48rpx',
          color: '#245D3C',
          fontWeight: '600',
          lineHeight: '72rpx',
          textAlign: 'center',
          maxLines: 6
        }
      },
      // 作者信息
      {
        type: 'text',
        text: `— ${author}`,
        css: {
          top: '800rpx',
          left: '75rpx',
          width: '600rpx',
          fontSize: '32rpx',
          color: '#73C088',
          textAlign: 'center',
          maxLines: 1
        }
      },
      // 水印
      {
        type: 'text',
        text: watermark,
        css: {
          bottom: '100rpx',
          left: '75rpx',
          width: '600rpx',
          fontSize: '24rpx',
          color: '#A5E1B8',
          textAlign: 'center',
          maxLines: 1
        }
      }
    ]
  };
};
