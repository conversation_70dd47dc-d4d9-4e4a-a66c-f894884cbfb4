<?php

//测试函数
function dump($var){
	echo "<pre>";
	if(is_array($var)){
		print_r($var);
	}else{
		var_dump($var);
	}
	echo "</pre>";
}
//获取IP
function get_client_ip(){
	$ip = "";
	$proxy_ip = isset($_SERVER[" HTTP_CLIENT_IP"]) ? $_SERVER["HTTP_CLIENT_IP"] : (isset($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : (isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : false));
	if($proxy_ip !== false){
		if(strpos($proxy_ip, ",") !== false){
			$tmp = explode(",", $ip);
			$ip = $tmp[0];
		}else{
			$ip = $proxy_ip;
		}
	}
	$ip = !empty($ip) && filter_var($ip,FILTER_VALIDATE_IP) ? $ip : (isset($_SERVER['REMOTE_ADDR']) && filter_var($_SERVER['REMOTE_ADDR'],FILTER_VALIDATE_IP) ? $_SERVER['REMOTE_ADDR'] : "0.0.0.0");
	$ip = mb_substr(htmlspecialchars(addslashes($ip)),0,15);
	return $ip;
}
//全局赋值
function assign($name,$value){
	$GLOBALS[$name] = $value;
}

//自动加载
spl_autoload_register("autoload");
function autoload($className){
	if(strpos($className,"\\") !== false){
		$pathArr = explode("\\", $className);
		$pathArr[count($pathArr)-1][0] = strtoupper($pathArr[count($pathArr)-1][0]);
		$filePath = BASE_PATH;
		foreach($pathArr as $path){
			$filePath .= $path . DS;
		}
		$filePath = rtrim($filePath,DS) . ".php";
	}else{
		$className[0] = strtoupper($className[0]);
		$filePath = BASE_PATH . $className . ".php";
	}
	if(file_exists($filePath)){
		require_once($filePath);
		if(!class_exists($className) && !interface_exists($className)){
			if(end($pathArr) == \core\Route::$controller){
				\core\Error::urlErr($className);
			}else{
				\core\Error::emptyClass($className);
			}
		}else{
			//echo "load {$className} success\r\n";
		}
	}else{
		\core\Error::emptyFile($filePath);
	}
}

//参数绑定
function invoke($className,$method){
	$method = new \ReflectionMethod($className,$method);
	$methodParams = $method->getParameters();
	$inParams = [];
	if(!empty($methodParams)){
		foreach($methodParams as $methodParam){
			if(!$methodParam->isDefaultValueAvailable() && !isset(\core\Route::$params[$methodParam->name])){
				header("Content-type: application/json; charset=utf-8;");
				echo json_encode(["status"=>"error","msg"=>"Missing parameter [ ".$methodParam->name." ]"]);
				exit;
			}
			$paramValue = isset(\core\Route::$params[$methodParam->name]) ? \core\Route::$params[$methodParam->name] : $methodParam->getDefaultValue();
			$inParams[$methodParam->name] = $paramValue;
		}
	}
	//dump($inParams);
	return $method->invokeArgs(new $className,$inParams);
}

//获取配置
function config($var=null){
	if(empty($var)){
		return \core\Config::getAll();
	}else{
		return \core\Config::get($var);
	}
}
//获取上传对象
function Upload(){
	return \core\Upload::getInstance();
}
//获取数据库对象并执行配置连接
function Db($useConfig=false,$reConn=false){
	$instance = \core\Db::getInstance();
	if($useConfig !== false)$instance::connect($useConfig,$reConn);
	return $instance;
}

//切换数据库连接
function dbConn($useConfig="",$reConn=false){
	return \core\Db::connect($useConfig,$reConn);
}

//设置输出数据类型 html|json|xml
function responseType($type){
	return \core\Response::setType($type);
}

//生成模版地址
function tplUrl($path=null){
	$templates_suffix = core\Config::get('sys.templates_suffix');
	if(empty($path))return BASE_PATH . "/templates/" . core\Route::$controller . "/" . core\Route::$action . $templates_suffix;
	return BASE_PATH."/templates/" . $path . $templates_suffix;
}

//快速返回模版输出
function view($tplPath=null,$data=null){
	responseType("html");
	\core\View::load($tplPath);
	return \core\View::output($data);
}

//返回分页渲染后的html
function pageRender($count=0,$pageSize=0){
	if($count > 0)\core\Page::$count = $count;
	if($pageSize > 0)\core\Page::$pageSize = $pageSize;
	return \core\Page::render();
}

//生成控制器地址
function url($addr="",$param=[]){
	$url_suffix = core\Config::get("sys.url_suffix");
	$index = core\Config::get("sys.pathinfo_index") ? "/index.php/" : "/";
	$isPathinfo = (isset($_SERVER['PATH_INFO']) && !empty($_SERVER['PATH_INFO'])) || core\Config::get("sys.pathinfo_only") ? true : false;
	if($isPathinfo){
		if($addr == ""){
			$url = $index . core\Route::$controller . "/" . core\Route::$action;
		}else if(strpos($addr,"/") === false){
			$url = $index . core\Route::$controller . "/" . $addr;
		}else{
			$url = $index . $addr;
		}
		if(!empty($param)){
			foreach($param as $k=>$v){
				$url .= "/".$k."/".$v;
			}
		}
		$url .= $url_suffix;
	}else{
		if($addr == ""){
			$url = "/index.php?controller=" . core\Route::$controller . "&action=" . core\Route::$action;
		}else if(strpos($addr,"/") === false){
			$url = "/index.php?controller=" . core\Route::$controller . "&action=" . $addr;
		}else{
			$addr = trim($addr,"/");
			$addrArr = explode("/",$addr);
			$url = "/index.php?controller=" . array_shift($addrArr) . "&action=" . array_shift($addrArr);
			if(!empty($addrArr) && count($addrArr) % 2 == 0){
				for($i=0;$i<count($addrArr);$i=$i+2){
					if(isset($addrArr[$i+1]) && !isset($param[$addrArr[$i]])){
						$param[$addrArr[$i]] = $addrArr[$i+1];
					}else if(!isset($addrArr[$i+1])){
						$param[$addrArr[$i]] = "";
					}
				}
			}
		}
		if(!empty($param)){
			foreach($param as $k=>$v){
				$url .= "&".$k."=".$v;
			}
		}
	}
	return $url;
}

//验证
//email|url|ip|mobile|int|intgt0|intlt0|integt0|intelt0|zh|azAZ09_|az|AZ|azAZ|azAZ09|account|id_card|date|time|datetime
function check($var,$validate){
	$result = false;
	switch($validate){
		case "email":
			if(filter_var($var, FILTER_VALIDATE_EMAIL)){
				$result = true;
			}
			break;
		case "url":
			if(filter_var($var, FILTER_VALIDATE_URL)){
				$result = true;
			}
			break;
		case "ip":
			if(filter_var($var, FILTER_VALIDATE_IP)){
				$result = true;
			}
			break;
		case "mobile":
			if(
				strlen($var) == 11 && preg_match("/^1[3456789]{1}\d{9}$/",$var)
			){
				$result = true;
			}
			break;
		case "int":
			if(preg_match("/^-?[1-9]\d*|0$/",$var)){
				$result = true;
			}
			break;
		case "intgt0":
			if(preg_match("/^[1-9]\d*$/",$var)){
				$result = true;
			}
			break;
		case "intlt0":
			if(preg_match("/^-[1-9]\d*$/",$var)){
				$result = true;
			}
			break;
		case "integt0":
			if(preg_match("/^[1-9]\d*|0$/",$var)){
				$result = true;
			}
			break;
		case "intelt0":
			if(preg_match("/^-[1-9]\d*|0$/",$var)){
				$result = true;
			}
			break;
		case "zh":
			if(preg_match("/([\x{4e00}-\x{9fa5}]+)/u",$var)){
				$result = true;
			}
			break;
		case "azAZ09_":
			if(preg_match("/^\w+$/",$var)){
				$result = true;
			}
			break;
		case "az":
			if(preg_match("/^[a-z]+$/",$var)){
				$result = true;
			}
			break;
		case "AZ":
			if(preg_match("/^[A-Z]+$/",$var)){
				$result = true;
			}
			break;
		case "azAZ09":
			if(preg_match("/^[A-Za-z0-9]+$/",$var)){
				$result = true;
			}
			break;
		case "azAZ":
			if(preg_match("/^[A-Za-z]+$/",$var)){
				$result = true;
			}
			break;
		case "account":
			if(preg_match("/^[a-zA-Z][a-zA-Z0-9_]{4,15}$/",$var)){
				$result = true;
			}
			break;
		case "id_card":
			$var = strtoupper($var);
			if(strlen($var) == 18 && preg_match("/^\d{17}([0-9]|X)$/", $var)){
				$regx = "/^(\d{6})+(\d{4})+(\d{2})+(\d{2})+(\d{3})([0-9]|X)$/";
				@preg_match($regx, $var, $arr_split);
				$birthay = $arr_split[2] . '/' . $arr_split[3]. '/' .$arr_split[4];
				if(strtotime($birthay)){
					$arr_int = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
					$arr_ch = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
					$sign = 0;
					for($i = 0; $i < 17; $i++){
						$b = (int) $var[$i];
						$w = $arr_int[$i];
						$sign += $b * $w;
					}
					$n = $sign % 11;
					$val_num = $arr_ch[$n];
					if($val_num == $var[17]){
						$result = true;
					}
				}
			}
			break;
		case "date":
			if(date("Y-m-d",strtotime($var)) === $var){
				$result = true;
			}
			break;
		case "time":
			if(date("H:i:s",strtotime($var)) === $var){
				$result = true;
			}
			break;		
		case "datetime":
			if(date("Y-m-d H:i:s",strtotime($var)) === $var){
				$result = true;
			}
			break;				
		default:
			$result = false;
	}
	return $result;
}

//发送请求
function curl($url, $data = NULL, $json = false,$header_params=[],$multipart_formdata=false){
	$curl = curl_init();
	curl_setopt($curl, CURLOPT_URL, $url);
	curl_setopt($curl, CURLOPT_TIMEOUT, 120);
	curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
	$request_body = "";
	if(!empty($data)){
		if($json && is_array($data)){
			$data = json_encode($data);
			$request_body = $data;
			$header_params = array_merge($header_params,['Content-Type: application/json; charset=utf-8','Content-Length:' . strlen($request_body)]);
		}else if($multipart_formdata){
			$header_params = array_merge($header_params,['Content-Type:multipart/form-data']);
		}else{
			$data = http_build_query($data);
			$request_body = $data;
		}
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
	}
	if(!empty($header_params)){
		curl_setopt($curl, CURLOPT_HEADER, 0);
		curl_setopt($curl, CURLOPT_HTTPHEADER,$header_params);		
	}
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($curl, CURLOPT_HEADER, TRUE);
    curl_setopt($curl, CURLOPT_NOBODY, FALSE);
	curl_setopt($curl,CURLINFO_HEADER_OUT,true);
	$response = curl_exec($curl);
	$getinfo = curl_getinfo($curl);
	$request_header = isset($getinfo['request_header']) ? $getinfo['request_header'] : "";
	$code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
	$headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
	curl_close($curl);
	$header = trim(substr($response, 0, $headerSize));
	$body = trim(substr($response, $headerSize));
	return ["request_header"=>$request_header,"request_body"=>$request_body,"header"=>$header,"body"=>$body];
}

//输出日志文件
function _log_($log=''){
	$filePath = "." . basename(BASE_PATH).".log";
	if(file_exists($filePath)){
		$size = filesize($filePath);
		if($size && $size > 5*1024*1024)rename($filePath,$filePath."-".date('Y_m_d_H_i_s'));
	}
	$file = fopen($filePath,'a+');
	if($file){
		$log = date("Y-m-d H:i:s") . " | " . $log;
		fputs($file,$log."\r\n");
	}
	fclose($file);
	return true;
}

//删除目录
function removeDir($dirName){
	if(! is_dir($dirName)){
		return false;
	}
	$handle = @opendir($dirName);
	while(($file = @readdir($handle)) !== false){
		if($file != '.' && $file != '..'){
			$dir = $dirName . '/' . $file;
			is_dir($dir) ? removeDir($dir) : @unlink($dir);
		}
	}
	closedir($handle);
	return rmdir($dirName) ;
}

//判断是否Ajax
function isAjax(){
	if(
		isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' //jquery
	){
		return true;
	}
	return false;
}

//获取路由参数for字符串
function getRouteParamsForStr(){
	$params = \core\Route::$params;
	$paramsStr = "";
	if(!empty($params)){
		foreach($params as $k=>$v){
			$paramsStr .= "/{$k}/{$v}";
		}
	}
	return $paramsStr;
}

//获取路由参数
function getRouteParams(){
	return \core\Route::$params;
}

//创建随机验证码
function makeCode($num = 6,$zimu=false) {
	$res = "";
	$rand = $zimu ? "0123456789abcdefghijklmnopqrstuvwxyz" : "0123456789";
	while(strlen($res)<$num) {
		$res .= $rand[rand(0, strlen($rand)-1)];
	}
	return $res;
}

//nginx 日志分析访问量
function nginx_access($type=1,$log_path="",$date="",$ip="") {
	if(strpos(strtolower(PHP_OS),"linux") === false){
		return ["error"=>"此功能只能在Linux系统使用"];
	}
	if(!file_exists($log_path)){
		return ["error"=>"分析日志文件【{$log_path}】未找到"];
	}
	switch($type){
		case 1:
			$title = "秒访问量";
			$cmd = "awk '{print $4}' {$log_path} |cut -c 2-21|sort|uniq -c|sort -nr|head -n 100";
			break;
		case 2:
			$title = "分钟访问量";
			$cmd = "awk '{print $4}' {$log_path} |cut -c 2-18|sort|uniq -c|sort -nr|head -n 100";
			break;
		case 3:
			$title = "小时访问量";
			$cmd = "awk '{print $4}' {$log_path} |cut -c 2-15|sort|uniq -c|sort -nr|head -n 100";
			break;
		case 4:
			$title = "特点时间点的ip访问量";
			$month_format = [
				"01"=>"Jan",
				"02"=>"Feb",
				"03"=>"Mar",
				"04"=>"Apr",
				"05"=>"May",
				"06"=>"Jun",
				"07"=>"Jul",
				"08"=>"Aug",
				"09"=>"Sep",
				"10"=>"Oct",
				"11"=>"Nov",
				"12"=>"Dec",
			];
			$date = empty($date) ? date("Y-m-d") : $date;
			$year = date("Y",strtotime($date));
			$moth = date("m",strtotime($date));
			$day  = (int)date("d",strtotime($date));
			$cmd = "grep '".$day."/".$month_format[$moth]."/".$year.":0[0 - 23]' {$log_path} | awk '{ print $1}' | sort | uniq -c| sort -nr | wc -l";
			break;
		case 5:
			$title = "访问最频繁的IP";
			$cmd = "awk '{print $1}' {$log_path} | sort -n |uniq -c | sort -rn | head -n 100";
			break;
		case 6:
			if(empty($ip))return ["error"=>"请输入ip"];

			$title = "IP【{$ip}】的详细访问情况,按访问频率排序";
			$cmd = "grep '".$ip."' {$log_path} |awk '{print $7}'|sort |uniq -c |sort -rn |head -n 100";
			break;
		case 7:
			$title = "访问最频的页面";
			$cmd = "awk '{print $7}' {$log_path} | sort |uniq -c | sort -rn | head -n 100";
			break;
		case 8:
			$title = "查看最近1000条记录，访问量最高的页面";
			$cmd = "tail -1000 {$log_path} |awk '{print $7}'|sort|uniq -c|sort -nr|less";
			break;
		default :
			return ["error"=>"未找到该类型"];

	}

	$res = shell_exec($cmd);
	$data = [
		"logname"=>$log_path,
		"title"=>$title,
		"res"=>$res,
		"error"=>"0",
	];
	return $data;

}

//取汉子首字母
function getFirstCharter($str){
	if(empty($str))return false;
	$str = mb_substr($str,0,1,"UTF-8");
	$fchar=ord($str[0]);
	if($fchar>=ord('A')&&$fchar<=ord('z')) return strtoupper($str[0]);
	$s1=iconv('UTF-8','gb2312',$str);
	$s2=iconv('gb2312','UTF-8',$s1);
	$s=$s2==$str?$s1:$str;
	$asc=ord($s[0])*256+ord($s[1])-65536;
	if($asc>=-20319&&$asc<=-20284) return 'A';
	if($asc>=-20283&&$asc<=-19776) return 'B';
	if($asc>=-19775&&$asc<=-19219) return 'C';
	if($asc>=-19218&&$asc<=-18711) return 'D';
	if($asc>=-18710&&$asc<=-18527) return 'E';
	if($asc>=-18526&&$asc<=-18240) return 'F';
	if($asc>=-18239&&$asc<=-17923) return 'G';
	if($asc>=-17922&&$asc<=-17418) return 'H';
	if($asc>=-17417&&$asc<=-16475) return 'J';
	if($asc>=-16474&&$asc<=-16213) return 'K';
	if($asc>=-16212&&$asc<=-15641) return 'L';
	if($asc>=-15640&&$asc<=-15166) return 'M';
	if($asc>=-15165&&$asc<=-14923) return 'N';
	if($asc>=-14922&&$asc<=-14915) return 'O';
	if($asc>=-14914&&$asc<=-14631) return 'P';
	if($asc>=-14630&&$asc<=-14150) return 'Q';
	if($asc>=-14149&&$asc<=-14091) return 'R';
	if($asc>=-14090&&$asc<=-13319) return 'S';
	if($asc>=-13318&&$asc<=-12839) return 'T';
	if($asc>=-12838&&$asc<=-12557) return 'W';
	if($asc>=-12556&&$asc<=-11848) return 'X';
	if($asc>=-11847&&$asc<=-11056) return 'Y';
	if($asc>=-11055&&$asc<=-10247) return 'Z';
	return false;
}

/**
 * 计算两点地理坐标之间的距离
 * @param  Decimal $longitude1 起点经度
 * @param  Decimal $latitude1  起点纬度
 * @param  Decimal $longitude2 终点经度
 * @param  Decimal $latitude2  终点纬度
 * @param  Int     $unit       单位 1:米 2:公里
 * @param  Int     $decimal    精度 保留小数位数
 * @return Decimal
 */
function getDistance($longitude1, $latitude1, $longitude2, $latitude2, $unit=2, $decimal=2){

    $EARTH_RADIUS = 6370.996; // 地球半径系数
    $PI = 3.1415926;

    $radLat1 = $latitude1 * $PI / 180.0;
    $radLat2 = $latitude2 * $PI / 180.0;

    $radLng1 = $longitude1 * $PI / 180.0;
    $radLng2 = $longitude2 * $PI /180.0;

    $a = $radLat1 - $radLat2;
    $b = $radLng1 - $radLng2;

    $distance = 2 * asin(sqrt(pow(sin($a/2),2) + cos($radLat1) * cos($radLat2) * pow(sin($b/2),2)));
    $distance = $distance * $EARTH_RADIUS * 1000;

    if($unit==2){
        $distance = $distance / 1000;
    }

    return round($distance, $decimal);

}

/**
 * 根据经纬度和半径计算出范围
 * @param String $lng 经度
 * @param string $lat 维度
 * @param float $radius 半径(米)
 * @return Array 范围数组
 */
function calcScope($lng, $lat, $radius) {
    $degree = (24901*1609)/360.0;
    $dpmLat = 1/$degree;

    $radiusLat = $dpmLat*$radius;
    $minLat = $lat - $radiusLat;       // 最小维度
    $maxLat = $lat + $radiusLat;       // 最大维度

    $mpdLng = $degree*cos($lat * (PI/180));
    $dpmLng = 1 / $mpdLng;
    $radiusLng = $dpmLng*$radius;
    $minLng = $lng - $radiusLng;      // 最小经度
    $maxLng = $lng + $radiusLng;      // 最大经度

    /** 返回范围数组 */
    $scope = array(
        'minLat'    =>  $minLat,
        'maxLat'    =>  $maxLat,
        'minLng'    =>  $minLng,
        'maxLng'    =>  $maxLng
        );
    return $scope;
}

//导出数据库
function mysqlDump(){
	$path = BASE_PATH . config("sys.databak");
	if(!is_dir($path))@mkdir($path, 0777, true);
	$is_win = substr(PHP_OS,0,3) == "WIN" ? true : false;
	$config = \core\Config::getDatabaseConfig();
	foreach($config as $k=>$v){
		if($v["host"] == "127.0.0.1" || $v["host"] == "localhost"){
			if($is_win){
				$code = makeCode(16,true);
				shell_exec("mysqldump {$v['database']} -u{$v['user']} -p{$v['password']} > {$path}/{$v['database']}_".date("Y_m_d_H_i_s")."_{$code}.sql");
			}else{
				$res = shell_exec(BASE_PATH . "databak.sh {$v['user']} {$v['password']} {$v['database']} {$path}");
				//dump($res);
			}
		}
	}
	return true;
}
//恢复数据库
function mysqlImport($sqlFile="",$useConfig=""){
	if(empty($sqlFile) || empty($useConfig) || !is_string($useConfig) || !is_string($sqlFile))return "参数错误";
	if(!file_exists($sqlFile))return "恢复文件不存在";
	if(substr($sqlFile,-4) != ".sql")return "恢复文件格式只能为.sql";
	$config = \core\Config::getDatabaseConfig();
	if(!isset($config[$useConfig]))return "数据库配置不存在";
	$config_info = $config[$useConfig];
	if($config_info["host"] != "127.0.0.1" && $config_info["host"] != "localhost")return "只能恢复本地数据";
	$is_win = substr(PHP_OS,0,3) == "WIN" ? true : false;
	if($is_win){
		$res = shell_exec("mysql {$config_info['database']} -u{$config_info['user']} -p{$config_info['password']} < {$sqlFile}");
		//dump($res);
	}else{
		$res = shell_exec("mysql {$config_info['database']} -u{$config_info['user']} -p{$config_info['password']} < {$sqlFile}");
		//dump($res);
	}
	return true;
}
//汉字分割成数组
function cn_to_array($str){
	return preg_split('/(?<!^)(?!$)/u', $str);
}

//XML转换成数组
function xml_to_array($str){
	if(is_file($str)){
		$obj = simplexml_load_file($str);
	}else{
		$obj = simplexml_load_string($str);
	}
	$arr = json_decode(json_encode($obj),TRUE);
	return $arr;
}

/**
 * 对字符串执行指定次数替换
 * @param  Mixed $search   查找目标值
 * @param  Mixed $replace  替换值
 * @param  Mixed $subject  执行替换的字符串／数组
 * @param  Int   $limit    允许替换的次数，默认为-1，不限次数
 * @return Mixed
 */
function str_replace_limit($search, $replace, $subject, $limit=-1){
    if(is_array($search)){
        foreach($search as $k=>$v){
            $search[$k] = '`'. preg_quote($search[$k], '`'). '`';
        }
    }else{
        $search = '`'. preg_quote($search, '`'). '`';
    }
    return preg_replace($search, $replace, $subject, $limit);
}

//递归树形结构
function getTree($data,$pid = 0,$node_high=0){
	static $res = [];
	$left = str_repeat('-----',$node_high).' └─ ';
	foreach($data as $row){
		if($row['pid'] == $pid){
			$row['name'] = $left.$row['name'];
			$res[] = $row;
			getTree($data,$row['id'],$node_high + 1);
		}
	}
	return $res;
}

//递归树形结构
function getTreeArr($data,$pid = 0){
	$return = [];
	foreach($data as $row) {
		if($row['pid'] == $pid) {
			foreach($data as $subleaf) {
				if($subleaf['pid'] == $row['id']) {
					$row['children'] = getTreeArr($data, $row['id']);
					break;
				}
			}
			$return[] = $row;
		}
	}
	return $return;
}

//提取树形结构的id
function tree2node($data){
	static $result = [];
	foreach($data as $row){
		$result[] = $row["id"];
		if(isset($row['children'])){
			tree2node($row['children']);
		}
	}
	return $result;
}

//导入excel
function excelReader($file,$index=0){
	//
	$file_name = basename($file);
	$file_arr = explode(".",$file_name);
	$ext = strtolower(end($file_arr));
	//
	require BASE_PATH . "PhpOffice/PHPExcel/Classes/PHPExcel.php";
	if($ext == "xlsx"){
		$excel = new \PHPExcel_Reader_Excel2007();
	}else if($ext == "xls"){
		$excel = new \PHPExcel_Reader_Excel5();
	}else{
		return ["status"=>"error","msg"=>"仅支持xlsx和xls文件"];
	}
	if(!$excel->canRead($file)){
		return ["status"=>"error","msg"=>"读取文件失败"];
	}
	$reader = $excel->load($file);  //建立excel对象 
	$currSheet = $reader->getSheet($index);   //获取指定的sheet表 
	$columnH = $currSheet->getHighestColumn();   //取得最大的列号
	$cellName = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ'];
    $columnCount = array_search($columnH, $cellName); 
    $rowCount = $currSheet->getHighestRow();   //获取总行数
	if($rowCount < 1){
		return ["status"=>"empty","msg"=>"未读取到内容"];	
	}
    $data = []; 
    for($row=1; $row<=$rowCount; $row++){  //读取内容 
        for($column=0; $column<=$columnCount; $column++){ 
            $cellId = $cellName[$column].$row; 
            $cellValue = $currSheet->getCell($cellId)->getValue(); 
             //$cellValue = $currSheet->getCell($cellId)->getCalculatedValue();  #获取公式计算的值 
            if($cellValue instanceof \PHPExcel_RichText){   //富文本转换字符串 
                $cellValue = $cellValue->__toString(); 
            } 
            $data[$row][$cellName[$column]] = $cellValue; 
        }
    }
    return ["status"=>"ok","rows"=>$rowCount,"data"=>$data]; 
}

/*
*处理Excel导出
*@param $data array 数据
*@param $column array 字段[[name=name1,type=number]]
*@param $title string 标题
*@param $filename string 文件名
*/
function excelOutput($data,$column,$filename,$title="sheet",$wrap_text=false){
	require BASE_PATH . "PhpOffice/PHPExcel/Classes/PHPExcel.php";
	$excel = new \PHPExcel();
	$excel->setActiveSheetIndex(0); 
	$excel->getActiveSheet()->setTitle($title); 
	$excel->getDefaultStyle()->getAlignment()
	//->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)//水平居中
    ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
	$excel->getDefaultStyle()->getAlignment()->setWrapText($wrap_text);//自动换行
	//
	$row_index = "A";
	for($i=0;$i<count($column);$i++){
		//
		$objRichText = new \PHPExcel_RichText();
		$objPayable = $objRichText->createTextRun($column[$i]['name']);
		$objPayable->getFont()->setBold(true);
		$objPayable->getFont()->setSize(12);
		$objPayable->getFont()->setColor(
			new \PHPExcel_Style_Color("FF4F4F4F")
		);
		//
		$excel->getActiveSheet()->getCell("{$row_index}1")->setValue($objRichText);
		//$excel->getActiveSheet()->setCellValueExplicit("{$row_index}1",$column[$i]['name'],\PHPExcel_Cell_DataType::TYPE_STRING);
		$width = isset($column[$i]['width']) ? $column[$i]['width'] : "20";
		$excel->getActiveSheet()->getColumnDimension($row_index)->setWidth($width);
		$row_index ++;
	}
	//
	for($i=2;$i<=count($data) + 1;$i++){
		$j = $i-2;
		$row_index = "A";
		foreach($data[$j] as $k=>$v){
			if($column[$k]['type'] == "number"){
				$excel->getActiveSheet()->setCellValueExplicit("{$row_index}{$i}",$v,\PHPExcel_Cell_DataType::TYPE_NUMERIC);
			}else{
				$excel->getActiveSheet()->setCellValueExplicit("{$row_index}{$i}",$v,\PHPExcel_Cell_DataType::TYPE_STRING);
			}
			$row_index ++;
		}
	}
	//
	header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
	header('Content-Disposition: attachment;filename="'.$filename.'.xlsx"');
	header('Cache-Control: max-age=0');
	$write = \PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
	$write->save('php://output');
	exit;
}

//秒数转时分秒
function seconds_format($seconds,$format=2,$chinese=true){
	$seconds = (int)$seconds;
	if($format == 1){//分秒
		$minute = floor($seconds / 60);
		$second = $seconds % 60;
		$minute = str_pad((string)$minute,2,"0",STR_PAD_LEFT);
		$second = str_pad((string)$second,2,"0",STR_PAD_LEFT);
		return $chinese ? "{$minute}分{$second}秒" : "{$minute}:{$second}";
	}else if($format == 2){//时分秒
		$hour = floor($seconds / 3600);
		$has_seconds = $hour == 0 ? $seconds : $seconds - $hour * 3600;
		$hour = str_pad((string)$hour,2,"0",STR_PAD_LEFT);
		return $chinese ?  "{$hour}时" . seconds_format($has_seconds,1,$chinese) : "{$hour}:" . seconds_format($has_seconds,1,$chinese);			
	}else if($format == 3){//日时分秒
		$day = floor($seconds / (3600 * 24));
		$has_seconds = $day == 0 ? $seconds : $seconds - $day * 3600 * 24;
		return $chinese ?   "{$day}天" . seconds_format($has_seconds,2,$chinese) : "{$day}/ " . seconds_format($has_seconds,2,$chinese);				
	}
	return false;
}
//格式化时间多少时间前
function format_time_ago($second){
	$str = "";
	if($second >= 365 * 24 * 3600){
		$num = floor($second / (365 * 24 * 3600));
		return $num . "年前";
	}else if($second >= 30 * 24 * 3600){
		$num = floor($second / (30 * 24 * 3600));
		return $num  . "月前";
	}else if($second >= 24 * 3600){
		$num = floor($second / (24 * 3600));
		return $num  . "天前";
	}else if($second >= 3600){
		$num = floor($second / 3600);
		return $num .  "小时前";
	}else if($second >= 60){
		$num = floor($second / 60);
		return $num  . "分钟前";
	}else{
		return $second .  "秒前";
	}

}
//格式化文件大小
function format_file_size($size_b){
	if($size_b > 1024 * 1024 * 1024){
		$size = number_format($size_b/1024/1024/1024,2)."G";
	}else if($size_b > 1024 * 1024){
		$size = number_format($size_b/1024/1024,2)."M";
	}else if($size_b > 1024){
		$size = number_format($size_b/1024,2)."K";
	}else{
		$size = number_format($size_b,2)."B";
	}
	return $size;
}
//获取m3u8格式视频的长度（秒）
function get_m3u8_seconds($url){
	if(empty($url) || substr($url,-5) != ".m3u8")return false;
	$text = @file_get_contents($url);
	if(empty($text))return false;
	$text = str_ireplace("\r\n","\n",$text);
	$text = str_ireplace("\r","",$text);
	$text = explode("\n",$text);
	if(empty($text))return false;
	$seconds = 0;
	$has_time = false;
	$has_sub = false;
	$has_ts = false;
	$sub = "";
	foreach($text as $row){
		$row = trim($row);
		if(substr($row,0,8) == "#EXTINF:" && substr($row,-1) == ","){
			$second = substr($row,8,-1);
			if(is_numeric($second) && $second > 0){
				$seconds += $second;
			}
			$has_time = true;
		}
		if(!$has_sub && strlen($row) > 5 && substr($row,-5) == ".m3u8" && $row[0] != "#"){//拿到第一个二级文件
			$has_sub = true;
			$sub = $row;
		}
		if(!$has_ts && strlen($row) > 3 && substr($row,-3) == ".ts" && $row[0] != "#"){
			$has_ts = true;
		}
	}
	if($has_sub && !$has_time){
		if(substr($sub,0,7) == "http://" || substr($sub,0,8) == "https://"){
			return get_m3u8_seconds($sub);
		}else{
			$arr = explode("/",$url);
			$arr[count($arr)-1] = $sub;
			$sub_url = implode("/",$arr);
			return get_m3u8_seconds($sub_url);
		}
	}
	return floor($seconds);
}

function lock($url,$timeout=10){
	$current_item_arr = explode("_",basename(dirname(__DIR__)));
	$current_item = $current_item_arr[0];
	$start = microtime(true);
	$lock_seconds = \core\Cache::getCache("lock_seconds");
	if($lock_seconds !== false){
		if(check($lock_seconds,"intgt0")){
			sleep(rand(ceil($lock_seconds/2),$lock_seconds));
		}
	}else{
		//
		$url_parse = parse_url($url);
		$url = $url_parse['scheme'] . "://" . $url_parse['host'];
		if(isset($url_parse['path']))$url .= $url_parse['path'];
		$url .= "?item={$current_item}";
		if(isset($url_parse['query']))$url .= "&".$url_parse['query'];
		//
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		$response = curl_exec($curl);//{"seconds":0,"cache":7200}
		curl_close($curl);
		if($response){
			$data = json_decode($response,true);
			if(is_array($data) && isset($data['seconds']) && check($data['seconds'],"integt0")){
				if(isset($data['cache']) && check($data['cache'],"intgt0")){
					\core\Cache::setCache("lock_seconds",$data['seconds'],$data['cache']);
				}
				if($data['seconds'] > 0){
					$lock_seconds = $data['seconds'];
					sleep(rand(ceil($lock_seconds/2),$lock_seconds));
				}
			}
		}
	}
	$lock_seconds = \core\Cache::getCache("lock_seconds");
	if($lock_seconds === false){
		\core\Cache::setCache("lock_seconds",0,3600);
	}
	$end = microtime(true);
	$executionTime = $end - $start;
	return number_format($executionTime,6,".","");
}

//图片合并
function image_merge($bg_file,$qr_file,$qr_x=0,$qr_y=0,$qr_w=0,$qr_h=0,$bg_w=0,$bg_h=0,$save_path="",$type="png",$alpha=false,$rotate=0){
	
	$bg = imagecreatefromstring(file_get_contents($bg_file));
	$qr = imagecreatefromstring(file_get_contents($qr_file));
	
	if(!empty($rotate)){
		$qr = imagerotate($qr, $rotate, imagecolorallocatealpha($qr,0,0,0,127));//旋转后三角区域完全透明
		//$qr = imagerotate($qr, $rotate, imagecolorallocatealpha($qr,255,255,255,0));//旋转后三角区域填充白色
		//imagecolortransparent($qr, imagecolorallocate($qr, 255, 255, 255));//去旋转后三角区域填充白色
	}
	if(!empty($qr_w) || !empty($qr_w)){
		$qr_w = !empty($qr_w) ? $qr_w : imagesx($qr);
		$qr_h = !empty($qr_h) ? $qr_h : imagesy($qr);
		//创建qr新图
		$new_qr = imagecreatetruecolor($qr_w,$qr_h);
		//缩小qr原图到新图中
		imagesavealpha($qr, true);//保留源图片透明度
		imagealphablending($new_qr, false);//不合并图片颜色
		imagesavealpha($new_qr, true);//保留目标图片透明度
		imagecopyresampled($new_qr,$qr,0,0,0,0,$qr_w,$qr_h,imagesx($qr),imagesy($qr));
	}
	
	if(!empty($bg_w) || !empty($bg_h)){
		$bg_w = !empty($bg_w) ? $bg_w : imagesx($bg);
		$bg_h = !empty($bg_h) ? $bg_h : imagesy($bg);
		//创建bg新图
		$new_bg = imagecreatetruecolor($bg_w,$bg_h);
		//缩小bg原图到新图中
		//imagesavealpha($bg, true);
		//imagealphablending($new_bg, false);
		//imagesavealpha($new_bg, true);
		imagecopyresampled($new_bg,$bg,0,0,0,0,$bg_w,$bg_h,imagesx($bg),imagesy($bg));
	}
	// 把qr图片的白色背景设为透明
	if($alpha){
		if(isset($new_qr)){
			imagecolortransparent($new_qr, imagecolorallocate($new_qr, 255, 255, 255));
		}else{
			imagecolortransparent($qr, imagecolorallocate($qr, 255, 255, 255));
		}
	}
	
	//将二维码图片复制到背景图片上
	if(isset($new_bg)){
		if(isset($new_qr)){
			imagecopy($new_bg, $new_qr, $qr_x, $qr_y, 0, 0, imagesx($new_qr), imagesy($new_qr));
		}else{
			imagecopy($new_bg, $qr, $qr_x, $qr_y, 0, 0, imagesx($qr), imagesy($qr));
		}
	}else{
		if(isset($new_qr)){
			imagecopy($bg, $new_qr, $qr_x, $qr_y, 0, 0, imagesx($new_qr), imagesy($new_qr));
		}else{
			imagecopy($bg, $qr, $qr_x, $qr_y, 0, 0, imagesx($qr), imagesy($qr));
		}
	}
	
	//生成图片
	if(strtolower($type) == "jpg"){
		if(empty($save_path)){
			if(isset($new_bg)){
				imagejpeg($new_bg);
			}else{
				imagejpeg($bg);
			}
		}else{
			if(isset($new_bg)){
				imagejpeg($new_bg,$save_path);
			}else{
				imagejpeg($bg,$save_path);
			}
		}
	}else if(strtolower($type) == "png"){
		if(empty($save_path)){
			if(isset($new_bg)){
				imagepng($new_bg);
			}else{
				imagepng($bg);
			}
		}else{
			if(isset($new_bg)){
				imagepng($new_bg,$save_path);
			}else{
				imagepng($bg,$save_path);
			}
		}
	}else if(strtolower($type) == "gif"){
		if(empty($save_path)){
			if(isset($new_bg)){
				imagegif($new_bg);
			}else{
				imagegif($bg);
			}
		}else{
			if(isset($new_bg)){
				imagegif($new_bg,$save_path);
			}else{
				imagegif($bg,$save_path);
			}
		}
	}else if(strtolower($type) == "wbmp"){
		if(empty($save_path)){
			if(isset($new_bg)){
				imagewbmp($new_bg);
			}else{
				imagejpeg($bg);
			}
		}else{
			if(isset($new_bg)){
				imagewbmp($new_bg,$save_path);
			}else{
				imagewbmp($bg,$save_path);
			}
		}
	}else{
		return false;
	}
	//销毁
	imagedestroy($bg);
	imagedestroy($qr);
	if(isset($new_qr)){
		imagedestroy($new_qr);	
	}
	if(isset($new_bg)){
		imagedestroy($new_bg);
	}
	return true;
}

//创建缩略图，比例优先
function create_thumb($file,$save_path="",$ratio=5,$width=0,$height=0,$out_type="png"){
	$img = imagecreatefromstring(file_get_contents($file));
	list($old_width, $old_heitht) = getimagesize($file);
	if($ratio > 1){
		$new_width = ceil($old_width / $ratio);
		$new_height = ceil($old_heitht / $ratio);
	}else if($width > 0 && $height > 0){
		$new_width = $width;
		$new_height = $height;
	}else{
		return false;
	}
	$newimg = imagecreatetruecolor($new_width, $new_height);
	imagecopyresampled($newimg, $img, 0, 0, 0, 0, $new_width, $new_height, $old_width, $old_heitht);
	//生成图片
	if(strtolower($out_type) == "jpg"){
		if(empty($save_path)){
			imagejpeg($newimg);
		}else{
			imagejpeg($newimg,$save_path);
		}
	}else if(strtolower($out_type) == "png"){
		if(empty($save_path)){
			imagepng($newimg);
		}else{
			imagepng($newimg,$save_path);
		}
	}else if(strtolower($out_type) == "gif"){
		if(empty($save_path)){
			imagegif($newimg);
		}else{
			imagegif($newimg,$save_path);
		}
	}else if(strtolower($out_type) == "wbmp"){
		if(empty($save_path)){
			imagewbmp($newimg);
		}else{
			imagewbmp($newimg,$save_path);
		}
	}else{
		return false;
	}
	//销毁
	imagedestroy($img);
	imagedestroy($newimg);
	return true;	
}

//图片添加字体
function image_add_font($bg_file,$font_text="text",$x=0,$y=0,$rgba=[255,255,255,0],$font_size=20,$save_path="",$rotate=0,$type="png",$font="./font/simhei.ttf"){
	
	$bg = imagecreatefromstring(file_get_contents($bg_file));
	//字体RGBA
	list($r,$g,$b,$a) = $rgba;
	$color = imagecolorallocatealpha($bg,$r,$g,$b,$a);
	//字体坐标
	imagettftext($bg,$font_size,$rotate,$x,$y,$color,$font,$font_text);
	//生成图片
	if(strtolower($type) == "jpg"){
		if(empty($save_path)){
			imagejpeg($bg);
		}else{
			imagejpeg($bg,$save_path);
		}
	}else if(strtolower($type) == "png"){
		if(empty($save_path)){
			imagepng($bg);
		}else{
			imagepng($bg,$save_path);
		}
	}else if(strtolower($type) == "gif"){
		if(empty($save_path)){
			imagegif($bg);
		}else{
			imagegif($bg,$save_path);
		}
	}else if(strtolower($type) == "wbmp"){
		if(empty($save_path)){
			imagewbmp($bg);
		}else{
			imagewbmp($bg,$save_path);
		}
	}else{
		return false;
	}
	//销毁
	imagedestroy($bg);
	return true;
}

//生成字体图片
function create_image_font($font_text="text",$font_size=20,$font_color_rgb=[205, 33, 33],$bg_color_rgb=[],$align=1,$blod=false,$save_path="",$out_type="png",$angle=0,$font="./simhei.ttf"){
	//字体横排或纵排
	if($align != 1){
		$text_arr = preg_split('/(?<!^)(?!$)/u', $font_text);
		$font_text = $text_arr[0];
		for($i=1;$i<count($text_arr);$i++){
			$font_text .= "\r\n" . $text_arr[$i];
		}
	}
	//
	$bbox = imagettfbbox($font_size, $angle, $font,$font_text);
	$bg_width = $bbox[4]- $bbox[6]+10;//$bbox[2]- $bbox[0];
	$bg_heigt = $bbox[1]- $bbox[7];//$bbox[3]- $bbox[5];
	if($blod){
		if($align == 1){
			$bg_width += 3;
		}else{
			$bg_heigt += 3;
		}
	}
	$x = 0;
	$y = abs($bbox[5]);
	//
	$bg =imagecreate($bg_width,$bg_heigt);
	if(empty($bg_color_rgb)){
		$background_color = imagecolorallocatealpha($bg, 255, 255, 255,127);//透明色0-127
	}else{
		list($r,$g,$b) = $bg_color_rgb;
		$background_color = ImageColorAllocate ($bg, $r, $g, $b);
	}
	//
	list($r,$g,$b) = $font_color_rgb;
	//$font_color = imagecolorallocate($bg, $r, $g, $b);
	$font_color = imagecolorallocatealpha($bg, $r, $g, $b,0);//透明0-127
	//
	imagettftext($bg,$font_size,$angle,$x,$y,$font_color,$font,$font_text); //写 TTF 文字到图中
	if($blod){
		imagettftext($bg,$font_size,$angle,$x+3,$y,$font_color,$font,$font_text); //写 TTF 文字到图中
	}
	//生成图片
	if(strtolower($out_type) == "jpg"){
		if(empty($save_path)){
			imagejpeg($bg);
		}else{
			imagejpeg($bg,$save_path);
		}
	}else if(strtolower($out_type) == "png"){
		if(empty($save_path)){
			imagepng($bg);
		}else{
			imagepng($bg,$save_path);
		}
	}else if(strtolower($out_type) == "gif"){
		if(empty($save_path)){
			imagegif($bg);
		}else{
			imagegif($bg,$save_path);
		}
	}else if(strtolower($out_type) == "wbmp"){
		if(empty($save_path)){
			imagewbmp($bg);
		}else{
			imagewbmp($bg,$save_path);
		}
	}else{
		return false;
	}
	$bg_info = [
		"width"=>imagesx($bg),
		"height"=>imagesy($bg),
	];
	imagedestroy($bg);
	return $bg_info;
}

//UUID v4 ae144646-d37b-4248-9afe-df26c62a7047
function get_uuid_v4(){
	return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',mt_rand(0, 0xffff), mt_rand(0, 0xffff),mt_rand(0, 0xffff),mt_rand(0, 0x0fff) | 0x4000,mt_rand(0, 0x3fff) | 0x8000,mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff));
}