<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { htmlindex } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { navto, getItem } from "@/utils";

const goods = ref([
  { num: 0, money: 20, zongMoney: 0 },
  { num: 0, money: 30, zongMoney: 0 },
]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const query = ref({ num: 0, money: "0.00" });
const guize = ref();

onLoad(async (e) => {
  const res = await htmlindex({ type: 2 });
  guize.value = res.data;
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
// const upCallback = async (mescroll) => {
//   apiGoods(mescroll.num, mescroll.size)
//     .then((res) => {
//       const curPageData = res.list || [];
//       if (mescroll.num == 1) goods.value = [];
//       goods.value = goods.value.concat(curPageData);
//       mescroll.endBySize(curPageData.length, res.count);
//     })
//     .catch(() => {
//       mescroll.endErr();
//     });
// };
</script>
<template>
  <view
    class="page"
    :style="{ backgroundImage: `url(${store().$state.url}zhuanyongjinBg.png)` }"
  >
    <myTitle height="178" title="" color="#fff" backColor="#fff"></myTitle>
    <view class="pa left50 tl50" style="top: 990rpx; width: 650rpx; color: #ec2d0d">
      <scroll-view scroll-y style="height: 160rpx">
        <u-parse :content="guize"></u-parse>
      </scroll-view>
      <u-gap height="100rpx"></u-gap>
      <u-text
        align="center"
        decoration="underline"
        color="#fce4c5"
        size="32rpx"
        bold
        text="佣金记录"
        @click="navto('/pages/bundle/user/commission')"
      ></u-text>
      <!-- <mescroll-uni
        class="list"
        height="300rpx"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
      >
        <view class="pr ma py30 df w690 borderBottom" v-for="(val, i) in goods" :key="i">
          <view class="df aic">
            <u-avatar size="40" :src="val.goodImg" mode="aspectFill"></u-avatar>
            <view class="ml20 f1 w">
              <view class="" style="width: 555rpx">
                <u-text size="26rpx" color="#fff" bold :text="val.goodName"></u-text>
              </view>
              <u-text
                size="22rpx"
                color="#fff"
                :text="$u.timeFormat(Date.now())"
              ></u-text>
            </view>
          </view>
        </view>
      </mescroll-uni> -->
    </view>
  </view>
</template>

<style scoped lang="less">
.page {
  background-size: cover;
  background-repeat: no-repeat;
}
</style>
