<template>
  <view class="lazy-image-container" :class="containerClass">
    <!-- 占位符 -->
    <view 
      v-if="!imageLoaded && !imageError" 
      class="lazy-image-placeholder"
      :style="placeholderStyle"
    >
      <view v-if="showSkeleton" class="skeleton-animation" />
      <u-icon 
        v-else
        name="image" 
        :size="placeholderIconSize" 
        color="#d0d0d0"
      />
    </view>

    <!-- 实际图片 -->
    <image
      v-show="imageLoaded && !imageError"
      :src="currentSrc"
      :mode="mode"
      :lazy-load="true"
      :fade-show="true"
      :webp="supportWebP"
      :show-menu-by-longpress="showMenuByLongpress"
      :class="imageClass"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
      @click="handleImageClick"
    />

    <!-- 错误状态 -->
    <view 
      v-if="imageError" 
      class="lazy-image-error"
      :style="placeholderStyle"
      @click="retryLoad"
    >
      <u-icon name="image-fill" :size="placeholderIconSize" color="#ff4757" />
      <text v-if="showErrorText" class="error-text">加载失败，点击重试</text>
    </view>

    <!-- 加载进度 -->
    <view 
      v-if="showProgress && isLoading" 
      class="lazy-image-progress"
    >
      <u-loading-icon mode="circle" size="24" />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
// 使用内联常量避免循环依赖
const SIZE_CONSTANTS = {
  MAX_IMAGE_SIZE: 1200,
  THUMBNAIL_SIZE: 200
};

// Props定义
const props = defineProps({
  // 图片源
  src: {
    type: String,
    required: true
  },
  
  // 备用图片源
  fallbackSrc: {
    type: String,
    default: ''
  },
  
  // 图片模式
  mode: {
    type: String,
    default: 'aspectFill',
    validator: (value) => [
      'scaleToFill', 'aspectFit', 'aspectFill', 'widthFix', 'heightFix'
    ].includes(value)
  },
  
  // 容器宽度
  width: {
    type: [String, Number],
    default: '100%'
  },
  
  // 容器高度
  height: {
    type: [String, Number],
    default: 'auto'
  },
  
  // 圆角
  borderRadius: {
    type: [String, Number],
    default: '0'
  },
  
  // 是否显示骨架屏
  showSkeleton: {
    type: Boolean,
    default: true
  },
  
  // 是否显示加载进度
  showProgress: {
    type: Boolean,
    default: false
  },
  
  // 是否显示错误文本
  showErrorText: {
    type: Boolean,
    default: true
  },
  
  // 占位符图标大小
  placeholderIconSize: {
    type: [String, Number],
    default: '48rpx'
  },
  
  // 是否支持长按菜单
  showMenuByLongpress: {
    type: Boolean,
    default: false
  },
  
  // 是否启用WebP
  enableWebP: {
    type: Boolean,
    default: true
  },
  
  // 图片质量（用于压缩）
  quality: {
    type: Number,
    default: 80,
    validator: (value) => value >= 1 && value <= 100
  },
  
  // 懒加载阈值（像素）
  threshold: {
    type: Number,
    default: 100
  },
  
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  }
});

// Emits定义
const emit = defineEmits(['load', 'error', 'click']);

// 状态管理
const imageLoaded = ref(false);
const imageError = ref(false);
const isLoading = ref(false);
const retryCount = ref(0);
const observer = ref(null);
const imageRef = ref(null);

// 计算属性
const supportWebP = computed(() => {
  return props.enableWebP && checkWebPSupport();
});

const currentSrc = computed(() => {
  if (!props.src) return '';
  
  // 如果支持WebP且原图不是WebP，尝试转换
  if (supportWebP.value && !props.src.includes('.webp')) {
    return convertToWebP(props.src);
  }
  
  return optimizeImageUrl(props.src);
});

const containerClass = computed(() => {
  return [
    'lazy-image-container',
    props.customClass,
    {
      'loading': isLoading.value,
      'loaded': imageLoaded.value,
      'error': imageError.value
    }
  ];
});

const imageClass = computed(() => {
  return [
    'lazy-image',
    {
      'fade-in': imageLoaded.value
    }
  ];
});

const placeholderStyle = computed(() => {
  return {
    width: formatSize(props.width),
    height: formatSize(props.height),
    borderRadius: formatSize(props.borderRadius)
  };
});

const imageStyle = computed(() => {
  return {
    width: formatSize(props.width),
    height: formatSize(props.height),
    borderRadius: formatSize(props.borderRadius)
  };
});

// 工具函数
const formatSize = (size) => {
  if (typeof size === 'number') {
    return `${size}rpx`;
  }
  return size;
};

const checkWebPSupport = () => {
  // 检查平台是否支持WebP
  try {
    let platform;

    // 优先使用新API获取平台信息
    try {
      if (uni.getDeviceInfo) {
        const deviceInfo = uni.getDeviceInfo();
        platform = deviceInfo.platform;
      }
    } catch (newApiError) {
      console.warn('新API获取设备信息失败，使用降级方案:', newApiError);
    }

    // 降级到旧API
    if (!platform) {
      // 使用新的API替换方案
      try {
        const { getDeviceInfo } = require('@/utils/systemInfo.js');
        const deviceInfo = getDeviceInfo();
        platform = deviceInfo.platform;
      } catch (error) {
        console.warn('lazy-image: 获取设备信息失败，使用默认值');
        platform = 'unknown';
      }
    }

    // 微信小程序基础库2.9.0+支持WebP
    // H5环境检查浏览器支持
    if (platform === 'h5') {
      // 创建canvas检测WebP支持
      try {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const ctx = canvas.getContext('2d');
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      } catch (e) {
        return false;
      }
    }
    // 小程序环境默认支持（除了开发工具）
    return platform !== 'devtools';
  } catch (error) {
    console.warn('检查WebP支持失败:', error);
    return false;
  }
};

const convertToWebP = (url) => {
  // 如果是外部URL，尝试添加WebP参数
  if (url.startsWith('http')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}format=webp&quality=${props.quality}`;
  }
  return url;
};

const optimizeImageUrl = (url) => {
  // 如果是外部URL，添加优化参数
  if (url.startsWith('http')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}quality=${props.quality}`;
  }
  return url;
};

// 事件处理
const handleImageLoad = (event) => {
  imageLoaded.value = true;
  imageError.value = false;
  isLoading.value = false;
  retryCount.value = 0;
  
  emit('load', event);
};

const handleImageError = (event) => {
  imageError.value = true;
  imageLoaded.value = false;
  isLoading.value = false;
  
  // 尝试使用备用图片
  if (props.fallbackSrc && retryCount.value === 0) {
    retryCount.value++;
    setTimeout(() => {
      retryLoad();
    }, 1000);
  }
  
  emit('error', event);
};

const handleImageClick = (event) => {
  emit('click', event);
};

const retryLoad = () => {
  if (retryCount.value < 3) {
    imageError.value = false;
    imageLoaded.value = false;
    isLoading.value = true;
    retryCount.value++;
    
    // 强制重新加载
    setTimeout(() => {
      isLoading.value = false;
    }, 100);
  }
};

// 生命周期
onMounted(() => {
  if (props.src) {
    isLoading.value = true;
  }
});

onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect();
  }
});

// 监听src变化
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    imageLoaded.value = false;
    imageError.value = false;
    isLoading.value = true;
    retryCount.value = 0;
  }
});
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: var(--color-background, #f5f5f5);
}

.lazy-image-placeholder,
.lazy-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background, #f5f5f5);
  transition: all 0.3s ease;
}

.lazy-image-error {
  cursor: pointer;
  
  &:hover {
    background-color: #f0f0f0;
  }
}

.error-text {
  margin-top: var(--spacing-sm, 8rpx);
  font-size: var(--font-size-xs, 20rpx);
  color: var(--color-text-secondary, #666);
  text-align: center;
}

.lazy-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.fade-in {
    opacity: 1;
  }
}

.lazy-image-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.skeleton-animation {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .lazy-image-container {
    max-width: 100%;
  }
}
</style>
