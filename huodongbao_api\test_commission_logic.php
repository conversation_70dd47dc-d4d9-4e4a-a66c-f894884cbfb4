<?php
/**
 * 测试邀请佣金发放逻辑
 * 验证首次成为会员和续费会员的佣金发放差异
 */

require_once __DIR__ . '/core/init.php';
require_once __DIR__ . '/controller/User.php';

class CommissionTest {
    private $user_controller;
    
    public function __construct() {
        $this->user_controller = new \controller\User();
    }
    
    /**
     * 测试首次会员检查逻辑
     */
    public function test_first_time_member_check() {
        echo "=== 测试首次会员检查逻辑 ===\n";
        
        // 测试用户ID（请根据实际情况修改）
        $test_uids = [5, 17, 22]; // 从数据库中选择一些测试用户
        
        foreach ($test_uids as $uid) {
            // 查询用户的会员订单历史
            $orders = \core\Db::_fetchAll("
                SELECT order_id, uid, status, pay_time 
                FROM user_huiyuan_order 
                WHERE uid = ? AND status = 1 
                ORDER BY pay_time ASC
            ", [$uid]);
            
            $order_count = count($orders);
            
            // 使用反射调用私有方法进行测试
            $reflection = new ReflectionClass($this->user_controller);
            $method = $reflection->getMethod('is_first_time_member');
            $method->setAccessible(true);
            $is_first_time = $method->invoke($this->user_controller, $uid);
            
            echo "用户ID: {$uid}\n";
            echo "  历史会员订单数: {$order_count}\n";
            echo "  是否首次成为会员: " . ($is_first_time ? '是' : '否') . "\n";
            echo "  订单详情:\n";
            
            foreach ($orders as $order) {
                echo "    - 订单: {$order['order_id']}, 支付时间: {$order['pay_time']}\n";
            }
            echo "\n";
        }
    }
    
    /**
     * 模拟会员佣金结算测试
     */
    public function test_commission_settlement() {
        echo "=== 测试会员佣金结算逻辑 ===\n";
        
        // 查询一些已支付的会员订单进行测试
        $test_orders = \core\Db::_fetchAll("
            SELECT * FROM user_huiyuan_order 
            WHERE status = 1 AND yongjin_uid_1 > 0 
            LIMIT 3
        ");
        
        foreach ($test_orders as $order) {
            echo "测试订单: {$order['order_id']}\n";
            echo "  购买用户: {$order['uid']}\n";
            echo "  邀请人: {$order['yongjin_uid_1']}\n";
            echo "  佣金金额: {$order['yongjin_money_1']}\n";
            
            // 检查是否首次成为会员
            $reflection = new ReflectionClass($this->user_controller);
            $method = $reflection->getMethod('is_first_time_member');
            $method->setAccessible(true);
            $is_first_time = $method->invoke($this->user_controller, $order['uid']);
            
            echo "  是否首次成为会员: " . ($is_first_time ? '是' : '否') . "\n";
            echo "  预期结果: " . ($is_first_time ? '发放邀请佣金 + 检查运营佣金' : '不发放邀请佣金 + 检查运营佣金') . "\n";
            echo "\n";
        }
    }
    
    /**
     * 检查运营佣金发放逻辑
     */
    public function test_operation_commission() {
        echo "=== 测试运营佣金发放逻辑 ===\n";
        
        // 查询分会长用户
        $presidents = \core\Db::_fetchAll("
            SELECT uid, nickname, branch_id 
            FROM user 
            WHERE role_type = '1' AND branch_id IS NOT NULL 
            LIMIT 3
        ");
        
        foreach ($presidents as $president) {
            echo "分会长: {$president['nickname']} (ID: {$president['uid']})\n";
            echo "  分会ID: {$president['branch_id']}\n";
            
            // 查询该分会长的运营佣金记录
            $operation_commissions = \core\Db::_fetchAll("
                SELECT * FROM user_yongjin_log 
                WHERE uid = ? AND commission_type = 'operation' 
                ORDER BY time DESC 
                LIMIT 3
            ", [$president['uid']]);
            
            echo "  运营佣金记录数: " . count($operation_commissions) . "\n";
            foreach ($operation_commissions as $commission) {
                echo "    - 金额: {$commission['money']}, 月份: {$commission['related_month']}, 状态: {$commission['status']}\n";
            }
            echo "\n";
        }
    }
}

// 执行测试
try {
    \core\dbConn();
    
    $test = new CommissionTest();
    
    echo "开始测试邀请佣金发放逻辑...\n\n";
    
    $test->test_first_time_member_check();
    $test->test_commission_settlement();
    $test->test_operation_commission();
    
    echo "测试完成！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
