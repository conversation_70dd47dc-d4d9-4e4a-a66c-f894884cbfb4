<script setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad, onShow, onReachBottom, onPullDownRefresh } from "@dcloudio/uni-app";
import { userget_notifications, usermark_notification_read } from "@/api";
import { store } from "@/store";
import { navto } from "@/utils";

const notifications = ref([]);
const loading = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = 10;
const total = ref(0);
const unreadCount = ref(0);

onLoad(() => {
  loadNotifications();
});

onShow(() => {
  // 标记所有通知为已读
  markAllAsRead();
});

onReachBottom(() => {
  if (!finished.value && !loading.value) {
    loadMore();
  }
});

onPullDownRefresh(() => {
  refresh();
});

// 加载通知列表
const loadNotifications = async (isRefresh = false) => {
  if (loading.value) return;

  if (isRefresh) {
    page.value = 1;
    finished.value = false;
  }

  loading.value = true;

  try {
    const res = await userget_notifications({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      page: page.value,
      page_size: pageSize
    });

    if (res?.status === 'ok') {
      const newNotifications = res.data.list || [];

      if (isRefresh) {
        notifications.value = newNotifications;
      } else {
        notifications.value = [...notifications.value, ...newNotifications];
      }

      total.value = res.data.total || 0;
      unreadCount.value = res.data.unread_count || 0;

      // 检查是否还有更多数据
      if (newNotifications.length < pageSize) {
        finished.value = true;
      }
    } else if (res?.status === 'empty') {
      if (isRefresh) {
        notifications.value = [];
      }
      finished.value = true;
    } else {
      uni.$u.toast(res?.msg || '获取通知失败');
    }
  } catch (error) {
    console.error('获取通知失败:', error);
    uni.$u.toast('获取通知失败');
  } finally {
    loading.value = false;
    if (isRefresh) {
      uni.stopPullDownRefresh();
    }
  }
};

// 加载更多
const loadMore = () => {
  page.value++;
  loadNotifications();
};

// 刷新
const refresh = () => {
  loadNotifications(true);
};

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    await usermark_notification_read({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      notification_id: 0 // 0表示标记所有为已读
    });
  } catch (error) {
    console.error('标记已读失败:', error);
  }
};

// 获取通知类型显示文本
const getNotificationTypeText = (type) => {
  const typeMap = {
    'share_success': '分享成功',
    'receive_vip': '会员领取',
    'activity_update': '活动更新',
    'activity_cancelled': '活动取消',
    'activity_registration': '活动报名',
    'registration_cancelled': '取消报名',
    'system': '系统通知'
  };
  return typeMap[type] || '通知';
};

// 判断是否为全局通知
const isGlobalNotification = (notification) => {
  return notification.is_global === 1 || notification.is_global === '1';
};

// 获取通知类型图标
const getNotificationIcon = (type) => {
  const iconMap = {
    'share_success': 'share-square',
    'receive_vip': 'vip',
    'activity_update': 'calendar',
    'system': 'bell'
  };
  return iconMap[type] || 'bell';
};

// 获取通知类型颜色
const getNotificationColor = (type) => {
  const colorMap = {
    'share_success': '#6AC086',
    'receive_vip': '#D19C69',
    'activity_update': '#5DADE2',
    'system': '#999'
  };
  return colorMap[type] || '#999';
};

// 格式化时间
const formatTime = (timeStr) => {
  // 修复iOS日期格式问题：将 "2025-05-27 12:39:27" 转换为 "2025/05/27 12:39:27"
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);

  // 检查日期是否有效
  if (isNaN(time.getTime())) {
    return '时间格式错误';
  }

  const now = new Date();
  const diff = now - time;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) { // 1天内
    return Math.floor(diff / 3600000) + '小时前';
  } else if (diff < 604800000) { // 1周内
    return Math.floor(diff / 86400000) + '天前';
  } else {
    return time.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }
};

// 点击通知项
const onNotificationClick = (notification) => {
  // 根据通知类型进行相应的跳转
  if (notification.type === 'activity_update' && notification.related_id) {
    navto(`/pages/bundle/active/activeInfo?id=${notification.related_id}`);
  } else if (notification.type === 'share_success' && notification.related_id) {
    navto(`/pages/bundle/user/shareRecords`);
  }
  // 其他类型的通知可以根据需要添加跳转逻辑
};
</script>

<template>
  <view class="page">
    <myTitle
      title="我的通知"
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      color="#ffffff"
      :blod="true"
      backColor="#ffffff"
      :backShow="true"
    ></myTitle>

    <view class="notification-container">
      <!-- 通知统计 -->
      <view v-if="total > 0" class="notification-stats">
        <text class="stats-text">共 {{ total }} 条通知</text>
        <text v-if="unreadCount > 0" class="unread-text">{{ unreadCount }} 条未读</text>
      </view>

      <!-- 通知列表 -->
      <view v-if="notifications.length > 0" class="notification-list">
        <view
          v-for="(item, index) in notifications"
          :key="item.id"
          class="notification-item"
          :class="{ 'unread': item.is_read == 0 }"
          @click="onNotificationClick(item)"
        >
          <!-- 通知图标 -->
          <view class="notification-icon" :style="{ backgroundColor: getNotificationColor(item.type) + '20' }">
            <u-icon
              :name="getNotificationIcon(item.type)"
              :color="getNotificationColor(item.type)"
              size="32rpx"
            ></u-icon>
          </view>

          <!-- 通知内容 -->
          <view class="notification-content">
            <view class="notification-header">
              <text class="notification-title">{{ item.title }}</text>
              <text class="notification-time">{{ formatTime(item.created_at) }}</text>
            </view>
            <text class="notification-text">{{ item.content }}</text>
            <view class="notification-footer">
              <view class="notification-tags">
                <text class="notification-type">{{ getNotificationTypeText(item.type) }}</text>
                <text v-if="isGlobalNotification(item)" class="global-tag">全局</text>
              </view>
              <view v-if="item.is_read == 0" class="unread-dot"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-state">
        <u-icon name="bell" color="#ccc" size="120rpx"></u-icon>
        <text class="empty-text">暂无通知</text>
        <text class="empty-desc">您的通知消息将在这里显示</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading && notifications.length === 0" class="loading-state">
        <u-loading-icon mode="circle" color="#6AC086" size="60rpx"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading && notifications.length > 0" class="load-more">
        <u-loading-icon mode="circle" color="#6AC086" size="40rpx"></u-loading-icon>
        <text class="load-more-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="finished && notifications.length > 0" class="no-more">
        <text class="no-more-text">没有更多通知了</text>
      </view>
    </view>
  </view>
</template>

<style lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
  padding-bottom: 40rpx;
  position: relative;
  /* 确保页面不会被导航栏遮挡 */
  overflow-x: hidden;
}

/* 确保myTitle组件正确显示 */
:deep(.title) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  width: 100% !important;
  /* 添加安全区域适配 */
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.notification-container {
  padding: 20rpx 30rpx;
  /* 增加顶部间距，确保不被导航栏遮挡 */
  margin-top: calc(200rpx + constant(safe-area-inset-top));
  margin-top: calc(200rpx + env(safe-area-inset-top));
  min-height: calc(100vh - 200rpx - constant(safe-area-inset-top));
  min-height: calc(100vh - 200rpx - env(safe-area-inset-top));
}

.notification-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
}

.stats-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.unread-text {
  font-size: 24rpx;
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 107, 107, 0.2);
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notification-item {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
  transition: all 0.3s ease;
  position: relative;

  &.unread {
    border-left: 6rpx solid #6AC086;
    background: linear-gradient(135deg, rgba(106, 192, 134, 0.02) 0%, rgba(255, 255, 255, 0.98) 100%);
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.15);
  }
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.notification-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  margin-right: 16rpx;
}

.notification-time {
  font-size: 22rpx;
  color: #999;
  flex-shrink: 0;
}

.notification-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.notification-type {
  font-size: 22rpx;
  color: #999;
  background: rgba(106, 192, 134, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.global-tag {
  font-size: 20rpx;
  color: #fff;
  background: #FF6B6B;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.unread-dot {
  width: 12rpx;
  height: 12rpx;
  background: #FF6B6B;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.3);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
  margin: 24rpx 0 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  line-height: 1.5;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
  margin-top: 16rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style>
