<template>
  <view class="empty-state-container" :class="{ 'full-height': fullHeight }">
    <!-- 图标区域 -->
    <view class="empty-icon-wrapper">
      <u-icon 
        v-if="icon" 
        :name="icon" 
        :size="iconSize" 
        :color="iconColor"
      />
      <image 
        v-else-if="image" 
        :src="image" 
        class="empty-image" 
        mode="aspectFit"
      />
      <u-icon 
        v-else 
        name="inbox" 
        :size="iconSize" 
        color="#d0d0d0"
      />
    </view>

    <!-- 文字区域 -->
    <view class="empty-text-wrapper">
      <text class="empty-title" v-if="title">{{ title }}</text>
      <text class="empty-description" v-if="description">{{ description }}</text>
    </view>

    <!-- 操作按钮区域 -->
    <view class="empty-action-wrapper" v-if="showAction">
      <u-button 
        :text="actionText"
        :type="actionType"
        :size="actionSize"
        :custom-style="actionStyle"
        @click="handleAction"
      />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// Props定义
const props = defineProps({
  // 图标相关
  icon: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  iconSize: {
    type: [String, Number],
    default: '120rpx'
  },
  iconColor: {
    type: String,
    default: '#d0d0d0'
  },
  
  // 文字相关
  title: {
    type: String,
    default: '暂无数据'
  },
  description: {
    type: String,
    default: ''
  },
  
  // 操作按钮相关
  showAction: {
    type: Boolean,
    default: false
  },
  actionText: {
    type: String,
    default: '重新加载'
  },
  actionType: {
    type: String,
    default: 'primary'
  },
  actionSize: {
    type: String,
    default: 'normal'
  },
  
  // 布局相关
  fullHeight: {
    type: Boolean,
    default: true
  },
  
  // 预设类型
  type: {
    type: String,
    default: 'default'
    // 移除validator避免在小程序中的兼容性问题
  }
});

// Emits定义
const emit = defineEmits(['action']);

// 计算属性
const actionStyle = computed(() => ({
  borderRadius: 'var(--radius-md, 16rpx)',
  marginTop: 'var(--spacing-lg, 24rpx)'
}));

// 根据类型预设配置
const typeConfig = computed(() => {
  const configs = {
    default: {
      icon: 'inbox',
      title: '暂无数据',
      description: ''
    },
    network: {
      icon: 'wifi-off',
      title: '网络连接失败',
      description: '请检查网络连接后重试',
      showAction: true,
      actionText: '重新加载'
    },
    search: {
      icon: 'search',
      title: '没有找到相关内容',
      description: '试试其他关键词吧',
      showAction: true,
      actionText: '重新搜索'
    },
    list: {
      icon: 'list',
      title: '暂无内容',
      description: '还没有相关数据'
    },
    comment: {
      icon: 'chat',
      title: '暂无评论',
      description: '快来发表第一条评论吧',
      showAction: true,
      actionText: '发表评论'
    },
    activity: {
      icon: 'calendar',
      title: '暂无活动',
      description: '当前没有相关活动',
      showAction: true,
      actionText: '发布活动'
    }
  };
  
  return configs[props.type] || configs.default;
});

// 最终配置（props优先级高于预设）
const finalConfig = computed(() => ({
  icon: props.icon || typeConfig.value.icon,
  title: props.title || typeConfig.value.title,
  description: props.description || typeConfig.value.description,
  showAction: props.showAction || typeConfig.value.showAction || false,
  actionText: props.actionText || typeConfig.value.actionText
}));

// 处理操作按钮点击
const handleAction = () => {
  emit('action');
};
</script>

<style lang="scss" scoped>
.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl, 40rpx) var(--spacing-lg, 24rpx);
  text-align: center;
  
  &.full-height {
    min-height: 400rpx;
  }
}

.empty-icon-wrapper {
  margin-bottom: var(--spacing-lg, 24rpx);
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.6;
}

.empty-text-wrapper {
  margin-bottom: var(--spacing-lg, 24rpx);
}

.empty-title {
  display: block;
  font-size: var(--font-size-lg, 32rpx);
  color: var(--color-text-primary, #333);
  font-weight: 500;
  margin-bottom: var(--spacing-sm, 16rpx);
  line-height: 1.4;
}

.empty-description {
  display: block;
  font-size: var(--font-size-md, 28rpx);
  color: var(--color-text-secondary, #666);
  line-height: 1.5;
  max-width: 400rpx;
}

.empty-action-wrapper {
  width: 100%;
  max-width: 300rpx;
}
</style>
