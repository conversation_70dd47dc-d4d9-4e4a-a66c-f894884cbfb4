<fieldset class="layui-elem-field layui-field-title">
    <legend>分会详情</legend>
</fieldset>

<div class="layui-form">
    <div class="layui-row layui-col-space15">
        <!-- 分会基本信息 -->
        <div class="layui-col-md6">
            <fieldset class="layui-elem-field">
                <legend>分会信息</legend>
                <div class="layui-field-box">
                    <table class="layui-table">
                        <tbody>
                            <tr>
                                <td width="120">分会ID：</td>
                                <td><?php echo $branch['branch_id']; ?></td>
                            </tr>
                            <tr>
                                <td>分会名称：</td>
                                <td><?php echo htmlspecialchars($branch['branch_name']); ?></td>
                            </tr>
                            <tr>
                                <td>分会地址：</td>
                                <td><?php echo htmlspecialchars($branch['branch_location']); ?></td>
                            </tr>
                            <tr>
                                <td>成员数量：</td>
                                <td><span class="layui-badge layui-bg-blue"><?php echo $branch['member_count']; ?> 人</span></td>
                            </tr>
                            <tr>
                                <td>活动数量：</td>
                                <td><span class="layui-badge layui-bg-green"><?php echo $branch['activity_count']; ?> 个</span></td>
                            </tr>
                            <tr>
                                <td>创建时间：</td>
                                <td><?php echo $branch['created_at']; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>
        
        <!-- 分会长信息 -->
        <div class="layui-col-md6">
            <fieldset class="layui-elem-field">
                <legend>分会长信息</legend>
                <div class="layui-field-box">
                    <?php if(!empty($branch['president_info'])): ?>
                    <table class="layui-table">
                        <tbody>
                            <tr>
                                <td width="120">分会长ID：</td>
                                <td><?php echo $branch['president_info']['uid']; ?></td>
                            </tr>
                            <tr>
                                <td>姓名：</td>
                                <td><?php echo htmlspecialchars($branch['president_info']['nickname']); ?></td>
                            </tr>
                            <tr>
                                <td>联系电话：</td>
                                <td><?php echo $branch['president_info']['mobile']; ?></td>
                            </tr>
                            <tr>
                                <td>任职时间：</td>
                                <td><?php echo date('Y-m-d', strtotime($branch['president_info']['time'])); ?></td>
                            </tr>
                        </tbody>
                    </table>
                    <?php else: ?>
                    <div style="text-align: center; padding: 50px;">
                        <i class="layui-icon layui-icon-face-cry" style="font-size: 50px; color: #ccc;"></i>
                        <p style="color: #999; margin-top: 10px;">暂无分会长信息</p>
                    </div>
                    <?php endif; ?>
                </div>
            </fieldset>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back();">
            <i class="layui-icon">&#xe65c;</i> 返回列表
        </button>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 页面加载完成提示
    layer.msg('分会详情加载完成', {icon: 1, time: 1000});
});
</script>
