<?php
namespace model;
use core\Db;
class Juhesms{
	
	static private $appkey = "a60ac235921747fad41656859aa51814";
	
	static private $sms_url = "http://v.juhe.cn/sms/send";//模板单发

	public function __construct(){
		
	}
	
    //模板单发 验证码
    static public function send($mobile,$tpl_params,$tpl_id=258704){
		$params = [
			"key"=>self::$appkey,
			"mobile"=>$mobile,
			"tpl_id"=>$tpl_id,
			"vars"=>is_string($tpl_params) ? json_encode(["code"=>$tpl_params]) : json_encode($tpl_params),
		];
		$res = curl(self::$sms_url,$params);
		$result = json_decode($res['body'],true);
		if(isset($result['error_code']) && $result['error_code'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['error_code']) ? $result['error_code'] : "";
			$msg .= isset($result['reason']) ? $result['reason'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }

    //中奖通知
    static public function zhongjiang($mobile,$tpl_params,$tpl_id=259083){
		$params = [
			"key"=>self::$appkey,
			"mobile"=>$mobile,
			"tpl_id"=>$tpl_id,
			"vars"=>is_string($tpl_params) ? json_encode(["money"=>$tpl_params]) : json_encode($tpl_params),
		];
		$res = curl(self::$sms_url,$params);
		$result = json_decode($res['body'],true);
		if(isset($result['error_code']) && $result['error_code'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['error_code']) ? $result['error_code'] : "";
			$msg .= isset($result['reason']) ? $result['reason'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }

    //结算通知
    static public function jiesuan($mobile,$tpl_params,$tpl_id=259084){
		$params = [
			"key"=>self::$appkey,
			"mobile"=>$mobile,
			"tpl_id"=>$tpl_id,
			"vars"=>is_string($tpl_params) ? json_encode(["money"=>$tpl_params]) : json_encode($tpl_params),
		];
		$res = curl(self::$sms_url,$params);
		$result = json_decode($res['body'],true);
		if(isset($result['error_code']) && $result['error_code'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['error_code']) ? $result['error_code'] : "";
			$msg .= isset($result['reason']) ? $result['reason'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
