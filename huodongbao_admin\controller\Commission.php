<?php
namespace controller;
use core\Controller;
use core\Db;

/*
 * @className 佣金管理
 */
class Commission extends Controller {

    public function __construct() {
        parent::__construct();
        $this->auth();
    }

    /*
     * @name 佣金管理主页
     * @method index
     */
    public function index() {
        dbConn();

        // 获取佣金统计数据
        $stats = [];

        // 当前月份
        $current_month = date('Y-m');
        $current_month_start = strtotime(date('Y-m-01 00:00:00'));
        $current_month_end = strtotime(date('Y-m-t 23:59:59'));

        // 本月运营佣金总额（根据time字段查询本自然月）
        $month_operation_commission = Db()->table("user_yongjin_log")
            ->where("commission_type='operation' AND time>=:start_time AND time<=:end_time")
            ->prepareParam([
                ":start_time" => $current_month_start,
                ":end_time" => $current_month_end
            ])
            ->sum("money");
        $stats['month_operation_commission'] = $month_operation_commission ?: 0;

        // 累计运营佣金
        $total_operation_commission = Db()->table("user_yongjin_log")
            ->where("commission_type='operation'")
            ->sum("money");
        $stats['total_operation_commission'] = $total_operation_commission ?: 0;

        // 本月销售佣金总额（邀请佣金，兼容旧数据）
        $month_sales_commission = Db()->table("user_yongjin_log")
            ->where("(commission_type='invite' OR commission_type IS NULL OR commission_type='') AND time>=:start_time AND time<=:end_time")
            ->prepareParam([
                ":start_time" => $current_month_start,
                ":end_time" => $current_month_end
            ])
            ->sum("money");
        $stats['month_sales_commission'] = $month_sales_commission ?: 0;

        // 累计销售佣金（邀请佣金，兼容旧数据）
        $total_sales_commission = Db()->table("user_yongjin_log")
            ->where("commission_type='invite' OR commission_type IS NULL OR commission_type=''")
            ->sum("money");
        $stats['total_sales_commission'] = $total_sales_commission ?: 0;

        // 保持向后兼容性
        $stats['month_commission'] = $stats['month_operation_commission'];
        $stats['total_commission'] = $stats['total_operation_commission'];

        // 🆕 新增：获取活动收入统计数据
        $activity_income_stats = $this->getActivityIncomeStats();
        assign("activity_income_stats", $activity_income_stats);

        assign("stats", $stats);
        return view(['header','commission/index','footer']);
    }

    /**
     * 🆕 新增：活动收入记录管理
     */
    public function activity_income_records() {
        dbConn();

        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        $activity_id = $_GET['activity_id'] ?? '';
        $publisher_uid = $_GET['publisher_uid'] ?? '';
        $start_date = $_GET['start_date'] ?? '';
        $end_date = $_GET['end_date'] ?? '';

        // 构建查询条件
        $where = "1=1";
        $params = [];

        if (!empty($status)) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        if (!empty($activity_id)) {
            $where .= " AND activity_id = :activity_id";
            $params[':activity_id'] = $activity_id;
        }

        if (!empty($publisher_uid)) {
            $where .= " AND publisher_uid = :publisher_uid";
            $params[':publisher_uid'] = $publisher_uid;
        }

        if (!empty($start_date)) {
            $where .= " AND time >= :start_date";
            $params[':start_date'] = $start_date . ' 00:00:00';
        }

        if (!empty($end_date)) {
            $where .= " AND time <= :end_date";
            $params[':end_date'] = $end_date . ' 23:59:59';
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM activity_income_log WHERE {$where}";
        $total = Db::_fetch($count_sql, $params)['total'];

        // 获取记录
        $offset = ($page - 1) * $limit;
        $sql = "SELECT ail.*, h.name as activity_name, u.nickname as publisher_name
                FROM activity_income_log ail
                LEFT JOIN huodong h ON ail.activity_id = h.id
                LEFT JOIN user u ON ail.publisher_uid = u.uid
                WHERE {$where}
                ORDER BY ail.time DESC
                LIMIT {$offset}, {$limit}";

        $records = Db::_fetchAll($sql, $params);

        // 处理状态文本
        foreach ($records as &$record) {
            $record['status_text'] = $this->getActivityIncomeStatusText($record['status']);
        }

        assign("records", $records);
        assign("total", $total);
        assign("page", $page);
        assign("limit", $limit);
        assign("filters", $_GET);
        return view(['header','commission/activity_income_records','footer']);
    }

    /**
     * 🆕 新增：活动收入提现申请管理
     */
    public function activity_income_withdrawals() {
        dbConn();

        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';

        // 构建查询条件
        $where = "source_type = 'activity_income'";
        $params = [];

        if (!empty($status)) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM user_tixian WHERE {$where}";
        $total = Db::_fetch($count_sql, $params)['total'];

        // 获取提现申请
        $offset = ($page - 1) * $limit;
        $sql = "SELECT ut.*, u.nickname, ub.bank_name, ub.bank_account, ub.account_name
                FROM user_tixian ut
                LEFT JOIN user u ON ut.uid = u.uid
                LEFT JOIN user_bank ub ON ut.bank_id = ub.id
                WHERE {$where}
                ORDER BY ut.id DESC
                LIMIT {$offset}, {$limit}";

        $withdrawals = Db::_fetchAll($sql, $params);

        // 处理状态文本
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['status_text'] = $this->getWithdrawalStatusText($withdrawal['status']);
        }

        assign("withdrawals", $withdrawals);
        assign("total", $total);
        assign("page", $page);
        assign("limit", $limit);
        assign("filters", $_GET);
        return view(['header','commission/activity_income_withdrawals','footer']);
    }

    /**
     * 🆕 新增：审核活动收入提现申请
     */
    public function approve_activity_income_withdrawal() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->error("请求方式错误");
        }

        $withdrawal_id = intval($_POST['withdrawal_id']);
        $action = $_POST['action']; // approve 或 reject
        $remark = $_POST['remark'] ?? '';

        if (!in_array($action, ['approve', 'reject'])) {
            $this->error("操作类型错误");
        }

        dbConn();
        Db::begin();
        try {
            // 获取提现申请信息
            $withdrawal = Db()->table("user_tixian")
                ->where("id={$withdrawal_id} AND source_type='activity_income'")
                ->fetch();

            if (!$withdrawal) {
                throw new \Exception("提现申请不存在");
            }

            if ($withdrawal['status'] != 0) {
                throw new \Exception("该申请已处理");
            }

            if ($action === 'approve') {
                // 批准提现
                Db()->table("user_tixian")
                    ->where("id={$withdrawal_id}")
                    ->update([
                        'status' => 1,
                        'beizhu' => $remark ?: '管理员批准提现'
                    ]);

                // 更新活动收入记录状态
                if (!empty($withdrawal['commission_ids'])) {
                    $income_ids = $withdrawal['commission_ids'];
                    Db()->table("activity_income_log")
                        ->where("id IN ({$income_ids})")
                        ->update([
                            'status' => 3,
                            'withdraw_time' => date('Y-m-d H:i:s')
                        ]);
                }

                $message = "活动收入提现申请已批准";
            } else {
                // 驳回提现
                Db()->table("user_tixian")
                    ->where("id={$withdrawal_id}")
                    ->update([
                        'status' => 2,
                        'beizhu' => $remark ?: '管理员驳回提现'
                    ]);

                // 恢复活动收入记录状态
                if (!empty($withdrawal['commission_ids'])) {
                    $income_ids = $withdrawal['commission_ids'];
                    Db()->table("activity_income_log")
                        ->where("id IN ({$income_ids})")
                        ->update([
                            'status' => 1,
                            'withdraw_apply_id' => null
                        ]);
                }

                $message = "活动收入提现申请已驳回";
            }

            Db::commit();
            $this->success($message);

        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    // 🗑️ 已删除：佣金计算功能（calculate方法）- 根据用户要求去除后台佣金计算页面和相关方法逻辑

    /*
     * @name 佣金记录查询
     * @method records
     */
    public function records() {
        $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
        $page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'], "intgt0") ? (int)$_REQUEST['page_size'] : 20;
        
        assign("page_size", $page_size);
        assign("page", $page);
        
        dbConn();
        
        $where = "1";
        $prepareParam = [];
        $pageParams = ["page" => $page, "page_size" => $page_size];
        
        // 佣金类型筛选
        if (isset($_REQUEST['commission_type']) && in_array($_REQUEST['commission_type'], ['invite', 'operation'])) {
            $where .= " AND l.commission_type=:commission_type";
            $prepareParam[":commission_type"] = $_REQUEST['commission_type'];
            $pageParams['commission_type'] = $_REQUEST['commission_type'];
            assign("commission_type", $_REQUEST['commission_type']);
        }
        
        // 用户搜索
        if (!empty($_REQUEST['user_name'])) {
            $where .= " AND u.nickname LIKE :user_name";
            $prepareParam[":user_name"] = "%" . trim($_REQUEST['user_name']) . "%";
            $pageParams['user_name'] = $_REQUEST['user_name'];
        }
        
        // 月份筛选（根据time字段）
        if (!empty($_REQUEST['month'])) {
            $month = $_REQUEST['month']; // 格式：2024-01
            $month_start = strtotime($month . '-01 00:00:00');
            $month_end = strtotime(date('Y-m-t 23:59:59', $month_start));
            $where .= " AND l.time>=:month_start AND l.time<=:month_end";
            $prepareParam[":month_start"] = $month_start;
            $prepareParam[":month_end"] = $month_end;
            $pageParams['month'] = $_REQUEST['month'];
        }
        
        // 检查是否支持状态管理
        $columns = Db::_fetchAll("SHOW COLUMNS FROM user_yongjin_log LIKE 'status'");
        $support_status = !empty($columns);

        // 查询佣金记录
        if ($support_status) {
            $select_fields = "id,uid,money,type,order_id,time,commission_type,branch_id,status,settlement_time,available_time,remark";
        } else {
            $select_fields = "id,uid,money,type,order_id,time,commission_type,branch_id";
        }

        $data = Db()->table("user_yongjin_log")
            ->select($select_fields)
            ->where($where)
            ->prepareParam($prepareParam)
            ->order("time DESC")
            ->page($page, $page_size, $pageParams);

        // 补充用户信息和其他必要字段
        foreach ($data as &$record) {
            $user = Db()->table("user")->select("nickname, mobile, branch_id")->where("uid={$record['uid']}")->fetch();
            $record['user_nickname'] = $user['nickname'] ?? '';
            $record['nickname'] = $user['nickname'] ?? '';
            $record['mobile'] = $user['mobile'] ?? '';

            // 获取分会信息（如果有分会ID）
            if (!empty($user['branch_id'])) {
                $branch = Db()->table("user_branch")->select("branch_name")->where("branch_id={$user['branch_id']}")->fetch();
                $record['branch_name'] = $branch['branch_name'] ?? '';
            } else {
                $record['branch_name'] = '';
            }

            // 添加活动数量统计（根据佣金类型）
            if ($record['commission_type'] == 'operation') {
                // 运营佣金：统计该用户审核的活动数量
                $activity_count = Db()->table("huodong")->where("reviewed_by_president={$record['uid']} AND president_review_status=1")->count();
            } else {
                // 邀请佣金：统计邀请的用户数量
                $activity_count = Db()->table("user")->where("p_uid={$record['uid']}")->count();
            }
            $record['activity_count'] = $activity_count;

            // 格式化创建时间
            $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['time']));

            // 添加状态信息（如果支持状态管理）
            if ($support_status && isset($record['status'])) {
                $status_texts = [
                    0 => '待结算',
                    1 => '可提取',
                    2 => '提现中',
                    3 => '已提现',
                    4 => '已驳回',
                    5 => '已冻结'
                ];
                $record['status_text'] = $status_texts[$record['status']] ?? '未知';

                // 格式化状态相关时间
                $record['settlement_time_formatted'] = $record['settlement_time'] ? date('Y-m-d H:i:s', strtotime($record['settlement_time'])) : '';
                $record['available_time_formatted'] = $record['available_time'] ? date('Y-m-d H:i:s', strtotime($record['available_time'])) : '';
            }
        }
        
        assign("data", $data);
        assign("pageParams", $pageParams);

        return view(['header','commission/records','footer']);
    }

    /*
     * @name 佣金记录详情
     * @method record_detail
     */
    public function record_detail() {
        if (!isset($_REQUEST['id']) || !check($_REQUEST['id'], "intgt0")) {
            alert("参数错误");
            return;
        }

        $id = (int)$_REQUEST['id'];

        dbConn();

        // 获取佣金记录详情
        $record = Db()->table("user_yongjin_log")->where("id={$id}")->fetch();

        if (empty($record)) {
            alert("佣金记录不存在");
            return;
        }

        // 补充用户信息
        $user = Db()->table("user")->select("nickname, mobile, branch_id, avatar, time as register_time")->where("uid={$record['uid']}")->fetch();
        $record['user_nickname'] = $user['nickname'] ?? '';
        $record['user_mobile'] = $user['mobile'] ?? '';
        $record['user_avatar'] = $user['avatar'] ?? '';
        $record['user_register_time'] = $user['register_time'] ?? '';

        // 获取分会信息
        if (!empty($user['branch_id'])) {
            $branch = Db()->table("user_branch")->select("branch_name, branch_location")->where("branch_id={$user['branch_id']}")->fetch();
            $record['branch_name'] = $branch['branch_name'] ?? '';
            $record['branch_location'] = $branch['branch_location'] ?? '';
        } else {
            $record['branch_name'] = '';
            $record['branch_location'] = '';
        }

        // 格式化时间
        $record['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($record['time']));

        // 获取相关活动信息（如果是运营佣金）
        if ($record['commission_type'] == 'operation' && !empty($record['related_activity_id'])) {
            $activity = Db()->table("huodong")->select("name, title, time")->where("id={$record['related_activity_id']}")->fetch();
            $record['activity_info'] = $activity;
        }

        assign("record", $record);

        return view(['header','commission/record_detail','footer']);
    }

    /*
     * @name 运营佣金记录
     * @method operation_records
     */
    public function operation_records() {
        $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
        $page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'], "intgt0") ? (int)$_REQUEST['page_size'] : 20;

        assign("page_size", $page_size);
        assign("page", $page);

        dbConn();

        $where = "commission_type='operation'";
        $prepareParam = [];
        $pageParams = ["page" => $page, "page_size" => $page_size];

        // 用户搜索
        if (!empty($_REQUEST['user_name'])) {
            $where .= " AND uid IN (SELECT uid FROM user WHERE nickname LIKE :user_name)";
            $prepareParam[":user_name"] = "%" . trim($_REQUEST['user_name']) . "%";
            $pageParams['user_name'] = $_REQUEST['user_name'];
        }

        // 月份筛选（根据time字段）
        if (!empty($_REQUEST['month'])) {
            $month = $_REQUEST['month']; // 格式：2024-01
            $month_start = strtotime($month . '-01 00:00:00');
            $month_end = strtotime(date('Y-m-t 23:59:59', $month_start));
            $where .= " AND time>=:month_start AND time<=:month_end";
            $prepareParam[":month_start"] = $month_start;
            $prepareParam[":month_end"] = $month_end;
            $pageParams['month'] = $_REQUEST['month'];
        }

        // 分会筛选
        if (!empty($_REQUEST['branch_id'])) {
            $where .= " AND branch_id=:branch_id";
            $prepareParam[":branch_id"] = $_REQUEST['branch_id'];
            $pageParams['branch_id'] = $_REQUEST['branch_id'];
        }

        // 检查是否支持状态管理
        $columns = Db::_fetchAll("SHOW COLUMNS FROM user_yongjin_log LIKE 'status'");
        $support_status = !empty($columns);

        // 查询运营佣金记录
        if ($support_status) {
            $select_fields = "id,uid,money,type,order_id,time,commission_type,branch_id,status,settlement_time,available_time,remark";
        } else {
            $select_fields = "id,uid,money,type,order_id,time,commission_type,branch_id";
        }

        $data = Db()->table("user_yongjin_log")
            ->select($select_fields)
            ->where($where)
            ->prepareParam($prepareParam)
            ->order("time DESC")
            ->page($page, $page_size, $pageParams);

        // 补充用户信息和分会信息
        foreach ($data as &$record) {
            $user = Db()->table("user")->select("nickname, mobile, branch_id")->where("uid={$record['uid']}")->fetch();
            $record['user_nickname'] = $user['nickname'] ?? '';
            $record['nickname'] = $user['nickname'] ?? '';
            $record['mobile'] = $user['mobile'] ?? '';

            // 获取分会信息
            if (!empty($record['branch_id'])) {
                $branch = Db()->table("user_branch")->select("branch_name")->where("branch_id={$record['branch_id']}")->fetch();
                $record['branch_name'] = $branch['branch_name'] ?? '';
            } else {
                $record['branch_name'] = '';
            }

            // 统计该分会长审核的活动数量
            $activity_count = Db()->table("huodong")->where("reviewed_by_president={$record['uid']} AND president_review_status=1")->count();
            $record['activity_count'] = $activity_count;

            // 格式化创建时间
            $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['time']));

            // 添加状态信息（如果支持状态管理）
            if ($support_status && isset($record['status'])) {
                $status_texts = [
                    0 => '待结算',
                    1 => '可提取',
                    2 => '提现中',
                    3 => '已提现',
                    4 => '已驳回',
                    5 => '已冻结'
                ];
                $record['status_text'] = $status_texts[$record['status']] ?? '未知';

                // 格式化状态相关时间
                $record['settlement_time_formatted'] = $record['settlement_time'] ? date('Y-m-d H:i:s', strtotime($record['settlement_time'])) : '';
                $record['available_time_formatted'] = $record['available_time'] ? date('Y-m-d H:i:s', strtotime($record['available_time'])) : '';
            }
        }

        // 获取分会列表用于筛选
        $branches = Db()->table("user_branch")->select("branch_id, branch_name")->fetchAll();
        assign("branches", $branches);

        assign("data", $data);
        assign("pageParams", $pageParams);
        assign("support_status", $support_status);

        return view(['header','commission/records','footer']);
    }







    /*
     * @name 搜索用户（AJAX接口）
     * @method search_users
     */
    public function search_users() {
        if (empty($_REQUEST['keyword'])) {
            echo json_encode(['status' => 'error', 'msg' => '请输入搜索关键词']);
            return;
        }
        
        $keyword = trim($_REQUEST['keyword']);
        
        dbConn();
        
        $users = Db()->table("user")
            ->select("uid, nickname, mobile")
            ->where("(nickname LIKE :keyword OR mobile LIKE :keyword) AND is_huiyuan=1")
            ->prepareParam([":keyword" => "%{$keyword}%"])
            ->limit(10)
            ->fetchAll();
        
        echo json_encode(['status' => 'ok', 'data' => $users]);
    }

    /*
     * @name 批量更新佣金状态
     * @method batch_update_commission_status
     * @param commission_ids string 佣金记录ID列表（逗号分隔）
     * @param target_status int 目标状态
     * @param remark string 操作备注
     * @param admin_id int 管理员ID
     */
    private function batch_update_commission_status($commission_ids, $target_status, $remark = '', $admin_id = 0) {
        if (empty($commission_ids) || !is_numeric($target_status)) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $target_status = (int)$target_status;
        $admin_id = (int)$admin_id;

        // 验证状态值
        if (!in_array($target_status, [0, 1, 2, 3, 4, 5])) {
            return ["status" => "error", "msg" => "无效的状态值"];
        }

        // 处理ID列表
        $commission_ids_str = trim($commission_ids);
        $ids_array = explode(',', $commission_ids_str);
        $ids_array = array_map('intval', $ids_array);
        $ids_array = array_filter($ids_array, function($id) { return $id > 0; });

        if (empty($ids_array)) {
            return ["status" => "error", "msg" => "无效的佣金记录ID"];
        }

        $commission_ids_str = implode(',', $ids_array);

        dbConn();

        // 检查佣金记录是否存在
        $records = Db()->table("user_yongjin_log")
            ->select("id,uid,money,status")
            ->where("id IN ({$commission_ids_str})")
            ->fetchAll();

        if (empty($records)) {
            return ["status" => "error", "msg" => "佣金记录不存在"];
        }

        if (count($records) !== count($ids_array)) {
            return ["status" => "error", "msg" => "部分佣金记录不存在"];
        }

        Db::begin();
        try {
            // 更新状态
            $update_data = [
                "status" => $target_status,
                "settlement_time" => date('Y-m-d H:i:s'),
                "remark" => $remark
            ];

            // 根据目标状态设置不同的时间字段
            if ($target_status == 1) { // 可提取
                $update_data["available_time"] = date('Y-m-d H:i:s');
            }

            $result = Db()->table("user_yongjin_log")
                ->where("id IN ({$commission_ids_str})")
                ->update($update_data);

            if ($result === false) {
                throw new \Exception("更新佣金状态失败");
            }

            // 🔧 P1修复：根据用户要求，佣金不更新到余额字段
            // 佣金状态更新为可提取时，不再更新用户余额
            // 佣金记录仅用于提现申请时的统计和管理

            // 记录操作日志
            $this->root_log("批量更新佣金状态，ID: {$commission_ids_str}，目标状态: {$target_status}，备注: {$remark}");

            Db::commit();
            return ["status" => "ok", "msg" => "状态更新成功"];

        } catch (\Exception $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "更新失败：" . $e->getMessage()];
        }
    }

    /*
     * @name 佣金状态管理
     * @method status_management
     */
    public function status_management() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理批量状态更新
            $commission_ids = $_REQUEST['commission_ids'] ?? '';
            $target_status = $_REQUEST['target_status'] ?? '';
            $remark = $_REQUEST['remark'] ?? '';

            if (empty($commission_ids) || !is_numeric($target_status)) {
                alert("参数错误");
                return;
            }

            $result = $this->batch_update_commission_status($commission_ids, $target_status, $remark, $_SESSION['root_info']['rid']);

            if ($result && $result['status'] === 'ok') {
                alert("状态更新成功", "success", "/commission/status_management");
            } else {
                alert($result['msg'] ?? "状态更新失败");
            }
        } else {
            // 显示状态管理页面
            $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
            $page_size = 20;

            dbConn();

            $where = "1";
            $prepareParam = [];
            $pageParams = ["page" => $page, "page_size" => $page_size];

            // 状态筛选
            if (!empty($_REQUEST['status']) && is_numeric($_REQUEST['status'])) {
                $where .= " AND status=:status";
                $prepareParam[":status"] = (int)$_REQUEST['status'];
                $pageParams['status'] = $_REQUEST['status'];
                assign("status", $_REQUEST['status']);
            }

            // 佣金类型筛选
            if (!empty($_REQUEST['commission_type'])) {
                $where .= " AND commission_type=:commission_type";
                $prepareParam[":commission_type"] = $_REQUEST['commission_type'];
                $pageParams['commission_type'] = $_REQUEST['commission_type'];
                assign("commission_type", $_REQUEST['commission_type']);
            }

            // 检查是否支持状态管理
            $columns = Db::_fetchAll("SHOW COLUMNS FROM user_yongjin_log LIKE 'status'");
            if (empty($columns)) {
                alert("系统未启用佣金状态管理功能，请先执行数据库升级");
                return;
            }

            // 查询佣金记录
            $data = Db()->table("user_yongjin_log")
                ->select("id,uid,money,commission_type,status,settlement_time,available_time,time,remark")
                ->where($where)
                ->prepareParam($prepareParam)
                ->order("time DESC")
                ->page($page, $page_size, $pageParams);

            // 补充用户信息
            foreach ($data as &$record) {
                $user = Db()->table("user")->select("nickname, mobile")->where("uid={$record['uid']}")->fetch();
                $record['user_nickname'] = $user['nickname'] ?? '';
                $record['user_mobile'] = $user['mobile'] ?? '';

                // 添加状态文本
                $status_texts = [
                    0 => '待结算',
                    1 => '可提取',
                    2 => '提现中',
                    3 => '已提现',
                    4 => '已驳回',
                    5 => '已冻结'
                ];
                $record['status_text'] = $status_texts[$record['status']] ?? '未知';

                // 格式化时间
                $record['settlement_time_formatted'] = $record['settlement_time'] ? date('Y-m-d H:i:s', strtotime($record['settlement_time'])) : '';
                $record['available_time_formatted'] = $record['available_time'] ? date('Y-m-d H:i:s', strtotime($record['available_time'])) : '';
            }

            assign("data", $data);
            assign("pageParams", $pageParams);

            return view(['header','commission/status_management','footer']);
        }
    }

    /*
     * @name 手动触发佣金结算（已废弃）
     * @method manual_settlement
     */
    public function manual_settlement() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            alert("手动结算功能已废弃，佣金将通过定时任务自动结算", "warning", "/commission/status_management");
        } else {
            return view(['header','commission/manual_settlement','footer']);
        }
    }

    /*
     * @name 佣金配置管理
     * @method config_management
     */
    public function config_management() {
        dbConn();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_REQUEST['action'] ?? '';

            switch ($action) {
                case 'add':
                    return $this->add_commission_config();
                case 'edit':
                    return $this->edit_commission_config();
                case 'delete':
                    return $this->delete_commission_config();
                case 'toggle_status':
                    return $this->toggle_commission_config_status();
                case 'get_config':
                    return $this->get_commission_config_detail();
                default:
                    alert("无效的操作");
                    return;
            }
        }

        // 获取分页参数
        $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
        $page_size = 20;

        // 构建查询条件
        $where = "1=1";
        $prepareParam = [];
        $pageParams = [];

        // 目标类型筛选
        if (!empty($_REQUEST['target_type'])) {
            $where .= " AND target_type=:target_type";
            $prepareParam[":target_type"] = $_REQUEST['target_type'];
            $pageParams['target_type'] = $_REQUEST['target_type'];
            assign("target_type", $_REQUEST['target_type']);
        }

        // 佣金类型筛选
        if (!empty($_REQUEST['commission_type'])) {
            $where .= " AND commission_type=:commission_type";
            $prepareParam[":commission_type"] = $_REQUEST['commission_type'];
            $pageParams['commission_type'] = $_REQUEST['commission_type'];
            assign("commission_type", $_REQUEST['commission_type']);
        }

        // 状态筛选
        if (isset($_REQUEST['status']) && $_REQUEST['status'] !== '') {
            $where .= " AND status=:status";
            $prepareParam[":status"] = (int)$_REQUEST['status'];
            $pageParams['status'] = $_REQUEST['status'];
            assign("status", $_REQUEST['status']);
        }

        // 查询数据
        $data = Db()->table("commission_configs")
            ->select("*")
            ->where($where)
            ->prepareParam($prepareParam)
            ->order("priority DESC, created_at DESC")
            ->page($page, $page_size, $pageParams);

        assign("data", $data);
        assign("pageParams", $pageParams);

        return view(['header','commission/config_management','footer']);
    }

    /*
     * @name 添加佣金配置
     * @method add_commission_config
     */
    private function add_commission_config() {
        responseType("json");

        // 参数验证
        $required_fields = ['config_name', 'target_type', 'target_id', 'commission_type', 'calculation_method'];
        foreach ($required_fields as $field) {
            if (empty($_REQUEST[$field])) {
                return ["status" => "error", "msg" => "请填写完整信息"];
            }
        }

        // 验证计算方式对应的金额字段
        if ($_REQUEST['calculation_method'] === 'fixed' && empty($_REQUEST['base_amount'])) {
            return ["status" => "error", "msg" => "固定金额不能为空"];
        }

        if ($_REQUEST['calculation_method'] === 'percentage' && empty($_REQUEST['percentage_rate'])) {
            return ["status" => "error", "msg" => "百分比率不能为空"];
        }

        try {
            $data = [
                'config_name' => trim($_REQUEST['config_name']),
                'target_type' => $_REQUEST['target_type'],
                'target_id' => $_REQUEST['target_id'],
                'commission_type' => $_REQUEST['commission_type'],
                'calculation_method' => $_REQUEST['calculation_method'],
                'base_amount' => !empty($_REQUEST['base_amount']) ? (float)$_REQUEST['base_amount'] : null,
                'percentage_rate' => !empty($_REQUEST['percentage_rate']) ? (float)$_REQUEST['percentage_rate'] : null,
                'min_amount' => !empty($_REQUEST['min_amount']) ? (float)$_REQUEST['min_amount'] : 0.00,
                'max_amount' => !empty($_REQUEST['max_amount']) ? (float)$_REQUEST['max_amount'] : null,
                'settlement_cycle' => $_REQUEST['settlement_cycle'] ?? 'monthly',
                'available_delay_days' => (int)($_REQUEST['available_delay_days'] ?? 0),
                'status' => 1,
                'priority' => (int)($_REQUEST['priority'] ?? 0),
                'effective_date' => !empty($_REQUEST['effective_date']) ? $_REQUEST['effective_date'] : null,
                'expiry_date' => !empty($_REQUEST['expiry_date']) ? $_REQUEST['expiry_date'] : null,
                'created_by' => $_SESSION['root_info']['rid']
            ];

            $id = Db()->table("commission_configs")->insert($data);

            if ($id) {
                return ["status" => "ok", "msg" => "添加成功"];
            } else {
                return ["status" => "error", "msg" => "添加失败"];
            }

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "添加失败：" . $e->getMessage()];
        }
    }

    /*
     * @name 编辑佣金配置
     * @method edit_commission_config
     */
    private function edit_commission_config() {
        responseType("json");

        if (empty($_REQUEST['id'])) {
            return ["status" => "error", "msg" => "配置ID不能为空"];
        }

        $id = (int)$_REQUEST['id'];

        // 检查配置是否存在
        $config = Db()->table("commission_configs")->where("id={$id}")->fetch();
        if (empty($config)) {
            return ["status" => "error", "msg" => "配置不存在"];
        }

        try {
            $data = [
                'config_name' => trim($_REQUEST['config_name']),
                'target_type' => $_REQUEST['target_type'],
                'target_id' => $_REQUEST['target_id'],
                'commission_type' => $_REQUEST['commission_type'],
                'calculation_method' => $_REQUEST['calculation_method'],
                'base_amount' => !empty($_REQUEST['base_amount']) ? (float)$_REQUEST['base_amount'] : null,
                'percentage_rate' => !empty($_REQUEST['percentage_rate']) ? (float)$_REQUEST['percentage_rate'] : null,
                'min_amount' => !empty($_REQUEST['min_amount']) ? (float)$_REQUEST['min_amount'] : 0.00,
                'max_amount' => !empty($_REQUEST['max_amount']) ? (float)$_REQUEST['max_amount'] : null,
                'settlement_cycle' => $_REQUEST['settlement_cycle'] ?? 'monthly',
                'available_delay_days' => (int)($_REQUEST['available_delay_days'] ?? 0),
                'priority' => (int)($_REQUEST['priority'] ?? 0),
                'effective_date' => !empty($_REQUEST['effective_date']) ? $_REQUEST['effective_date'] : null,
                'expiry_date' => !empty($_REQUEST['expiry_date']) ? $_REQUEST['expiry_date'] : null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = Db()->table("commission_configs")->where("id={$id}")->update($data);

            if ($result !== false) {
                return ["status" => "ok", "msg" => "更新成功"];
            } else {
                return ["status" => "error", "msg" => "更新失败"];
            }

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "更新失败：" . $e->getMessage()];
        }
    }

    /*
     * @name 删除佣金配置（真实删除）
     * @method delete_commission_config
     */
    private function delete_commission_config() {
        responseType("json");

        if (empty($_REQUEST['id'])) {
            return ["status" => "error", "msg" => "配置ID不能为空"];
        }

        $id = (int)$_REQUEST['id'];

        try {
            // 检查配置是否存在
            $config = Db()->table("commission_configs")->where("id={$id}")->fetch();
            if (empty($config)) {
                return ["status" => "error", "msg" => "配置不存在"];
            }

            // 检查是否有关联的佣金记录
            $commission_count = Db()->table("user_yongjin_log")
                ->where("config_id={$id}")
                ->count();

            if ($commission_count > 0) {
                return ["status" => "error", "msg" => "该配置已有关联的佣金记录，不能删除。建议使用停用功能。"];
            }

            // 执行真实删除
            $result = Db()->table("commission_configs")->where("id={$id}")->delete();

            if ($result !== false) {
                return ["status" => "ok", "msg" => "删除成功"];
            } else {
                return ["status" => "error", "msg" => "删除失败"];
            }

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "删除失败：" . $e->getMessage()];
        }
    }

    /*
     * @name 切换佣金配置状态
     * @method toggle_commission_config_status
     */
    private function toggle_commission_config_status() {
        responseType("json");

        if (empty($_REQUEST['id'])) {
            return ["status" => "error", "msg" => "配置ID不能为空"];
        }

        $id = (int)$_REQUEST['id'];

        try {
            $config = Db()->table("commission_configs")->select("status")->where("id={$id}")->fetch();
            if (empty($config)) {
                return ["status" => "error", "msg" => "配置不存在"];
            }

            $new_status = $config['status'] == 1 ? 0 : 1;
            $result = Db()->table("commission_configs")
                ->where("id={$id}")
                ->update(['status' => $new_status, 'updated_at' => date('Y-m-d H:i:s')]);

            if ($result !== false) {
                $status_text = $new_status == 1 ? '启用' : '停用';
                return ["status" => "ok", "msg" => "{$status_text}成功"];
            } else {
                return ["status" => "error", "msg" => "操作失败"];
            }

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "操作失败：" . $e->getMessage()];
        }
    }

    /*
     * @name 获取佣金配置详情
     * @method get_commission_config_detail
     */
    private function get_commission_config_detail() {
        responseType("json");

        if (empty($_REQUEST['id'])) {
            return ["status" => "error", "msg" => "配置ID不能为空"];
        }

        $id = (int)$_REQUEST['id'];

        try {
            $config = Db()->table("commission_configs")->where("id={$id}")->fetch();

            if (empty($config)) {
                return ["status" => "error", "msg" => "配置不存在"];
            }

            return ["status" => "ok", "data" => $config];

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "获取失败：" . $e->getMessage()];
        }
    }

    /**
     * 🆕 新增：获取活动收入统计数据
     */
    private function getActivityIncomeStats() {
        // 本月统计
        $current_month = date('Y-m');
        $month_stats = Db::_fetch("
            SELECT
                COUNT(*) as total_records,
                SUM(publisher_income) as total_income,
                SUM(platform_fee) as total_platform_fee,
                COUNT(DISTINCT activity_id) as activity_count,
                COUNT(DISTINCT publisher_uid) as publisher_count
            FROM activity_income_log
            WHERE DATE_FORMAT(time, '%Y-%m') = '{$current_month}'
        ");

        // 总计统计
        $total_stats = Db::_fetch("
            SELECT
                COUNT(*) as total_records,
                SUM(publisher_income) as total_income,
                SUM(platform_fee) as total_platform_fee,
                COUNT(DISTINCT activity_id) as activity_count,
                COUNT(DISTINCT publisher_uid) as publisher_count
            FROM activity_income_log
        ");

        // 状态统计
        $status_stats = Db::_fetchAll("
            SELECT status, COUNT(*) as count, SUM(publisher_income) as amount
            FROM activity_income_log
            GROUP BY status
        ");

        return [
            'month' => $month_stats ?: ['total_records' => 0, 'total_income' => 0, 'total_platform_fee' => 0, 'activity_count' => 0, 'publisher_count' => 0],
            'total' => $total_stats ?: ['total_records' => 0, 'total_income' => 0, 'total_platform_fee' => 0, 'activity_count' => 0, 'publisher_count' => 0],
            'status' => $status_stats ?: []
        ];
    }

    /**
     * 🆕 新增：获取活动收入状态文本
     */
    private function getActivityIncomeStatusText($status) {
        $status_texts = [
            0 => '待结算',
            1 => '可提取',
            2 => '提现中',
            3 => '已提现',
            4 => '已驳回',
            5 => '已冻结'
        ];
        return $status_texts[$status] ?? '未知状态';
    }

    /**
     * 🆕 新增：获取提现状态文本
     */
    private function getWithdrawalStatusText($status) {
        $status_texts = [
            0 => '待审核',
            1 => '已批准',
            2 => '已驳回'
        ];
        return $status_texts[$status] ?? '未知状态';
    }

    /**
     * 🆕 新增：成功响应
     */
    private function success($message) {
        echo json_encode(['status' => 'ok', 'msg' => $message]);
        exit;
    }

    /**
     * 🆕 新增：错误响应
     */
    private function error($message) {
        echo json_encode(['status' => 'error', 'msg' => $message]);
        exit;
    }
}
