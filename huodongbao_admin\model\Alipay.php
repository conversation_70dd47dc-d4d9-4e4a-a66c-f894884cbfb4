<?php
namespace model;
use core\Db;

//支付宝支付 Api V2、V3 混合版本
class Alipay{
	
	static private $app_id = "";
	static private $app_private_key = "";
	static private $app_public_key = "";
	static private $alipay_public_key = "";
	static private $alipay_root_key = "";
	static private $app_cert_sn = "";
	static private $alipay_root_cert_sn = "";
	static private $notify_url = HTTP_HOST . "/pay/alipay_notify";
	
	public function __construct(){
		
	}
	
	//初始化配置信息
	static public function init(){
		self::$app_id = config("app.alipay_appid");
		self::$app_private_key = file_get_contents(config("app.alipay_app_private_key"));
		if(stripos(self::$app_private_key,"PRIVATE KEY") === false){
			self::$app_private_key = "-----BEGIN RSA PRIVATE KEY-----\n" . wordwrap(self::$app_private_key, 64, "\n", true) . "\n-----END RSA PRIVATE KEY-----";
		}
		self::$app_public_key = file_get_contents(config("app.alipay_app_public_key"));
		self::$alipay_public_key = file_get_contents(config("app.alipay_ali_public_key"));
		self::$alipay_root_key = file_get_contents(config("app.alipay_ali_root_key"));
		self::$alipay_root_cert_sn = self::getRootCertSN();
		self::$app_cert_sn = self::getCertSN();
	}

	//app支付 V2
	static public function app_v2($order_id,$money,$subject="商品支付"){
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]，金额不能为0
			"subject"=>$subject,//订单标题
			"time_expire"=>date("Y-m-d H:i:s",strtotime("+30 minutes")),//订单超时时间，可选
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.app.pay",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
			"notify_url"=>self::$notify_url,
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		if(!$sign)return false;
		$params = $systemParams;
		$params["sign"] = $sign;
		return http_build_query($params);
	}

	//wap支付 V2
	static public function wap_v2($order_id,$money,$subject="商品支付",$return_url="",$quit_url=""){
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]，金额不能为0
			"subject"=>$subject,//订单标题
			"quit_url"=>$quit_url,//中途退出url
			"product_code" => "QUICK_WAP_WAY",
			"time_expire"=>date("Y-m-d H:i:s",strtotime("+30 minutes")),//订单超时时间，可选
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.wap.pay",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
			"notify_url"=>self::$notify_url,
			"return_url"=>$return_url,
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		if(!$sign)return false;
		$params = $systemParams;
		$params["sign"] = $sign;
		//
        $sHtml = "<form id='alipaysubmit' name='alipaysubmit' action='https://openapi.alipay.com/gateway.do?charset=UTF-8' method='POST'>";
        foreach($params as $key=>$val){
            if(!empty($val)){
                $val = str_replace("'", "&apos;", $val);
                $val = str_replace("\"","&quot;",$val);
                $sHtml .= "<input type='hidden' name='" . $key . "' value='" . $val . "'/>";
            }
        }
        $sHtml = $sHtml . "<input type='submit' value='ok' style='display:none;'></form>";
        $sHtml = $sHtml . "<script>document.forms['alipaysubmit'].submit();</script>";
        return $sHtml;
	}

	//PC支付 V2
	static public function pc_v2($order_id,$money,$subject="商品支付",$return_url=""){
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]，金额不能为0
			"subject"=>$subject,//订单标题
			"product_code"=>"FAST_INSTANT_TRADE_PAY",
			"qr_pay_mode"=>"4",//0：订单码-简约前置模式，对应 iframe 宽度不能小于600px，高度不能小于300px；1：订单码-前置模式，对应iframe 宽度不能小于 300px，高度不能小于600px；3：订单码-迷你前置模式，对应 iframe 宽度不能小于 75px，高度不能小于75px；4：订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小。跳转模式下，用户的扫码界面是由支付宝生成的，不在商户的域名下。支持传入的枚举值有：2：订单码-跳转模式
			"qrcode_width"=>"100",//商户自定义二维码宽度。注：qr_pay_mode=4时该参数有效
			"time_expire"=>date("Y-m-d H:i:s",strtotime("+30 minutes")),//订单超时时间，可选
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.page.pay",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
			"notify_url"=>self::$notify_url,
			"return_url"=>$return_url,
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		//dump($sign);
		if(!$sign)return false;
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		return $res;
	}
	
	//qr 出示二维码支付 V2
	static public function qr_pay_v2($order_id,$money,$auth_code,$subject="商品支付"){
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]，金额不能为0
			"subject"=>$subject,//订单标题
			"auth_code"=>$auth_code,//支付授权码
			"scene"=>"bar_code",//bar_code：当面付条码支付场景；security_code：当面付刷脸支付场景
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.pay",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
			"notify_url"=>self::$notify_url,
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		if(!$sign)return false;
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['alipay_trade_pay_response']['code']) && $data['alipay_trade_pay_response']['code'] == "10000" && bccomp($data['alipay_trade_pay_response']['total_amount'],"0.00",2) > 0){
			$data = [
				"transaction_id"=>$data['alipay_trade_pay_response']['trade_no'],
				"buyer_logon_id"=>$data['alipay_trade_pay_response']['buyer_logon_id'],//买家支付宝账号
				"pay_time"=>$data['alipay_trade_pay_response']['gmt_payment'],//支付时间
				"buyer_pay_amount"=>$data['alipay_trade_pay_response']['buyer_pay_amount'],//买家付款的金额
				"total_amount"=>$data['alipay_trade_pay_response']['total_amount'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['alipay_trade_pay_response']['sub_code'])){
			return ["status"=>"error","msg"=>$data['alipay_trade_pay_response']['sub_code'] . (isset($data['alipay_trade_pay_response']['sub_msg']) ? ":{$data['alipay_trade_pay_response']['sub_msg']}" : "")];
		}
		return false;
	}	
	
	//qr 二维码扫码支付 V2 预下单请求生成的二维码有效时间为2小时
	static public function qr_scan_pay_v2($order_id,$money,$subject="商品支付"){
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]，金额不能为0
			"subject"=>$subject,//订单标题
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.precreate",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
			"notify_url"=>self::$notify_url,
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		if(!$sign)return false;
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			//return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['alipay_trade_precreate_response']['code']) && $data['alipay_trade_precreate_response']['code'] == "10000" && isset($data['alipay_trade_precreate_response']['qr_code'])){
			$data = [
				"out_trade_no"=>$data['alipay_trade_precreate_response']['out_trade_no'],
				"qr_code"=>$data['alipay_trade_precreate_response']['qr_code'],
				"share_code"=>isset($data['alipay_trade_precreate_response']['share_code']) ? $data['alipay_trade_precreate_response']['share_code'] : "",
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['alipay_trade_precreate_response']['sub_code'])){
			return ["status"=>"error","msg"=>$data['alipay_trade_precreate_response']['sub_code'] . (isset($data['alipay_trade_precreate_response']['sub_msg']) ? ":{$data['alipay_trade_precreate_response']['sub_msg']}" : "")];
		}
		return false;
	}	
	//qr 出示二维码支付 V3
	static public function qr_pay_v3($order_id,$money,$auth_code,$subject="商品支付"){
		$url = "https://openapi.alipay.com/v3/alipay/trade/pay";
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额。单位为元，精确到小数点后两位，取值范围：[0.01,100000000] 。
			"subject"=>$subject,//订单标题。不可使用特殊字符，如 /，=，& 等。
			"auth_code"=>$auth_code,//支付授权码。
			"scene"=>"bar_code",//bar_code：当面付条码支付场景；security_code：当面付刷脸支付场景，对应的auth_code为fp开头的刷脸标识串；
			"notify_url"=>self::$notify_url,//支付宝服务器主动通知商户服务器里指定的页面http/https路径。在body参数中传递
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['trade_no']) && isset($data['total_amount']) && bccomp($data['total_amount'],"0.00",2) > 0){
			$data = [
				"transaction_id"=>$data['trade_no'],
				"out_trade_no"=>$data['out_trade_no'],
				"total_amount"=>$data['total_amount'],//交易金额
				"receipt_amount"=>$data['receipt_amount'],//实收金额
				"pay_time"=>$data['gmt_payment'],//支付时间
				"buyer_user_id"=>$data['buyer_user_id'],//买家在支付宝的用户id
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}
	//qr 二维码扫码支付 V3 预下单请求生成的二维码有效时间为2小时
	static public function qr_scan_pay_v3($order_id,$money,$subject="商品支付"){
		$url = "https://openapi.alipay.com/v3/alipay/trade/precreate";
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"total_amount"=>$money,//订单总金额。单位为元，精确到小数点后两位，取值范围：[0.01,100000000] 。
			"subject"=>$subject,//订单标题。不可使用特殊字符，如 /，=，& 等。
			"notify_url"=>self::$notify_url,//支付宝服务器主动通知商户服务器里指定的页面http/https路径。在body参数中传递
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['qr_code']) && isset($data['out_trade_no'])){
			$data = [
				"qr_code"=>$data['qr_code'],
				"out_trade_no"=>$data['out_trade_no'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}	
	
	//支付查询订单 V2
	static public function get_transactions_v2($order_id){
		$bizParams = [
			"out_trade_no"=>$order_id
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.query",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['alipay_trade_query_response']['code']) && $data['alipay_trade_query_response']['code'] == "10000" && ($data['alipay_trade_query_response']['trade_status'] == "TRADE_SUCCESS" || $data['alipay_trade_query_response']['trade_status'] == "TRADE_FINISHED")){
			$data = [
				"transaction_id"=>$data['alipay_trade_query_response']['trade_no'],
				"trade_status"=>$data['alipay_trade_query_response']['trade_status'],
				"success_time"=>$data['alipay_trade_query_response']['send_pay_date'],
				"buyer_pay_amount"=>$data['alipay_trade_query_response']['buyer_pay_amount'],
				"total"=>$data['alipay_trade_query_response']['total_amount'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['alipay_trade_query_response']['sub_code'])){
			return ["status"=>"error","msg"=>"查询失败:" . $data['alipay_trade_query_response']['sub_code'] . (isset($data['alipay_trade_query_response']['sub_msg']) ? ":{$data['alipay_trade_query_response']['sub_msg']}" : "")];
		}
		return false;
	}
	
	//支付查询订单 V3 商户订单号
	static public function get_transactions_v3($order_id){
		$url = "https://openapi.alipay.com/v3/alipay/trade/query";
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['trade_status']) && ($data['trade_status'] == "TRADE_SUCCESS" || $data['trade_status'] == "TRADE_FINISHED")){
			$data = [
				"transaction_id"=>$data['trade_no'],
				"trade_status"=>$data['trade_status'],
				"success_time"=>$data['send_pay_date'],
				"buyer_pay_amount"=>$data['buyer_pay_amount'],
				"total"=>$data['total_amount'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}

	//退款 V2 商户订单号
	static public function refund_v2($order_id,$refund_amount,$refund_reason="协商退款"){
		$bizParams = [
			"out_trade_no"=>$order_id,
			"out_request_no"=>$order_id,//退款请求号
			"refund_amount"=>$refund_amount,//退款金额,该金额不能大于订单金额，单位为元，支持两位小数
			"refund_reason"=>$refund_reason,//退款原因说明,可选
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.refund",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['alipay_trade_refund_response']['code']) && 
			$data['alipay_trade_refund_response']['code'] == "10000" && 
			isset($data['alipay_trade_refund_response']['refund_fee']) &&
			bccomp($data['alipay_trade_refund_response']['refund_fee'],"0.00",2) > 0
		){
			$data = [
				"transaction_id"=>$data['alipay_trade_refund_response']['trade_no'],
				"out_trade_no"=>$data['alipay_trade_refund_response']['out_trade_no'],
				"buyer_logon_id"=>$data['alipay_trade_refund_response']['buyer_logon_id'],//用户的登录id
				"refund_fee"=>$data['alipay_trade_refund_response']['refund_fee'],//退款总金额。单位：元。 指该笔交易累计已经退款成功的金额。
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['alipay_trade_refund_response']['sub_code'])){
			return ["status"=>"error","msg"=>"查询失败:" . $data['alipay_trade_refund_response']['sub_code'] . (isset($data['alipay_trade_refund_response']['sub_msg']) ? ":{$data['alipay_trade_refund_response']['sub_msg']}" : "")];
		}
		return false;
	}
	
	//退款 V3 商户订单号
	static public function refund_v3($order_id,$refund_amount,$refund_reason="协商退款"){
		$url = "https://openapi.alipay.com/v3/alipay/trade/refund";
		$bizParams = [
			"out_trade_no"=>$order_id,//商户订单号
			"out_request_no"=>$order_id,//退款请求号
			"refund_amount"=>$refund_amount,//退款金额,该金额不能大于订单金额，单位为元，支持两位小数
			"refund_reason"=>$refund_reason,//退款原因说明,可选
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['refund_fee']) &&
			bccomp($data['refund_fee'],"0.00",2) > 0
		){
			$data = [
				"transaction_id"=>$data['trade_no'],//支付宝交易单号
				"out_trade_no"=>$data['out_trade_no'],//商户订单号
				"buyer_logon_id"=>$data['buyer_logon_id'],//用户的登录id
				"refund_fee"=>$data['refund_fee'],//退款总金额。单位：元。 指该笔交易累计已经退款成功的金额。
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}

	//退款查询订单 V2 商户订单号
	static public function refund_query_v2($order_id){
		$bizParams = [
			"out_trade_no"=>$order_id,
			"out_request_no"=>$order_id,//退款请求号
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.fastpay.refund.query",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['alipay_trade_fastpay_refund_query_response']['code']) && 
			$data['alipay_trade_fastpay_refund_query_response']['code'] == "10000" && 
			isset($data['alipay_trade_fastpay_refund_query_response']['refund_status']) &&
			$data['alipay_trade_fastpay_refund_query_response']['refund_status'] == "REFUND_SUCCESS"
		){
			$data = [
				"transaction_id"=>$data['alipay_trade_fastpay_refund_query_response']['trade_no'],
				"out_trade_no"=>$data['alipay_trade_fastpay_refund_query_response']['out_trade_no'],
				"total_amount"=>$data['alipay_trade_fastpay_refund_query_response']['total_amount'],
				"refund_amount"=>$data['alipay_trade_fastpay_refund_query_response']['refund_amount'],
				"refund_status"=>$data['alipay_trade_fastpay_refund_query_response']['refund_status'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['alipay_trade_fastpay_refund_query_response']['sub_code'])){
			return ["status"=>"error","msg"=>$data['alipay_trade_fastpay_refund_query_response']['sub_code'] . (isset($data['alipay_trade_fastpay_refund_query_response']['sub_msg']) ? ":{$data['alipay_trade_fastpay_refund_query_response']['sub_msg']}" : "")];
		}
		return false;
	}
	
	//退款查询订单 V3 商户订单号
	static public function refund_query_v3($order_id){
		$url = "https://openapi.alipay.com/v3/alipay/trade/fastpay/refund/query";
		$bizParams = [
			"out_trade_no"=>$order_id,//由商家订单号，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
			"out_request_no"=>$order_id,//退款请求号
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['refund_status']) && $data['refund_status'] == "REFUND_SUCCESS"){
			$data = [
				"transaction_id"=>$data['trade_no'],
				"out_trade_no"=>$data['out_trade_no'],
				"total_amount"=>$data['total_amount'],
				"refund_amount"=>$data['refund_amount'],
				"refund_status"=>$data['refund_status'],
			];
			return ["status"=>"ok","data"=>$data];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}

	//关闭交易 V2 商户订单号
	static public function close_v2($order_id){
		$bizParams = [
			"out_trade_no"=>$order_id,
		];
		$systemParams = [
			"app_id"=>self::$app_id,
			"method"=>"alipay.trade.close",
			"format"=>"json",
			"charset"=>"UTF-8",
			"sign_type"=>"RSA2",
			"timestamp"=>date("Y-m-d H:i:s"),
			"version"=>"1.0",
            "app_cert_sn" => self::$app_cert_sn,
            "alipay_root_cert_sn" => self::$alipay_root_cert_sn,
			"biz_content"=>json_encode($bizParams,JSON_UNESCAPED_UNICODE),
		];
		$sign = self::sign_v2($systemParams);
		$systemParams['sign'] = $sign;
		$url = "https://openapi.alipay.com/gateway.do?" . http_build_query($systemParams);
		$res = curl($url,$bizParams,true);
		//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		if(!self::verify_sign_v2($res['body'],$systemParams['method'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['alipay_trade_close_response']['code']) && 
			$data['alipay_trade_close_response']['code'] == "10000" && 
			isset($data['alipay_trade_close_response']['trade_no'])
		){
			return ["status"=>"ok"];
		}else if(isset($data['alipay_trade_close_response']['sub_code'])){
			return ["status"=>"error","msg"=>$data['alipay_trade_close_response']['sub_code'] . (isset($data['alipay_trade_close_response']['sub_msg']) ? ":{$data['alipay_trade_close_response']['sub_msg']}" : "")];
		}
		return false;
	}
	
	//关闭交易 V3 商户订单号
	static public function close_v3($order_id){
		$url = "https://openapi.alipay.com/v3/alipay/trade/close";
		$bizParams = [
			"out_trade_no"=>$order_id,//商户订单号
		];
		$header = [
			self::sign_v3($url,json_encode($bizParams)),
			"Content-Type: application/json",
			"Accept: application/json",
			"Alipay-root-cert-sn: " . self::getRootCertSN(),
		];
		$res = curl($url,$bizParams,true,$header);
		if(!self::verify_sign_v3($res["header"],$res['body'])){
			return ["status"=>"error","msg"=>"验签失败"];
		}
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['trade_no'])
		){
			return ["status"=>"ok"];
		}else if(isset($data['code'])){
			return ["status"=>"error","msg"=>$data['code'] . (isset($data['message']) ? ":{$data['message']}" : "")];
		}
		return false;
	}

	//支付异步通知验签
    static public function notify_verify_sign($params=[]){
		$params = $params ?: $_GET;
		if(!isset($params['sign']))return false;
		if(!isset($params['sign_type']))return false;
		$sign = $params['sign'];
        ksort($params);
        unset($params['sign']);
        unset($params['sign_type']);
        $msg = "";
        $i = 0;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                if ($i == 0) {
                    $msg .= "$k" . "=" . "$v";
                } else {
                    $msg .= "&" . "$k" . "=" . "$v";
                }
                $i++;
            }
        }
        $result = openssl_verify($msg, base64_decode($sign), self::$alipay_public_key, OPENSSL_ALGO_SHA256);
		if($result === 1){
			return true;
		}
		return false;
    }

	//签名 V2
	static private function sign_v2($params){
		//
        ksort($params);
		$sign_str = "";
        foreach ($params as $k => $v) {
			if($k == "sign" || empty($v) || substr($v,0,1) == "@")continue;
			$sign_str .= $k."=".$v . "&";
        }
		$sign_str = substr($sign_str,0,-1);
		if(empty(self::$app_private_key))return false;
		openssl_sign($sign_str, $signature, self::$app_private_key,OPENSSL_ALGO_SHA256);
		$sign = base64_encode($signature);
		if(is_resource(self::$app_private_key))openssl_free_key(self::$app_private_key);
		return $sign ?: false;		
	}
	
	//验签 V2
    static public function verify_sign_v2($input,$method){
		$body = json_decode($input, true);
		$method_response = str_replace(".","_",$method) . "_response";
		if(empty($body) || !isset($body[$method_response]) || !isset($body['sign'])){
			return false;
		}
		$msg = json_encode($body[$method_response],JSON_UNESCAPED_UNICODE);
		$sign = $body['sign'];
        $result = openssl_verify($msg, base64_decode($sign), self::$alipay_public_key, OPENSSL_ALGO_SHA256);
		if($result === 1){
			return true;
		}
		return false;
    }
	
	//签名v3
	static private function sign_v3($url,$body,$http_method = "POST"){
		$url_parts = parse_url($url);
		$canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?{$url_parts['query']}" : ""));
		if(empty($timestamp)){
			$timestamp = time() * 1000;
		}
		if(empty($nonce)){
			$nonce = date("YmdHis");
		}
		$authString = "app_id=" . self::$app_id . ",app_cert_sn=".self::getCertSN().",nonce={$nonce},timestamp={$timestamp}";
		$message = $authString."\n".$http_method."\n".$canonical_url."\n".$body."\n";
		openssl_sign($message, $raw_sign, self::$app_private_key, OPENSSL_ALGO_SHA256);
		$sign = base64_encode($raw_sign);
		$schema = 'ALIPAY-SHA256withRSA';
		return "Authorization: {$schema} {$authString},sign={$sign}";
	}

	//验签v3
	static private function verify_sign_v3($alipay_header,$input){
		$alipay_header = self::http_header_to_arr($alipay_header);
		if(!isset($alipay_header['alipay-signature']) || !isset($alipay_header['alipay-timestamp']) || !isset($alipay_header['alipay-nonce']) || !isset($alipay_header['alipay-sn']) || empty($input))return false;
		$message = "{$alipay_header['alipay-timestamp']}\n{$alipay_header['alipay-nonce']}\n{$input}\n";

        $result = openssl_verify($message, base64_decode($alipay_header['alipay-signature']), self::$alipay_public_key, OPENSSL_ALGO_SHA256);
		if($result === 1){
			return true;
		}
		return false;
	}
	
	//获取应用公钥序列号
    static private function getCertSN(){
        $cert = self::$app_public_key;
        $ssl = openssl_x509_parse($cert);
        $SN = md5(self::array2string(array_reverse($ssl['issuer'])) . $ssl['serialNumber']);
        return $SN;
    }
	
	//获取支付宝根证书序列号
    static private function getRootCertSN(){
        $cert = self::$alipay_root_key;
        $array = explode("-----END CERTIFICATE-----", $cert);
        $SN = null;
        for ($i = 0; $i < count($array) - 1; $i++) {
            $ssl[$i] = openssl_x509_parse($array[$i] . "-----END CERTIFICATE-----");
            if (strpos($ssl[$i]['serialNumber'], '0x') === 0) {
                $ssl[$i]['serialNumber'] = self::hex2dec($ssl[$i]['serialNumberHex']);
            }
            if ($ssl[$i]['signatureTypeLN'] == "sha1WithRSAEncryption" || $ssl[$i]['signatureTypeLN'] == "sha256WithRSAEncryption") {
                if ($SN == null) {
                    $SN = md5(self::array2string(array_reverse($ssl[$i]['issuer'])) . $ssl[$i]['serialNumber']);
                } else {

                    $SN = $SN . "_" . md5(self::array2string(array_reverse($ssl[$i]['issuer'])) . $ssl[$i]['serialNumber']);
                }
            }
        }
        return $SN;
    }
	
    static private function hex2dec($hex){
        $dec = 0;
        $len = strlen($hex);
        for ($i = 1; $i <= $len; $i++) {
            $dec = bcadd($dec, bcmul(strval(hexdec($hex[$i - 1])), bcpow('16', strval($len - $i))));
        }
        return $dec;
    }
	
    static private function array2string($array){
        $string = [];
        if ($array && is_array($array)) {
            foreach ($array as $key => $value) {
                $string[] = $key . '=' . $value;
            }
        }
        return implode(',', $string);
    }
	
	//header字符串转数组
	static private function http_header_to_arr($header_str){
		$header_list = explode("\n", $header_str);
		$header_arr = [];
		foreach ($header_list as $key => $value){
			if(strpos($value, ':') === false){
				continue;
			}
			list($header_key, $header_value) = explode(":", $value, 2);
			$header_arr[$header_key] = trim($header_value);
		}
		if(isset($header_arr['Content-MD5'])){
			$header_arr['md5'] = bin2hex(base64_decode($header_arr['Content-MD5']));
		}
		return $header_arr;
	}
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
