<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="活动宝 - 发现身边有趣的活动">
    <title>活动宝 - 首页</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 页面特定样式 */
        .header-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            padding: var(--spacing-lg) 0;
            position: sticky;
            top: 0;
            z-index: var(--z-index-sticky);
        }
        
        .search-container {
            position: relative;
            margin: 0 var(--spacing-lg);
        }
        
        .search-input {
            width: 100%;
            height: 2.34rem; /* 优化触摸区域至44rpx */
            padding: 0 var(--spacing-md) 0 2.67rem;
            border: none;
            border-radius: var(--radius-round);
            background: var(--color-surface);
            font-size: var(--font-size-sm);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
        }

        .search-input:focus {
            box-shadow: 0 0 0 0.11rem rgba(92, 184, 92, 0.25);
            transform: translateY(-0.05rem);
        }
        
        .search-prefix {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            color: var(--color-text-primary);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
        }
        
        .location-icon {
            margin-right: var(--spacing-xs);
            color: var(--color-text-primary);
        }
        
        .location-divider {
            width: 0.05rem;
            height: 0.69rem; /* 13rpx */
            background: var(--color-border);
            margin: 0 var(--spacing-sm);
        }
        
        .carousel-container {
            margin: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: 0 0.27rem 0.4rem rgba(0, 0, 0, 0.2);
            position: relative;
        }
        
        .carousel-slide {
            width: 100%;
            height: 5.33rem; /* 100rpx */
            background: linear-gradient(45deg, var(--color-primary), var(--color-primary-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            position: relative;
        }

        .carousel-indicators {
            position: absolute;
            bottom: var(--spacing-sm);
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: var(--spacing-xs);
        }

        .carousel-indicator {
            width: 0.32rem;
            height: 0.32rem;
            border-radius: var(--radius-round);
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .carousel-indicator.active {
            background: white;
            transform: scale(1.2);
        }
        
        .activity-categories {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .activity-categories::-webkit-scrollbar {
            display: none;
        }
        
        .category-tag {
            flex-shrink: 0;
            padding: var(--spacing-xs) var(--spacing-md);
            background: var(--color-surface);
            border: 0.05rem solid var(--color-border-light);
            border-radius: var(--radius-round);
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            text-decoration: none;
            transition: all var(--transition-fast);
        }
        
        .category-tag:hover,
        .category-tag.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        
        .date-separator {
            margin: var(--spacing-lg) var(--spacing-lg) var(--spacing-sm);
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            font-weight: var(--font-weight-medium);
        }
        
        .weekday {
            margin-left: var(--spacing-xs);
            color: var(--color-text-tertiary);
            font-weight: var(--font-weight-normal);
        }
        
        .activity-card {
            margin: 0 var(--spacing-lg) var(--spacing-md);
            background: var(--color-surface);
            border-radius: var(--radius-md);
            box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: all var(--transition-normal);
            position: relative;
            min-height: 5.33rem; /* 统一卡片高度 */
        }

        .activity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 0.11rem;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            transform: scaleX(0);
            transition: transform var(--transition-normal);
        }

        .activity-card:hover {
            transform: translateY(-0.05rem);
            box-shadow: 0 0.21rem 0.64rem rgba(0, 0, 0, 0.08);
        }

        .activity-card:hover::before {
            transform: scaleX(1);
        }
        
        .activity-image {
            width: 100%;
            height: 5.33rem; /* 100rpx */
            object-fit: cover;
            background: var(--color-gray-100);
        }
        
        .activity-content {
            padding: var(--spacing-md);
        }
        
        .activity-title {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
            line-height: var(--line-height-tight);
        }
        
        .activity-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
        }
        
        .activity-location {
            display: flex;
            align-items: center;
            gap: 0.13rem; /* 2rpx */
        }
        
        .activity-time {
            display: flex;
            align-items: center;
            gap: 0.13rem; /* 2rpx */
        }
        
        .activity-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);
        }
        
        .activity-price {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-semibold);
            color: var(--color-primary);
        }
        
        .activity-status {
            padding: 0.13rem 0.27rem; /* 2rpx 5rpx */
            background: var(--color-primary-lighter);
            color: var(--color-primary-dark);
            border-radius: var(--radius-xs);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }
        
        .loading-more {
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
        }
        
        /* 底部导航栏适配 */
        .main-content {
            padding-bottom: 4rem; /* 为底部导航栏留空间 */
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- 头部搜索区域 -->
        <header class="header-section">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索感兴趣的项目活动">
                <div class="search-prefix">
                    <i class="fas fa-map-marker-alt location-icon"></i>
                    <span id="location-text">北京市</span>
                    <div class="location-divider"></div>
                </div>
            </div>
        </header>
        
        <main class="main-content">
            <!-- 轮播图区域 -->
            <section class="carousel-container">
                <div class="carousel-slide">
                    <span>精彩活动轮播</span>
                    <div class="carousel-indicators">
                        <div class="carousel-indicator active"></div>
                        <div class="carousel-indicator"></div>
                        <div class="carousel-indicator"></div>
                    </div>
                </div>
            </section>
            
            <!-- 活动分类 -->
            <section class="activity-categories">
                <a href="#" class="category-tag active">全部活动</a>
                <a href="#" class="category-tag">线上活动</a>
                <a href="#" class="category-tag">历史活动</a>
                <a href="#" class="category-tag">户外运动</a>
                <a href="#" class="category-tag">文化艺术</a>
                <a href="#" class="category-tag">商务会议</a>
            </section>
            
            <!-- 活动列表 -->
            <section class="activity-list">
                <!-- 日期分隔线 -->
                <div class="date-separator">
                    今天
                    <span class="weekday">星期一</span>
                </div>
                
                <!-- 活动卡片 -->
                <article class="activity-card hover-lift interactive-element fade-in">
                    <img src="https://via.placeholder.com/345x100/5CB85C/ffffff?text=活动封面" alt="活动封面" class="activity-image">
                    <div class="activity-content">
                        <h3 class="activity-title text-hierarchy-h2">今天天气真好，出门散散步，心情舒畅！</h3>
                        <div class="activity-meta">
                            <div class="activity-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="text-hierarchy-caption">北京市朝阳区</span>
                            </div>
                            <div class="activity-time">
                                <i class="fas fa-clock"></i>
                                <span class="text-hierarchy-caption">14:00-16:00</span>
                            </div>
                        </div>
                        <div class="activity-footer">
                            <div class="activity-price">免费</div>
                            <div class="activity-status">报名中</div>
                        </div>
                    </div>
                </article>
                
                <!-- 更多活动卡片 -->
                <article class="activity-card">
                    <img src="https://via.placeholder.com/345x100/88D7A0/ffffff?text=美食活动" alt="活动封面" class="activity-image">
                    <div class="activity-content">
                        <h3 class="activity-title">昨晚和朋友吃了顿大餐，太满足了！</h3>
                        <div class="activity-meta">
                            <div class="activity-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>北京市海淀区</span>
                            </div>
                            <div class="activity-time">
                                <i class="fas fa-clock"></i>
                                <span>18:00-20:00</span>
                            </div>
                        </div>
                        <div class="activity-footer">
                            <div class="activity-price">¥128</div>
                            <div class="activity-status">已结束</div>
                        </div>
                    </div>
                </article>
                
                <article class="activity-card">
                    <img src="https://via.placeholder.com/345x100/5CB85C/ffffff?text=户外活动" alt="活动封面" class="activity-image">
                    <div class="activity-content">
                        <h3 class="activity-title">周末户外徒步活动，欢迎大家参加</h3>
                        <div class="activity-meta">
                            <div class="activity-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>北京市昌平区</span>
                            </div>
                            <div class="activity-time">
                                <i class="fas fa-clock"></i>
                                <span>09:00-17:00</span>
                            </div>
                        </div>
                        <div class="activity-footer">
                            <div class="activity-price">¥50</div>
                            <div class="activity-status">报名中</div>
                        </div>
                    </div>
                </article>
            </section>
            
            <!-- 加载更多 -->
            <div class="loading-more">
                <div class="loading"></div>
                <div class="loading-text">加载更多活动...</div>
            </div>
        </main>
        
        <!-- 底部导航栏 -->
        <nav class="bottom-nav">
            <a href="index.html" class="bottom-nav-item active">
                <div class="bottom-nav-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="bottom-nav-text">活动</div>
            </a>
            <a href="world.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="bottom-nav-text">世界</div>
            </a>
            <a href="add-activity.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="bottom-nav-text">发布</div>
            </a>
            <a href="my.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="bottom-nav-text">我的</div>
            </a>
        </nav>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/common.js"></script>
    <script>
        // 页面特定的JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('focus', function() {
                this.style.boxShadow = '0 0 0 0.11rem rgba(106, 192, 134, 0.25)';
            });
            
            searchInput.addEventListener('blur', function() {
                this.style.boxShadow = 'var(--shadow-sm)';
            });
            
            // 分类标签切换
            const categoryTags = document.querySelectorAll('.category-tag');
            categoryTags.forEach(tag => {
                tag.addEventListener('click', function(e) {
                    e.preventDefault();
                    categoryTags.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 活动卡片点击
            const activityCards = document.querySelectorAll('.activity-card');
            activityCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 这里可以添加跳转到活动详情页的逻辑
                    console.log('点击活动卡片');
                });
            });
            
            // 模拟加载更多
            let isLoading = false;
            window.addEventListener('scroll', function() {
                if (isLoading) return;
                
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;
                
                if (scrollTop + windowHeight >= documentHeight - 100) {
                    isLoading = true;
                    setTimeout(() => {
                        // 模拟加载完成
                        isLoading = false;
                    }, 2000);
                }
            });
        });
    </script>
</body>
</html>
