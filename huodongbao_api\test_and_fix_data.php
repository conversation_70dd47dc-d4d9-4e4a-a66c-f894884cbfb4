<?php
/**
 * 测试和修复世界模块数据
 */

require_once 'core/init.php';

// 连接数据库
dbConn();

echo "<h2>世界模块数据测试和修复</h2>\n";

// 1. 检查当前数据状态
echo "<h3>1. 当前数据状态检查</h3>\n";

try {
    // 检查feeds表中的日记数据
    $diaryCountSql = "SELECT COUNT(*) as count FROM feeds WHERE type = 'diary'";
    $diaryCount = Db::_fetch($diaryCountSql);
    echo "<p>当前日记数量: " . ($diaryCount['count'] ?? 0) . "</p>\n";
    
    // 检查quotes表中的摘录数据
    $quoteCountSql = "SELECT COUNT(*) as count FROM quotes";
    $quoteCount = Db::_fetch($quoteCountSql);
    echo "<p>当前摘录数量: " . ($quoteCount['count'] ?? 0) . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>检查数据失败: " . $e->getMessage() . "</p>\n";
}

// 2. 添加测试数据
echo "<h3>2. 添加测试数据</h3>\n";

try {
    // 添加日记测试数据
    $diaryData = [
        [
            'user_id' => 215,
            'type' => 'diary',
            'content' => '今天是个特别的日子，早上起来看到窗外的阳光特别温暖。决定去公园走走，感受一下大自然的美好。路上遇到了很多有趣的人和事，让我对生活又有了新的感悟。',
            'images_json' => '[]',
            'location' => '',
            'tags' => '生活,感悟,阳光',
            'like_count' => 0,
            'comment_count' => 0,
            'status' => 'published',
            'privacy' => 'public',
            'created_at' => '2025-07-04 10:30:00',
            'updated_at' => '2025-07-04 10:30:00'
        ],
        [
            'user_id' => 215,
            'type' => 'diary',
            'content' => '最近在读一本关于心理学的书，里面有很多有趣的观点。人的思维方式真的很奇妙，同样的事情在不同的人眼中会有完全不同的解读。',
            'images_json' => '[]',
            'location' => '',
            'tags' => '读书,心理学,思考',
            'like_count' => 0,
            'comment_count' => 0,
            'status' => 'published',
            'privacy' => 'public',
            'created_at' => '2025-07-03 20:15:00',
            'updated_at' => '2025-07-03 20:15:00'
        ],
        [
            'user_id' => 215,
            'type' => 'diary',
            'content' => '今天和朋友一起做饭，虽然厨艺不精，但是过程很开心。我们一起准备食材，一起调味，一起品尝。最后虽然味道一般，但是那种一起创造的快乐是无价的。',
            'images_json' => '[]',
            'location' => '',
            'tags' => '朋友,做饭,快乐',
            'like_count' => 0,
            'comment_count' => 0,
            'status' => 'published',
            'privacy' => 'public',
            'created_at' => '2025-07-02 18:45:00',
            'updated_at' => '2025-07-02 18:45:00'
        ]
    ];
    
    // 插入日记数据
    foreach ($diaryData as $diary) {
        // 先检查是否已存在相同内容的日记
        $checkSql = "SELECT id FROM feeds WHERE user_id = ? AND type = 'diary' AND content = ?";
        $existing = Db::_fetch($checkSql, [$diary['user_id'], $diary['content']]);

        if (!$existing) {
            $insertSql = "INSERT INTO feeds (user_id, type, content, images_json, location, tags, like_count, comment_count, status, privacy, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $result = Db::_execute($insertSql, [
                $diary['user_id'], $diary['type'], $diary['content'], $diary['images_json'],
                $diary['location'], $diary['tags'], $diary['like_count'], $diary['comment_count'],
                $diary['status'], $diary['privacy'], $diary['created_at'], $diary['updated_at']
            ]);

            if ($result) {
                echo "<p style='color: green;'>成功添加日记: " . mb_substr($diary['content'], 0, 30) . "...</p>\n";
            } else {
                echo "<p style='color: red;'>添加日记失败: " . mb_substr($diary['content'], 0, 30) . "...</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>日记已存在，跳过: " . mb_substr($diary['content'], 0, 30) . "...</p>\n";
        }
    }
    
    // 添加摘录测试数据
    $quoteData = [
        [
            'user_id' => 215,
            'content' => '生活不是等待暴风雨过去，而是要学会在雨中跳舞。',
            'author' => '维维安·格林',
            'source' => '《生活的艺术》',
            'tags' => '生活,态度,积极',
            'like_count' => 0,
            'comment_count' => 0,
            'privacy' => 'public',
            'created_at' => '2025-07-04 09:15:00',
            'updated_at' => '2025-07-04 09:15:00'
        ],
        [
            'user_id' => 215,
            'content' => '真正的智慧不在于知道答案，而在于知道问什么问题。',
            'author' => '爱因斯坦',
            'source' => '《相对论》',
            'tags' => '智慧,思考,问题',
            'like_count' => 0,
            'comment_count' => 0,
            'privacy' => 'public',
            'created_at' => '2025-07-03 15:30:00',
            'updated_at' => '2025-07-03 15:30:00'
        ],
        [
            'user_id' => 215,
            'content' => '成功不是终点，失败不是致命的，继续前进的勇气才是最重要的。',
            'author' => '温斯顿·丘吉尔',
            'source' => '《二战回忆录》',
            'tags' => '成功,勇气,坚持',
            'like_count' => 0,
            'comment_count' => 0,
            'privacy' => 'public',
            'created_at' => '2025-07-02 11:45:00',
            'updated_at' => '2025-07-02 11:45:00'
        ]
    ];
    
    // 插入摘录数据
    foreach ($quoteData as $quote) {
        // 先检查是否已存在相同内容的摘录
        $checkSql = "SELECT id FROM quotes WHERE user_id = ? AND content = ?";
        $existing = Db::_fetch($checkSql, [$quote['user_id'], $quote['content']]);

        if (!$existing) {
            $insertSql = "INSERT INTO quotes (user_id, content, author, source, tags, like_count, comment_count, privacy, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $result = Db::_execute($insertSql, [
                $quote['user_id'], $quote['content'], $quote['author'], $quote['source'],
                $quote['tags'], $quote['like_count'], $quote['comment_count'],
                $quote['privacy'], $quote['created_at'], $quote['updated_at']
            ]);

            if ($result) {
                echo "<p style='color: green;'>成功添加摘录: " . mb_substr($quote['content'], 0, 30) . "...</p>\n";
            } else {
                echo "<p style='color: red;'>添加摘录失败: " . mb_substr($quote['content'], 0, 30) . "...</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>摘录已存在，跳过: " . mb_substr($quote['content'], 0, 30) . "...</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>添加测试数据失败: " . $e->getMessage() . "</p>\n";
}

// 3. 测试API调用
echo "<h3>3. 测试API调用</h3>\n";

try {
    require_once 'controller/World.php';
    $worldController = new \controller\World();
    
    // 测试获取日记列表
    echo "<h4>3.1 测试日记列表API</h4>\n";
    $diaryResult = $worldController->get_feeds(1, 10, 215, '8bde525b2267e3b1ee716884a9a3b0a0', 0, 'latest', 'diary');
    echo "<p>日记列表API结果:</p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($diaryResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
    
    // 测试获取摘录列表
    echo "<h4>3.2 测试摘录列表API</h4>\n";
    $quoteResult = $worldController->get_quotes(1, 20, 215, '8bde525b2267e3b1ee716884a9a3b0a0');
    echo "<p>摘录列表API结果:</p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($quoteResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>测试API调用失败: " . $e->getMessage() . "</p>\n";
}

// 4. 最终数据统计
echo "<h3>4. 最终数据统计</h3>\n";

try {
    $finalDiaryCount = Db::_fetch("SELECT COUNT(*) as count FROM feeds WHERE type = 'diary'");
    $finalQuoteCount = Db::_fetch("SELECT COUNT(*) as count FROM quotes");
    
    echo "<p>最终日记数量: " . ($finalDiaryCount['count'] ?? 0) . "</p>\n";
    echo "<p>最终摘录数量: " . ($finalQuoteCount['count'] ?? 0) . "</p>\n";
    
    // 显示最新的几条数据
    echo "<h4>最新日记数据:</h4>\n";
    $latestDiaries = Db::_fetchAll("SELECT id, LEFT(content, 50) as content_preview, created_at FROM feeds WHERE type = 'diary' ORDER BY created_at DESC LIMIT 5");
    if ($latestDiaries) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>内容预览</th><th>创建时间</th></tr>\n";
        foreach ($latestDiaries as $diary) {
            echo "<tr><td>" . $diary['id'] . "</td><td>" . htmlspecialchars($diary['content_preview']) . "...</td><td>" . $diary['created_at'] . "</td></tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h4>最新摘录数据:</h4>\n";
    $latestQuotes = Db::_fetchAll("SELECT id, LEFT(content, 50) as content_preview, author, created_at FROM quotes ORDER BY created_at DESC LIMIT 5");
    if ($latestQuotes) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>内容预览</th><th>作者</th><th>创建时间</th></tr>\n";
        foreach ($latestQuotes as $quote) {
            echo "<tr><td>" . $quote['id'] . "</td><td>" . htmlspecialchars($quote['content_preview']) . "...</td><td>" . htmlspecialchars($quote['author']) . "</td><td>" . $quote['created_at'] . "</td></tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>获取最终统计失败: " . $e->getMessage() . "</p>\n";
}

echo "<h3>测试完成</h3>\n";
echo "<p>请检查以上结果，确认数据和API是否正常工作。</p>\n";
echo "<p><strong>下一步：</strong>在小程序中重新测试日记和摘录列表功能。</p>\n";
?>
