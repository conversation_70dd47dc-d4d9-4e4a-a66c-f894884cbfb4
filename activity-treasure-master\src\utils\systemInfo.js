/**
 * 系统信息获取工具 - 兼容新旧API
 * 解决微信小程序wx.getSystemInfoSync已废弃的问题
 */

// 缓存系统信息，避免重复调用
let cachedSystemInfo = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

/**
 * 获取设备信息（替代wx.getSystemInfoSync的设备相关部分）
 */
export const getDeviceInfo = () => {
  try {
    // 优先使用新API
    if (uni.getDeviceInfo) {
      return uni.getDeviceInfo();
    }
    // 尝试使用微信原生新API
    if (typeof wx !== 'undefined') {
      try {
        const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
        return {
          brand: deviceInfo.brand || 'unknown',
          model: deviceInfo.model || 'unknown',
          platform: deviceInfo.platform || 'unknown',
          system: deviceInfo.system || 'unknown'
        };
      } catch (wxError) {
        console.warn('微信原生API获取设备信息失败:', wxError);
      }
    }

    // 最后降级到旧API（仅在必要时）
    console.warn('所有新API都不可用，降级使用getSystemInfoSync');
    try {
      const systemInfo = uni.getSystemInfoSync();
      return {
        brand: systemInfo.brand,
        model: systemInfo.model,
        platform: systemInfo.platform,
        system: systemInfo.system
      };
    } catch (error) {
      console.error('旧API也失败，使用默认值:', error);
      return {
        brand: 'unknown',
        model: 'unknown',
        platform: 'unknown',
        system: 'unknown'
      };
    }
  } catch (error) {
    console.warn('获取设备信息失败:', error);
    return {
      brand: 'unknown',
      model: 'unknown', 
      platform: 'unknown',
      system: 'unknown'
    };
  }
};

/**
 * 获取应用基础信息
 */
export const getAppBaseInfo = () => {
  try {
    if (uni.getAppBaseInfo) {
      return uni.getAppBaseInfo();
    }
    // 尝试使用微信原生新API
    if (typeof wx !== 'undefined') {
      try {
        const appInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};
        return {
          version: appInfo.version || 'unknown',
          language: appInfo.language || 'zh_CN',
          theme: appInfo.theme || 'light'
        };
      } catch (wxError) {
        console.warn('微信原生API获取应用信息失败:', wxError);
      }
    }

    // 最后降级到旧API
    console.warn('降级使用getSystemInfoSync获取应用信息');
    try {
      const systemInfo = uni.getSystemInfoSync();
      return {
        version: systemInfo.version,
        language: systemInfo.language,
        theme: systemInfo.theme
      };
    } catch (error) {
      console.error('获取应用信息失败，使用默认值:', error);
      return {
        version: 'unknown',
        language: 'zh_CN',
        theme: 'light'
      };
    }
  } catch (error) {
    console.warn('获取应用基础信息失败:', error);
    return {
      version: 'unknown',
      language: 'zh_CN',
      theme: 'light'
    };
  }
};

/**
 * 获取窗口信息
 */
export const getWindowInfo = () => {
  try {
    if (uni.getWindowInfo) {
      return uni.getWindowInfo();
    }
    // 尝试使用微信原生新API
    if (typeof wx !== 'undefined') {
      try {
        const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
        return {
          windowWidth: windowInfo.windowWidth || 375,
          windowHeight: windowInfo.windowHeight || 667,
          screenWidth: windowInfo.screenWidth || 375,
          screenHeight: windowInfo.screenHeight || 667,
          pixelRatio: windowInfo.pixelRatio || 2,
          statusBarHeight: windowInfo.statusBarHeight || 20,
          safeArea: windowInfo.safeArea || { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },
          safeAreaInsets: windowInfo.safeAreaInsets || { top: 0, left: 0, right: 0, bottom: 0 }
        };
      } catch (wxError) {
        console.warn('微信原生API获取窗口信息失败:', wxError);
      }
    }

    // 最后降级到旧API
    console.warn('降级使用getSystemInfoSync获取窗口信息');
    try {
      const systemInfo = uni.getSystemInfoSync();
      return {
        windowWidth: systemInfo.windowWidth,
        windowHeight: systemInfo.windowHeight,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        pixelRatio: systemInfo.pixelRatio,
        statusBarHeight: systemInfo.statusBarHeight,
        safeArea: systemInfo.safeArea,
        safeAreaInsets: systemInfo.safeAreaInsets
      };
    } catch (error) {
      console.error('获取窗口信息失败，使用默认值:', error);
      return {
        windowWidth: 375,
        windowHeight: 667,
        screenWidth: 375,
        screenHeight: 667,
        pixelRatio: 2,
        statusBarHeight: 20,
        safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },
        safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 }
      };
    }
  } catch (error) {
    console.warn('获取窗口信息失败:', error);
    return {
      windowWidth: 375,
      windowHeight: 667,
      screenWidth: 375,
      screenHeight: 667,
      pixelRatio: 2,
      statusBarHeight: 20,
      safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },
      safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 }
    };
  }
};

/**
 * 获取系统设置信息
 */
export const getSystemSetting = () => {
  try {
    if (uni.getSystemSetting) {
      return uni.getSystemSetting();
    }
    // 降级处理
    return {
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false
    };
  } catch (error) {
    console.warn('获取系统设置失败:', error);
    return {
      bluetoothEnabled: false,
      locationEnabled: false,
      wifiEnabled: false
    };
  }
};

/**
 * 获取完整的系统信息（兼容旧API）
 * 这是主要的替代函数，用于替换wx.getSystemInfoSync()
 */
export const getSystemInfo = () => {
  const now = Date.now();
  
  // 检查缓存
  if (cachedSystemInfo && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedSystemInfo;
  }

  try {
    const deviceInfo = getDeviceInfo();
    const appInfo = getAppBaseInfo();
    const windowInfo = getWindowInfo();
    const systemSetting = getSystemSetting();

    // 合并所有信息，保持与旧API的兼容性
    const systemInfo = {
      // 设备信息
      brand: deviceInfo.brand,
      model: deviceInfo.model,
      platform: deviceInfo.platform,
      system: deviceInfo.system,
      
      // 应用信息
      version: appInfo.version,
      language: appInfo.language,
      theme: appInfo.theme,
      
      // 窗口信息
      windowWidth: windowInfo.windowWidth,
      windowHeight: windowInfo.windowHeight,
      screenWidth: windowInfo.screenWidth,
      screenHeight: windowInfo.screenHeight,
      pixelRatio: windowInfo.pixelRatio,
      statusBarHeight: windowInfo.statusBarHeight,
      safeArea: windowInfo.safeArea,
      safeAreaInsets: windowInfo.safeAreaInsets,
      
      // 系统设置
      bluetoothEnabled: systemSetting.bluetoothEnabled,
      locationEnabled: systemSetting.locationEnabled,
      wifiEnabled: systemSetting.wifiEnabled,
      
      // 添加时间戳
      _timestamp: now
    };

    // 更新缓存
    cachedSystemInfo = systemInfo;
    cacheTimestamp = now;

    return systemInfo;
  } catch (error) {
    console.error('获取系统信息失败，使用降级方案:', error);
    
    // 最后的降级方案
    try {
      console.warn('使用最后的降级方案：getSystemInfoSync');
      const fallbackInfo = uni.getSystemInfoSync();
      cachedSystemInfo = fallbackInfo;
      cacheTimestamp = now;
      return fallbackInfo;
    } catch (fallbackError) {
      console.error('所有方案都失败，使用硬编码默认值:', fallbackError);
      // 返回默认值
      const defaultInfo = {
        brand: 'unknown',
        model: 'unknown',
        platform: 'unknown',
        system: 'unknown',
        version: 'unknown',
        language: 'zh_CN',
        theme: 'light',
        windowWidth: 375,
        windowHeight: 667,
        screenWidth: 375,
        screenHeight: 667,
        pixelRatio: 2,
        statusBarHeight: 20,
        safeArea: { top: 0, left: 0, right: 375, bottom: 667, width: 375, height: 667 },
        safeAreaInsets: { top: 0, left: 0, right: 0, bottom: 0 },
        bluetoothEnabled: false,
        locationEnabled: false,
        wifiEnabled: false,
        _timestamp: now
      };
      cachedSystemInfo = defaultInfo;
      cacheTimestamp = now;
      return defaultInfo;
    }
  }
};

/**
 * 清除缓存（在需要获取最新信息时调用）
 */
export const clearSystemInfoCache = () => {
  cachedSystemInfo = null;
  cacheTimestamp = 0;
};

/**
 * px转rpx工具函数（使用新的API）
 */
export const pxToRpx = (px) => {
  const windowInfo = getWindowInfo();
  return (750 * Number.parseInt(px)) / windowInfo.windowWidth;
};

/**
 * rpx转px工具函数
 */
export const rpxToPx = (rpx) => {
  const windowInfo = getWindowInfo();
  return (Number.parseInt(rpx) * windowInfo.windowWidth) / 750;
};
