<?php
namespace lib;
use core\Config;
class Redis{
	
    static protected $instance = null;

    public function __construct(){
        
    }
	
	//获取实例
	static public function getInstance(){
		if(self::$instance === null){
			self::connect();
		}
		if(self::ping()){
			return self::$instance;
		}else{
			self::connect();
			if(self::ping()){
				return self::$instance;
			}
		}
		return false;
	}
	
	//连接
    static public function connect($pconnect=false,$timeout=30){
        if(class_exists('\Redis')){
			self::$instance = new \Redis();
			if($pconnect){
				self::$instance->pconnect(Config::get("sys.redis.host"),Config::get("sys.redis.port"),$timeout);
			}else{
				self::$instance->connect(Config::get("sys.redis.host"),Config::get("sys.redis.port"),$timeout);
			}
			if(!empty(Config::get("sys.redis.password")))self::$instance->auth(Config::get("sys.redis.password"));
		}else{
            throw new \Exception("Redis扩展未安装");
        }
    }
	
	//释放资源
	static public function close(){
		self::$instance->close();
	}
	
	//选择redis库,0~15 共16个库
	static public function select($index=0){
		self::$instance->select($index);
	}

	////查看失效时间[-1 | timestamps]
	static public function ttl($key){
		self::$instance->ttl($key);
	}

	//移除失效时间[ 1 | 0]
	static public function persist($key){
		self::$instance->persist($key);
	}

	//返回或保存给定列表、集合、有序集合key中经过排序的元素，$array为参数limit等！【配合$array很强大】 [array|false]
	static public function sort($key,$array=[]){
		self::$instance->sort($key,$array);
	}

	//设置失效时间[true | false]
	static public function expire($key,$second=60){
		self::$instance->expire($key,$second);
	}
	//把当前库中的key移动到15库中[0|1]
	static public function move($key,$db_index=15){
		self::$instance->move($key,$db_index);
	}
	
    //简单redis String操作

	//key=value
    static public function set($key,$value){
        return self::$instance->set($key,$value);
    }

	//key=value，有效期为秒[true]
    static public function setex($key,$value,$expiration){
        return self::$instance->setex($key,$expiration,$value);
    }

	//获取key [value]
    static public function get($key){
        return self::$instance->get($key);
    }

	//设置一个或多个键值[true]
    static public function mset($arr){
        return self::$instance->mset($arr);
    }

	//(string|arr),返回所查询键的值
    static public function mget($arr){
        return self::$instance->mget($arr);
    }

	//先获得key的值，然后重新赋值,[old_value | false]
    static public function getset($key,$new_val){
        return self::$instance->getset($key,$new_val);
    }
	
	//(string|arr)删除key，支持数组批量删除【返回删除个数】
    static public function del($key_arr){
        return self::$instance->del($key_arr);
    }

	//自增1
    static public function incr($key){
        return self::$instance->incr($key);
    }
	
	//自增 num
    static public function incrby($key,$num=1){
        return self::$instance->incrby($key,$num);
    }

	//自减1
    static public function decr($key){
        return self::$instance->decr($key);
    }

	//自减 num
    static public function decrby($key,$num=1){
        return self::$instance->decrby($key,$num);
    }

    //简单操作 list链表操作

	//返回列表key的长度,不存在key返回0， [ len | 0]
    static public function llen($key){
        return self::$instance->llen($key);
    }
	
	//插入链表头部/左侧，返回链表长度
    static public function lPush($liseKey,$value){
        return self::$instance->lPush($liseKey,$value);
    }
	
	//插入链表尾部/右侧，返回链表长度
    static public function rPush($liseKey,$value){
        return self::$instance->rPush($liseKey,$value);
    }
	
	// 插入链表头部/左侧,链表不存在返回0，存在即插入成功，返回当前链表长度
    static public function lPushx($liseKey,$value){
        return self::$instance->lPushx($liseKey,$value);
    }
	
	// 插入链表尾部/右侧,链表不存在返回0，存在即插入成功，返回当前链表长度
    static public function rPushx($liseKey,$value){
        return self::$instance->rPushx($liseKey,$value);
    }
	
	//弹出并返回LIST顶部（左侧）的VALUE[被删元素 | false]
    static public function lPop($liseKey){
        return self::$instance->lPop($liseKey);
    }
	
	//弹出并返回LIST尾部（右侧）的VALUE[被删元素 | false]
    static public function rPop($liseKey){
        return self::$instance->rPop($liseKey);
    }
	
	//如果是链表则返回链表长度，空链表返回0 不存在则返回false
    static public function lSize($liseKey){
        return self::$instance->lSize($liseKey);
    }
	
	// 通过索引获取链表元素 0获取左侧一个  -1获取最后一个
    static public function lGet($liseKey,$index){
        return self::$instance->lGet($liseKey,$index);
    }
	
	// index位置元素替换为value
    static public function lSet($liseKey,$index,$value){
        return self::$instance->lSet($liseKey,$index,$value);
    }
	
	//从$indexStart开始 $indexEnd位置结束 ，结束位置为-1 获取开始位置之后的全部
    static public function lRange($liseKey,$indexStart,$indexEnd){
        return self::$instance->lRange($liseKey,$indexStart,$indexEnd);
    }
	
	//修剪 从$indexStart开始 $indexEnd位置结束 ，结束位置为-1,保留(start,end)之间的值 [true|false]
    static public function lTrim($liseKey,$indexStart,$indexEnd){
        return self::$instance->lTrim($liseKey,$indexStart,$indexEnd);
    }

	//删，根据参数count的值，移除列表中与参数value相等的元素count=(0|-n表头向尾|+n表尾向头移除n个value) [被移除的数量 | 0]
    static public function lrem($liseKey,$value,$count=0){
        return self::$instance->lrem($liseKey,$value,$count);
    }
	
    //server操作
	
	static private function ping(){
        if(!is_object(self::$instance) || !method_exists(self::$instance, 'ping')) {
            return false;
        }
        $response = self::$instance->ping();
        if ($response != '+PONG') {
            return false;
		}
		return true;
	}
	
	//返回当前库中的key的个数
    static public function dbSize(){
        return self::$instance->dbSize();
    }

	//清空整个redis[总true]
    static public function flushAll(){
        return self::$instance->flushAll();
    }

	//清空当前redis库[总true]
    static public function flushDB(){
        return self::$instance->flushDB();
    }

	//同步??把数据存储到磁盘-dump.rdb[true]
    static public function save(){
        return self::$instance->save();
    }

	//异步？？把数据存储到磁盘-dump.rdb[true]
    static public function bgsave(){
        return self::$instance->bgsave();
    }

	//查询当前redis的状态 [verson:2.4.5....]
    static public function info(){
        return self::$instance->info();
    }

	//上次存储时间key的时间[timestamp]
    static public function lastSave(){
        return self::$instance->lastSave();
    }
	
	//
	public function __destruct(){

	}
}
?>
