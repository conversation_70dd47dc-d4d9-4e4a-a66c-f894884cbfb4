<script setup>
import { watch, ref, reactive } from "vue";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { back } from "@/utils";
</script>
<template>
  <view class="mask">
    <view class="h">
      <u-status-bar></u-status-bar>
      <u-text
        color="red"
        align="center"
        :text="store().$state.popContent.title"
        bold
        size="42rpx"
        margin="80rpx 10rpx 50rpx"
      ></u-text>
      <view class="px30">
        <u-parse :content="store().$state.popContent.msg"></u-parse>
      </view>
    </view>
  </view>
</template>

<style scoped lang="less">
page {
  background: transparent;
}

.mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0);
}
</style>
