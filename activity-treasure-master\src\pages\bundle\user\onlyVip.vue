<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { goodsget_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, getListHeight, navto } from "@/utils";

const goods = ref([]);
const badgeNum = ref(0);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const query = ref({ num: 0, money: "0.00" });
const height = ref("");

onReady(async () => {
  const res = await setListHeight();
  height.value = res - 50 + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  goodsget_list({ page: mescroll.num, page_size: mescroll.size, is_shiyong: 1 })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr(); // 请求失败, 结束加载
    });
};
const change = (e, val) => {
  val.zongMoney = val.money * val.num;
  query.value.num = 0;
  query.value.money = 0;
  goods.value.map((val) => {
    query.value.num += val.num;
    if (val.zongMoney > 0) query.value.money += val.zongMoney;
  });
};
const goNext = (e) => {
  let obj = {};
  for (let i in e.guige) {
    obj[i] = e.guige[i][0];
  }
  navto(`/pages/bundle/shop/goodInfo?id=${e.id}&guige=${JSON.stringify(obj)}`);
};
</script>
<template>
  <view class="page">
    <myTitle
      bgColor="linear-gradient(103deg, #8efffe 0%, #c6e538 100%)"
      height="176rpx"
      title="会员专享"
    ></myTitle>
    <view class="px30">
      <mescroll-uni
        class="list"
        :height="height"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
      >
        <view class="mt20 df aic jcsb fw">
          <view
            class="pr mb10 pb10 r10 b6f item"
            v-for="(val, i) in goods"
            :key="i"
            @click="goNext(val)"
          >
            <view class="pa top0 left0 z20">
              <u-image
                width="130rpx"
                height="36rpx"
                :src="`${store().$state.url}vipshiyong.png`"
              ></u-image>
            </view>
            <u-image width="340rpx" height="340rpx" :src="val.img_url"></u-image>
            <view class="px20">
              <u-text
                margin="20rpx 0 10rpx"
                size="22rpx"
                lines="2"
                :text="val.name"
              ></u-text>
              <view class="df aic jcsb">
                <u-text
                  mode="price"
                  :text="$u.priceFormat(val.price, 2)"
                  color="#EF6227"
                  size="18rpx"
                ></u-text>
                <u-text
                  align="right"
                  :text="`销量：${val.sell_num}`"
                  size="18rpx"
                ></u-text>
                <!-- <u-number-box
                  v-model="val.num"
                  :longPress="true"
                  min="0"
                  :max="val.max"
                  :name="i"
                  @change="change($event, val)"
                >
                  <template #minus>
                    <u-image
                      v-if="val.num >= 1"
                      width="44rpx"
                      height="44rpx"
                      :src="`${store().$state.url}del.png`"
                    ></u-image>
                  </template>
                  <template #input>
                    <text
                      v-if="val.num >= 1"
                      style="
                        width: 100rpx;
                        text-align: center;
                        font-size: 24rpx;
                        height: 44rpx;
                        line-height: 44rpx;
                      "
                    >
                      {{ val.num || 0 }}
                    </text>
                  </template>
                  <template #plus>
                    <u-image
                      width="44rpx"
                      height="44rpx"
                      :src="`${store().$state.url}add.png`"
                    ></u-image>
                  </template>
                </u-number-box> -->
              </view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <view
      class="pfx z20"
      style="right: 50rpx; bottom: 100rpx"
      @click="navto('/pages/bundle/shop/shopCart')"
    >
      <u-icon
        :name="`${store().$state.url}shopcart.png`"
        size="50rpx"
        space="10rpx"
        color="#333333"
      ></u-icon>
      <u-badge type="error" max="99" :value="badgeNum"></u-badge>
    </view>
    <!-- <view class="pfx bottom0 pt20 px30 w h100 b6f z20 bottomBox">
      <view class="df aic jcsb">
        <view class="f1">
          <u-icon
            :name="`${store().$state.url}shopcart.png`"
            size="32rpx"
            :label="`已选${query.num}个`"
            space="10rpx"
            label-pos="right"
            label-size="24rpx"
            color="#333333"
          ></u-icon>
        </view>
        <view class="df aic mr20">
          <u-text color="#AAAAAA" size="28rpx" text="合计："></u-text>
          <u-text
            color="#EF6227"
            mode="price"
            :text="$u.priceFormat(query.money, 2)"
          ></u-text>
        </view>
        <u-button
          text="支付"
          shape="circle"
          :customStyle="{
            width: '160rpx',
            height: '60rpx',
            background: '#FAD000',
          }"
          @click="navto(`/pages/bundle/common/pay`)"
        ></u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view> -->
  </view>
</template>

<style scoped lang="less">
.page {
  .item {
    width: 340rpx;
  }
}
</style>
