<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 常见问题 
*/
class Question extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND (title LIKE :keyword OR contents LIKE :keyword)";
			$prepareParam[":keyword"]="%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		$data = Db()->table("question")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		return view(['header','question/index','footer']);
	}
	/*
	* @name 添加
	* @method add
	*/		
	public function add(){
		dbConn();
		if(
			!empty($_POST['title']) && 
			!empty($_POST['contents'])
		){
			$data = [
				"title"=>":title",
				"contents"=>":contents",
			];
			$prepareParam = [
				":title"=>htmlspecialchars($_POST['title']),
				":contents"=>htmlspecialchars($_POST['contents']),
			];			
			try{
				$res = Db()->table("question")->prepareParam($prepareParam)->insert($data);
				if($res){
					$insert_id = Db()->insertId();
					$this->root_log("添加常见问题,id:[ {$insert_id} ]");
					assign("alertTpl",true);
					header("location:".url("question/index/alert/{$insert_id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		return view(['header','question/add','footer']);
	}
	/*
	* @name 编辑
	* @method edit
	*/	
	public function edit(){
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("question/index"));
		}
		$id = (int)$_REQUEST['id'];
		dbConn();
		if(
			!empty($_POST['title']) && 
			!empty($_POST['contents'])
		){
			$data = [
				"title"=>":title",
				"contents"=>":contents",
			];
			$prepareParam = [
				":title"=>htmlspecialchars($_POST['title']),
				":contents"=>htmlspecialchars($_POST['contents']),
			];			
			try{
				$res = Db()->table("question")->prepareParam($prepareParam)->where("id={$id}")->update($data);
				if($res){
					$this->root_log("编辑常见问题,id:{$id}");
					assign("alertTpl",true);
					header("location:".url("question/index/alert/{$id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$data = Db()->table("question")->where("id={$id}")->fetch();
		if(empty($data))header("location:".url("question/index"));
		if(isset($data['contents']))$data['contents'] = Db()->quote(htmlspecialchars_decode($data['contents']));
		assign("data",$data);
		return view(['header','question/edit','footer']);
	}	
	/*
	* @name 删除
	* @method del
	*/	
	public function del(){
		responseType("json");
		if(empty($_POST['ids']))return ["status"=>"error","msg"=>"参数错误"];
		$ids = trim($_POST['ids']);
		$ids_arr = explode(",",$ids);
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		foreach($ids_arr as &$v){
			if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
		}
		$ids = implode(",",$ids_arr);
		if(empty($ids))return ["status"=>"error","msg"=>"参数错误"];
		try{
			$sql = "DELETE FROM `question` WHERE id IN ({$ids})";
			$rowCount = Db()->_exec($sql);
			if($rowCount){
				$this->root_log("删除常见问题,ids[{$ids}]");
				return ["status"=>"ok","msg"=>"影响数量：{$rowCount}"];
			}else{
				return ["status"=>"error","msg"=>"影响数量为零行"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	public function _empty(){
		
	}
	function __destruct(){

	}
}
