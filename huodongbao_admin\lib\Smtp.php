<?php
namespace lib;
class Smtp{

	private $smtp_port = 25;
	private $time_out = 30;
	private $host_name = "localhost";
	private $relay_host = "";
	public  $debug = [];
	private $auth = true;
	private $mail_from = "";
	private $user = "";
	private $pass = "";
	private $sock = false;

	public function __construct(){

	}
	public function sendMail($to,$subject = "",$body = "",$mailtype="html"){
		$body = preg_replace("/(^|(\r\n))(\.)/", "\1.\3", $body);

		$header = "MIME-Version:1.0\r\n";
		if($mailtype == "html"){
			$header .= "Content-Type:text/html\r\n";
		}
		$header .= "To: ".$to."\r\n";
		$header .= "From: {$this->mail_from}<{$this->mail_from}>\r\n";
		$header .= "Subject: {$subject}\r\n";
		$header .= "Date: ".date("r")."\r\n";
		$header .= "X-Mailer:By Redhat (PHP/".phpversion().")\r\n";
		list($msec, $sec) = explode(" ", microtime());
		$header .= "Message-ID: <".date("YmdHis", $sec).".".($msec*1000000).".{$this->mail_from}>\r\n";
		if(!$this->smtp_sockopen_relay($to)){
			$this->debug("Error: sockopen to ".$to."\n");
			return false;
		}
		$res = $this->smtp_send($this->host_name, $this->mail_from, $to, $header, $body);
		fclose($this->sock);
		return $res;
	}

	private function smtp_send($helo, $from, $to, $header, $body = ""){
		if (!$this->smtp_putcmd("HELO", $helo)){
			$this->debug("Error:  HELO command");
			return false;
		}
		if($this->auth){
			if (!$this->smtp_putcmd("AUTH LOGIN", base64_encode($this->user))){
				$this->debug("Error:  AUTH LOGIN command");
				return false;
			}
			if (!$this->smtp_putcmd("", base64_encode($this->pass))){
				$this->debug("Error:  password command");
				return false;
			}
		}
		if(!$this->smtp_putcmd("MAIL", "FROM:<".$from.">")){
			$this->debug("Error:  MAIL FROM  command");
			return false;
		}
		if(!$this->smtp_putcmd("RCPT", "TO:<".$to.">")){
			$this->debug("Error:  RCPT TO command");
			return false;
		}
		if(!$this->smtp_putcmd("DATA")){
			$this->debug("Error:  DATA command");
			return false;

		}
		if(!$this->smtp_message($header, $body)){
			$this->debug("Error:  message");
			return false;
		}
		if(!$this->smtp_eom()){
			$this->debug("Error:  <CR><LF>.<CR><LF> [EOM]");
			return false;
		}
		if(!$this->smtp_putcmd("QUIT")){
			$this->debug("Error:  password command");
			return false;
		}
		return true;
	}

	private function smtp_sockopen_relay(){
		$this->sock = @fsockopen($this->relay_host, $this->smtp_port, $errno, $errstr, $this->time_out);
		if(!($this->sock && $this->smtp_ok())){
			$this->debug("Error: Cannot connenct to relay host ".$this->relay_host."\n");
			$this->debug("Error: ".$errstr." (".$errno.")\n");
			return false;
		}
		$this->debug("Connected to relay host ".$this->relay_host."\n");
		return true;
	}

	private function smtp_message($header, $body){
		$cmd = $header."\r\n".$body;
		fputs($this->sock, $cmd);
		$this->debug(">>> ".$cmd);
		return true;
	}

	private function smtp_eom(){
		fputs($this->sock, "\r\n.\r\n");
		$this->debug(">>> \r\n.\r\n");
		return $this->smtp_ok();
	}

	private function smtp_putcmd($cmd, $arg = ""){
		if ($arg != ""){
			if($cmd=="") $cmd = $arg;
			else $cmd = $cmd." ".$arg;
		}
		fputs($this->sock, $cmd."\r\n");
		$this->debug("# ".$cmd);
		return $this->smtp_ok();
	}

	private function smtp_ok(){
		$response = str_replace("\r\n", "", fgets($this->sock, 512));
		$this->debug(">>> ".$response);
		if(!preg_match("/^[23]/", $response)){
			fputs($this->sock, "QUIT\r\n");
			fgets($this->sock, 512);
			return false;
		}
		return true;
	}

	public function debug($message){
		$this->debug[] = $message;
	}

}

?>
