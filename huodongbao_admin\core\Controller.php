<?php
namespace core;

class Controller{

	static public $config = null;

	public function __construct(){
		self::init();
	}

	static public function init(){
		self::home_nav();
	}

	public function auth(){
		if(empty($_SESSION['root_info'])){
			if(isAjax()){
				responseType("json");
				echo json_encode(["status"=>"error","msg"=>"授权失败"]);
			}else{
				header("location:".url("login/login"));
			}
			exit;
		}
		
		$privileges = strtolower(\core\Route::$controller.".".\core\Route::$action);
		$common_privileges = [
			"root.update_password",
            "huodong.zhiding"
		];
		
		if(!in_array($privileges,$common_privileges) && (!isset($_SESSION["root_info"]["privileges"]) || !in_array($privileges,$_SESSION["root_info"]["privileges"]))){
			if(isAjax()){
				header("Content-type: application/json; charset=utf-8;");
				echo json_encode(["status"=>"error","msg"=>"没有访问权限"]);
			}else{
				if($privileges == "index.index"){
					echo view(['header','hello','footer']);
				}else{
					assign("msg","没有访问权限");
					echo view(['header','noprivileges','footer']);
				}
			}
			exit;
		}
		
		//检测IP是否发生变化
		if(empty($_SESSION['root_info']['login_ip']) || $_SESSION['root_info']['login_ip'] != IP){
			$this->exception_log("IP地址【".IP."】管理端操作被限制，原登陆IP【{$_SESSION['root_info']['login_ip']}】,用户名【{$_SESSION['root_info']['r_name']}】");
			if(isAjax()){
				header("Content-type: application/json; charset=utf-8;");
				echo json_encode(["status"=>"error","msg"=>"没有访问权限：【IP变化】"]);
			}else{
				header("location:".url("login/logout"));
			}
			exit;
		}
		//
	}

	public function root_log($msg){
		dbConn();
		$dirName = basename(BASE_PATH);
		$sql = "INSERT INTO `root_log` (`r_id`,`r_name`,`msg`) VALUES ({$_SESSION['root_info']['rid']},'".$_SESSION['root_info']['r_name']."',:msg)";
		$msg = "{$dirName} : [ ".Route::$privileges." ] : " . htmlspecialchars($msg);
		$msg = mb_substr($msg,0,150);
		$params = [':msg'=>$msg];
		if(\core\Db::_exec($sql,$params)){
			return true;
		}
		return false;
	}
	
	public function exception_log($msg){
		dbConn();
		$dirName = basename(BASE_PATH);
		$msg = "{$dirName} : [ ".Route::$privileges." ] : " .htmlspecialchars($msg);
		$msg = mb_substr($msg,0,600);
		$sql = "INSERT INTO `exception_log` (`msg`,`ip`) VALUES (:msg,'".IP."')";
		$params = [':msg'=>$msg];
		if(\core\Db::_exec($sql,$params)){
			return true;
		}
		return false;
	}

	public static function get_config($name){
		dbConn();
		if(is_string($name)){
			$res = Db()->table("config")->where("name=:name")->prepareParam([":name"=>$name])->getColumn("val",false);
			return $res;
		}else if(is_array($name)){
			$data = [];
			foreach($name as $v){
				$res = Db()->table("config")->where("name=:name")->prepareParam([":name"=>$v])->getColumn("val",false);
				if($res !== false){
					$data[$v] = $res;
				}
			}
			return $data;
		}
		return false;
	}
	
	static private function home_nav(){
		$role = strtolower(\core\Route::$controller.".".\core\Route::$action);
		$data = \core\Cache::getCache("home_nav_{$role}");
		if(empty($data)){
			dbConn();
			$res = Db()->table("root_role")->select("role_name,module_name")->where("role=:role")->prepareParam([":role"=>$role])->fetch();
			if($res){
				$data = [$res['module_name'],$res['role_name']];
				\core\Cache::setCache("home_nav_{$role}",$data,24*60*60);
			}
		}
		if(!empty($data)){
			assign("nav_ui",$data);
			return true;
		}
		return false;
	}
	
	public function __destruct(){

	}
}
