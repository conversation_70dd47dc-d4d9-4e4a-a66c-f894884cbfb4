<?php
namespace core;
use core\config;
class Upload{
	
    static private $ext = [];//允许的格式
    static private $size = 0;//允许最大字节
    static private $error = [];//错误信息
	static private $_instance = null;//实例
	static private $thumbSwitch = false;
    public function __construct($allow=["ext"=>[],"size"=>0]){
        if(!empty($allow['ext']) && is_array($allow['ext']))self::$ext = $allow['ext'];
        if(!empty($allow['size']) && is_numeric($allow['size']))self::$ext = $allow['size'];
    }
	
	static public function getInstance(){
		if(self::$_instance instanceOf self)return self::$_instance;
		self::$_instance = new self();
		return self::$_instance;
	}
	
    static public function ext(array $ext){
        self::$ext = $ext;
        return self::getInstance();
    }
    static public function thumb(){
        self::$thumbSwitch = true;
        return self::getInstance();
    }	
    static public function size(int $size){
        self::$size = $size;
        return self::getInstance();
    }
	

    static public function move($file,$path="",$allow=["ext"=>[],"size"=>0]){
        if(!empty($allow['ext']) && is_array($allow['ext']))self::$ext = $allow['ext'];
        if(!empty($allow['size']) && is_numeric($allow['size']))self::$size = $allow['size'];
		self::initAllow();
        self::$error = [];
		self::$error[] = "允许格式:" . implode(",",self::$ext);
		self::$error[] = "允许大小:" . self::$size;
        if(
            isset($file['name']) && is_array($file['name']) &&
            isset($file['type']) && is_array($file['type']) &&
            isset($file['tmp_name']) && is_array($file['tmp_name']) &&
            isset($file['error']) && is_array($file['error']) &&
            isset($file['size']) && is_array($file['size'])
        ){
            $fileArr = self::getFiles($file);
            $res = [];
            foreach($fileArr as $v){
                $res[] = self::uploadAction($v,$path);
            }
        }else{
            $res = self::uploadAction($file,$path);
        }
		self::$thumbSwitch = false;
        return $res;
    }	
	
	//删除文件
    static public function delFile($url,$dir=false){
		if(empty($url) || !is_string($url))return false;
        $parse_url = parse_url($url);
		$path = $parse_url['path'];
		if(strtolower(substr($path,0,7)) != "/upload"){
			$path = "/upload" . $path;
		}
        $filePath = ".".$path;
        //
        if(is_file($filePath)){
            return @unlink($filePath);
        }
        if(is_dir($filePath) && $dir){
            return removeDir($filePath);
        }
        return false;
    }
	
    static public function getError(){
        return self::$error;
    }	
	
    static private function uploadAction($file,$path){
        $ext = self::getExt($file);
        //check
		if(!in_array($ext,self::$ext)){
			self::$error[] = "{$file['name']}文件的格式不允许上传，当前格式[ {$ext} ],允许 ".implode(",",self::$ext);
			return false;
		}
		if($file['size'] > self::$size){
			self::$error[] = "{$file['name']}文件的大小超出限制，当前大小[ {$file['size']} B ],允许 [".self::$size." B]";
			return false;
		}
        //
        $path = trim($path,"/");
        $path = !empty($path) ? "./upload/{$path}/" : "./upload/";
        $path .= date("Y_m_d")."/";
        if(!is_dir($path))@mkdir($path, 0777, true);
        $fileName =  self::getUniName().($ext ? ".{$ext}" : "");
        $filePath = $path.$fileName;
		//
		$upload_domain = config("sys.upload_domain");
		if(empty($upload_domain)){
			$upload_domain = HTTP_HOST;
			$file_url = substr($filePath,1);
		}else{
			$file_url = substr($filePath,8);
		}
		//
		if(self::$thumbSwitch){
			if(in_array($ext,["mp4","mov"])){
				$videoCover_path = $path . self::getUniName() . ".jpg";
				self::getCover($file['tmp_name'],$videoCover_path);
				//
				if(empty(config("sys.upload_domain"))){
					$file_url = substr($videoCover_path,1);
				}else{
					$file_url = substr($videoCover_path,8);
				}
				if(!file_exists($videoCover_path)){
					self::$error[] = "生成视频缩略图检测文件不存在:{$file['tmp_name']} -> {$videoCover_path}";
					return false;
				}
				//
				list($old_width, $old_heitht) = getimagesize($videoCover_path);
				$ratio_1 = ceil($old_width / 300);
				$ratio_2 = ceil($old_heitht / 300);
				$ratio = max($ratio_1,$ratio_2);
				//
				if($ratio > 1){
					self::createThumbImg($videoCover_path,$videoCover_path,$ratio,0,0,"jpg");
				}
				return $upload_domain . $file_url;
			}else if(in_array($ext,["jpg","jpeg","gif","png","wbmp"])){
				//
				list($old_width, $old_heitht) = getimagesize($file['tmp_name']);
				$ratio_1 = ceil($old_width / 300);
				$ratio_2 = ceil($old_heitht / 300);
				$ratio = max($ratio_1,$ratio_2);
				if($ratio < 2)$ratio = 2;
				//
				$res = self::createThumbImg($file['tmp_name'],$filePath,$ratio,0,0,$ext);
				if($res){
					return $upload_domain . $file_url;
				}
			}
			return false;
		}else{
			if(move_uploaded_file($file['tmp_name'],$filePath)){
				return $upload_domain . $file_url;
			}
			self::$error[] = "移动文件失败:{$file['tmp_name']} -> {$filePath}";
        }
        return false;
    }
	//
    static private function getFiles($files){
        $count = count($files['name']);
        $file = [];
        for($i=0;$i<$count;$i++){
            $file[] = [
                "name"=>$files["name"][$i],
                "type"=>$files["type"][$i],
                "tmp_name"=>$files["tmp_name"][$i],
                "error"=>$files["error"][$i],
                "size"=>$files["size"][$i]
            ];
        }
        return $file;
    }

    static private function initAllow(){
        if(empty(self::$ext))self::$ext = Config::get("sys.upload_allow_ext");
        if(empty(self::$size))self::$size = Config::get("sys.upload_allow_size");
    }

    static private function getExt($file){
		$type_arr = [
			"image/jpg"=>"jpg",
			"image/jpeg"=>"jpg",
			"image/pjpeg"=>"jpg",
			"image/x-citrix-jpeg"=>"jpg",
			"image/bmp"=>"bmp",
			"image/gif"=>"gif",
			"image/png"=>"png",
			"image/x-png"=>"png",
			"image/x-citrix-png"=>"png",
			"image/x-icon"=>"icon",
			"image/webp"=>"webp",
			"image/vnd.adobe.photoshop"=>"psd",
			"image/svg+xml"=>"svg",
			"image/pjpeg"=>"pjpeg",
			"video/mp4"=>"mp4",
			"video/3gpp"=>"3gp",
			"video/x-flv"=>"flv",
			"video/x-msvideo"=>"avi",
		];
		if(strpos($file['name'],".") !== false){
			$name_arr = explode('.', basename($file['name']));
			return strtolower(end($name_arr));			
		}else{
			if(isset($type_arr[$file['type']])){
				return $type_arr[$file['type']];
			}
			self::$error[] = "获取格式失败:{$file['tmp_name']} -> {$file['type']}";
			return "";
		}

    }

    static private function getUniName(){
        return md5(uniqid(microtime(true),true));
    }
	
	static public function createThumbImg($imgSrc,$thumbSrc, $ratio=5,$width=0,$height=0,$out_type="jpg") {
		try{
			$img = imagecreatefromstring(file_get_contents($imgSrc));
			list($old_width, $old_heitht) = getimagesize($imgSrc);
			if($ratio > 1){
				$new_width = ceil($old_width / $ratio);
				$new_height = ceil($old_heitht / $ratio);
			}else if($width > 0 && $height > 0){
				$new_width = $width;
				$new_height = $height;
			}else{
				return false;
			}
			$newimg = imagecreatetruecolor($new_width, $new_height);
			imagecopyresampled($newimg, $img, 0, 0, 0, 0, $new_width, $new_height, $old_width, $old_heitht);
			//生成图片
			if(strtolower($out_type) == "jpg" || strtolower($out_type) == "jpeg"){
				$res = imagejpeg($newimg,$thumbSrc);
			}else if(strtolower($out_type) == "png"){
				$res = imagepng($newimg,$thumbSrc);
			}else if(strtolower($out_type) == "gif"){
				$res = imagegif($newimg,$thumbSrc);
			}else if(strtolower($out_type) == "wbmp"){
				$res = imagewbmp($newimg,$thumbSrc);
			}else{
				return false;
			}
			//销毁
			imagedestroy($img);
			imagedestroy($newimg);
			return $res;
		}catch(\Exception $e){
			\core\Controller::exception_log($e->getMessage());
			return false;
		}
	}
	
    static public function getCover($file, $filePath = '') {
        $str = "/usr/local/ffmpeg/bin/ffmpeg -i {$file} -y -ss 00:00:1.000 -vframes 1 -an {$filePath}";
        return system($str);
    }
	
	public function __destruct(){

	}
}
?>
