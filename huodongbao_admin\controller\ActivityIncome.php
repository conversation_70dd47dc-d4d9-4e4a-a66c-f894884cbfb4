<?php
/**
 * 活动收入管理控制器
 * 提供活动收入的查看、统计、审核和管理功能
 */
class ActivityIncome extends \core\Controller {

    /**
     * 活动收入管理首页
     */
    public function index() {
        // 获取统计数据
        $stats = $this->getIncomeStats();
        
        // 获取最近的收入记录
        $recent_records = $this->getRecentIncomeRecords();
        
        $this->assign("stats", $stats);
        $this->assign("recent_records", $recent_records);
        $this->display("activityincome/index.html");
    }

    /**
     * 活动收入记录列表
     */
    public function records() {
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        $activity_id = $_GET['activity_id'] ?? '';
        $publisher_uid = $_GET['publisher_uid'] ?? '';
        $start_date = $_GET['start_date'] ?? '';
        $end_date = $_GET['end_date'] ?? '';

        // 构建查询条件
        $where = "1=1";
        $params = [];

        if (!empty($status)) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        if (!empty($activity_id)) {
            $where .= " AND activity_id = :activity_id";
            $params[':activity_id'] = $activity_id;
        }

        if (!empty($publisher_uid)) {
            $where .= " AND publisher_uid = :publisher_uid";
            $params[':publisher_uid'] = $publisher_uid;
        }

        if (!empty($start_date)) {
            $where .= " AND time >= :start_date";
            $params[':start_date'] = $start_date . ' 00:00:00';
        }

        if (!empty($end_date)) {
            $where .= " AND time <= :end_date";
            $params[':end_date'] = $end_date . ' 23:59:59';
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM activity_income_log WHERE {$where}";
        $total = Db::_fetch($count_sql, $params)['total'];

        // 获取记录
        $offset = ($page - 1) * $limit;
        $sql = "SELECT ail.*, h.name as activity_name, u.nickname as publisher_name
                FROM activity_income_log ail
                LEFT JOIN huodong h ON ail.activity_id = h.id
                LEFT JOIN user u ON ail.publisher_uid = u.uid
                WHERE {$where}
                ORDER BY ail.time DESC
                LIMIT {$offset}, {$limit}";
        
        $records = Db::_fetchAll($sql, $params);

        // 处理状态文本
        foreach ($records as &$record) {
            $record['status_text'] = $this->getStatusText($record['status']);
        }

        $this->assign("records", $records);
        $this->assign("total", $total);
        $this->assign("page", $page);
        $this->assign("limit", $limit);
        $this->assign("filters", $_GET);
        $this->display("activityincome/records.html");
    }

    /**
     * 提现申请管理
     */
    public function withdrawals() {
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';

        // 构建查询条件
        $where = "source_type = 'activity_income'";
        $params = [];

        if (!empty($status)) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM user_tixian WHERE {$where}";
        $total = Db::_fetch($count_sql, $params)['total'];

        // 获取提现申请
        $offset = ($page - 1) * $limit;
        $sql = "SELECT ut.*, u.nickname, ub.bank_name, ub.bank_account, ub.account_name
                FROM user_tixian ut
                LEFT JOIN user u ON ut.uid = u.uid
                LEFT JOIN user_bank ub ON ut.bank_id = ub.id
                WHERE {$where}
                ORDER BY ut.id DESC
                LIMIT {$offset}, {$limit}";
        
        $withdrawals = Db::_fetchAll($sql, $params);

        // 处理状态文本
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['status_text'] = $this->getWithdrawalStatusText($withdrawal['status']);
        }

        $this->assign("withdrawals", $withdrawals);
        $this->assign("total", $total);
        $this->assign("page", $page);
        $this->assign("limit", $limit);
        $this->assign("filters", $_GET);
        $this->display("activityincome/withdrawals.html");
    }

    /**
     * 审核提现申请
     */
    public function approve_withdrawal() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->error("请求方式错误");
        }

        $withdrawal_id = intval($_POST['withdrawal_id']);
        $action = $_POST['action']; // approve 或 reject
        $remark = $_POST['remark'] ?? '';

        if (!in_array($action, ['approve', 'reject'])) {
            $this->error("操作类型错误");
        }

        Db::begin();
        try {
            // 获取提现申请信息
            $withdrawal = Db()->table("user_tixian")
                ->where("id={$withdrawal_id} AND source_type='activity_income'")
                ->fetch();

            if (!$withdrawal) {
                throw new \Exception("提现申请不存在");
            }

            if ($withdrawal['status'] != 0) {
                throw new \Exception("该申请已处理");
            }

            if ($action === 'approve') {
                // 批准提现
                Db()->table("user_tixian")
                    ->where("id={$withdrawal_id}")
                    ->update([
                        'status' => 1,
                        'beizhu' => $remark ?: '管理员批准提现'
                    ]);

                // 更新活动收入记录状态
                if (!empty($withdrawal['commission_ids'])) {
                    $income_ids = $withdrawal['commission_ids'];
                    Db()->table("activity_income_log")
                        ->where("id IN ({$income_ids})")
                        ->update([
                            'status' => 3,
                            'withdraw_time' => date('Y-m-d H:i:s')
                        ]);
                }

                $message = "提现申请已批准";
            } else {
                // 驳回提现
                Db()->table("user_tixian")
                    ->where("id={$withdrawal_id}")
                    ->update([
                        'status' => 2,
                        'beizhu' => $remark ?: '管理员驳回提现'
                    ]);

                // 恢复活动收入记录状态
                if (!empty($withdrawal['commission_ids'])) {
                    $income_ids = $withdrawal['commission_ids'];
                    Db()->table("activity_income_log")
                        ->where("id IN ({$income_ids})")
                        ->update([
                            'status' => 1,
                            'withdraw_apply_id' => null
                        ]);
                }

                $message = "提现申请已驳回";
            }

            Db::commit();
            $this->success($message);

        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取收入统计数据
     */
    private function getIncomeStats() {
        // 本月统计
        $current_month = date('Y-m');
        $month_stats = Db::_fetch("
            SELECT 
                COUNT(*) as total_records,
                SUM(publisher_income) as total_income,
                SUM(platform_fee) as total_platform_fee,
                COUNT(DISTINCT activity_id) as activity_count,
                COUNT(DISTINCT publisher_uid) as publisher_count
            FROM activity_income_log 
            WHERE DATE_FORMAT(time, '%Y-%m') = '{$current_month}'
        ");

        // 总计统计
        $total_stats = Db::_fetch("
            SELECT 
                COUNT(*) as total_records,
                SUM(publisher_income) as total_income,
                SUM(platform_fee) as total_platform_fee,
                COUNT(DISTINCT activity_id) as activity_count,
                COUNT(DISTINCT publisher_uid) as publisher_count
            FROM activity_income_log
        ");

        // 状态统计
        $status_stats = Db::_fetchAll("
            SELECT status, COUNT(*) as count, SUM(publisher_income) as amount 
            FROM activity_income_log 
            GROUP BY status
        ");

        return [
            'month' => $month_stats,
            'total' => $total_stats,
            'status' => $status_stats
        ];
    }

    /**
     * 获取最近的收入记录
     */
    private function getRecentIncomeRecords($limit = 10) {
        $sql = "SELECT ail.*, h.name as activity_name, u.nickname as publisher_name
                FROM activity_income_log ail
                LEFT JOIN huodong h ON ail.activity_id = h.id
                LEFT JOIN user u ON ail.publisher_uid = u.uid
                ORDER BY ail.time DESC
                LIMIT {$limit}";
        
        $records = Db::_fetchAll($sql);
        
        foreach ($records as &$record) {
            $record['status_text'] = $this->getStatusText($record['status']);
        }
        
        return $records;
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status) {
        $status_texts = [
            0 => '待结算',
            1 => '可提取',
            2 => '提现中',
            3 => '已提现',
            4 => '已驳回',
            5 => '已冻结'
        ];
        return $status_texts[$status] ?? '未知状态';
    }

    /**
     * 获取提现状态文本
     */
    private function getWithdrawalStatusText($status) {
        $status_texts = [
            0 => '待审核',
            1 => '已批准',
            2 => '已驳回'
        ];
        return $status_texts[$status] ?? '未知状态';
    }
}
