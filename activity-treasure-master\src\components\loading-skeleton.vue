<template>
  <view class="skeleton-container" :class="containerClass">
    <!-- 头像骨架 -->
    <view v-if="avatar" class="skeleton-avatar" :style="avatarStyle" />
    
    <!-- 标题骨架 -->
    <view v-if="title" class="skeleton-title" :style="titleStyle" />
    
    <!-- 段落骨架 -->
    <view v-if="paragraph" class="skeleton-paragraph">
      <view 
        v-for="(line, index) in paragraphLines" 
        :key="index"
        class="skeleton-line"
        :style="getLineStyle(index)"
      />
    </view>
    
    <!-- 图片骨架 -->
    <view v-if="image" class="skeleton-image" :style="imageStyle" />
    
    <!-- 按钮骨架 -->
    <view v-if="button" class="skeleton-button" :style="buttonStyle" />
    
    <!-- 自定义骨架 -->
    <view v-if="custom" class="skeleton-custom">
      <slot name="skeleton" />
    </view>
    
    <!-- 卡片骨架 -->
    <view v-if="card" class="skeleton-card">
      <view class="skeleton-card-header">
        <view class="skeleton-avatar small" />
        <view class="skeleton-card-meta">
          <view class="skeleton-line short" />
          <view class="skeleton-line shorter" />
        </view>
      </view>
      <view class="skeleton-card-content">
        <view class="skeleton-image" />
        <view class="skeleton-paragraph">
          <view class="skeleton-line" />
          <view class="skeleton-line" />
          <view class="skeleton-line short" />
        </view>
      </view>
    </view>
    
    <!-- 列表骨架 -->
    <view v-if="list" class="skeleton-list">
      <view 
        v-for="item in listCount" 
        :key="item"
        class="skeleton-list-item"
      >
        <view class="skeleton-avatar small" />
        <view class="skeleton-list-content">
          <view class="skeleton-line" />
          <view class="skeleton-line short" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
// 使用内联常量避免循环依赖
const ANIMATION_CONSTANTS = {
  DURATION_FAST: 200,
  DURATION_NORMAL: 300,
  DURATION_SLOW: 500
};

// Props定义
const props = defineProps({
  // 是否显示头像骨架
  avatar: {
    type: Boolean,
    default: false
  },
  
  // 头像大小
  avatarSize: {
    type: [String, Number],
    default: '80rpx'
  },
  
  // 头像形状
  avatarShape: {
    type: String,
    default: 'circle',
    validator: (value) => ['circle', 'square'].includes(value)
  },
  
  // 是否显示标题骨架
  title: {
    type: Boolean,
    default: false
  },
  
  // 标题宽度
  titleWidth: {
    type: [String, Number],
    default: '60%'
  },
  
  // 是否显示段落骨架
  paragraph: {
    type: Boolean,
    default: false
  },
  
  // 段落行数
  paragraphRows: {
    type: Number,
    default: 3
  },
  
  // 是否显示图片骨架
  image: {
    type: Boolean,
    default: false
  },
  
  // 图片宽度
  imageWidth: {
    type: [String, Number],
    default: '100%'
  },
  
  // 图片高度
  imageHeight: {
    type: [String, Number],
    default: '200rpx'
  },
  
  // 是否显示按钮骨架
  button: {
    type: Boolean,
    default: false
  },
  
  // 按钮宽度
  buttonWidth: {
    type: [String, Number],
    default: '120rpx'
  },
  
  // 是否显示卡片骨架
  card: {
    type: Boolean,
    default: false
  },
  
  // 是否显示列表骨架
  list: {
    type: Boolean,
    default: false
  },
  
  // 列表项数量
  listCount: {
    type: Number,
    default: 3
  },
  
  // 是否自定义骨架
  custom: {
    type: Boolean,
    default: false
  },
  
  // 是否启用动画
  animated: {
    type: Boolean,
    default: true
  },
  
  // 动画速度
  animationSpeed: {
    type: String,
    default: 'normal'
    // 移除validator避免小程序兼容性问题
  },
  
  // 圆角大小
  borderRadius: {
    type: [String, Number],
    default: '8rpx'
  },
  
  // 背景色
  backgroundColor: {
    type: String,
    default: '#f0f0f0'
  },
  
  // 高亮色
  highlightColor: {
    type: String,
    default: '#e0e0e0'
  }
});

// 计算属性
const containerClass = computed(() => {
  return [
    'skeleton-container',
    {
      'animated': props.animated,
      [`speed-${props.animationSpeed}`]: props.animated
    }
  ];
});

const avatarStyle = computed(() => {
  const size = formatSize(props.avatarSize);
  return {
    width: size,
    height: size,
    borderRadius: props.avatarShape === 'circle' ? '50%' : formatSize(props.borderRadius)
  };
});

const titleStyle = computed(() => {
  return {
    width: formatSize(props.titleWidth),
    borderRadius: formatSize(props.borderRadius)
  };
});

const imageStyle = computed(() => {
  return {
    width: formatSize(props.imageWidth),
    height: formatSize(props.imageHeight),
    borderRadius: formatSize(props.borderRadius)
  };
});

const buttonStyle = computed(() => {
  return {
    width: formatSize(props.buttonWidth),
    borderRadius: formatSize(props.borderRadius)
  };
});

const paragraphLines = computed(() => {
  return Array.from({ length: props.paragraphRows }, (_, index) => index);
});

// 工具函数
const formatSize = (size) => {
  if (typeof size === 'number') {
    return `${size}rpx`;
  }
  return size;
};

const getLineStyle = (index) => {
  const isLast = index === props.paragraphRows - 1;
  const width = isLast ? '60%' : '100%';
  
  return {
    width,
    borderRadius: formatSize(props.borderRadius)
  };
};

// 获取动画持续时间
const getAnimationDuration = () => {
  const durations = {
    slow: ANIMATION_CONSTANTS.DURATION_SLOW * 3,
    normal: ANIMATION_CONSTANTS.DURATION_NORMAL * 5,
    fast: ANIMATION_CONSTANTS.DURATION_FAST * 8
  };
  return durations[props.animationSpeed] || durations.normal;
};
</script>

<style lang="scss" scoped>
.skeleton-container {
  width: 100%;
  
  &.animated {
    .skeleton-avatar,
    .skeleton-title,
    .skeleton-line,
    .skeleton-image,
    .skeleton-button {
      background: linear-gradient(
        90deg,
        v-bind(backgroundColor) 25%,
        v-bind(highlightColor) 50%,
        v-bind(backgroundColor) 75%
      );
      background-size: 200% 100%;
      animation: skeleton-loading v-bind(getAnimationDuration() + 'ms') infinite;
    }
  }
  
  &:not(.animated) {
    .skeleton-avatar,
    .skeleton-title,
    .skeleton-line,
    .skeleton-image,
    .skeleton-button {
      background-color: v-bind(backgroundColor);
    }
  }
}

.skeleton-avatar {
  background-color: #f0f0f0;
  margin-bottom: var(--spacing-md, 20rpx);
  
  &.small {
    width: 60rpx;
    height: 60rpx;
  }
}

.skeleton-title {
  height: 32rpx;
  background-color: #f0f0f0;
  margin-bottom: var(--spacing-md, 20rpx);
}

.skeleton-paragraph {
  .skeleton-line {
    height: 24rpx;
    background-color: #f0f0f0;
    margin-bottom: var(--spacing-sm, 16rpx);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.short {
      width: 60%;
    }
    
    &.shorter {
      width: 40%;
    }
  }
}

.skeleton-image {
  background-color: #f0f0f0;
  margin-bottom: var(--spacing-md, 20rpx);
}

.skeleton-button {
  height: 60rpx;
  background-color: #f0f0f0;
  margin-top: var(--spacing-md, 20rpx);
}

.skeleton-card {
  border: 1rpx solid #e0e0e0;
  border-radius: var(--radius-md, 16rpx);
  padding: var(--spacing-md, 20rpx);
  margin-bottom: var(--spacing-md, 20rpx);
  
  .skeleton-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md, 20rpx);
    
    .skeleton-card-meta {
      flex: 1;
      margin-left: var(--spacing-md, 20rpx);
      
      .skeleton-line {
        margin-bottom: var(--spacing-sm, 16rpx);
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .skeleton-card-content {
    .skeleton-image {
      width: 100%;
      height: 300rpx;
      margin-bottom: var(--spacing-md, 20rpx);
    }
  }
}

.skeleton-list {
  .skeleton-list-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md, 20rpx) 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .skeleton-list-content {
      flex: 1;
      margin-left: var(--spacing-md, 20rpx);
      
      .skeleton-line {
        margin-bottom: var(--spacing-sm, 16rpx);
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 速度变体
.speed-slow {
  .skeleton-avatar,
  .skeleton-title,
  .skeleton-line,
  .skeleton-image,
  .skeleton-button {
    animation-duration: 2s;
  }
}

.speed-fast {
  .skeleton-avatar,
  .skeleton-title,
  .skeleton-line,
  .skeleton-image,
  .skeleton-button {
    animation-duration: 1s;
  }
}
</style>
