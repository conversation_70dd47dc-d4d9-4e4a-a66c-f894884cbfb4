<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import { store } from "@/store";

const props = defineProps({
  size: {
    type: String || Number,
    default: "14"
  },
  color: {
    type: String,
    default: "#000"
  },
  title: {
    type: String
  },
  blod: {
    type: Boolean,
    default: false
  },
  rSize: {
    type: String || Number,
    default: "20"
  },
  rTitle: {
    type: String
  },
  rColor: {
    type: String
  },
  icon: {
    type: String
  },
  iconStyle: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emit = defineEmits(["click"]);
</script>
<template>
  <view class="mtStitle">
    <view class="df aie jcsb">
      <view
        class="f1"
        v-if="title"
        :style="{
          color,
          fontSize: size + 'rpx',
          fontWeight: blod ? 'bold' : 'normal'
        }"
      >
        {{ title }}
      </view>
      <slot v-else></slot>
      <view @click="emit('click')">
        <u-text
          :suffix-icon="icon"
          :icon-style="iconStyle"
          :color="rColor ? rColor : color"
          :text="rTitle"
          :size="rSize + 'rpx'"
        ></u-text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
