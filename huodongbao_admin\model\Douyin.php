<?php
namespace model;
use core\Db;
class Douyin{
	
	public function __construct(){
		
	}
	
	//小程序登录
	static public function getOpenid($code="",$anonymous_code="",$debug=false){
		$appid = config("app.douyin_appid");
		$appsecret = config("app.douyin_appsecret");
		$url = "https://developer.toutiao.com/api/apps/v2/jscode2session?appid={$appid}&secret={$appsecret}&js_code={$code}&grant_type=authorization_code";
		$data = [
			"appid"=>$appid,
			"secret"=>$appsecret,
			"code"=>$code,
			"anonymous_code"=>$anonymous_code,
		];
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		if(isset($data['err_no']) && $data['err_no'] == 0 && isset($data['data'])){
			return $data['data'];
		}else{
			if($debug === false)return false;
			return $data;
		}
	}

	//获取接口调用的凭证 client_token
	static public function get_client_token($is_online=false){
		//
		if(empty($is_online)){
			$client_token = \core\Cache::getCache("douyin_client_token");
			if(!empty($client_token))return $client_token;
		}
		//
		$appid = config("app.douyin_appid");
		$appsecret = config("app.douyin_appsecret");
		//
		$url = "https://open.douyin.com/oauth/client_token/";
		$params = [
			"grant_type"=>"client_credential",
			"client_key"=>$appid,//应用唯一标识
			"client_secret"=>$appsecret,//应用唯一标识对应的密钥
		];
		$res = curl($url,$params,false,[],true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['data']['access_token']) && !empty($data['data']['access_token']) && isset($data['data']['expires_in'])){
			//\core\Cache::setCache("douyin_client_token",$data['data']['access_token'],$data['data']['expires_in'] - 10);
			return $data['data']['access_token'];
		}else{
			return false;
		}
	}
	
	//获取接口调用的凭证 getAccessToken
	static public function getAccessToken($is_online=false){
		//
		if(empty($is_online)){
			$client_token = \core\Cache::getCache("douyin_access_token");
			if(!empty($client_token))return $client_token;
		}
		//
		$appid = config("app.douyin_appid");
		$appsecret = config("app.douyin_appsecret");
		//
		$url = "https://developer.toutiao.com/api/apps/v2/token";
		$params = [
			"grant_type"=>"client_credential",
			"appid"=>$appid,//应用唯一标识
			"secret"=>$appsecret,//应用唯一标识对应的密钥
		];
		$res = curl($url,$params,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['data']['access_token']) && !empty($data['data']['access_token']) && isset($data['data']['expires_in'])){
			//\core\Cache::setCache("douyin_access_token",$data['data']['access_token'],$data['data']['expires_in'] - 10);
			return $data['data']['access_token'];
		}
		return false;
	}
	
	//担保支付下单
	static public function danbao_pay($money_fen,$out_order_no,$subject="商品描述"){
		//
		$url = "https://developer.toutiao.com/api/apps/ecpay/v1/create_order";
		$data = [
			"app_id"=>config("app.douyin_appid"),//小程序APPID
			"out_order_no"=>$out_order_no,//开发者的单号，长度 <= 64 byte
			"total_amount"=>$money_fen,//支付价格。 单位为[分]
			"subject"=>$subject,//商品描述。 长度限制不超过 128 字节且不超过 42 字符
			"body"=>$subject,//商品详情 长度限制不超过 128 字节且不超过 42 字符
			"valid_time"=>60*60,//订单过期时间(秒)。最小5分钟，最大2天，小于5分钟会被置为5分钟，大于2天会被置为2天
			"notify_url"=>HTTP_HOST . "/pay/douyin_pay_notice",//商户自定义回调地址，必须以 https 开头，支持 443 端口。 指定时，支付成功后抖音会请求该地址通知开发者
		];
		$data['sign'] = self::getRequestSign($data);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			isset($data['data']['order_token']) && 
			isset($data['data']['order_id']) &&
			!empty($data['data']['order_token']) &&
			!empty($data['data']['order_id'])
			
		){
			$data = [
				"order_token"=>$data['data']['order_token'],//签名后的订单信息
				"order_id"=>$data['data']['order_id'],//抖音侧唯一订单号
			];
			return ["status"=>"ok","data"=>$data];
		}
		$msg = isset($data['err_no']) ? $data['err_no'] : "下单失败";
		$msg .= isset($data['err_tips']) ? ":".$data['err_tips'] : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//担保支付-订单支付结果查询
	static public function query_order($out_order_no){
		//
		$url = "https://developer.toutiao.com/api/apps/ecpay/v1/query_order";
		$data = [
			"app_id"=>config("app.douyin_appid"),//小程序APPID
			"out_order_no"=>$out_order_no,//开发者的单号，长度 <= 64 byte
		];
		$data['sign'] = self::getRequestSign($data);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['payment_info']['order_status']) && $data['payment_info']['order_status'] == "SUCCESS"){
			return ["status"=>"ok","data"=>$data];
		}
		return false;
	}
	
	//担保支付退款
	static public function danbao_pay_tuikuan($refund_amount,$out_order_no,$reason="协商退款"){
		//
		$url = "https://developer.toutiao.com/api/apps/ecpay/v1/create_refund";
		$data = [
			"app_id"=>config("app.douyin_appid"),//小程序APPID
			"out_order_no"=>$out_order_no,//商户分配支付单号，标识进行退款的订单
			"out_refund_no"=>$out_order_no,//商户分配退款号，保证在商户中唯一
			"reason"=>$reason,//退款原因
			"refund_amount"=>$refund_amount,//退款金额，单位分
			"notify_url"=>"https://api.benbene.cn/pay/douyin_pay_tuikuan_notify",//商户自定义回调地址，必须以 https 开头，支持 443 端口
		];
		$data['sign'] = self::getRequestSign($data);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			isset($data['err_no']) && 
			isset($data['refund_no']) &&
			empty($data['err_no']) &&
			!empty($data['refund_no'])
			
		){
			return ["status"=>"ok","data"=>$data['refund_no']];
		}
		$msg = isset($data['err_no']) ? $data['err_no'] : "受理失败";
		$msg .= isset($data['err_tips']) ? ":".$data['err_tips'] : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//获取担保支付请求签名
    static public  function getRequestSign($params){
        $filtered = [];
        foreach ($params as $key => $value) {
            if (in_array($key, ['sign', 'app_id', 'thirdparty_id','prod_id'])) {
                continue;
            }
            $filtered[] = is_string($value) ? trim($value): $value;
        }
        $filtered[] = config("app.douyin_pay_salt");
        sort($filtered, SORT_STRING);
        return md5(implode('&', $filtered));
    }
	
	//验证担保支付回调签名
    static public function getNotifySign($params){
		if(!is_array($params) || !isset($params['msg_signature']) || !isset($params['msg']) || !is_string($params['msg'])){
			return false;
		}
        $filtered = [];
        foreach ($params as $key => $value) {
            if (in_array($key, ['msg_signature', 'type'])) {
                continue;
            }
            $filtered[] = is_string($value) ? trim($value) : $value;
        }
        $filtered[] = config("app.douyin_pay_token");
        sort($filtered, SORT_STRING);
        $filtered_str = implode('', $filtered);
        $sign = sha1($filtered_str);
		if($sign == $params['msg_signature']){
			return true;
		}
		return false;
    }
	
	//担保支付订单同步推送
	static public function danbao_pay_push($open_id,$order_id,$total_price,$create_time,$update_time,$status,$order_status,$item_code="111",$img=""){
		//
		$url = "https://developer.toutiao.com/api/apps/order/v2/push";
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		$data = [
			"access_token"=>$access_token,
			"app_name"=>"douyin",
			"open_id"=>$open_id,
			"order_detail"=>json_encode([
				"order_id"=>$order_id,//开发者侧业务单号
				"create_time"=>$create_time,//订单创建的时间，13 位毫秒时间戳
				"status"=>$status,//订单状态，建议采用以下枚举值：待支付 已支付 已取消 已超时 已核销 退款中 已退款 退款失败
				"amount"=>1,//订单商品总数
				"total_price"=>$total_price,//订单总价，单位为分
				"detail_url"=>"/pages/bundle/myOrder/myOrder",//小程序订单详情页 path，长度<=1024 byte (备注：该路径需要保证在小程序内配置过，相对路径即可）
				"item_list"=>[
					[
						"item_code"=>$item_code,//开发者侧商品 ID，长度 <= 64 byte
						"img"=>$img,//子订单商品图片 URL，长度 <= 512 byte
						"title"=>"会员充值",//子订单商品介绍标题，长度 <= 256 byte
						"amount"=>1,//单类商品的数目
						"price"=>$total_price//单类商品的总价，单位为分
					]
				]
			],JSON_UNESCAPED_UNICODE),
			"order_status"=>$order_status,//0：待支付1：已支付2：已取消（用户主动取消或者超时未支付导致的关单）4：已核销（核销状态是整单核销,即一笔订单买了 3 个券，核销是指 3 个券核销的整单）5：退款中6：已退款8：退款失败
			"order_type"=>0,//订单类型，枚举值:0：普通小程序订单（非POI订单）9101：团购券订单（POI 订单）9001：景区门票订单（POI订单）
			"update_time"=>$update_time,//订单信息变更时间，13 位毫秒级时间戳
		];
		$data = json_encode($data,JSON_UNESCAPED_UNICODE);
		$header = [
			'Content-Type: application/json; charset=utf-8'
		];
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_TIMEOUT, 120);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_HTTPHEADER,$header);	
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_HEADER, TRUE);
		curl_setopt($curl, CURLOPT_NOBODY, FALSE);
		curl_setopt($curl,CURLINFO_HEADER_OUT,true);
		$response = curl_exec($curl);
		$getinfo = curl_getinfo($curl);
		$request_header = isset($getinfo['request_header']) ? $getinfo['request_header'] : "";
		$code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
		$headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
		curl_close($curl);
		$header = trim(substr($response, 0, $headerSize));
		$body = trim(substr($response, $headerSize));
		
		//dump($request_header);
		//dump($data);
		//dump($header);
		//dump($body);
		$data = json_decode($body,true);
		//dump($data);
		if(!empty($data) && isset($data['err_code']) && $data['err_code'] == 0){
			return ["status"=>"ok"];
		}
		$msg = isset($data['err_code']) ? $data['err_code'] : "抖音担保支付同步推送订单失败";
		$msg .= isset($data['err_msg']) ? ":".$data['err_msg'] : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
