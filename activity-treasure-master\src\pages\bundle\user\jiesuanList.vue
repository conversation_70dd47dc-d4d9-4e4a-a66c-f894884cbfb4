<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  userget_daijiesuan_order_huodong,
  userget_daijiesuan_order_huodong_yongjin,
  userget_daijiesuan_order_goods_yongjin,
  userget_daijiesuan_order_huiyuan_yongjin,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, navto } from "@/utils";
import { getItem } from "../../../utils";

const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");
const api = ref();
const type = ref();

onReady(async () => {
  height.value = (await setListHeight()) - 50 + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  api
    .value({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || []; // 当前页数据
      if (mescroll.num == 1) goods.value = []; // 第一页需手动制空列表
      goods.value = goods.value.concat(curPageData); //追加新数据
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr(); // 请求失败, 结束加载
    });
};

onLoad((e) => {
  if (e?.id) {
    type.value = e.id;
    let title;
    switch (e.id) {
      case "1":
        api.value = userget_daijiesuan_order_huodong;
        title = "活动结算列表";
        break;
      case "2":
        api.value = userget_daijiesuan_order_huodong_yongjin;
        title = "活动佣金列表";
        break;
      case "3":
        api.value = userget_daijiesuan_order_goods_yongjin;
        title = "商品佣金列表";
        break;
      case "4":
        api.value = userget_daijiesuan_order_huiyuan_yongjin;
        title = "会员佣金列表";
        break;
    }
    uni.setNavigationBarTitle({ title });
  }
});
</script>
<template>
  <view>
    <mescroll-uni
      class="list"
      :height="height"
      :down="{
        auto: false,
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <view class="p30 df borderBottom" v-for="(val, i) in goods" :key="i">
        <u-avatar
          size="110rpx"
          mode="aspectFill"
          :src="type == 1 ? val.img_url : val.user_info.avatar"
        ></u-avatar>
        <view class="ml20 df fdc f1">
          <u-text color="#414141" :text="type == 1 ? val.name : val.user_info.nickname">
          </u-text>
          <u-text
            v-if="type != 1"
            color="#333"
            size="22rpx"
            lines="1"
            :text="val?.time || val?.create_time"
          ></u-text>
        </view>
        <u-text
          mode="price"
          align="right"
          color="#FF2A00"
          size="32rpx"
          :text="type == 1 ? val.huodong_daijiesuan : val?.yongjin_money || val?.money"
        ></u-text>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less"></style>
