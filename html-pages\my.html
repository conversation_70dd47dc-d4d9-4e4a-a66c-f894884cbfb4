<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="活动宝 - 个人中心">
    <title>活动宝 - 我的</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 页面特定样式 */
        .profile-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            padding: var(--spacing-xxl) var(--spacing-lg) var(--spacing-xl);
            color: white;
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.8s ease-out;
        }
        
        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(-15deg);
        }
        
        .profile-info {
            position: relative;
            z-index: 2;
        }
        
        .edit-profile-btn {
            position: absolute;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: 0.21rem; /* 4rpx */
            color: white;
            font-size: var(--font-size-xs);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-xs);
            transition: all var(--transition-fast);
        }
        
        .edit-profile-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .profile-main {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .profile-avatar-container {
            position: relative;
        }
        
        .profile-avatar {
            width: 3.73rem; /* 70rpx */
            height: 3.73rem;
            border-radius: var(--radius-round);
            border: 0.11rem solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .vip-badge {
            position: absolute;
            bottom: -0.27rem; /* -5rpx */
            left: 1.28rem; /* 24rpx */
            width: 2.03rem; /* 38rpx */
            height: 0.91rem; /* 17rpx */
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: var(--radius-xs);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.48rem; /* 9rpx */
            font-weight: var(--font-weight-bold);
            color: #333;
            box-shadow: var(--shadow-sm);
        }
        
        .profile-details h2 {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            margin-bottom: 0.21rem; /* 4rpx */
        }
        
        .profile-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-sm);
            opacity: 0.9;
        }
        
        .profile-stats {
            display: flex;
            gap: var(--spacing-xl);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: var(--font-size-xxl);
            font-weight: var(--font-weight-bold);
            margin-bottom: 0.13rem; /* 2rpx */
        }
        
        .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.8;
        }
        
        .profile-card {
            background: var(--color-surface);
            margin: -1.6rem var(--spacing-lg) var(--spacing-md); /* -30rpx */
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-card);
            position: relative;
            z-index: 3;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0;
            padding: var(--spacing-lg);
            border-bottom: 0.05rem solid var(--color-border-light);
        }
        
        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--spacing-sm);
            text-decoration: none;
            color: var(--color-text-secondary);
            transition: all var(--transition-fast);
            border-radius: var(--radius-xs);
        }
        
        .quick-action:hover {
            color: var(--color-primary);
            background: var(--color-primary-lighter);
        }
        
        .quick-action-icon {
            width: 2.13rem; /* 40rpx */
            height: 2.13rem;
            border-radius: var(--radius-round);
            background: var(--color-primary-lighter);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-md);
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
            transition: all var(--transition-fast);
        }
        
        .quick-action:hover .quick-action-icon {
            background: var(--color-primary);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 0.21rem 0.64rem rgba(92, 184, 92, 0.3);
        }

        .quick-action:active .quick-action-icon {
            transform: scale(0.95);
        }
        
        .quick-action-text {
            font-size: var(--font-size-xs);
            text-align: center;
        }
        
        .membership-section {
            padding: var(--spacing-lg);
        }
        
        .membership-card {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            color: #333;
            position: relative;
            overflow: hidden;
        }
        
        .membership-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 150%;
            height: 150%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
            transform: rotate(15deg);
        }
        
        .membership-content {
            position: relative;
            z-index: 2;
        }
        
        .membership-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--spacing-xs);
        }
        
        .membership-desc {
            font-size: var(--font-size-sm);
            opacity: 0.8;
            margin-bottom: var(--spacing-md);
        }
        
        .membership-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .membership-badge {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 0.13rem 0.27rem; /* 2rpx 5rpx */
            border-radius: var(--radius-xs);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }
        
        .menu-section {
            background: var(--color-surface);
            margin: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-card);
            overflow: hidden;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md) var(--spacing-lg);
            text-decoration: none;
            color: var(--color-text-primary);
            transition: all var(--transition-fast);
            border-bottom: 0.05rem solid var(--color-border-light);
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: var(--color-gray-50);
        }
        
        .menu-icon {
            width: 1.6rem; /* 30rpx */
            height: 1.6rem;
            margin-right: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-md);
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            margin-bottom: 0.13rem; /* 2rpx */
        }
        
        .menu-subtitle {
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
        }
        
        .menu-arrow {
            color: var(--color-text-tertiary);
            font-size: var(--font-size-sm);
        }
        
        .main-content {
            padding-bottom: 4rem; /* 为底部导航栏留空间 */
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- 个人信息头部 -->
        <header class="profile-header">
            <div class="edit-profile-btn">
                <i class="fas fa-pen"></i>
                <span>编辑资料</span>
            </div>
            
            <div class="profile-info">
                <div class="profile-main">
                    <div class="profile-avatar-container">
                        <img src="https://via.placeholder.com/70x70/88D7A0/ffffff?text=青" alt="用户头像" class="profile-avatar">
                        <div class="vip-badge">会员</div>
                    </div>
                    <div class="profile-details">
                        <h2 class="text-hierarchy-h1">青</h2>
                        <div class="profile-meta">
                            <span class="text-hierarchy-caption">♂ 男</span>
                            <span class="text-hierarchy-caption">0关注</span>
                            <span class="text-hierarchy-caption">0粉丝</span>
                        </div>
                    </div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-value">0</div>
                        <div class="stat-label">我的积分</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">0.00</div>
                        <div class="stat-label">我的提现</div>
                    </div>
                </div>
            </div>
        </header>
        
        <main class="main-content">
            <!-- 个人信息卡片 -->
            <div class="profile-card">
                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <a href="#" class="quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-thumbs-up"></i>
                        </div>
                        <div class="quick-action-text">赞过</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="quick-action-text">收藏</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="quick-action-text">评论</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="quick-action-icon">
                            <i class="fas fa-share"></i>
                        </div>
                        <div class="quick-action-text">发布</div>
                    </a>
                </div>
                
                <!-- 会员专区 -->
                <div class="membership-section">
                    <div class="membership-card">
                        <div class="membership-content">
                            <div class="membership-title">分享会员体验券</div>
                            <div class="membership-desc">邀请好友免费体验30天会员</div>
                            <div class="membership-status">
                                <div class="membership-badge">已开通</div>
                                <div style="font-size: var(--font-size-sm); opacity: 0.8;">会员服务</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 功能菜单 -->
            <div class="menu-section">
                <a href="#" class="menu-item">
                    <div class="menu-icon" style="color: #FF6B6B;">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">我的活动</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
                
                <a href="#" class="menu-item">
                    <div class="menu-icon" style="color: #4ECDC4;">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">我的通知</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
                
                <a href="#" class="menu-item">
                    <div class="menu-icon" style="color: #FFD93D;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">我的推广列表</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
                
                <a href="#" class="menu-item">
                    <div class="menu-icon" style="color: #6C5CE7;">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="menu-content">
                        <div class="menu-title">我的推广码</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
            </div>
        </main>
        
        <!-- 底部导航栏 -->
        <nav class="bottom-nav">
            <a href="index.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="bottom-nav-text">活动</div>
            </a>
            <a href="world.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="bottom-nav-text">世界</div>
            </a>
            <a href="add-activity.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="bottom-nav-text">发布</div>
            </a>
            <a href="my.html" class="bottom-nav-item active">
                <div class="bottom-nav-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="bottom-nav-text">我的</div>
            </a>
        </nav>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 编辑资料按钮
            const editBtn = document.querySelector('.edit-profile-btn');
            editBtn.addEventListener('click', function() {
                console.log('编辑资料');
                // 这里可以添加跳转到编辑页面的逻辑
            });
            
            // 快捷操作点击
            const quickActions = document.querySelectorAll('.quick-action');
            quickActions.forEach(action => {
                action.addEventListener('click', function(e) {
                    e.preventDefault();
                    const text = this.querySelector('.quick-action-text').textContent;
                    console.log('点击快捷操作:', text);
                    
                    // 添加点击反馈
                    const icon = this.querySelector('.quick-action-icon');
                    icon.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            icon.style.transform = '';
                        }, 150);
                    }, 100);
                });
            });
            
            // 菜单项点击
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const title = this.querySelector('.menu-title').textContent;
                    console.log('点击菜单项:', title);
                    
                    // 添加点击反馈
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // 会员卡片点击
            const membershipCard = document.querySelector('.membership-card');
            membershipCard.addEventListener('click', function() {
                console.log('点击会员卡片');
                
                // 添加点击反馈
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
