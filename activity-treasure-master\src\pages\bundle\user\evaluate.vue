<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { upload_img, goodsadd_pingjia } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import myLine from "@/components/myLine.vue";
import { store } from "@/store";
import { getListHeight, back } from "@/utils";

const info = ref({});
const gapHeight = ref(0);

onLoad((e) => {
  info.value = store().$state.goodInfo;
  info.value.goods_info.forEach((val) => {
    val.fileList = [];
    val.imgs_url = [];
    val.star_num = 1;
    val.contents = "";
  });
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});

// 删除图片
const deletePic = (event) => {
  info.value.goods_info[event.name].fileList.splice(event.index, 1);
  info.value.goods_info[event.name].imgs_url.splice(event.index, 1);
};

// 新增图片
const afterRead = async (event) => {
  let list = info.value.goods_info[event.name].fileList;
  // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file);
  let fileListLen = list.length;
  lists.map((item) => {
    list.push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });
  for (let i = 0; i < lists.length; i++) {
    const res = await upload_img(lists[i].url);
    let item = list[fileListLen];
    list.splice(fileListLen, 1, {
      ...item,
      status: "success",
      message: "",
      url: res.data,
    });
    fileListLen++;
    info.value.goods_info[event.name].imgs_url.push(res.data);
  }
};

const submit = async () => {
  let data = [];
  info.value.goods_info.forEach((val) => {
    data.push({
      goods_id: val.goods_id,
      guige_id: val.guige_id,
      star_num: val.star_num,
      contents: val.contents,
      imgs_url: val.imgs_url,
    });
  });
  const res = await goodsadd_pingjia({
    order_id: info.value.order_id,
    pingjia_data: JSON.stringify(data),
  });
  if (res.status === "ok") back({ tip: "提交成功，即将返回", time: 2000 });
  else uni.$u.toast(res.msg);
};
</script>
<template>
  <view class="page px30">
    <view class="pt20 borderBottom" v-for="(val, i) in info.goods_info" :key="i">
      <myLine
        w="8"
        h="30"
        bg="#EF6227"
        c="#EF6227"
        :title="val.goods_name"
        size="32"
      ></myLine>
      <u-gap height="20rpx"></u-gap>
      <u-rate count="5" v-model="val.star_num" activeColor="#EF6227"></u-rate>
      <u-gap height="20rpx"></u-gap>
      <u-textarea
        v-model="val.contents"
        placeholder="请输入评价内容~（选填）"
        count
        maxlength="500"
      ></u-textarea>
      <u-gap height="20rpx"></u-gap>
      <u-upload
        :fileList="val.fileList"
        @afterRead="afterRead"
        @delete="deletePic"
        :name="i + ''"
        multiple
        :maxCount="9"
        width="215rpx"
        height="215rpx"
      >
        <view class="df aic jcc b6e" style="width: 215rpx; height: 215rpx">
          <u-icon name="plus" size="32" color="#aaa"></u-icon>
        </view>
      </u-upload>
      <u-gap height="20rpx"></u-gap>
    </view>
    <u-gap :height="gapHeight + 10"></u-gap>
    <view class="pfx w690 bottom0 tl50 left50 bottomBox">
      <u-button
        shape="circle"
        color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
        text="提交"
        :customStyle="{ color: '#333' }"
        @click="submit"
      ></u-button>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
