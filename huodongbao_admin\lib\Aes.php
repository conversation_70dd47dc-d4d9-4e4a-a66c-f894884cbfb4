<?php
namespace lib;

class Aes{
	
	public static $key = "";//密钥
	private static $cipher_algo = "aes-256-cbc";//加密方法 cbc gcm
	private static $data_type = "hex";// hex base64
	private static $cbc_cipher_algo = [
		"aes-128-cbc",			
		"aes-192-cbc",
		"aes-256-cbc",
	];
	private static $gcm_cipher_algo = [
		"aes-128-gcm",		
		"aes-192-gcm",		
		"aes-256-gcm",	
	];
	
	//设置密钥
	public static function set_key($key){
		self::$key = (string)$key;
		return true;
	}
	
	//设置加密方法
	public static function set_cipher_algo($cipher_algo){
		$cipher_algo = strtolower($cipher_algo);
		$cipher_algo_arr = array_merge(self::$cbc_cipher_algo,self::$gcm_cipher_algo);
		if(in_array($cipher_algo,$cipher_algo_arr)){
			self::$cipher_algo = $cipher_algo;
			return true;
		}
		return false;
	}

	//设置输入输出类型
	public static function set_output($data_type="hex"){
		$data_type = strtolower($data_type);
		$data_type_arr = [
			"hex",
			"base64",
		];
		if(in_array($data_type,$data_type_arr)){
			self::$data_type = $data_type;
			return true;
		}
		return false;
	}
	
	//加密
    public static function encrypt($data,$aad=""){
		if(in_array(self::$cipher_algo,self::$cbc_cipher_algo)){
			return self::cbc_encrypt($data);
		}else if(in_array(self::$cipher_algo,self::$gcm_cipher_algo)){
			return self::gcm_encrypt($data,$aad);
		}
		return false;
    }

	//解密
    public static function decrypt($cipher,$iv,$tag="",$aad=""){
		if(in_array(self::$cipher_algo,self::$cbc_cipher_algo)){
			return self::cbc_decrypt($cipher,$iv);
		}else if(in_array(self::$cipher_algo,self::$gcm_cipher_algo)){
			return self::gcm_decrypt($cipher,$iv,$tag,$aad);
		}
		return false;
    }
	
	//cbc加密 
    private static function cbc_encrypt($data){
		$iv = openssl_random_pseudo_bytes(16);
		$cipher = openssl_encrypt($data, self::$cipher_algo, self::$key, OPENSSL_RAW_DATA, $iv);
		if(!$cipher)return false;
		if(self::$data_type == "hex"){
			return [
				"iv"=>bin2hex($iv),
				"cipher"=>bin2hex($cipher),
			];
		}else{
			return [
				"iv"=>base64_encode($iv),
				"cipher"=>base64_encode($cipher),
			];
		}
    }

	//cbc解密
    private static function cbc_decrypt($cipher,$iv){
		if(self::$data_type == "hex"){
			if(!ctype_xdigit($cipher))return false;
			if(!ctype_xdigit($iv))return false;
			$cipher = hex2bin($cipher);
			$iv = hex2bin($iv);
		}else{
			$cipher = base64_decode($cipher);
			$iv = base64_decode($iv);
		}
		return openssl_decrypt($cipher, self::$cipher_algo, self::$key, OPENSSL_RAW_DATA, $iv);
    }

	//gcm加密
    private static function gcm_encrypt($data,$aad=""){
		$iv = openssl_random_pseudo_bytes(16);
		$cipher = openssl_encrypt($data, self::$cipher_algo, self::$key, OPENSSL_RAW_DATA, $iv,$tag,$aad);
		if(!$cipher)return false;
		if(self::$data_type == "hex"){
			return [
				"iv"=>bin2hex($iv),
				"tag"=>bin2hex($tag),
				"aad"=>bin2hex($aad),
				"cipher"=>bin2hex($cipher),
			];
		}else{
			return [
				"iv"=>base64_encode($iv),
				"tag"=>base64_encode($tag),
				"aad"=>base64_encode($aad),
				"cipher"=>base64_encode($cipher),
			];
		}
    }
	
	//gcm解密
    private static function gcm_decrypt($cipher,$iv,$tag,$aad){
		if(self::$data_type == "hex"){
			if(!ctype_xdigit($cipher))return false;
			if(!ctype_xdigit($iv))return false;
			if(!ctype_xdigit($tag))return false;
			if(!ctype_xdigit($aad))return false;
			$cipher = hex2bin($cipher);
			$iv = hex2bin($iv);
			$tag = hex2bin($tag);
			$aad = hex2bin($aad);
		}else{
			$cipher = base64_decode($cipher);
			$iv = base64_decode($iv);
			$tag = base64_decode($tag);
			$aad = base64_decode($aad);
		}
		return openssl_decrypt($cipher, self::$cipher_algo, self::$key, OPENSSL_RAW_DATA, $iv,$tag,$aad);
    }
}

