<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 图片配置 
*/
class Imgconfig extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['name'])){
			$where .= " AND name LIKE :name";
			$prepareParam[":name"]="%".$_REQUEST['name']."%";
			$pageParams['name'] = $_REQUEST['name'];
		}
		$data = Db()->table("img_config")->where($where)->prepareParam($prepareParam)->order("id")->page($page,$page_size,$pageParams);
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		return view(['header','imgconfig/index','footer']);
	}
	/*
	* @name 编辑
	* @method edit
	*/	
	public function edit(){
		responseType("json");
		if(
			empty($_POST['id']) ||
			!check($_POST['id'],"intgt0") ||
			empty($_POST['img_url'])||
			!check($_POST['img_url'],"url")
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = (int)$_POST['id'];
		$img_url = htmlspecialchars($_POST['img_url']);
		$data = [
			"img_url"=>":img_url",
		];
		$prepareParam = [
			":img_url"=>$img_url,
		];
		dbConn();
		Db::begin();
		try{
			$res = Db()->table("img_config")->prepareParam($prepareParam)->where("id={$id}")->update($data);
			if(empty($res)){
				throw new \Exception("操作失败");
			}
			$this->root_log("编辑图片配置信息,id:{$id}");
			Db::commit();
			return ["status"=>"ok","msg"=>"编辑成功"];
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	public function _empty(){
		
	}
	function __destruct(){

	}
}
