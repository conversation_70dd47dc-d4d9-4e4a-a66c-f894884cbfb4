<script setup>
import { ref, reactive, computed } from 'vue';
import { publishFeed, upload_img } from '@/api/index.js';
import { store } from '@/store';
import { onLoad } from '@dcloudio/uni-app';
import customNavbar from '@/components/customNavbar.vue';

// --- State Refs ---
const content = ref(''); // 动态内容
const images = ref([]); // u-upload fileList
const location = ref(null); // 地点对象 { name: '', address: '', latitude: 0, longitude: 0 }
const tags = ref(''); // 标签字符串 (逗号分隔)
const privacy = ref('public'); // 隐私设置: 'public' or 'private'
const isSubmitting = ref(false); // 是否正在提交中
const feedIdToEdit = ref(null); // 用于编辑模式

// --- Computed Properties ---
const locationDisplay = computed(() => {
    return location.value ? (location.value.name || location.value.address) : '添加位置';
});

// 计算最大上传图片数量（会员4张，非会员1张）
const maxImageCount = computed(() => {
    const userInfo = store().$state.userInfo;
    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员
    return isVip ? 4 : 1;
});

// --- Lifecycle Hooks ---
onLoad((options) => {
    if (options && options.id) {
        feedIdToEdit.value = options.id;
        // TODO: Fetch feed data for editing
        console.log("Editing feed with ID:", feedIdToEdit.value);
        // fetchFeedDataForEdit(feedIdToEdit.value);
    }
});

// --- Event Handlers ---
const handleClose = () => {
    uni.navigateBack();
};

/**
 * u-upload 组件读取文件后的处理函数
 */
const handleAfterRead = async (event) => {
    // 将新选择的文件添加到列表，并标记为上传中
    let lists = [].concat(event.file);
    let fileListLen = images.value.length;

    lists.map((item) => {
        images.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        });
    });

    // 依次上传新选择的文件
    for (let i = 0; i < lists.length; i++) {
        const currentFileIndex = fileListLen + i; // 记录当前处理文件在 images 数组中的索引
        try {
            // 调用上传 API - 注意这里不再传递第二个参数
            const res = await upload_img(lists[i].url);
            console.log(`Upload result for index ${currentFileIndex}:`, JSON.stringify(res)); // 记录上传结果

            // 检查上传结果
            if (res.status === 'ok' && res.data) {
                // 更新文件状态为成功，并保存返回的 URL
                let item = images.value[currentFileIndex];
                if (item) { // 确保项目存在
                    images.value.splice(currentFileIndex, 1, {
                        ...item,
                        status: 'success',
                        message: '',
                        url: res.data // 使用返回的data字段作为URL
                    });
                }
            } else {
                // 处理上传成功但返回错误状态的情况
                if (images.value[currentFileIndex]) {
                    images.value[currentFileIndex].status = 'failed';
                    images.value[currentFileIndex].message = res.msg || '上传失败';
                }
                console.error("Upload API error:", res);
                uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });
            }
        } catch (error) {
            // 处理上传过程中的异常
            if (images.value[currentFileIndex]) {
                images.value[currentFileIndex].status = 'failed';
                images.value[currentFileIndex].message = '上传失败';
            }
            console.error("Upload exception:", error);
            uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });
        }
    }
};

/**
 * u-upload 组件删除图片时的处理函数
 */
const handleDeletePic = (event) => {
    images.value.splice(event.index, 1);
};

/**
 * 处理添加位置按钮点击事件
 */
const handleAddLocation = () => {
    uni.chooseLocation({
        success: (res) => {
            location.value = { // Store location object
                name: res.name,
                address: res.address,
                latitude: res.latitude,
                longitude: res.longitude
            };
        },
        fail: (err) => {
            console.error('Choose location failed:', err);
        }
    });
};

/**
 * 处理发布按钮点击事件
 */
const handleSubmit = async () => {
  console.log('handleSubmit triggered!');
  // 检查用户是否已登录
  if (!store().$state.userInfo?.uid || !store().$state.userInfo?.token) {
    uni.showToast({ title: '请先登录', icon: 'none' });
    return;
  }

  if (!content.value.trim() && images.value.filter(img => img.status === 'success').length === 0) {
    uni.showToast({ title: '内容或图片至少要有一个哦', icon: 'none' });
    return;
  }
  if (isSubmitting.value) return;

  isSubmitting.value = true;

  try {
    // 确保所有图片都已上传成功
    const pendingUploads = images.value.filter(img => img.status === 'uploading');
    if (pendingUploads.length > 0) {
      uni.showToast({ title: '图片上传中，请稍候再试', icon: 'none' });
      isSubmitting.value = false;
      return;
    }

    // 获取所有上传成功的图片URL
    const uploadedImageUrls = images.value
      .filter(img => img.status === 'success' && img.url) // 确保URL存在
      .map(img => img.url);

    console.log('Submitting images:', uploadedImageUrls);
    console.log('Images type:', Array.isArray(uploadedImageUrls) ? 'Array' : typeof uploadedImageUrls);

    // 准备参数 - 确保与后端API一致
    const params = {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      content: content.value.trim(),
      images: uploadedImageUrls, // 直接传递数组，后端会处理
      location: location.value ? JSON.stringify({
        name: location.value.name,
        address: location.value.address,
        latitude: location.value.latitude,
        longitude: location.value.longitude
      }) : '',
      tags: tags.value.trim(),
      privacy: privacy.value,
      type: 'feed' // 标识为动态类型
    };

    // 详细记录参数信息
    console.log('Submitting params:', JSON.parse(JSON.stringify(params))); // 记录所有参数
    console.log('Images param type:', Array.isArray(params.images) ? 'Array' : typeof params.images);
    console.log('Images param value:', params.images);

    let res;
    if (feedIdToEdit.value) {
      // 调用updateFeed API（需要创建/导入）
      // res = await updateFeed({ ...params, id: feedIdToEdit.value });
      uni.showToast({ title: '编辑功能待实现', icon: 'none' });
      isSubmitting.value = false; // 重置提交状态
      return; // 暂时在这里停止
    } else {
      // 调用publishFeed API
      res = await publishFeed(params);
    }

    if (res.status === 'ok') {
      uni.showToast({ title: feedIdToEdit.value ? '修改成功' : '发布成功', icon: 'success' });
      // 考虑仅在新帖子成功时清除表单
      // if (!feedIdToEdit.value) { clearForm(); }
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    } else if (res.status === 'relogin') {
      uni.showToast({ title: '请先登录', icon: 'none' });
    } else {
      uni.showToast({ title: res.msg || (feedIdToEdit.value ? '修改失败' : '发布失败'), icon: 'none' });
    }
  } catch (error) {
    console.error('Submit feed error:', error);
    uni.showToast({ title: (feedIdToEdit.value ? '修改失败' : '发布失败') + '，请重试', icon: 'none' });
  } finally {
    isSubmitting.value = false;
  }
};

</script>

<template>
  <view class="post-page">
    <!-- 统一导航栏 -->
    <customNavbar
      title="发布动态"
      backIcon="close"
      @back="handleClose"
    />

    <!-- Main Content Area -->
    <scroll-view scroll-y class="main-content">
        <!-- Content Textarea -->
        <view class="textarea-wrapper">
            <u--textarea
                v-model="content"
                placeholder="分享你的新鲜事... #话题#"
                height="150"
                maxlength="-1"
                border="none"
                :customStyle="{ padding: '0', lineHeight: '1.6' }"
            ></u--textarea>
        </view>

        <!-- Image Upload -->
        <view class="upload-wrapper">
             <u-upload
                :fileList="images"
                @afterRead="handleAfterRead"
                @delete="handleDeletePic"
                name="file"
                multiple
                :maxCount="maxImageCount"
                :previewImage="true"
                width="200rpx"
                height="200rpx"
                uploadIconColor="#ccc"
            ></u-upload>
            <view v-if="maxImageCount === 1" class="upload-tip">
                <text class="tip-text">非会员最多上传1张图片，升级会员可上传4张</text>
            </view>
        </view>

        <!-- Options Section -->
        <view class="options-section">
            <view class="option-item" @click="handleAddLocation">
                <u-icon name="map-fill" size="20" :color="location ? '#5ac725' : '#666'"></u-icon>
                <text class="option-text" :class="{ 'selected': location }">{{ locationDisplay }}</text>
                <u-icon name="arrow-right" size="16" color="#ccc" customStyle="margin-left: auto;"></u-icon>
            </view>
             <view class="option-item">
                 <u-icon name="tags-fill" size="20" color="#666"></u-icon>
                 <!-- Simple tag input for now -->
                  <input class="tag-input" v-model="tags" placeholder="添加相关标签" />
             </view>
             <view class="option-item">
                 <u-icon name="lock-fill" size="20" color="#666"></u-icon>
                 <text class="option-text">谁可以看</text>
                  <view class="privacy-switch">
                     <u-radio-group v-model="privacy" placement="row">
                        <u-radio label="公开" name="public" :customStyle="{marginRight: '16rpx'}"></u-radio>
                        <u-radio label="私密" name="private"></u-radio>
                    </u-radio-group>
                 </view>
             </view>
        </view>
    </scroll-view>

    <!-- 发布按钮 - 固定在页面底部右侧 -->
    <view class="publish-button-container">
      <view
        class="publish-btn"
        :class="{ 'disabled': isSubmitting || (!content.trim() && images.filter(img => img.status === 'success').length === 0) }"
        @click="handleSubmit"
      >
        <u-icon name="checkmark" size="44rpx" color="#ffffff" v-if="!isSubmitting"></u-icon>
        <u-loading-icon v-if="isSubmitting" color="#ffffff" size="40rpx"></u-loading-icon>
        <text class="publish-text" v-if="!isSubmitting">发布</text>
      </view>
    </view>

  </view>
</template>

<style lang="scss" scoped>
/* 统一设计变量 */
:root {
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --radius-card: 20rpx;
  --radius-button: 50rpx;
  --color-bg-page: #f8f9fa;
  --color-bg-card: #ffffff;
  --color-text-title: #333333;
  --color-text-body: #666666;
  --color-text-caption: #999999;
  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.post-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-bg-page);
}

/* 发布按钮容器 */
.publish-button-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 1000;
}

/* 发布按钮样式 */
.publish-btn {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;

  .publish-text {
    font-size: 24rpx;
    color: #ffffff;
    margin-top: 8rpx;
    font-weight: 500;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &:active {
    transform: scale(0.95);
  }
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */
  background-color: var(--color-bg-page);
  width: 100%;
  box-sizing: border-box;
}

.textarea-wrapper {
  margin-bottom: var(--spacing-lg);
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);

  :deep(.u-textarea) {
    font-size: 28rpx;
    color: var(--color-text-body);
    line-height: 1.6;

    .u-textarea__field::placeholder {
      color: var(--color-text-caption);
      font-size: 28rpx;
    }
  }
}

.upload-wrapper {
  margin-bottom: var(--spacing-lg);
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);

  :deep(.u-upload__wrap) {
    gap: var(--spacing-md);
  }

  :deep(.u-upload__button) {
    background-color: var(--color-bg-page);
    border: 2rpx dashed #e0e0e0;
    border-radius: var(--radius-card);
    transition: all 0.3s ease;
  }

  :deep(.u-upload__item) {
    border-radius: var(--radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-card);
  }

  .upload-tip {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: #fff3cd;
    border: 2rpx solid #ffeaa7;
    border-radius: var(--radius-card);

    .tip-text {
      font-size: 24rpx;
      color: #856404;
      line-height: 1.4;
    }
  }
}

.options-section {
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-card);
  overflow: hidden;

  .option-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 2rpx solid var(--color-bg-page);
    transition: background-color 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    .option-text {
      margin-left: var(--spacing-md);
      font-size: 28rpx;
      color: var(--color-text-body);

      &.selected {
        color: #6AC086;
        font-weight: 500;
      }
    }

    .tag-input {
      flex: 1;
      margin-left: var(--spacing-md);
      font-size: 28rpx;
      color: var(--color-text-body);
      text-align: right;
      background: transparent;
      border: none;
      outline: none;
      width: 100%;
      max-width: 300rpx;

      &::placeholder {
        color: var(--color-text-caption);
      }
    }

    .privacy-switch {
      margin-left: auto;

      :deep(.u-radio) {
        font-size: 28rpx;

        .u-radio__label {
          color: var(--color-text-body);
        }
      }

      :deep(.u-radio--checked) {
        .u-radio__label {
          color: #6AC086;
          font-weight: 500;
        }
      }

      :deep(.u-radio__icon-wrap--checked) {
        background-color: #6AC086 !important;
        border-color: #6AC086 !important;
      }
    }
  }
}

</style>