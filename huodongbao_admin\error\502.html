<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>502 错误 - phpstudy</title>
  <meta name="keywords" content="">
  <meta name="description" content="">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black"> 
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">
  <meta HTTP-EQUIV="pragma" CONTENT="no-cache"> 
  <meta HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
  <meta HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
  <meta HTTP-EQUIV="expires" CONTENT="0">
  <style>
    body{
      font: 16px arial,'Microsoft Yahei','Hiragino Sans GB',sans-serif;
    }
    h1{
      margin: 0;
      color:#3a87ad;
      font-size: 26px;
    }
    .content{
      width: 45%;
      margin: 0 auto;
     
    }
    .content >div{
      margin-top: 50px;
      padding: 20px;
      background: #d9edf7;  
      border-radius: 12px;
    }
    .content dl{
      color: #2d6a88;
      line-height: 40px;
    } 
    .content div div {
      padding-bottom: 20px;
      text-align:center;
    }
  </style>
</head>
<body>
  <div class="content">
      <div>
           <h1>HTTP 502 - Bad Gateway 没有响应</h1>
        <dl>
          <dt>错误说明：坏的网关，http向后端节点请求，没有响应</dt>
          <dt>原因1：DNS 缓冲</dt>
		  <dd>解决办法：</dd>
          <dd>在dos窗口运行 ipconfig /flushdns，该命令会刷新DNS缓冲。</dd>
          <dt>原因2：浏览器代理</dt>
		  <dd>解决办法：</dd>
          <dd>关掉代理。</dd>
		  <dt>原因3：dns 被劫持了，即使使用国外的dns，也会被劫持</dt>
		  <dd>解决办法：</dd>
          <dd>去掉VPN服务器的DNS。切换另外的dns。在windows系统中，可以在本地网络连接的属性中，去掉默认的dns，选用国外的dns，比如google的或opendns。</dd>
		  <dt>原因4：php执行超时</dt>
		  <dd>解决办法：</dd>
          <dd>修改/usr/local/php/etc/php.ini 将max_execution_time 改为300。</dd>
		  <dt>原因5：nginx等待时间超时</dt>
		  <dd>解决办法：</dd>
          <dd>适当增加nginx.conf配置文件中FastCGI的timeout时间。</dd>
        </dl>
        <div>使用手册，视频教程，BUG反馈，官网地址： <a href="https://www.xp.cn"  target="_blank">www.xp.cn</a> </div>
    
      </div>
    </div> 
</body>
</html>