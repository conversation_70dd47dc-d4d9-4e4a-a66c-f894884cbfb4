/**
 * 安全工具函数
 * 提供XSS防护、输入验证、数据加密等安全功能
 */

import { REGEX_PATTERNS } from '@/constants';

/**
 * XSS过滤器
 * 过滤HTML中的危险标签和属性
 */
export class XSSFilter {
  constructor() {
    // 允许的HTML标签
    this.allowedTags = [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'pre', 'code'
    ];
    
    // 允许的属性
    this.allowedAttributes = {
      '*': ['class', 'style'],
      'a': ['href', 'title', 'target'],
      'img': ['src', 'alt', 'title', 'width', 'height']
    };
    
    // 危险的事件属性
    this.dangerousEvents = [
      'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
      'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset'
    ];
  }

  /**
   * 过滤HTML内容
   * @param {string} html - 原始HTML
   * @returns {string} 过滤后的安全HTML
   */
  filter(html) {
    if (!html || typeof html !== 'string') {
      return '';
    }

    // 移除script标签
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    
    // 移除style标签
    html = html.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');
    
    // 移除危险的事件属性
    this.dangerousEvents.forEach(event => {
      const regex = new RegExp(`\\s*${event}\\s*=\\s*[^>]*`, 'gi');
      html = html.replace(regex, '');
    });
    
    // 移除javascript:协议
    html = html.replace(/javascript:/gi, '');
    
    // 移除data:协议（除了图片）
    html = html.replace(/data:(?!image\/)/gi, '');
    
    // 转义特殊字符
    html = this.escapeHtml(html);
    
    return html;
  }

  /**
   * 转义HTML特殊字符
   * @param {string} str - 原始字符串
   * @returns {string} 转义后的字符串
   */
  escapeHtml(str) {
    const htmlEscapes = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;'
    };
    
    return str.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
  }

  /**
   * 反转义HTML字符
   * @param {string} str - 转义后的字符串
   * @returns {string} 原始字符串
   */
  unescapeHtml(str) {
    const htmlUnescapes = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#x27;': "'",
      '&#x2F;': '/'
    };
    
    return str.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, (match) => htmlUnescapes[match]);
  }
}

/**
 * 输入验证器
 */
export class InputValidator {
  /**
   * 验证手机号
   * @param {string} phone - 手机号
   * @returns {boolean} 是否有效
   */
  static validatePhone(phone) {
    return REGEX_PATTERNS.PHONE.test(phone);
  }

  /**
   * 验证邮箱
   * @param {string} email - 邮箱
   * @returns {boolean} 是否有效
   */
  static validateEmail(email) {
    return REGEX_PATTERNS.EMAIL.test(email);
  }

  /**
   * 验证URL
   * @param {string} url - URL
   * @returns {boolean} 是否有效
   */
  static validateUrl(url) {
    return REGEX_PATTERNS.URL.test(url);
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @returns {object} 验证结果
   */
  static validatePassword(password) {
    const result = {
      valid: false,
      strength: 0,
      messages: []
    };

    if (!password) {
      result.messages.push('密码不能为空');
      return result;
    }

    if (password.length < 6) {
      result.messages.push('密码长度至少6位');
    }

    if (password.length > 20) {
      result.messages.push('密码长度不能超过20位');
    }

    // 检查是否包含数字
    if (/\d/.test(password)) {
      result.strength += 1;
    } else {
      result.messages.push('密码应包含数字');
    }

    // 检查是否包含小写字母
    if (/[a-z]/.test(password)) {
      result.strength += 1;
    } else {
      result.messages.push('密码应包含小写字母');
    }

    // 检查是否包含大写字母
    if (/[A-Z]/.test(password)) {
      result.strength += 1;
    }

    // 检查是否包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.strength += 1;
    }

    result.valid = password.length >= 6 && password.length <= 20 && result.strength >= 2;
    
    return result;
  }

  /**
   * 验证文件类型
   * @param {string} fileName - 文件名
   * @param {Array} allowedTypes - 允许的文件类型
   * @returns {boolean} 是否有效
   */
  static validateFileType(fileName, allowedTypes) {
    if (!fileName || !allowedTypes || !Array.isArray(allowedTypes)) {
      return false;
    }

    const extension = fileName.split('.').pop().toLowerCase();
    return allowedTypes.includes(extension);
  }

  /**
   * 验证文件大小
   * @param {number} fileSize - 文件大小（字节）
   * @param {number} maxSize - 最大允许大小（字节）
   * @returns {boolean} 是否有效
   */
  static validateFileSize(fileSize, maxSize) {
    return fileSize <= maxSize;
  }

  /**
   * 清理用户输入
   * @param {string} input - 用户输入
   * @returns {string} 清理后的输入
   */
  static sanitizeInput(input) {
    if (!input || typeof input !== 'string') {
      return '';
    }

    // 移除前后空格
    input = input.trim();
    
    // 移除多余的空格
    input = input.replace(/\s+/g, ' ');
    
    // 移除控制字符
    input = input.replace(/[\x00-\x1F\x7F]/g, '');
    
    return input;
  }
}

/**
 * 数据加密工具
 */
export class DataEncryption {
  /**
   * 简单的Base64编码（兼容小程序环境）
   * @param {string} str - 原始字符串
   * @returns {string} 编码后的字符串
   */
  static encode(str) {
    try {
      // 检查是否在小程序环境
      if (typeof uni !== 'undefined' && uni.base64ToArrayBuffer) {
        // 使用uni-app的API
        const buffer = uni.base64ToArrayBuffer(str);
        return uni.arrayBufferToBase64(buffer);
      } else if (typeof btoa !== 'undefined') {
        // 使用浏览器API
        return btoa(unescape(encodeURIComponent(str)));
      } else {
        // 简单的Base64实现
        return DataEncryption.simpleBase64Encode(str);
      }
    } catch (error) {
      console.error('编码失败:', error);
      return str;
    }
  }

  /**
   * 简单的Base64解码（兼容小程序环境）
   * @param {string} str - 编码后的字符串
   * @returns {string} 原始字符串
   */
  static decode(str) {
    try {
      // 检查是否在小程序环境
      if (typeof uni !== 'undefined' && uni.arrayBufferToBase64) {
        // 使用uni-app的API
        const buffer = uni.base64ToArrayBuffer(str);
        return uni.arrayBufferToBase64(buffer);
      } else if (typeof atob !== 'undefined') {
        // 使用浏览器API
        return decodeURIComponent(escape(atob(str)));
      } else {
        // 简单的Base64实现
        return DataEncryption.simpleBase64Decode(str);
      }
    } catch (error) {
      console.error('解码失败:', error);
      return str;
    }
  }

  /**
   * 简单的Base64编码实现（用于不支持btoa的环境）
   * @param {string} str - 原始字符串
   * @returns {string} 编码后的字符串
   */
  static simpleBase64Encode(str) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;

    while (i < str.length) {
      const a = str.charCodeAt(i++);
      const b = i < str.length ? str.charCodeAt(i++) : 0;
      const c = i < str.length ? str.charCodeAt(i++) : 0;

      const bitmap = (a << 16) | (b << 8) | c;

      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += i - 2 < str.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      result += i - 1 < str.length ? chars.charAt(bitmap & 63) : '=';
    }

    return result;
  }

  /**
   * 简单的Base64解码实现（用于不支持atob的环境）
   * @param {string} str - 编码后的字符串
   * @returns {string} 原始字符串
   */
  static simpleBase64Decode(str) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;

    str = str.replace(/[^A-Za-z0-9+/]/g, '');

    while (i < str.length) {
      const encoded1 = chars.indexOf(str.charAt(i++));
      const encoded2 = chars.indexOf(str.charAt(i++));
      const encoded3 = chars.indexOf(str.charAt(i++));
      const encoded4 = chars.indexOf(str.charAt(i++));

      const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;

      result += String.fromCharCode((bitmap >> 16) & 255);
      if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
      if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
    }

    return result;
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  static generateRandomString(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 生成UUID
   * @returns {string} UUID
   */
  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

/**
 * CSRF防护
 */
export class CSRFProtection {
  constructor() {
    this.tokenKey = 'csrf_token';
    this.token = this.generateToken();
  }

  /**
   * 生成CSRF令牌
   * @returns {string} CSRF令牌
   */
  generateToken() {
    return DataEncryption.generateRandomString(32);
  }

  /**
   * 获取CSRF令牌
   * @returns {string} CSRF令牌
   */
  getToken() {
    return this.token;
  }

  /**
   * 验证CSRF令牌
   * @param {string} token - 待验证的令牌
   * @returns {boolean} 是否有效
   */
  validateToken(token) {
    return token === this.token;
  }

  /**
   * 刷新CSRF令牌
   */
  refreshToken() {
    this.token = this.generateToken();
  }
}

// 创建全局实例
export const xssFilter = new XSSFilter();
export const csrfProtection = new CSRFProtection();

// 导出便捷函数
export const sanitizeHtml = (html) => xssFilter.filter(html);
export const validateInput = InputValidator;
export const encrypt = DataEncryption;

// 安全配置
export const SECURITY_CONFIG = {
  // 密码策略
  PASSWORD_POLICY: {
    minLength: 6,
    maxLength: 20,
    requireNumbers: true,
    requireLetters: true,
    requireSpecialChars: false
  },
  
  // 文件上传限制
  UPLOAD_LIMITS: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    allowedVideoTypes: ['mp4', 'avi', 'mov'],
    allowedDocTypes: ['pdf', 'doc', 'docx', 'txt']
  },
  
  // 请求限制
  REQUEST_LIMITS: {
    maxRequestsPerMinute: 60,
    maxRequestsPerHour: 1000,
    timeout: 10000
  }
};
