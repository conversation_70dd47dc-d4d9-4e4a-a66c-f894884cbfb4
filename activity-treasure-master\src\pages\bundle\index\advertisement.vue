<script setup>
import { watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app";
import { store } from "@/store";

const form = ref({});

onLoad((e) => {
  form.value = e.url_params;
});
</script>
<template>
  <view class="page"> </view>
</template>

<style scoped lang="less"></style>
