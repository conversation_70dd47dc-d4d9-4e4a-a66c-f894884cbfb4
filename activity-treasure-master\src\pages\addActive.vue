<script setup>
import { watch, ref, reactive } from "vue";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";
import CustomTabBar from "../components/CustomTabBar.vue";

// 会员提示弹窗状态
const showMemberModal = ref(false);

// 检查会员状态
const checkMemberStatus = () => {
  const userInfo = store().$state.userInfo;

  // 使用统一的登录校验，登录成功后返回到当前中转页面
  if (!requireLogin('', '请先登录后再发布活动')) {
    return false;
  }

  // 检查会员状态 - 修复类型比较问题
  const isHuiyuan = userInfo.is_huiyuan;
  if (isHuiyuan !== 1 && isHuiyuan !== '1' && isHuiyuan !== true) {
    showMemberModal.value = true;
    return false;
  }

  return true;
};

// 跳转到会员页面
const navigateToVip = () => {
  showMemberModal.value = false;
  navto('/pages/bundle/user/vip');
};

// 取消操作
const cancelAction = () => {
  showMemberModal.value = false;
  // 返回上一页或首页
  uni.switchTab({
    url: '/pages/index'
  });
};

onLoad(() => {
  // 检查会员状态，通过后才跳转到发布页面
  if (checkMemberStatus()) {
    uni.redirectTo({
      url: "/pages/bundle/index/addActive"
    });
  }
});

onShow(() => {
  // 页面显示时重新检查会员状态（处理登录后返回的情况）
  if (checkMemberStatus()) {
    uni.redirectTo({
      url: "/pages/bundle/index/addActive"
    });
  }
});
</script>
<template>
  <view class="page">
    <!-- 会员提示弹窗 -->
    <u-modal
      :show="showMemberModal"
      title="会员功能"
      :closeOnClickOverlay="false"
      :showCancelButton="true"
      confirmText="成为会员"
      cancelText="取消"
      :confirmColor="'#6AC086'"
      :cancelColor="'#666666'"
      @confirm="navigateToVip"
      @cancel="cancelAction"
    >
      <view class="member-modal-content">
        <text class="member-modal-text">
          本功能为会员使用，你目前不是会员{{ store().$state.userInfo?.is_huiyuan === 0 ? '(或已到期)' : '' }} 是否成为会员？
        </text>
      </view>
    </u-modal>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="2" />
  </view>
</template>

<style scoped lang="less">
.page {
  background: #f8f7f5;
  min-height: 100vh;
  // 为自定义底部导航栏预留空间
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.member-modal-content {
  padding: 20rpx 0;
  text-align: center;
}

.member-modal-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.6;
  display: block;
}
</style>
