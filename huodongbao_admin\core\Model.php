<?php
namespace core;
use core\db;
class Model{

	public $table = "";

	public function __construct(){

	}

	//切换数据库连接
	public function connect($useConfig="",$reConn=false){
		Db::connect($useConfig,$reConn);
		return $this;
	}

	//设置表名称
	public function table($table){
		$this->table = $table;
		return $this;
	}

	//show tables
	static public function showTables(){
		$res = Db::_fetchAll("show tables");
		$tables = [];
		if(empty($res))return $tables;
		foreach($res as $v){
			$tmp = array_values($v);
			$tables[] = $tmp[0];
		}
		return $tables;
	}

	//show columns
	public function showColumns(){
		if(empty($this->table)) return [];
		$res = Db::_fetchAll("show columns from ".$this->table);
		$columns = [];
		if(empty($res))return $columns;
		foreach($res as $v){
			$columns[$v['Field']] = [
				"type"=>$v["Type"],
				"null"=>$v["Null"],
				"key"=>$v["Key"],
				"default"=>$v["Default"],
				"extra"=>$v["Extra"]
			];
		}
		return $columns;
	}

	//show fileds
	public function showFields($field){
		$res = $this->showColumns();
		if(!isset($res[$field]))return null;
		return $res[$field];
	}

	//post提交快速插入
	public function postInsert($allowFields){
		if(empty($this->table) || empty($allowFields) || !is_array($allowFields))return false;
		$data = [];
		$prepareParam = [];
		foreach($allowFields as $v){
			if(!isset($_POST[$v]))return false;
			$data[$v] = ":".$v;
			$prepareParam[":".$v] = $_POST[$v];
		}
		//
		$keys = implode("`,`",array_keys($data));
		$values = implode(',',array_values($data));
		$sql = 'INSERT INTO '.$this->table.' (`'.$keys.'`) VALUES ('.$values.')';//echo $sql."<br />";
		return Db::_exec($sql,$prepareParam);
	}

	//post提交快速更新
	public function postUpdate($primaryKey,$allowFields){
		if(empty($this->table) || empty($primaryKey) || empty($allowFields) || !is_array($allowFields))return false;
		$prepareParam = [];
		$str = "";
		foreach($allowFields as $v){
			if(!isset($_POST[$v]))return false;
			$str .= "{$v}=:{$v},";
			$prepareParam[":".$v] = $_POST[$v];
		}
		$str = substr($str,0,-1);
		$prepareParam[":{$primaryKey}"] = $_POST[$primaryKey];

		$sql = "UPDATE {$this->table} SET {$str} WHERE {$primaryKey}=:{$primaryKey}"; //dump($sql."<br />");dump($prepareParam);
		return Db::_exec($sql,$prepareParam);
	}

	public function __destruct(){

	}

}
