<?php
namespace model;
use core\Db;
class Repair{

	public function __construct(){
		
	}
	
	//批量替换数据库字段内容
	//$data = ["table1"=>[column1,column2..],"table2"=>[column1,column2..]]
	static function columns_replace($data,$search,$replace){
		if(empty($data) || !is_array($data) || empty($search) || empty($replace))return "empty data";
		dbConn();
		$response = [];
		Db::begin();
		try{
			foreach($data as $table=>$columns){
				foreach($columns as $column){
					$sql = "UPDATE `{$table}` SET `{$column}`=REPLACE(`{$column}`,'{$search}','{$replace}') WHERE 1";
					$rowCount = Db::_exec($sql);
					$response[$table."/".$column] = $rowCount;
				}
			}
			Db::commit();
		}catch(\Exception $e){
			Db::rollback();
			return $e->getMessage();
		}
		return $response;
		
	}
	//根据p_uid 修复 p_uid_list,l_node,r_node,high_node
	static function node_tree(){
		dbConn();
		$sql = "UPDATE `user` SET p_uid_list='',l_node=null,r_node=null,high_node=0 WHERE 1";
		Db()->_exec($sql);
		$data = Db()->table("user")->select("uid,p_uid,p_uid_list,l_node,r_node,high_node")->order("uid ASC")->fetchAll();
		foreach($data as $user){
			if($user['p_uid'] == 0){
				$update = [
					"p_uid_list"=>"",
					"l_node"=>1,
					"r_node"=>2,
					"high_node"=>1,
				];
				Db()->table("user")->where("uid={$user['uid']}")->update($update);
			}else{
				$p_user = Db()->table("user")->where("uid={$user['p_uid']}")->select("uid,p_uid_list,l_node,r_node,high_node")->fetch();
				if(empty($p_user)){
					exit("上级信息未找到:".$pid);
				}
				//
				$sql = "UPDATE `user` SET `r_node`=`r_node`+2 WHERE `r_node`>={$p_user['r_node']} ORDER BY r_node DESC";
				Db()->_exec($sql);
				$sql = "UPDATE `user` SET `l_node`=`l_node`+2 WHERE `l_node`>{$p_user['r_node']} ORDER BY l_node DESC";
				Db()->_exec($sql);			
				//
				$update = [
					"p_uid_list"=>rtrim($p_user['p_uid_list'],"|") . "|" .$user['p_uid'] . "|",
					"l_node"=>$p_user['r_node'],
					"r_node"=>$p_user['r_node'] + 1,
					"high_node"=>$p_user['high_node'] + 1,
				];
				Db()->table("user")->where("uid={$user['uid']}")->update($update);
			}
		}
		echo "done\n";
	}
	public function _empty(){
		
	}
	function __destruct(){

	}
}
