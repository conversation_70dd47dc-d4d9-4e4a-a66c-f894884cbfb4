<template>
  <view class="page branch-management">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="分会成员管理"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <!-- 统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <view class="stats-number">{{ totalMembers }}</view>
          <view class="stats-label">总成员数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ activeMembers }}</view>
          <view class="stats-label">活跃成员</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ newMembers }}</view>
          <view class="stats-label">本月新增</view>
        </view>
      </view>
      
      <!-- 搜索栏 -->
      <view class="search-container">
        <u-search
          v-model="searchKeyword"
          placeholder="搜索成员昵称或手机号"
          :show-action="false"
          bg-color="#f5f5f5"
          border-color="transparent"
          @search="handleSearch"
          @custom="handleSearch"
        ></u-search>
      </view>
      
      <!-- 筛选器 -->
      <view class="filter-container">
        <u-button
          :type="filterType === 'all' ? 'primary' : 'info'"
          size="small"
          @click="setFilter('all')"
          customStyle="border-radius: 30rpx; margin-right: 20rpx;"
          :customTextStyle="filterType === 'all' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          全部成员
        </u-button>
        <u-button
          :type="filterType === 'active' ? 'primary' : 'info'"
          size="small"
          @click="setFilter('active')"
          customStyle="border-radius: 30rpx; margin-right: 20rpx;"
          :customTextStyle="filterType === 'active' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          活跃成员
        </u-button>
        <u-button
          :type="filterType === 'new' ? 'primary' : 'info'"
          size="small"
          @click="setFilter('new')"
          customStyle="border-radius: 30rpx;"
          :customTextStyle="filterType === 'new' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          新成员
        </u-button>
      </view>
      
      <!-- 成员列表 -->
      <view class="member-list" v-if="memberList.length > 0">
        <view 
          class="member-item"
          v-for="member in memberList"
          :key="member.uid"
          @click="viewMemberDetail(member)"
        >
          <view class="member-avatar">
            <u-avatar
              :src="member.avatar || '/static/default-avatar.png'"
              size="80"
              shape="circle"
            ></u-avatar>
            <view class="member-status" v-if="member.is_active">
              <u-icon name="checkmark-circle-fill" color="#6AC086" size="20"></u-icon>
            </view>
          </view>
          
          <view class="member-info">
            <view class="member-name-container">
              <text class="member-name">{{ member.nickname || '未设置昵称' }}</text>
              <!-- 🆕 新增：系统分配用户标识 -->
              <text class="system-assigned-badge" v-if="isSystemAssignedUser(member)">系统分配用户</text>
            </view>
            <view class="member-phone">{{ formatPhone(member.mobile) }}</view>
            <view class="member-meta">
              <text class="join-time">{{ formatJoinTime(member.time) }}</text>
              <text class="activity-count">参与{{ member.activity_count || 0 }}次活动</text>
            </view>
          </view>
          
          <view class="member-actions">
            <u-icon name="arrow-right" color="#999" size="24"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <u-empty
          mode="list"
          text="暂无成员数据"
          textColor="#999999"
          textSize="28"
        ></u-empty>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && memberList.length > 0">
        <u-loadmore 
          :status="loadStatus"
          @loadmore="loadMore"
        ></u-loadmore>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { store } from '@/store';
import { branch_presidentget_members } from '@/api';

// 响应式数据
const loading = ref(false);
const memberList = ref([]);
const searchKeyword = ref('');
const filterType = ref('all');
const currentPage = ref(1);
const hasMore = ref(true);
const loadStatus = ref('loadmore');
const pageSize = 20;

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  new: 0
});

// 计算属性
const totalMembers = computed(() => stats.total);
const activeMembers = computed(() => stats.active);
const newMembers = computed(() => stats.new);

// 页面加载
onLoad(() => {
  loadMembers(true);
});

// 下拉刷新
onPullDownRefresh(() => {
  loadMembers(true);
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});

// 上拉加载更多
onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    loadMore();
  }
});

// 加载成员列表
const loadMembers = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1;
      hasMore.value = true;
      loading.value = true;
      loadStatus.value = 'loading';
    } else if (!hasMore.value) {
      return;
    }
    
    const userInfo = store().$state.userInfo;
    const res = await branch_presidentget_members({
      uid: userInfo.uid,
      token: userInfo.token,
      page: currentPage.value,
      page_size: pageSize,
      keyword: searchKeyword.value,
      filter: filterType.value
    });
    
    if (res.status === 'ok') {
      const newMembers = res.data || [];
      
      if (isRefresh) {
        memberList.value = newMembers;
      } else {
        memberList.value.push(...newMembers);
      }
      
      // 更新统计数据
      if (res.stats) {
        stats.total = res.stats.total || 0;
        stats.active = res.stats.active || 0;
        stats.new = res.stats.new || 0;
      }
      
      // 判断是否还有更多数据
      hasMore.value = newMembers.length === pageSize;
      currentPage.value++;
      
      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
      
    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: res.msg || '加载失败',
        icon: 'none'
      });
      loadStatus.value = 'loadmore';
    }
  } catch (error) {
    console.error('加载成员列表失败:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  loadMembers(true);
};

// 设置筛选器
const setFilter = (type) => {
  filterType.value = type;
  loadMembers(true);
};

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadStatus.value = 'loading';
    loadMembers(false);
  }
};

// 查看成员详情
const viewMemberDetail = (member) => {
  uni.showModal({
    title: '成员详情',
    content: `昵称：${member.nickname}\n手机：${formatPhone(member.mobile)}\n加入时间：${formatJoinTime(member.time)}\n参与活动：${member.activity_count || 0}次`,
    showCancel: false,
    confirmText: '知道了'
  });
};

// 🆕 新增：判断是否为系统分配用户
const isSystemAssignedUser = (member) => {
  return member.assignment_type === 'system';
};

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return '未绑定';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 格式化加入时间
const formatJoinTime = (timestamp) => {
  if (!timestamp) return '未知';
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return '今天加入';
  if (days === 1) return '昨天加入';
  if (days < 30) return `${days}天前加入`;
  if (days < 365) return `${Math.floor(days / 30)}个月前加入`;
  return `${Math.floor(days / 365)}年前加入`;
};
</script>

<style lang="scss" scoped>
@import '@/style/wcag-colors.scss';
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.stats-card {
  display: flex;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(106, 192, 134, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
  
  .stats-number {
    font-size: 48rpx;
    font-weight: bold;
    color: #6AC086;
    margin-bottom: 10rpx;
  }
  
  .stats-label {
    font-size: 24rpx;
    color: #666666; /* 提高对比度，从#999改为#666666 */
  }
}

.search-container {
  margin-bottom: 30rpx;
}

.filter-container {
  display: flex;
  margin-bottom: 30rpx;
}

.member-list {
  .member-item {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    
    .member-avatar {
      position: relative;
      margin-right: 30rpx;
      
      .member-status {
        position: absolute;
        bottom: -5rpx;
        right: -5rpx;
        background: #ffffff;
        border-radius: 50%;
        padding: 2rpx;
      }
    }
    
    .member-info {
      flex: 1;
      
      .member-name-container {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
      }

      .member-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #1a1a1a; /* 提高对比度 */
        margin-right: 16rpx;
      }

      // 🆕 新增：系统分配用户标识样式
      .system-assigned-badge {
        background: linear-gradient(135deg, #FF9800, #FF5722);
        color: white;
        font-size: 20rpx;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        font-weight: bold;
        position: relative;

        &::before {
          content: '🤖';
          margin-right: 4rpx;
        }
      }

      .member-phone {
        font-size: 28rpx;
        color: #4a4a4a; /* 提高对比度，从#666改为#4a4a4a */
        margin-bottom: 10rpx;
      }

      .member-meta {
        display: flex;
        font-size: 24rpx;
        color: #666666; /* 提高对比度，从#999改为#666666 */
        
        .join-time {
          margin-right: 30rpx;
        }
      }
    }
    
    .member-actions {
      padding: 10rpx;
    }
  }
}

.empty-state {
  padding: 100rpx 0;
}

.load-more {
  padding: 30rpx 0;
}
</style>
