<?php
namespace model;
use core\Db;
//阿里云文字识别OCR
class Aliocr{
	
	static private $AccessKeyId = "";
	static private $AccessKeySecret = "";
	static private $endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
	static private $commonParams = [
		"Action"=>"",
		"Version"=>"2021-07-07",
		"Format"=>"JSON",
		"AccessKeyId"=>"",
		"SignatureNonce"=>"",
		"Timestamp"=>"",
		"SignatureMethod"=>"HMAC-SHA1",
		"SignatureVersion"=>"1.0",
		"Signature"=>"",
	];
	
	public function __construct(){
		
	}
	
	//身份证识别
	static public function recognizeIdcard ($img_url,$OutputFigure="false",$OutputQualityInfo="false"){

		$data = [
			"Url"=>$img_url,//图片链接（长度不超2048，不支持base64）
			"OutputFigure"=>$OutputFigure,//是否需要图案检测功能，默认不需要 true：需要；false：不需要 boolean
			"OutputQualityInfo"=>$OutputQualityInfo,//是否需要身份证质量检测功能，默认不需要 身份证质量检测功能包含：是否翻拍，是否是复印件，完整度评分，整体质量分数、篡改指数 boolean
		];
		(self::$commonParams)['Action'] = "RecognizeIdcard";
		self::getRequestSign($data,"GET");
		$url = "https://" . self::$endpoint . "/?" . http_build_query(array_merge($data,self::$commonParams));
		$res = curl($url);
		$result = json_decode($res['body'],true);
		//dump($res);exit;
		$data = isset($result['Data']) ? json_decode(stripslashes($result['Data']),true) : [];
		if(isset($data['data']['face']['data'])){
			return ["status"=>"ok","data"=>$data['data']['face']['data']];
		}else if(isset($data['data']['back']['data'])){
			return ["status"=>"ok","data"=>$data['data']['back']['data']];
		}
		$msg = isset($result['Code']) ? $result['Code'] : "操作失败";
		$msg .= isset($result['Message']) ? ":【".$result['Message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//获取签名
    static public  function getRequestSign($params,$method="GET"){
		//
		$SignatureNonce = makeCode(16,true);
		$Timestamp = date("Y-m-d\TH:i:s\Z",time()-8*60*60);
		(self::$commonParams)['SignatureNonce'] = $SignatureNonce;
		(self::$commonParams)['Timestamp'] = $Timestamp;
		(self::$commonParams)['AccessKeyId'] = self::$AccessKeyId;
		//
		$params = array_merge($params,self::$commonParams);
        ksort($params);
		$sign_str = "";
        foreach ($params as $k => $v) {
			if($k == "Signature"){
				continue;
			}else{
				$sign_str .= $k . "=" . urlencode($v) . "&";
			}
        }
		$sign_str = substr($sign_str,0,-1);
		$sign_str = urlencode($sign_str);
		$sign_str = $method . "&" . urlencode("/") . "&" . $sign_str;
		$key = self::$AccessKeySecret . "&";
		$Signature = base64_encode(hash_hmac('sha1',$sign_str,$key,true));
		(self::$commonParams)['Signature'] = $Signature;
        return $Signature;
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
