<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 管理员 
*/
class Root extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 查看
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['group_id']) && check($_REQUEST['group_id'],"intgt0")){
			$group_id = (int)$_REQUEST['group_id'];
			$where .= " AND group_id={$group_id}";
			$pageParams['group_id'] = $_REQUEST['group_id'];
			assign("group_id",$group_id);
		}
		if(!empty($_REQUEST['r_name'])){
			$where .= " AND r_name LIKE :r_name";
			$prepareParam[":r_name"]="%".$_REQUEST['r_name']."%";
			$pageParams['r_name'] = $_REQUEST['r_name'];
		}
		
		$data = Db()->table("root")->where($where)->prepareParam($prepareParam)->order("rid")->page($page,$page_size,$pageParams);
		//
		$path = BASE_PATH . "session";
		if(!is_dir($path))@mkdir($path, 0777, true);
		$arr = scandir($path);
		$online = [];
		foreach($arr as $v){
			if($v != "." && $v != ".."){
				$session = file_get_contents($path . "/" .$v);
				$decode = unserialize($session);
				$dateline = strtotime($decode['deadline']);
				if($dateline > _NOW_){
					if(isset($decode['data']['root_info']['rid'])){
						$row = ["deadline"=>$decode['deadline'],"file"=>$v];
						if(isset($decode['data']['root_info']['login_ip'])){
							$row['ip'] = $decode['data']['root_info']['login_ip'];
						}
						$online[$decode['data']['root_info']['rid']][] = $row;
					}
				}
			}
		}
		//
		if(!empty($data)){
			foreach($data as $k=>$v){
				$group = Db()->table("root_group")->where("id={$v['group_id']}")->fetch();
				$data[$k]['group_name'] = isset($group['group_name']) ? $group["group_name"] : "未知组";
				//
				$data[$k]['online'] = isset($online[$v['rid']]) ? $online[$v['rid']] : [];
				//
				if(!empty($v['google_auth_key'])){
					$data[$k]["google_auth_code"] = \lib\GoogleAuthenticator::getCode($v['google_auth_key']);
				}
				if(!isset($data[$k]["google_auth_code"]) || empty($data[$k]["google_auth_code"])){
					$data[$k]["google_auth_code"] = "";
				}
				//
			}
		}
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		$root_group = Db()->select("id,group_name")->table("root_group")->fetchAll();
		$root_group = array_column($root_group,"group_name","id");
		assign("root_group",$root_group);
		//
		$root_arr = Db()->table("root")->select("rid,r_name")->fetchAll();
		$root_arr = array_column($root_arr,"r_name","rid");
		assign("root_arr",$root_arr);
		return view(['header','root/index','footer']);
	}
	
	/*
	* @name 添加
	* @method add
	*/	
	public function add(){
		responseType("json");
		if(
			empty($_POST['r_name']) || 
			empty($_POST['password']) || 
			empty($_POST['group_id']) || 
			!check($_POST['group_id'],"intgt0")
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$r_name = htmlspecialchars(trim($_POST['r_name']));
		$password = trim($_POST['password']);
		$group_id = intval($_POST['group_id']);
		
		dbConn();
		$check = Db()->table("root")->where("r_name=:r_name")->prepareParam([":r_name"=>$r_name])->fetch();
		if(!empty($check)){
			return ["status"=>"error","msg"=>"该管理员名称已存在"];
		}
		$data = [
			"group_id"=>$group_id,
			"password"=>":password",
			"r_name"=>":r_name",
		];
		$prepareParam = [
			":password"=>md5(config("sys.root_salt").$password),
			':r_name'=>$r_name,
		];
		try{
			$res = Db()->table("root")->prepareParam($prepareParam)->insert($data);
			if($res){
				$rid = Db()->insertId();
				$this->root_log("添加管理员,rid:[ {$rid} ]");
				return ["status"=>"ok","msg"=>"添加成功"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	/*
	* @name 发送管理员消息
	* @method msg
	*/	
	public function msg($to_rid,$msg){
		responseType("json");
		if(
			!check($to_rid,"intgt0") || 
			empty($msg)
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$msg = htmlspecialchars(trim($msg));
		$to_rid = intval($to_rid);
		
		dbConn();
		$check = Db()->table("root")->where("rid={$to_rid}")->fetch();
		if(empty($check)){
			return ["status"=>"error","msg"=>"该管理员不存在"];
		}
		$data = [
			"rid"=>$_SESSION['root_info']['rid'],
			"to_rid"=>$to_rid,
			"msg"=>":msg",
		];
		$prepareParam = [
			':msg'=>$msg,
		];
		try{
			$res = Db()->table("root_msg")->prepareParam($prepareParam)->insert($data);
			if($res){
				$id = Db()->insertId();
				return ["status"=>"ok","msg"=>"发送成功"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	/*
	* @name 下线
	* @method offline
	*/	
	public function offline(){
		responseType("json");
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids = htmlspecialchars(trim($_POST['ids']));
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}				
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);		
		//
		$rowCount = 0;
		$path = BASE_PATH . "session";
		if(!is_dir($path))@mkdir($path, 0777, true);
		$arr = scandir($path);
		foreach($arr as $v){
			if($v != "." && $v != ".."){
				$session = file_get_contents($path . "/" .$v);
				$decode = unserialize($session);
				if(isset($decode['data']['root_info']['rid'])){
					$rid = $decode['data']['root_info']['rid'];
					$r_name = $decode['data']['root_info']['r_name'];
					if(in_array($rid,$ids_arr) && $r_name != "root"){
						$res = unlink($path . "/" .$v);
						if($res){
							$rowCount ++;
						}
					}
				}
			}
		}
		//
		return ["status"=>"ok","msg"=>"操作成功，影响行数【{$rowCount}】"];
	}
	/*
	* @name 删除
	* @method del
	*/	
	public function del(){
		responseType("json");
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids = htmlspecialchars(trim($_POST['ids']));
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}				
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);		
		//
		$path = BASE_PATH . "session";
		if(!is_dir($path))@mkdir($path, 0777, true);
		$arr = scandir($path);
		foreach($arr as $v){
			if($v != "." && $v != ".."){
				$session = file_get_contents($path . "/" .$v);
				$decode = unserialize($session);
				if(isset($decode['data']['root_info']['rid'])){
					$rid = $decode['data']['root_info']['rid'];
					$r_name = $decode['data']['root_info']['r_name'];
					if(in_array($rid,$ids_arr) && $r_name != "root"){
						@unlink($path . "/" .$v);
					}
				}
			}
		}
		//
		dbConn();
		$rowCount = Db()->table("root")->where("rid IN ({$ids}) AND r_name!='root'")->del();
		if($rowCount){
			$this->root_log("删除管理员,id:{$ids}");
			return ["status"=>"ok","msg"=>"删除行数[ {$rowCount} ]"];
		}
		return ["status"=>"error","msg"=>"删除行数为零"];
	}
	/*
	* @name 更新密码
	* @method update_password
	*/	
	public function update_password(){
		if(
			!empty($_POST['old_password']) && 
			!empty($_POST['new_password'])
		){
			if($_SESSION['root_info']['password'] == md5(config("sys.root_salt").$_POST['old_password'])){
				dbConn();
				$new_password = md5(config("sys.root_salt").trim($_POST['new_password']));
				$res = Db()->table("root")->where("rid={$_SESSION['root_info']['rid']}")->prepareParam([':password'=>$new_password])->update(['password'=>':password']);
				if($res){
					$this->root_log("修改密码");
					$_SESSION['root_info']['password'] = $new_password;
					assign("alertTpl",true);
				}else{
					assign("alertTpl",false);
				}
			}else{
				assign("alertTpl",false);
				assign("alertMsg","原始密码错误");
			}
		}
		//
		if(!empty($_SESSION['root_info']['google_auth_key'])){
			$google_auth_qrcode_url = \lib\GoogleAuthenticator::getQRCodeGoogleUrl($_SERVER["HTTP_HOST"],$_SESSION['root_info']['google_auth_key']);
		}else{
			$google_auth_qrcode_url = "";
		}
		assign("google_auth_qrcode_url",$google_auth_qrcode_url);
		//
		return view(["header","root/update_password","footer"]);
	}
	/*
	* @name 更新谷歌验证密钥
	* @method reset_google_auth_key
	*/	
	public function reset_google_auth_key(){
		responseType("json");
		try{
			$google_auth_key = \lib\GoogleAuthenticator::createSecret(32);
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		if(empty($google_auth_key))return ["status"=>"error","msg"=>"生成密钥失败"];
		dbConn();
		$res = Db()->table("root")->where("rid={$_SESSION['root_info']['rid']}")->prepareParam([':google_auth_key'=>$google_auth_key])->update(['google_auth_key'=>':google_auth_key']);
		if($res){
			$this->root_log("设置/重置谷歌验证码密钥");
			$_SESSION['root_info']['google_auth_key'] = $google_auth_key;
		}else{
			return ["status"=>"error","msg"=>"更新密钥失败"];
		}
		//
		return ["status"=>"ok","msg"=>"操作成功，即将刷新..."];
	}
	/*
	* @name 用户组查看
	* @method group_index
	*/	
	public function group_index(){
		dbConn();
		$data = Db()->table("root_group")->fetchAll();
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		return view(["header","root/group_index","footer"]);
	}
	/*
	* @name 用户组添加
	* @method group_add
	*/	
	public function group_add(){
		dbConn();
		$res = Db()->table("root_role")->fetchAll();
		$data = [];
		foreach($res as $v){
			$data[$v['module_name']][] = $v;
		}
		if(
			!empty($_POST['group_name']) && 
			!empty($_POST['privileges'])
		){
			$privileges = "|".implode("|",$_POST['privileges'])."|";
			$group_name = trim($_POST['group_name']);
			$data = [
				'privileges'=>":privileges",
				'group_name'=>':group_name',
			];
			try{
				$res = Db()->table("root_group")
					->prepareParam([':privileges'=>$privileges,':group_name'=>$group_name])
					->insert($data);
				if($res){
					$group_id = Db()->insertId();
					$this->root_log("添加用户组,id:[ {$group_id} ],名称:[{$group_name}]");
					header("location:".url("root/group_index/alert/{$group_id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		assign("data",$data);
		return view(['header','root/group_add','footer']);
	}
	/*
	* @name 用户组删除
	* @method group_del
	*/	
	public function group_del(){
		responseType("json");
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids = htmlspecialchars(trim($_POST['ids']));
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}				
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);		
		//
		dbConn();
		$rowCount = Db()->table("root_group")->where("id IN ({$ids}) AND group_name!='超级管理员'")->del();
		if($rowCount){
			$this->root_log("删除用户组,id:{$ids}");
			return ["status"=>"ok","msg"=>"删除行数[ {$rowCount} ]"];
		}
		return ["status"=>"error","msg"=>"删除行数为零"];
		
	}
	/*
	* @name 用户组更新
	* @method group_update
	*/	
	public function group_update(){
		if(!isset($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0"))header("location:".url("root/group_index"));
		$id = (int)$_REQUEST['id'];
		dbConn();
		$res = Db()->table("root_role")->fetchAll();
		$data = [];
		foreach($res as $v){
			$data[$v['module_name']][] = $v;
		}
		if(
			!empty($_POST['group_name']) && 
			!empty($_POST['privileges'])
		){
			$privileges = "|".implode("|",$_POST['privileges'])."|";
			$group_name = trim($_POST['group_name']);
			$date_update = [
				'privileges'=>":privileges",
				'group_name'=>':group_name',
			];
			try{
				$res = Db()->table("root_group")
					->prepareParam([':privileges'=>$privileges,':group_name'=>$group_name])
					->where("id={$id}")
					->update($date_update);
				if($res){
					$this->root_log("更新用户组【{$id}】");
					assign("alertTpl",true);
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$info = Db()->table("root_group")->where("id={$id}")->fetch();
		if(empty($info)){
			header("location:".url("root/group_index"));
			exit;
		}
		$info['privileges_arr'] = explode("|",$info['privileges']);
		assign("data",$data);
		assign("info",$info);	
		return view(['header','root/group_update','footer']);
	}
	/*
	* @name 日志
	* @method log
	*/	
	public function log(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['rid']) && check($_REQUEST['rid'],"intgt0")){
			$rid = (int)$_REQUEST['rid'];
			$where .= " AND r_id={$rid}";
			$pageParams['rid'] = $_REQUEST['rid'];
			assign("rid",$rid);
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND msg LIKE :keyword";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
			assign("keyword",$_REQUEST['keyword']);
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("root_log")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		$root_arr = Db()->table("root")->select("rid,r_name")->fetchAll();
		$root_arr = array_column($root_arr,"r_name","rid");
		assign("root_arr",$root_arr);	
		return view(['header','root/log','footer'],$data);		
	}
	/*
	* @name 刷新权限
	* @method flush_role
	*/		
	public function flush_role(){
		if(!empty($_POST['ajax'])){
			responseType("json");
			dbConn();
			try{
				$sql = "TRUNCATE `root_role`";
				$rowCount = Db::_exec($sql);
				$this->root_log("清空权限表");
				return ["status"=>"ok","msg"=>"操作成功"];
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				return ["status"=>"error","msg"=>$e->getMessage()];
			}
		}
		return view(['header','root/flush_role','footer']);
	}
	/*
	* @name IP查看
	* @method ip_index
	*/		
	public function ip_index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['ip'])){
			$where .= " AND ip LIKE :ip";
			$prepareParam[":ip"] = "%".$_REQUEST['ip']."%";
			$pageParams['ip'] = $_REQUEST['ip'];
		}
		if(!empty($_REQUEST['msg'])){
			$where .= " AND msg LIKE :msg";
			$prepareParam[":msg"]="%".$_REQUEST['msg']."%";
			$pageParams['msg'] = $_REQUEST['msg'];
		}		
		$data = Db()->table("root_ip")->where($where)->prepareParam($prepareParam)->order("id")->page($page,$page_size,$pageParams);
		//
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		//
		$allow_token = Db()->table("config")->where("`name`='root_ip_token'")->getColumn("val");
		assign("allow_token",$allow_token);
		//
		return view(['header','root/ip_index','footer']);
	}
	
	/*
	* @name IP添加
	* @method ip_add
	*/	
	public function ip_add(){
		responseType("json");
		
		if(
			empty($_POST['ip']) ||
			empty($_POST['msg']) ||
			!check($_POST['ip'],"ip")
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$ip = htmlspecialchars(trim($_POST['ip']));
		$msg = htmlspecialchars(trim($_POST['msg']));
		dbConn();
		//
		$check = Db()->table("root_ip")->where("ip=:ip")->prepareParam([":ip"=>$ip])->fetch();
		if(!empty($check)){
			return ["status"=>"error","msg"=>"IP已存在"];
		}
		//
		$data = [
			"ip"=>":ip",
			"msg"=>":msg",
		];
		$prepareParam = [
			':ip'=>$ip,
			':msg'=>$msg,
		];
		try{
			$res = Db()->table("root_ip")->prepareParam($prepareParam)->insert($data);
			if($res){
				$id = Db()->insertId();
				$this->root_log("添加允许管理员操作IP,id:[ {$id} ]");
				return ["status"=>"ok","msg"=>"添加成功"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	/*
	* @name IP删除
	* @method ip_del
	*/	
	public function ip_del(){
		//
		responseType("json");
		dbConn();
		if(!empty($_POST['ids'])){
			$id_arr = explode(",",$_POST['ids']);
			$ids = [];
			foreach($id_arr as $id){
				if(!check($id,"intgt0")){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$ids[] = $id;
			}
			if(empty($ids))return ["status"=>"error","msg"=>"参数错误"];
			$ids_str = implode(",",$ids);
			$rowCount = Db()->table("root_ip")->where("id IN ({$ids_str})")->del();
			if($rowCount){
				$this->root_log("删除允许管理员操作IP,id:{$ids_str}");
				return ["status"=>"ok","msg"=>"删除行数【{$rowCount}】"];
			}
			return ["status"=>"error","msg"=>"删除行数为零"];
		}else if(!empty($_POST['days']) && check($_POST['days'],"intgt0")){
			responseType("json");
			$days = (int)$_POST['days'];
			$start_time = date("Y-m-d H:i:s",strtotime("-{$days} days"));
			$rowCount = Db()->table("root_ip")->where("`last_use_time`<'{$start_time}' OR (`time`<'{$start_time}' AND `last_use_time` IS NULL)")->del();
			if($rowCount){
				$this->root_log("删除允许管理员操作IP【{$days}天内未使用】");
				return ["status"=>"ok","msg"=>"删除行数【{$rowCount}】"];
			}
			return ["status"=>"error","msg"=>"删除行数为零"];
		}
	}
	/*
	* @name 重置便携管理链接
	* @method resettoken
	*/	
	public function resettoken(){
		responseType("json");
		dbConn();
		$code = makeCode(64,true);
		$rowCount = Db()->table("config")->where("`name`='root_ip_token'")->update(["val"=>$code]);
		if($rowCount){
			$this->root_log("修改系统配置,name:[ root_ip_token ],值:[ {$code} ]");
			return ["status"=>"ok","msg"=>"重置成功"];
		}
		return ["status"=>"error","msg"=>"重置失败"];
		
	}	
	public function _empty(){
		echo "_empty";
	}
	
	function __destruct(){

	}
}
