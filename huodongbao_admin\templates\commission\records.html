<?php
// 检测当前访问的方法
$current_action = $_SERVER['REQUEST_URI'];
$is_operation_records = strpos($current_action, 'operation_records') !== false;
?>

<fieldset class="table-search-fieldset">
    <legend><?php echo $is_operation_records ? '运营佣金记录查询' : '佣金记录查询'; ?></legend>
    <div style="margin: 10px 10px 10px 10px">
        <form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="post" id="search">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><?php echo $is_operation_records ? '分会长' : '用户'; ?></label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_name" value="<?php if(!empty($_REQUEST['user_name']))echo $_REQUEST['user_name']; ?>" placeholder="<?php echo $is_operation_records ? '请输入分会长姓名' : '请输入用户姓名'; ?>" class="layui-input">
                    </div>
                </div>

                <?php if($is_operation_records): ?>
                <div class="layui-inline">
                    <label class="layui-form-label">月份筛选</label>
                    <div class="layui-input-inline">
                        <input type="text" name="month" value="<?php if(!empty($_REQUEST['month']))echo $_REQUEST['month']; ?>" placeholder="格式：2024-01" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">分会</label>
                    <div class="layui-input-inline">
                        <select name="branch_id" lay-verify="">
                            <option value="">请选择分会</option>
                            <?php if(!empty($branches)): ?>
                                <?php foreach($branches as $branch): ?>
                                    <option value="<?php echo $branch['branch_id']; ?>" <?php if(!empty($_REQUEST['branch_id']) && $_REQUEST['branch_id'] == $branch['branch_id']) echo 'selected'; ?>>
                                        <?php echo htmlspecialchars($branch['branch_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
                <?php else: ?>
                <div class="layui-inline">
                    <label class="layui-form-label">佣金类型</label>
                    <div class="layui-input-inline">
                        <select name="commission_type" lay-verify="">
                            <option value="">全部类型</option>
                            <option value="invite" <?php if(!empty($_REQUEST['commission_type']) && $_REQUEST['commission_type'] == 'invite') echo 'selected'; ?>>销售佣金</option>
                            <option value="operation" <?php if(!empty($_REQUEST['commission_type']) && $_REQUEST['commission_type'] == 'operation') echo 'selected'; ?>>运营佣金</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">关联月份</label>
                    <div class="layui-input-inline">
                        <input type="text" name="month" value="<?php if(!empty($_REQUEST['month']))echo $_REQUEST['month']; ?>" placeholder="格式：2024-01" class="layui-input">
                    </div>
                </div>
                <?php endif; ?>

                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</fieldset>

<div class="layui-form">
    <table class="layui-table">
        <colgroup>
            <col width="80">
            <col width="120">
            <col width="120">
            <col width="100">
            <col width="100">
            <col width="100">
            <col width="150">
            <col width="200">
        </colgroup>
        <thead>
            <tr>
                <th>ID</th>
                <th><?php echo $is_operation_records ? '分会长' : '用户信息'; ?></th>
                <?php if($is_operation_records): ?>
                <th>分会名称</th>
                <th>关联月份</th>
                <th>审核活动数</th>
                <?php else: ?>
                <th>佣金类型</th>
                <th>订单编号</th>
                <th>关联月份</th>
                <?php endif; ?>
                <th>佣金金额</th>
                <?php if($support_status): ?>
                <th>佣金状态</th>
                <th>可提取时间</th>
                <?php endif; ?>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php if(!empty($data)){ ?>
                <?php foreach($data as $k=>$v){ ?>
                <tr>
                    <td><?php echo $v['id']; ?></td>
                    <td>
                        <?php echo $v['uid']; ?>【<?php echo htmlspecialchars($v['user_nickname']); ?>】
                        <?php if(!empty($v['mobile'])): ?>
                            <br><small><?php echo $v['mobile']; ?></small>
                        <?php endif; ?>
                    </td>

                    <?php if($is_operation_records): ?>
                    <td><?php echo htmlspecialchars($v['branch_name']); ?></td>
                    <td><?php echo $v['related_month']; ?></td>
                    <td><?php echo $v['activity_count']; ?></td>
                    <?php else: ?>
                    <td>
                        <?php
                        $commission_type_text = '';
                        if($v['commission_type'] == 'operation') {
                            $commission_type_text = '<span class="layui-badge layui-bg-blue">运营佣金</span>';
                        } else {
                            $commission_type_text = '<span class="layui-badge layui-bg-green">销售佣金</span>';
                        }
                        echo $commission_type_text;
                        ?>
                    </td>
                    <td><?php echo $v['order_id'] ?? '-'; ?></td>
                    <td><?php echo $v['related_month'] ?? '-'; ?></td>
                    <?php endif; ?>

                    <td class="layui-text-em">￥<?php echo number_format($v['money'], 2); ?></td>

                    <?php if($support_status): ?>
                    <td>
                        <?php if(isset($v['status_text'])): ?>
                            <?php
                            $status_colors = [
                                0 => 'layui-bg-orange',
                                1 => 'layui-bg-green',
                                2 => 'layui-bg-blue',
                                3 => 'layui-bg-gray',
                                4 => 'layui-bg-red',
                                5 => 'layui-bg-black'
                            ];
                            $color_class = $status_colors[$v['status']] ?? 'layui-bg-gray';
                            ?>
                            <span class="layui-badge <?php echo $color_class; ?>"><?php echo $v['status_text']; ?></span>
                        <?php else: ?>
                            <span class="layui-badge layui-bg-green">可提取</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $v['available_time_formatted'] ?? '-'; ?></td>
                    <?php endif; ?>

                    <td><?php echo $v['create_time']; ?></td>
                    <td class="layui-table-col-special">
                        <div class="layui-table-cell">
                            <a onclick="viewDetail(<?php echo $v['id']; ?>);" class="layui-btn layui-btn-normal layui-btn-xs">查看详情</a>
                            <?php if($support_status): ?>
                            <a href="/commission/status_management" class="layui-btn layui-btn-primary layui-btn-xs">状态管理</a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php } ?>
            <?php }else{ ?>
                <tr>
                    <td colspan="<?php echo $is_operation_records ? ($support_status ? '9' : '7') : ($support_status ? '10' : '8'); ?>" style="text-align:center;">暂无数据</td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
    <?php echo pageRender(); ?>
</div>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 搜索表单提交
    form.on('submit(search)', function(data){
        var params = data.field;
        var url = "<?php echo $is_operation_records ? url('commission/operation_records') : url('commission/records'); ?>";
        var query = [];
        for(var key in params){
            if(params[key] && params[key] !== ''){
                query.push(key + '=' + encodeURIComponent(params[key]));
            }
        }
        if(query.length > 0){
            url += '?' + query.join('&');
        }
        window.location.href = url;
        return false;
    });
});

// 查看详情
function viewDetail(id) {
    layer.open({
        type: 2,
        title: '佣金详情',
        shadeClose: true,
        shade: 0.8,
        area: ['80%', '80%'],
        content: "<?php echo url('commission/record_detail'); ?>?id=" + id
    });
}
</script>
