<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
const form = ref({
  id: null,
  page: 1,
  page_size: 10,
});
const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  // apiGoods(mescroll.num, mescroll.size)
  //   .then((res) => {
  //     const curPageData = res.list || [];
  //     if (mescroll.num == 1) goods.value = [];
  //     goods.value = goods.value.concat(curPageData);
  //     mescroll.endBySize(curPageData.length, res.count);
  //   })
  //   .catch(() => {
  //     mescroll.endErr();
  //   });
};
</script>
<template>
  <view class="page">
    <mescroll-uni
      class="list"
      :height="height"
      :up="{
        page: {
          num: 0,
          size: 20,
          time: null,
        },
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <view class="p30 df aic borderBottom" v-for="(val, i) in goods" :key="i">
        <view class="df aic f1" @click="navto(`/pages/bundle/msg/personage?to_uid=${val.uid || val.to_uid}`)">
          <u-avatar size="68rpx" :src="val.goodImg" mode="aspectFill"></u-avatar>
          <view class="df fdc jcsb ml20">
            <u-text size="28rpx" :text="val.goodName"></u-text>
            <u-text
              size="22rpx"
              color="#aaa"
              text="“世界那么大丨总要出去看看吧！"
            ></u-text>
          </view>
        </view>
        <view>
          <u-text
            align="right"
            color="#FF2A00"
            mode="price"
            size="32rpx"
            :text="val.goodPrice"
          ></u-text>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less"></style>
