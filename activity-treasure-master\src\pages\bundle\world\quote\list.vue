<script setup>
import { ref, onMounted } from 'vue';
import { getQuoteList } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// 状态管理
const quoteList = ref([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);
const page = ref(1);
const pageSize = 20;

// 获取摘录列表
const fetchQuoteList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return;

  loading.value = true;

  try {
    const currentPage = isRefresh ? 1 : page.value;
    const params = {
      page: currentPage,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    };

    console.log('请求摘录列表参数:', params);
    const res = await getQuoteList(params);
    console.log('摘录列表API响应:', res);

    if (res.status === 'ok' && res.data) {
      const newList = res.data.list || [];
      console.log('获取到的摘录列表:', newList);

      if (isRefresh) {
        quoteList.value = newList;
        page.value = 1;
      } else {
        quoteList.value = [...quoteList.value, ...newList];
      }

      hasMore.value = newList.length === pageSize;
      if (!isRefresh) page.value++;
    } else if (res.status === 'empty') {
      console.log('服务器返回空数据:', res.msg);
      if (isRefresh) {
        quoteList.value = [];
      }
      hasMore.value = false;
    } else {
      console.error('API返回错误:', res);
      uni.showToast({ title: res.msg || '获取摘录列表失败', icon: 'none' });
    }
  } catch (error) {
    console.error('获取摘录列表失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  fetchQuoteList(true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (hasMore.value && !loading.value) {
    fetchQuoteList();
  }
};

// 跳转到摘录详情
const goToQuoteDetail = (quote) => {
  uni.navigateTo({
    url: `/pages/bundle/world/quote/detail?id=${quote.id}`
  });
};

// 跳转到发布摘录
const goToPostQuote = () => {
  uni.navigateTo({
    url: '/pages/bundle/world/quote/post'
  });
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  let date;
  // 处理不同的时间格式
  if (typeof timeStr === 'string') {
    // 如果是字符串格式的时间戳或日期字符串
    if (/^\d+$/.test(timeStr)) {
      // 纯数字字符串，当作时间戳处理
      date = new Date(parseInt(timeStr) * 1000);
    } else {
      // 日期字符串
      date = new Date(timeStr);
    }
  } else if (typeof timeStr === 'number') {
    // 数字时间戳
    date = new Date(timeStr * 1000);
  } else {
    return '';
  }

  if (isNaN(date.getTime())) {
    console.warn('无效的时间格式:', timeStr);
    return '';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;

  return date.toLocaleDateString();
};

onMounted(() => {
  fetchQuoteList();
});
</script>

<template>
  <view class="quote-list-page">
    <!-- 统一导航栏 -->
    <customNavbar title="摘录">
      <template #right>
        <view class="post-btn" @click="goToPostQuote">
          <u-icon name="plus" size="44rpx" color="#ffffff"></u-icon>
        </view>
      </template>
    </customNavbar>

    <!-- 摘录列表 -->
    <scroll-view 
      scroll-y 
      class="quote-scroll"
      @scrolltolower="onLoadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="quote-list">
        <view 
          v-for="quote in quoteList" 
          :key="quote.id"
          class="quote-item"
          @click="goToQuoteDetail(quote)"
        >
          <view class="quote-content">
            <view class="quote-text">{{ quote.content }}</view>
            <view class="quote-author" v-if="quote.author">
              —— {{ quote.author }}
            </view>
            <view class="quote-meta">
              <text class="quote-time">{{ formatTime(quote.created_at) }}</text>
              <text class="quote-source" v-if="quote.source">{{ quote.source }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="load-status" v-if="loading && !refreshing">
        <u-loading-icon size="40rpx" color="#6AC086"></u-loading-icon>
        <text class="load-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view class="load-status" v-if="!hasMore && quoteList.length > 0">
        <text class="load-text">没有更多了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && quoteList.length === 0">
        <u-icon name="bookmark" size="120rpx" color="#cccccc"></u-icon>
        <text class="empty-text">还没有摘录，开始收集美好文字吧</text>
        <view class="empty-btn" @click="goToPostQuote">
          <text class="btn-text">发布摘录</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
/* 统一设计变量 */
:root {
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --radius-card: 20rpx;
  --radius-button: 50rpx;
  --color-bg-page: #f8f9fa;
  --color-bg-card: #ffffff;
  --color-text-title: #333333;
  --color-text-body: #666666;
  --color-text-caption: #999999;
  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.quote-list-page {
  height: 100vh;
  background-color: var(--color-bg-page);
  display: flex;
  flex-direction: column;
}

.post-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.quote-scroll {
  flex: 1;
  padding: var(--spacing-lg);
}

.quote-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.quote-item {
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.quote-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.quote-text {
  font-size: 28rpx;
  color: var(--color-text-body);
  line-height: 1.8;
  font-style: italic;
}

.quote-author {
  font-size: 24rpx;
  color: var(--color-text-caption);
  text-align: right;
  font-style: normal;
}

.quote-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.quote-time {
  font-size: 24rpx;
  color: var(--color-text-caption);
}

.quote-source {
  font-size: 24rpx;
  color: #6AC086;
  background-color: rgba(106, 192, 134, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.load-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.load-text {
  font-size: 24rpx;
  color: var(--color-text-caption);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx var(--spacing-lg);
  gap: var(--spacing-lg);
}

.empty-text {
  font-size: 28rpx;
  color: var(--color-text-caption);
  text-align: center;
}

.empty-btn {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  padding: 24rpx 48rpx;
  box-shadow: var(--shadow-card);

  .btn-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 500;
  }
}
</style>
