#!/bin/bash
# update_daemon.sh
crontab crontab #加载定时任务到系统
set -e  # 遇到错误立即退出

PROJECT_DIR="/home/<USER>/default/huodongbao_admin"
BACKUP_DIR="/home/<USER>/backup/$(date +%Y%m%d_%H%M%S)"
NEW_FILE_PATH="$1"

if [ -z "$NEW_FILE_PATH" ]; then
    echo "用法: $0 <新文件路径>"
    exit 1
fi

echo "开始更新流程..."

# 1. 创建备份
echo "1. 创建备份..."
mkdir -p "$BACKUP_DIR"
cp "$PROJECT_DIR/controller/Daemon.php" "$BACKUP_DIR/"

# 2. 停止服务
echo "2. 停止Daemon服务..."
cd "$PROJECT_DIR/access"
php cli controller=daemon action=stop || echo "停止命令执行完成"

# 等待进程完全停止
sleep 3

# 3. 验证停止
echo "3. 验证服务已停止..."
if ps aux | grep start_daemon_huodongbao | grep -v grep > /dev/null; then
    echo "警告: 进程仍在运行，尝试强制停止..."
    PID=$(ps aux | grep start_daemon_huodongbao | grep -v grep | awk '{print $2}')
    kill -KILL $PID
    sleep 2
fi

# 4. 替换文件
echo "4. 替换文件..."
cp "$NEW_FILE_PATH" "$PROJECT_DIR/controller/Daemon.php"
chmod 644 "$PROJECT_DIR/controller/Daemon.php"

# 5. 启动服务
echo "5. 启动服务..."
cd "$PROJECT_DIR/access"
nohup php cli controller=daemon action=start_daemon_huodongbao >/dev/null 2>&1 &

# 6. 验证启动
echo "6. 验证启动..."
sleep 5
if ps aux | grep start_daemon_huodongbao | grep -v grep > /dev/null; then
    echo "✅ 更新成功！Daemon进程已启动"
    echo "备份位置: $BACKUP_DIR"
else
    echo "❌ 启动失败，正在回滚..."
    cp "$BACKUP_DIR/Daemon.php" "$PROJECT_DIR/controller/"
    cd "$PROJECT_DIR/access"
    nohup php cli controller=daemon action=start_daemon_huodongbao >/dev/null 2>&1 &
    echo "已回滚到原版本"
    exit 1
fi

echo "更新完成！"