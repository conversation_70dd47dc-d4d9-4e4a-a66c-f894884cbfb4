
<form class="layui-form" enctype="multipart/form-data" method="post"  action="" id="form1">
	<div class="layui-form-item">
		<label class="layui-form-label">用户编号</label>
		<div class="layui-input-block">
			<input type="text" value="<?php echo $data['uid']; ?>" disabled lay-verify="required" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">昵称</label>
		<div class="layui-input-block">
			<input type="text" name="nickname" value="<?php echo $data['nickname']; ?>" <?php if(!empty($data['nickname'])){ ?> lay-verify="required"<?php } ?> class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">头像</label>
		<div class="layui-input-block">
			<img style="width:100px" id="img_preview" data-action="zoom" src="<?php echo $data['avatar']; ?>" /><br />
		</div>
		<div class="layui-input-block" style="margin-top:5px;">
			<button type="button" class="layui-btn" id="auto_upload"><i class="layui-icon"></i>上传头像</button>
		</div>
		<div class="layui-input-block" style="margin-top:5px;">
			<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="progress">
			  <div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
			</div>
		</div>
		<input type="hidden" name="avatar" id="avatar" value="<?php echo $data['avatar']; ?>" />
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">性别</label>
		<div class="layui-input-block">
			<select name="sex" value="<?php echo $data['sex']; ?>">
				<option value="1">男</option>
				<option value="2">女</option>
				<option value="0">未知</option>
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">出生日期</label>
		<div class="layui-input-block">
			<input type="text" class="layui-input" name="birthday" id="birthday" placeholder=" - ">
			<script>var laydate = layui.laydate;laydate.render({elem: '#birthday',value: '<?php if(isset($data['birthday']))echo $data['birthday']; ?>',type: 'date',format:'yyyy-MM-dd',trigger: 'click'});</script>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">手机号</label>
		<div class="layui-input-block">
			<input type="text" name="mobile" value="<?php echo $data['mobile']; ?>" lay-verify="required" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">个性签名</label>
		<div class="layui-input-block">
			<input type="text" name="gexingqianming" value="<?php echo $data['gexingqianming']; ?>" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">会员</label>
		<div class="layui-input-block">
			<select name="is_huiyuan" value="<?php echo $data['is_huiyuan']; ?>">
				<option value="1">是</option>
				<option value="0">否</option>
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">会员期限</label>
		<div class="layui-input-block">
			<input type="text" class="layui-input" name="huiyuan_end_time" id="huiyuan_end_time" placeholder=" - ">
			<script>var laydate = layui.laydate;laydate.render({elem: '#huiyuan_end_time',value: '<?php if(isset($data['huiyuan_end_time']))echo $data['huiyuan_end_time']; ?>',type: 'datetime',format:'yyyy-MM-dd HH:mm:ss',trigger: 'click'});</script>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">冻结</label>
		<div class="layui-input-block">
			<select name="is_dongjie" value="<?php echo $data['is_dongjie']; ?>">
				<option value="1">是</option>
				<option value="0">否</option>
			</select>
		</div>
	</div>

	<!-- 🆕 新增：用户角色管理 -->
	<div class="layui-form-item">
		<label class="layui-form-label">用户角色</label>
		<div class="layui-input-block">
			<!-- role_type为varchar类型，确保字符串比较 -->
			<select name="role_type" id="role_type" lay-filter="role_type" value="<?php echo $data['role_type']; ?>">
				<option value="0" <?php echo trim($data['role_type']) == '0' ? 'selected' : ''; ?>>管理员</option>
				<option value="1" <?php echo trim($data['role_type']) == '1' ? 'selected' : ''; ?>>分会长</option>
				<option value="2" <?php echo trim($data['role_type']) == '2' ? 'selected' : ''; ?>>普通用户</option>
				<option value="3" <?php echo trim($data['role_type']) == '3' ? 'selected' : ''; ?>>场地与活动第三方</option>
				<option value="4" <?php echo trim($data['role_type']) == '4' ? 'selected' : ''; ?>>城市分会长</option>
				<option value="5" <?php echo trim($data['role_type']) == '5' ? 'selected' : ''; ?>>场地第三方-不管理分会</option>
			</select>
		</div>
	</div>

	<!-- 🔧 优化：分会管理（支持分会长、城市分会长、场地第三方） -->
	<div class="layui-form-item" id="branch_management" style="display: none;">
		<label class="layui-form-label">分会名称</label>
		<div class="layui-input-block">
			<input type="text" name="branch_name" id="branch_name" value="<?php echo isset($data['branch_name']) ? $data['branch_name'] : ''; ?>" placeholder="请输入分会名称" class="layui-input">
			<div class="layui-form-mid layui-word-aux">分会长、城市分会长、场地与活动第三方需要设置分会名称</div>
		</div>
	</div>

	<div class="layui-form-item" id="branch_location_item" style="display: none;">
		<label class="layui-form-label">分会位置</label>
		<div class="layui-input-block">
			<input type="text" name="branch_location" id="branch_location" value="" placeholder="请输入分会所在地" class="layui-input">
			<div class="layui-form-mid layui-word-aux">可选，分会的地理位置</div>
		</div>
	</div>

	<!-- 🆕 新增：当前角色信息显示 -->
	<div class="layui-form-item">
		<label class="layui-form-label">当前状态</label>
		<div class="layui-input-block">
			<div class="layui-form-mid layui-word-aux">
				<?php
				$role_names = [
					0 => '管理员',
					1 => '分会长',
					2 => '普通用户',
					3 => '场地与活动第三方',
					4 => '城市分会长',
					5 => '场地第三方-不管理分会'
				];
				$current_role = isset($role_names[$data['role_type']]) ? $role_names[$data['role_type']] : '未知角色';
				echo "当前角色：{$current_role}";

				if (!empty($data['branch_name'])) {
					echo " | 所属分会：{$data['branch_name']}";
				}

				if ($data['assignment_type'] === 'system') {
					echo " | <span style='color: #FF9800;'>系统分配用户</span>";
				}
				?>
			</div>
		</div>
	</div>

	<input type="hidden" name="id" value="<?php echo $data['uid']; ?>" />
  <div class="layui-form-item">
    <div class="layui-input-block">
      <button type="submit" lay-submit="" class="layui-btn">提交</button>
      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
  </div>
</form>
<br />
<script>

layui.use(['upload', 'element', 'layer', 'form'], function(){
	var $ = layui.jquery
	,upload = layui.upload
	,element = layui.element
	,layer = layui.layer
	,form = layui.form;
	element.init();

	// 🆕 新增：角色选择控制逻辑
	form.on('select(role_type)', function(data){
		var role_type = data.value;
		var branch_management = $('#branch_management');
		var branch_location_item = $('#branch_location_item');
		var branch_name_input = $('#branch_name');
		var branch_location_input = $('#branch_location');

		// 🔧 优化：分会长(1)、场地第三方(3)和城市分会长(4)需要显示分会管理
		if (role_type == '1' || role_type == '3' || role_type == '4') {
			branch_management.show();
			branch_location_item.show();
			branch_name_input.attr('lay-verify', 'required');
		} else {
			branch_management.hide();
			branch_location_item.hide();
			branch_name_input.removeAttr('lay-verify');
			branch_name_input.val(''); // 清空分会名称
			branch_location_input.val(''); // 清空分会位置
		}
	});

	// 🔧 优化：页面加载时初始化显示状态
	var current_role = $('#role_type').val();
	if (current_role == '1' || current_role == '3' || current_role == '4') {
		$('#branch_management').show();
		$('#branch_location_item').show();
		$('#branch_name').attr('lay-verify', 'required');
	}

	// 🔧 修复：表单提交前的角色变更确认（解决双重提交问题）
	var isSubmitting = false; // 防止重复提交

	form.on('submit(demo1)', function(data){
		if (isSubmitting) {
			return false;
		}

		var original_role = '<?php echo $data['role_type']; ?>';
		var new_role = data.field.role_type;

		// 🔧 优化：统一的表单验证（包含场地第三方）
		if ((new_role == '1' || new_role == '3' || new_role == '4') && !data.field.branch_name) {
			layer.msg('分会长、场地与活动第三方、城市分会长必须设置分会名称', {icon: 2});
			return false;
		}

		if (original_role != new_role) {
			var role_names = {
				'0': '管理员',
				'1': '分会长',
				'2': '普通用户',
				'3': '场地与活动第三方',
				'4': '城市分会长',
				'5': '场地第三方-不管理分会'
			};

			var confirm_msg = '确定要将用户角色从 "' + role_names[original_role] + '" 修改为 "' + role_names[new_role] + '" 吗？';

			layer.confirm(confirm_msg, {
				btn: ['确定修改', '取消']
			}, function(index) {
				layer.close(index);
				submitForm(data.field);
			});

			return false; // 阻止默认提交
		}

		// 🔧 修复：角色未变更时也使用统一的提交方式
		submitForm(data.field);
		return false;
	});

	// 🆕 新增：统一的表单提交方法
	function submitForm(formData) {
		if (isSubmitting) {
			return;
		}

		isSubmitting = true;

		$.post('', formData, function(res) {
			isSubmitting = false;
			if (res.status === 'ok') {
				layer.msg('修改成功', {icon: 1});
				setTimeout(function() {
					location.reload();
				}, 1000);
			} else {
				layer.msg(res.msg || '修改失败', {icon: 2});
			}
		}, 'json').fail(function() {
			isSubmitting = false;
			layer.msg('网络错误，请重试', {icon: 2});
		});
	}
	var files_obj;
	var files_list;
	var uploadInst = upload.render({
		elem: '#auto_upload'
		,url: '<?php echo url("Uploadimg/index"); ?>' //此处配置你自己的上传接口即可
		,accept:'file'
		,acceptMime:'file'
		,auto:true
		,exts:"jpg|jpeg|png|gif|mp4|mov|aac|mp3"
		,bindAction:"#upload"
		,field:"image"
		,data:{
			//"uid":function (){return $("input[name='uid']").val();}
			//,"text":function (){return $("input[name='contents']").val();}
			path:"avatar"
		}
		,multiple: false
		,size:50*1024 //kb
		,choose: function(obj){
			files_obj = obj;
			files_list = obj.pushFile();
		}
		,before:function(obj){
			//console.log(files_list);
			if($.isEmptyObject(files_list)){
				layui.layer.msg("请先选择文件");
			}else{
				loadIndex = layui.layer.load(0, {shade: [0.1, '#fff']});
				element.progress('progress', "0%");
			}
		}
		,done: function(res,index,upload){
			//上传完毕
			if(res.errno == 1){
				layer.msg(res.message,{icon:2});
			}else{
				var url = res.data.url;
				$("input[name='avatar']").val(url);
				$("#img_preview").attr("src",url);
			}
			delete files_list[index];
			layui.layer.close(loadIndex);
		}
		,allDone:function(obj){
			//$("#preview").empty();
			//layui.layer.close(loadIndex);
		}
		,error:function(){
			layer.msg("网络出错",{icon:2});
			layui.layer.close(loadIndex);
		}
		,progress:function(n, elem, res, index){
			var percent = n + '%' //获取进度百分比
			element.progress('progress', percent); //可配合 layui 进度条元素使用
		}
	}); 
	
});
</script>