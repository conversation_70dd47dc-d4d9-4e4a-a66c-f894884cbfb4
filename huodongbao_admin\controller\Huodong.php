<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 活动管理 
*/
class <PERSON><PERSON>ong extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 分类管理
	* @method type
	*/		
	public function type(){
		dbConn();
		if(!empty($_POST['ac'])){
			responseType("json");
			$ac = trim($_POST['ac']);
			if($ac == "add"){
				if(
					empty($_POST['name']) || 
					empty($_POST['img_url'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$data = [
					"name"=>":name",
					"icon"=>":img_url",
				];
				$prepareParam = [
					":name"=>isset($_POST['name']) ? htmlspecialchars($_POST['name']) : "",
					":img_url"=>isset($_POST['img_url']) ? htmlspecialchars($_POST['img_url']) : "",
				];
				Db()->table("huodong_type")->prepareParam($prepareParam)->insert($data);
				return ["status"=>"ok","msg"=>"操作成功"];
			}else if($ac == "update"){
				if(
					empty($_POST['id']) || 
					!check($_POST['id'],"intgt0") || 
					empty($_POST['name']) || 
					empty($_POST['img_url'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$id = intval($_POST['id']);
				$data = [
					"name"=>":name",
					"icon"=>":img_url",
				];
				$prepareParam = [
					":name"=>isset($_POST['name']) ? htmlspecialchars($_POST['name']) : "",
					":img_url"=>isset($_POST['img_url']) ? htmlspecialchars($_POST['img_url']) : "",
				];
				$rowCount = Db()->table("huodong_type")->where("id={$id}")->prepareParam($prepareParam)->update($data);	
				if($rowCount){
					return ["status"=>"ok","msg"=>"操作成功"];
				}
			}else if($ac == "del"){
				if(
					empty($_POST['ids'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$ids_arr = explode(",",$_POST['ids']);
				foreach($ids_arr as $v){
					if(!check($v,"intgt0")){
						return ["status"=>"error","msg"=>"参数错误"];
					}
				}
				$ids_str = implode(",",$ids_arr);
				$rowCount = Db()->table("huodong_type")->where("id IN ({$ids_str})")->del();	
				if($rowCount){
					return ["status"=>"ok","msg"=>"操作成功"];
				}
			}else{
				return ["status"=>"error","msg"=>"参数错误"];
			}
			return ["status"=>"error","msg"=>"操作失败"];
		}
		$data = Db()->table("huodong_type")->order("id ASC")->fetchAll();
		assign("data",$data);
		//
		return view(['header','huodong/type','footer']);
	}
	
	/*
	* @name 活动列表
	* @method index
	*/			
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['id']) && check($_REQUEST['id'],"intgt0")){
			$id = (int)$_REQUEST['id'];
			$where .= " AND id={$id}";
			$pageParams['id'] = $_REQUEST['id'];
		}
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		if(!empty($_REQUEST['type_id']) && check($_REQUEST['type_id'],"intgt0")){
			$type_id = (int)$_REQUEST['type_id'];
			$where .= " AND type_id={$type_id}";
			$pageParams['type_id'] = $_REQUEST['type_id'];
			assign("type_id",$type_id);
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $_REQUEST['status'];
			assign("status",$status);
		}
		if(isset($_REQUEST['is_tuijian']) && in_array($_REQUEST['is_tuijian'],[0,1])){
			$is_tuijian = (int)$_REQUEST['is_tuijian'];
			$where .= " AND is_tuijian={$is_tuijian}";
			$pageParams['is_tuijian'] = $_REQUEST['is_tuijian'];
			assign("is_tuijian",$is_tuijian);
		}

        if (isset($_REQUEST['pinned_at']) && in_array($_REQUEST['pinned_at'], ['all', '1', '0'])) {
            $pinned_at = $_REQUEST['pinned_at'];
            $pageParams['pinned_at'] = $pinned_at;
            assign("pinned_at", $pinned_at);

            if ($pinned_at === '1') {
                // 筛选置顶的记录，假设置顶的记录 `pinned_at` 不为 NULL
                $where .= " AND pinned_at IS NOT NULL";
            } elseif ($pinned_at === '0') {
                // 筛选未置顶的记录，假设未置顶的记录 `pinned_at` 为 NULL
                $where .= " AND pinned_at IS NULL";
            }
            // 如果是 'all'，则不添加任何筛选条件
        }

		if(isset($_REQUEST['jiesuan_status']) && check($_REQUEST['jiesuan_status'],"integt0")){
			$jiesuan_status = (int)$_REQUEST['jiesuan_status'];
			$where .= " AND jiesuan_status={$jiesuan_status}";
			$pageParams['jiesuan_status'] = $_REQUEST['jiesuan_status'];
			assign("jiesuan_status",$jiesuan_status);
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND (name LIKE :keyword OR title LIKE :keyword OR addr LIKE :keyword OR sheng LIKE :keyword OR shi LIKE :keyword OR qu LIKE :keyword)";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-1 years"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("huodong")->where($where)->prepareParam($prepareParam)->order("pinned_at DESC,id DESC")->page($page,$page_size,$pageParams);
//        echo count($data);
		foreach($data as &$row){
			//$row['huodong_img'] = Db()->table("huodong_img")->where("huodong_id={$row['id']}")->fetchAll();
			$row['user'] = Db()->table("user")->select("uid,nickname")->where("uid={$row['uid']}")->fetch();
		}
		assign("data",$data);
		//dump($data);exit;
		//
		$type_arr = Db()->table("huodong_type")->fetchAll();
		$type_arr = array_column($type_arr,"name","id");
		assign("type_arr",$type_arr);
		//
		$status_arr = [0=>"审核中",1=>"审核通过",2=>"审核未通过",3=>"取消"];
		assign("status_arr",$status_arr);
		$jiesuan_status_arr = [0=>"未结算",1=>"已结算",2=>"无需结算",3=>"暂停结算"];
		assign("jiesuan_status_arr",$jiesuan_status_arr);
		$pay_type_arr = [1=>"线上",2=>"线下"];
		assign("pay_type_arr",$pay_type_arr);
		//
//        dump($data);
//        return ["data"=>$data];
		return view(['header','huodong/index','footer']);
	}
	/*
	* @name 编辑
	* @method edit
	*/			
	public function edit(){
		dbConn();
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("huodong/index"));
			exit;
		}
		$id = (int)$_REQUEST['id'];
		if(
			!empty($_POST['name']) && 
			!empty($_POST['title']) && 
			!empty($_POST['type_id']) && 
			!empty($_POST['lianxi_name']) && 
			!empty($_POST['lianxi_mobile']) && 
			isset($_POST['money']) && 
			isset($_POST['num']) && 
			isset($_POST['baoming_start_time']) && 
			isset($_POST['baoming_end_time']) && 
			isset($_POST['start_time']) && 
			isset($_POST['end_time']) && 
			isset($_POST['sheng_id']) && 
			isset($_POST['shi_id']) && 
			isset($_POST['qu_id']) && 
			isset($_POST['status']) && 
			isset($_POST['yongjin_bili']) && 
			!empty($_POST['addr']) && 
			check($_POST['type_id'],"intgt0") &&
			check($_POST['num'],"intgt0") &&
			check($_POST['sheng_id'],"intgt0") &&
			check($_POST['shi_id'],"intgt0") &&
			check($_POST['qu_id'],"intgt0") &&
			check($_POST['status'],"integt0") &&
			check($_POST['yongjin_bili'],"integt0") &&
			check($_POST['baoming_start_time'],"datetime") &&
			check($_POST['baoming_end_time'],"datetime") &&
			check($_POST['start_time'],"datetime") &&
			check($_POST['end_time'],"datetime") &&
			is_numeric($_POST['money']) &&
			$_POST['money'] >= 0 &&
			$_POST['yongjin_bili'] <= 100 &&
			check($_POST['choujiang_status'],"integt0") &&
			check($_POST['jiesuan_status'],"integt0") &&
			check($_POST['pay_type'],"integt0")
		){
			//
			Db()->begin();
			try{
				//
				$data = [
					"type_id"=>intval($_POST['type_id']),
					"num"=>intval($_POST['num']),
					"sheng_id"=>intval($_POST['sheng_id']),
					"shi_id"=>intval($_POST['shi_id']),
					"qu_id"=>intval($_POST['qu_id']),
					"yongjin_bili"=>intval($_POST['yongjin_bili']),
					"status"=>intval($_POST['status']),
					"choujiang_status"=>intval($_POST['choujiang_status']),
					"jiesuan_status"=>intval($_POST['jiesuan_status']),
					"pay_type"=>intval($_POST['pay_type']),
					"money"=>number_format($_POST['money'],2,".",""),
					"baoming_start_time"=>date("Y-m-d H:i:s",strtotime($_POST['baoming_start_time'])),
					"baoming_end_time"=>date("Y-m-d H:i:s",strtotime($_POST['baoming_end_time'])),
					"start_time"=>date("Y-m-d H:i:s",strtotime($_POST['start_time'])),
					"end_time"=>date("Y-m-d H:i:s",strtotime($_POST['end_time'])),
					"name"=>":name",
					"title"=>":title",
					"addr"=>":addr",
					"sheng"=>":sheng",
					"shi"=>":shi",
					"qu"=>":qu",
					"lianxi_name"=>":lianxi_name",
					"lianxi_mobile"=>":lianxi_mobile",
				];
				$prepareParam = [
					":name"=>htmlspecialchars($_POST['name']),
					":title"=>htmlspecialchars($_POST['title']),
					":addr"=>htmlspecialchars($_POST['addr']),
					":sheng"=>htmlspecialchars($_POST['sheng']),
					":shi"=>htmlspecialchars($_POST['shi']),
					":qu"=>htmlspecialchars($_POST['qu']),
					":lianxi_name"=>htmlspecialchars($_POST['lianxi_name']),
					":lianxi_mobile"=>htmlspecialchars($_POST['lianxi_mobile']),
				];
				//
				if(!empty($_POST['jiesuan_time']) && check($_POST['jiesuan_time'],"datetime")){
					$data['jiesuan_time'] = $_POST['jiesuan_time'];
				}
				if(!empty($_POST['choujiang_time']) && check($_POST['choujiang_time'],"datetime")){
					$data['choujiang_time'] = $_POST['choujiang_time'];
				}
				//
				if($data['pay_type'] == 2){
					$data['yongjin_bili'] = 0;
					$data['choujiang_status'] = 2;
					$data['jiesuan_status'] = 2;
				}
				//
				$res = Db()->table("huodong")->where("id={$id}")->prepareParam($prepareParam)->update($data);
				$this->root_log("编辑活动信息,id:{$id}");
				Db()->commit();
				$params = $_GET;
				unset($params['id']);
				header("location:".url("huodong/index",$params));
				exit;
			}catch(\Exception $e){
				Db()->rollback();
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$data = Db()->table("huodong")->where("id={$id}")->fetch();
		if(empty($data)){
			header("location:".url("huodong/index"));
			exit;			
		}
		//$data['contents'] = Db()->quote(htmlspecialchars_decode($data['contents']));
		$data['liucheng'] = json_decode(stripslashes(trim($data['liucheng'],'"')),true);
		$data['choujiang_config'] = json_decode($data['choujiang_config_json'],true);
		assign("data",$data);
		//
		$type_arr = Db()->table("huodong_type")->fetchAll();
		$type_arr = array_column($type_arr,"name","id");
		assign("type_arr",$type_arr);
		//
		$status_arr = [0=>"审核中",1=>"审核通过",2=>"审核未通过",3=>"取消"];
		assign("status_arr",$status_arr);
		$choujiang_status_arr = [0=>"待抽奖",1=>"抽奖完成",2=>"无抽奖"];
		assign("choujiang_status_arr",$choujiang_status_arr);
		$jiesuan_status_arr = [0=>"未结算",1=>"已结算",2=>"无需结算",3=>"暂停结算"];
		assign("jiesuan_status_arr",$jiesuan_status_arr);
		$pay_type_arr = [1=>"线上",2=>"线下"];
		assign("pay_type_arr",$pay_type_arr);
		//
		return view(['header','huodong/edit','footer']);
	}	

	/*
	* @name 审核
	* @method shenhe
	*/	
	public function shenhe($ids,$status){
		dbConn();
		responseType("json");
		$ids_arr = explode(",",trim($ids));
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		$status_arr = [0=>"审核中",1=>"审核通过",2=>"审核未通过",3=>"取消"];
		assign("status_arr",$status_arr);
		if(!isset($status_arr[$status])){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$status = intval($status);
		$ids_str = implode(",",$ids_arr);
		//
		Db()->begin();
		try{
			$sql = "UPDATE `huodong` SET `status`={$status} WHERE `id` IN ({$ids_str})";
			$rowCount = Db()->_exec($sql);
			$this->root_log("活动审核:{$ids} => {$status_arr[$status]}，影响数量: {$rowCount}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"影响数量: {$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 推荐设置
	* @method tuijian
	*/			
	public function tuijian($ac,$ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		if($ac == "tuijian"){
			$sql = "UPDATE `huodong` SET `is_tuijian`=1 WHERE id IN ({$ids_str})";
		}else if($ac == "cancel_tuijian"){
			$sql = "UPDATE `huodong` SET `is_tuijian`=0 WHERE id IN ({$ids_str})";
		}else{
			return ["status"=>"error","msg"=>"参数错误3"];
		}
		Db()->begin();
		try{
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量修改活动推荐信息:{$ids_str}|{$ac}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}

    /*
	* @name 置顶设置
	* @method zhiding
	*/
    public function zhiding($ac, $ids)  {
        dbConn();
        responseType("json");
        if(empty($_POST['ids'])){
            return ["status"=>"error","msg"=>"参数错误1"];
        }
        $ids_arr = explode(",",$_POST['ids']);
        foreach($ids_arr as $id){
            if(!check($id,"intgt0")){
                return ["status"=>"error","msg"=>"参数错误2"];
            }
        }
        $ids_str = implode(",", $ids_arr);
        if($ac == "zhiding"){
            $sql = "UPDATE `huodong` SET `pinned_at`=NOW() WHERE id IN ({$ids_str})";
        }else if($ac == "cancel"){
            $sql = "UPDATE `huodong` SET `pinned_at`=NULL WHERE id IN ({$ids_str})";
        }else{
            return ["status"=>"error","msg"=>"参数错误3"];
        }
        Db()->begin();
        try{
            $rowCount = Db()->_exec($sql);
            $this->root_log("批量修改活动置顶信息:{$ids_str}|{$ac}");
            Db()->commit();
            return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
        }catch(\Exception $e){
            Db()->rollback();
            $this->exception_log($e->getMessage());
            return ["status"=>"error","msg"=>$e->getMessage()];
        }
    }

    /*
	* @name 报名订单
	* @method orders
	*/		
	public function orders(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['huodong_id']) && check($_REQUEST['huodong_id'],"intgt0")){
			$huodong_id = (int)$_REQUEST['huodong_id'];
			$where .= " AND huodong_id={$huodong_id}";
			$pageParams['huodong_id'] = $huodong_id;
		}
		if(!empty($_REQUEST['order_id'])){
			$where .= " AND `order_id`=:order_id";
			$prepareParam[':order_id'] = $_REQUEST['order_id'];
			$pageParams['order_id'] = $_REQUEST['order_id'];;
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $status;
			assign("status",$status);
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("huodong_baoming_order")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ?: [];
				//
				$huodong = Db()->table("huodong")->select("id,name,title,status")->where("id={$v['huodong_id']}")->fetch();
				$data[$k]["huodong"] = $huodong ?: [];
				//
			}
		}
		assign("data",$data);
		//
		$status_arr = [0=>"未支付",1=>"已支付",2=>"已取消",3=>"退款中",4=>"退款成功",5=>"退款失败"];
		assign("status_arr",$status_arr);
		//
		$pay_type_arr = [1=>"微信",2=>"余额",3=>"其他"];
		assign("pay_type_arr",$pay_type_arr);
		//
		return view(['header','huodong/orders','footer']);
	}
	/*
	* @name 报名退款
	* @method tuikuan
	*/	
	public function tuikuan($id){
		dbConn();
		responseType("json");
		if(
			!check($id,"intgt0")
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = (int)$id;
		$tuikuan_yuanyin = "取消报名";
		$order_info = Db()->table("huodong_baoming_order")->where("id={$id}")->fetch();
		if(empty($order_info))return ["status"=>"error","msg"=>"订单未找到"];
		if(!in_array($order_info['status'],[1])){
			return ["status"=>"error","msg"=>"仅支持已支付的订单退款"];
		}
		$tukuan_money = $order_info['money'];
		//
		$money_fen = (int)($tukuan_money * 100);
		$total_fen = (int)($order_info['money'] * 100);
		$order_id = $order_info['order_id'];
		//
		$pay_type = $order_info['pay_type'];
		//
		Db()->begin();
		try{
			if($pay_type == 1){
				$res = \model\Wechat::tuikuan($money_fen,$total_fen,$order_id,$tuikuan_yuanyin);
				if($res['status'] != "ok"){
					throw new \Exception("微信支付退款受理失败:".$res['msg']);
				}
				if($res['data'] == "success"){
					$sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE `order_id`={$order_id} AND status=1";
					Db()->_exec($sql);
				}else{
					$sql = "UPDATE `huodong_baoming_order` SET `status`=3 WHERE `order_id`={$order_id} AND status=1";
					Db()->_exec($sql);
				}
			}else if($pay_type == 2){
				//
				$sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE `order_id`={$order_id} AND status=1";
				$rowCount = Db()->_exec($sql);
				if(empty($rowCount)){
					throw new \Exception("订单状态异常或已处理");
				}
				//
				$sql = "UPDATE `user` SET money=money+{$order_info['money']} WHERE uid={$order_info['uid']}";
				$rowCount = Db::_exec($sql);
				if(empty($rowCount)){
					throw new \Exception("余额支付退款失败");
				}
				//
				$shengyu = Db()->table("user")->where("uid={$order_info['uid']}")->getColumn("money",0);
				$zhangdan = [
					"uid"=>$order_info['uid'],
					"money"=>$order_info['money'],
					"type"=>9,
					"shengyu"=>$shengyu,
					"msg"=>"活动退款：{$order_id}",
				];
				Db()->table("user_zhangdan")->insert($zhangdan);
				//
			}else{
				throw new \Exception("未知支付类型");
			}
			//
			$sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`-1 WHERE id={$order_info['huodong_id']}";
			Db::_exec($sql);
			//
			$this->root_log("订单退款:{$order_id}|{$tukuan_money}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"退款受理成功"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}	
	/*
	* @name 评价列表
	* @method pingjia
	*/			
	public function pingjia(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['huodong_id']) && check($_REQUEST['huodong_id'],"intgt0")){
			$huodong_id = (int)$_REQUEST['huodong_id'];
			$where .= " AND huodong_id={$kecheng_id}";
			$pageParams['huodong_id'] = $_REQUEST['huodong_id'];
		}
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		if(isset($_REQUEST['has_img']) && in_array($_REQUEST['has_img'],[0,1])){
			$has_img = intval($_REQUEST['has_img']);
			if($has_img == 1){
				$where .= " AND imgs_url IS NOT NULL";
			}else if($has_img == 0){
				$where .= " AND imgs_url IS NULL";
			}
			$pageParams['has_img'] = $_REQUEST['has_img'];
			assign("has_img",$has_img);
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND contents LIKE :keyword";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-3 month"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("huodong_pingjia")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as &$row){
				$row['huodong'] = Db()->table("huodong")->select("id,name")->where("id={$row['huodong_id']}")->fetch();
				$row['user'] = Db()->table("user")->select("uid,nickname")->where("uid={$row['uid']}")->fetch();
			}
		}
		assign("data",$data);
		//dump($data);exit;
		return view(['header','huodong/pingjia','footer']);
	}
	/*
	* @name 评价删除
	* @method pingjia_del
	*/			
	public function pingjia_del($ids){
		dbConn();
		responseType("json");
		if(empty($_POST['ids'])){
			return ["status"=>"error","msg"=>"参数错误1"];
		}
		$ids_arr = explode(",",$_POST['ids']);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误2"];
			}
		}
		$ids_str = implode(",",$ids_arr);
		$data = Db()->table("huodong_pingjia")->select("id,imgs_url")->where("id IN ({$ids_str})")->fetchAll();
		if(empty($data)){
			return ["status"=>"error","msg"=>"资源未找到"];
		}
		Db()->begin();
		try{
			//
			$sql = "DELETE FROM `huodong_pingjia` WHERE id IN ({$ids_str})";
			$rowCount = Db()->_exec($sql);
			//
			$this->root_log("批量删除活动评价信息:{$ids_str}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"操作成功,更改数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}
	/*
	* @name 中奖记录
	* @method zhongjiang_log
	*/			
	public function zhongjiang_log(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['huodong_id']) && check($_REQUEST['huodong_id'],"intgt0")){
			$huodong_id = (int)$_REQUEST['huodong_id'];
			$where .= " AND huodong_id={$huodong_id}";
			$pageParams['huodong_id'] = $_REQUEST['huodong_id'];
		}
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-3 month"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("huodong_zhongjiang_log")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as &$row){
				$row['huodong'] = Db()->table("huodong")->select("id,name")->where("id={$row['huodong_id']}")->fetch();
				$row['user'] = Db()->table("user")->select("uid,nickname")->where("uid={$row['uid']}")->fetch();
			}
		}
		assign("data",$data);
		//dump($data);exit;
		//
		$pay_type_arr = [1=>"微信",2=>"余额",3=>"其他"];
		assign("pay_type_arr",$pay_type_arr);
		//
		return view(['header','huodong/zhongjiang_log','footer']);
	}	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
