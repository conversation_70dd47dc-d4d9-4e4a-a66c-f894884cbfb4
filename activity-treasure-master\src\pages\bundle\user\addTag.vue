<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import myLine from "@/components/myLine.vue";
import { store } from "@/store";

const lableList = ref([1, 2, 3, 4, 5, 6, 7, 8, 9]);
const popupShow = ref(false);
</script>
<template>
  <view class="page">
    <view class="p30">
      <myLine bg="#333333" w="4" h="26" title="添加我的标签" size="28">
        <u-button
          shape="circle"
          color="#5F84EF"
          text="添加标签"
          :customStyle="{ width: '140rpx', height: '46rpx', fontSize: '20rpx' }"
          @click="popupShow = true"
        ></u-button>
      </myLine>
      <view class="mt30 df aic fw">
        <view
          class="pr m20 p20 x24"
          style="background-color: #eeeeee; border-radius: 26rpx"
          v-for="(val, i) in lableList"
          :key="i"
        >
          {{ val }}
          <view class="pa" style="top: -5rpx; right: -5rpx">
            <u-icon
              size="30rpx"
              name="close-circle-fill"
              @click="lableList.splice(i, 1)"
            ></u-icon>
          </view>
        </view>
      </view>
      <u-gap height="40rpx"></u-gap>
      <myLine bg="#333333" w="4" h="26" title="添加个人信息标签" size="28">
        <u-button
          shape="circle"
          color="#4DAAF2"
          text="添加标签"
          :customStyle="{ width: '140rpx', height: '46rpx', fontSize: '20rpx' }"
          @click="popupShow = true"
        ></u-button>
      </myLine>
    </view>
    <u-popup
      :show="popupShow"
      round="20"
      mode="center"
      :safe-area-inset-bottom="false"
      @close="popupShow = false"
    >
      <view class="p30">
        <view
          class="df aic jcsb px20 pt10 pb10 r20"
          style="border: 2rpx #aaa solid"
        >
          <u-input
            v-model="text"
            placeholder="请输入标签（最多10字）"
            border="none"
            maxlength="10"
            :customStyle="{
              width: '500rpx'
            }"
          ></u-input>
          <u-button
            shape="circle"
            text="添加"
            color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
            :customStyle="{
              margin: '0',
              width: '88rpx',
              border: 'none',
              color: '#333333',
              fontSize: '30rpx'
            }"
            @click="
              if (form.lableList.length < 10) form.lableList.unshift(text);
              else $u.toast('最多添加10个标签');
            "
          ></u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<style scoped lang="less"></style>
