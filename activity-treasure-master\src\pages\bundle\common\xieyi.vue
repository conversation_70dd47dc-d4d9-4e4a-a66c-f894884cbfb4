<script setup>
import { watch, ref, reactive } from "vue";
import { htmlindex } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";

const info = ref({});

onLoad(async (e) => {
  const res = await htmlindex({ type: e.type });
  if (res?.status === "ok") info.value = res.data;
  uni.setNavigationBarTitle({
    title: res?.data?.name || "相关协议",
  });
});
</script>
<template>
  <view class="page p30">
    <u-text
      margin="0 0 20rpx"
      color="#ccc"
      size="24rpx"
      :text="`发布时间：${info?.time}`"
    ></u-text>
    <u-parse :content="info?.contents"></u-parse>
  </view>
</template>

<style scoped lang="less"></style>
