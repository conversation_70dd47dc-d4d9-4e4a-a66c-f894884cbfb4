<?php
/**
 * 世界模块控制器
 */

namespace controller;

use core\Controller;
use core\Db; // 引入 Huodong.php 中使用的 Db 类

class World extends Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 构建日卡查询的FROM和SELECT子句
     * @param int $userId 用户ID，用于获取点赞和收藏状态
     * @return array ['from' => string, 'select' => string, 'params' => array]
     */
    private function buildCardQueryClauses($userId = 0)
    {
        $fromClause = 'daily_cards';
        $selectFields = 'daily_cards.id, daily_cards.card_date as date, daily_cards.description, daily_cards.author, daily_cards.background_image_url, daily_cards.like_count as likeCount, daily_cards.comment_count as commentCount';
        $params = [];

        if ($userId > 0) {
            $fromClause .= ' LEFT JOIN user_card_likes ucl ON ucl.card_id = daily_cards.id AND ucl.user_id = :userId';
            $fromClause .= ' LEFT JOIN user_favorites uf ON uf.item_id = daily_cards.id AND uf.item_type = "card" AND uf.user_id = :userId';
            $selectFields .= ', IF(ucl.id IS NOT NULL, 1, 0) as isLiked, IF(uf.id IS NOT NULL, 1, 0) as isFavorited';
            $params[':userId'] = $userId;
        } else {
            $selectFields .= ', 0 as isLiked, 0 as isFavorited';
        }

        return ['from' => $fromClause, 'select' => $selectFields, 'params' => $params];
    }

    /**
     * 构建动态查询的FROM和SELECT子句
     * @param int $userId 用户ID，用于获取点赞和收藏状态
     * @return array ['from' => string, 'select' => string, 'params' => array]
     */
    private function buildFeedQueryClauses($userId = 0)
    {
        $fromClause = 'feeds LEFT JOIN user u ON u.uid = feeds.user_id';
        $selectFields = 'feeds.id, feeds.user_id, feeds.content, feeds.images_json, feeds.location, feeds.tags, feeds.like_count as likeCount, feeds.comment_count as commentCount, feeds.created_at, u.nickname, u.avatar as avatar_url';
        $params = [];

        if ($userId > 0) {
            $fromClause .= ' LEFT JOIN user_feed_likes ufl ON ufl.feed_id = feeds.id AND ufl.user_id = :userId';
            $fromClause .= ' LEFT JOIN user_favorites uf ON uf.item_id = feeds.id AND uf.item_type = "feed" AND uf.user_id = :userId';
            $selectFields .= ', IF(ufl.id IS NOT NULL, 1, 0) as isLiked, IF(uf.id IS NOT NULL, 1, 0) as isFavorited';
            $params[':userId'] = $userId;
        } else {
            $selectFields .= ', 0 as isLiked, 0 as isFavorited';
        }

        return ['from' => $fromClause, 'select' => $selectFields, 'params' => $params];
    }

    /**
     * 用于测试路由和控制器加载的简单方法
     *
     * @return Json
     */
    public function test()
    {
        // 返回简单的成功信息，不依赖复杂逻辑
        return ["status" => "ok", "msg" => "World test method executed successfully!"];
    }

    /**
     * @apiName 获取指定日期范围的日卡数据
     * @method get_daily_cards
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选, 用于验证用户)
     * @param startDate string 开始日期 YYYY-MM-DD
     * @param endDate string 结束日期 YYYY-MM-DD
     * @return {"status":"ok","data":{"cards":[...]} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_daily_cards($startDate, $endDate, $uid = 0, $token = "")
    {
        // 参数验证
        if (
            empty($startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) ||
            empty($endDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)
        ) {
            return ["status" => "error", "msg" => "日期参数错误"];
        }

        // (可选) 用户验证，用于获取点赞状态
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                 // 不严格要求验证，未登录或验证失败则不显示点赞状态
             } else if ($this->auth($uid, $token)) {
                 $userId = (int)$uid;
             }
        }


        // (可选) 限制日期范围
        $startTs = strtotime($startDate);
        $endTs = strtotime($endDate);
        if ($endTs - $startTs > 30 * 86400) { // 例如限制最大查询 31 天
           $endDate = date('Y-m-d', $startTs + 30 * 86400);
        }

        dbConn(); // 确保数据库连接
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 合并参数
            $allParams = array_merge([":startDate" => $startDate, ":endDate" => $endDate], $queryClauses['params']);

            // 执行查询
            $cards = Db()->table($queryClauses['from'])
                ->select($queryClauses['select'])
                ->where("daily_cards.card_date >= :startDate AND daily_cards.card_date <= :endDate") // 明确指定表名
                ->order('daily_cards.card_date', 'asc') // 明确指定表名
                ->prepareParam($allParams)
                ->fetchAll();

            if (empty($cards)) {
                return ["status" => "empty", "msg" => "指定日期范围无数据"];
            }

            return ["status" => "ok", "data" => ["cards" => $cards]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取日卡数据失败"];
        }
    }

    /**
     * @apiName 获取单张日卡详情
     * @method get_card_detail
     * @POST
     * @param id int 日卡ID
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_card_detail($id, $uid = 0, $token = "")
    {
        if (!check($id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $cardId = (int)$id;

        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
            } else if ($this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 合并参数
            $allParams = array_merge([':cardId' => $cardId], $queryClauses['params']);

            $card = Db()->table($queryClauses['from'])
                ->select($queryClauses['select'])
                ->where('daily_cards.id = :cardId')
                ->prepareParam($allParams)
                ->fetch();

            if (empty($card)) {
                return ["status" => "empty", "msg" => "日卡不存在"];
            }

            // 未来可能需要关联评论信息等

            return ["status" => "ok", "data" => $card];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取日卡详情失败"];
        }
    }

    /**
     * @apiName 获取卡片列表 (分页)
     * @method get_cards
     * @POST
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 10)
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_cards($page = 1, $page_size = 10, $uid = 0, $token = "")
    {
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
        $page = (int)$page;
        $page_size = (int)$page_size;

        $userId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                 // fail silently
             } else if ($this->auth($uid, $token)) {
                 $userId = (int)$uid;
             }
        }

        dbConn();
        try {
            $queryBase = Db()->table('daily_cards')
                         ->order('card_date', 'desc'); // 假设按日期倒序

            // 克隆查询对象用于计算总数 (在应用 JOIN 和 SELECT 之前)
            $countQuery = clone $queryBase;
            $total = $countQuery->count(); // 获取总数

            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 重新构建查询，因为需要使用JOIN
            if ($userId > 0) {
                $queryBase = Db()->table($queryClauses['from'])
                           ->select($queryClauses['select'])
                           ->order('daily_cards.card_date', 'desc');
            } else {
                $queryBase->select('id, card_date as date, description, author, background_image_url, like_count as likeCount, comment_count as commentCount, 0 as isLiked, 0 as isFavorited');
            }

            // 最后调用 page (它会使用 $queryBase 当前的 SELECT 设置并执行查询)
            $cards = $queryBase->page($page, $page_size); // page 方法现在会获取正确的字段

            if (empty($cards)) {
                return ["status" => "empty", "msg" => "暂无卡片数据"];
            }

            // 注意：Db::page 返回的是 fetchAll 的结果，所以不需要再调用 fetchAll
            // $cards = $query->fetchAll(); // 这行是多余的，因为 page 内部处理了

            return ["status" => "ok", "data" => ["list" => $cards, "total" => \core\Page::$count ?? $total]]; // 使用 \core\Page::$count

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取卡片列表失败"];
        }
    }

    /**
     * @apiName 获取动态/日记列表 (分页)
     * @method get_feeds
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 10)
     * @param user_id int 要筛选的用户ID (可选, 0 表示不筛选)
     * @param type string 类型筛选 ('feed', 'diary', 'all') (可选, 默认 'feed')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feeds($page = 1, $page_size = 10, $uid = 0, $token = "", $user_id = 0, $filter_type = 'latest', $type = 'feed')
    {
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
         if (!check($user_id, "integt0")) { // 允许为 0
             return ["status" => "error", "msg" => "用户筛选参数错误"];
        }
        $page = (int)$page;
        $page_size = (int)$page_size;
        $filterUserId = (int)$user_id;

        $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        dbConn();
        try {
             $this->exception_log("Attempting to get feeds. Page: {$page}, Size: {$page_size}, CurrentUID: {$currentUserId}, FilterUID: {$filterUserId}, FilterType: {$filter_type}");

            // 使用私有方法构建查询子句
            $queryClauses = $this->buildFeedQueryClauses($currentUserId);

            // --- Apply WHERE condition ---
            $where = "1";
            $prepareParam = []; // Initialize prepareParam array
            $baseWhereConditions = []; // Array to hold actual WHERE conditions

            if ($filter_type === 'following') {
                if ($currentUserId <= 0) {
                    return ["status" => "relogin", "msg" => "请先登录以查看关注内容"];
                }
                $followingUids = Db()->table('user_guanzhu')
                                      ->where('uid = :currentUserId')
                                      ->prepareParam([':currentUserId' => $currentUserId])
                                      ->fetchAll();
                if (empty($followingUids)) {
                    return ["status" => "empty", "msg" => "您还没有关注任何人"];
                }
                $followingUidList = array_column($followingUids, 'to_uid');
                if (empty($followingUidList)) {
                     return ["status" => "empty", "msg" => "您关注的人还没有发布内容"];
                }
                $followParamIndex = 0;
                $followingPlaceholdersNamed = [];
                foreach ($followingUidList as $followUid) {
                    $paramName = ':followUid' . $followParamIndex++;
                    $followingPlaceholdersNamed[] = $paramName;
                    $prepareParam[$paramName] = $followUid;
                }
                // $where .= " AND feeds.user_id IN (" . implode(',', $followingPlaceholdersNamed) . ")"; // Removed direct modification
                 if (!empty($followingPlaceholdersNamed)) {
                    $baseWhereConditions[] = "feeds.user_id IN (" . implode(',', $followingPlaceholdersNamed) . ")";
                }

            } elseif ($filterUserId > 0) {
                // Filter by a specific user_id
                // $where .= " AND feeds.user_id = :filterUserId"; // Removed direct modification
                $baseWhereConditions[] = "feeds.user_id = :filterUserId";
                $prepareParam[':filterUserId'] = $filterUserId;
            }
            // Always add status filter
            $baseWhereConditions[] = "feeds.status = 'published'";

            // Add type filter
            if ($type !== 'all') {
                $baseWhereConditions[] = "feeds.type = :type";
                $prepareParam[':type'] = $type;
            }

            // Build WHERE clause string from conditions
            $whereClauseString = "";
            if (!empty($baseWhereConditions)) {
                 $whereClauseString = " WHERE " . implode(" AND ", $baseWhereConditions);
                 // Update $where for main query SQL string (used later)
                 $where = $whereClauseString;
                 // Note: If $where started as "1", this replaces it.
            } else {
                 // Should not happen now because status is always added
                 $where = ""; // No WHERE clause if no conditions
            }


            // --- Determine Order ---
            // 根据分类参数决定排序方式 - 支持多种参数传递方式
            $categoryParam = null;
            if (isset($_REQUEST['category'])) {
                $categoryParam = $_REQUEST['category'];
            } elseif (isset($_POST['category'])) {
                $categoryParam = $_POST['category'];
            } elseif ($filter_type === 'hot') {
                $categoryParam = 'hot';
            }

            if ($categoryParam === 'hot') {
                // 最热排序：按点赞数和评论数总和降序，再按创建时间降序
                $orderByClause = "ORDER BY (feeds.like_count + feeds.comment_count) DESC, feeds.created_at DESC";
                $this->exception_log("使用最热排序: (like_count + comment_count) DESC, category={$categoryParam}");
            } else {
                // 默认最新排序：按创建时间降序
                $orderByClause = "ORDER BY feeds.created_at DESC";
                $this->exception_log("使用最新排序: created_at DESC, category={$categoryParam}");
            }

            // --- Calculate Total Count ---
            // Use the constructed $whereClauseString directly
            $countSql = "SELECT COUNT(1) as totalCount FROM feeds" . $whereClauseString;
            $this->exception_log("Executing count query for feeds. SQL: {$countSql} with params: " . json_encode($prepareParam));
            // Pass $prepareParam, it's needed if filters were applied
            $countResult = Db::_fetch($countSql, $prepareParam);
            $total = $countResult ? (int)$countResult['totalCount'] : 0;
            $this->exception_log("Count query result: {$total}");
            // --- End Count Calculation ---

            // --- Calculate LIMIT ---
            $limitOffset = ($page - 1) * $page_size;
            $limitRowCount = $page_size;
            $limitClause = "LIMIT {$limitOffset}, {$limitRowCount}";

            // --- Construct the final SQL manually ---
            // Use the final $where string which includes the WHERE keyword if needed
            $sql = "SELECT {$queryClauses['select']} FROM {$queryClauses['from']}{$where} {$orderByClause} {$limitClause}";
            $this->exception_log("Executing prepared page query: {$sql}");

            // 合并查询参数
            $allParams = array_merge($prepareParam, $queryClauses['params']);

            // --- Execute the prepared query ---
            $feeds = Db::_fetchAll($sql, $allParams); // Pass allParams to _fetchAll
            $this->exception_log("Prepared query executed. Found " . count($feeds) . " feeds.");

            // --- Process Results ---
            if (empty($feeds)) {
                // Adjust empty message based on filter type
                $emptyMsg = "暂无日记数据";
                if ($filter_type === 'following') {
                    $emptyMsg = "您关注的人还没有发布内容";
                } else if ($filterUserId > 0) {
                     $emptyMsg = "该用户还没有发布内容";
                }
                return ["status" => "empty", "msg" => $emptyMsg];
            }

            foreach ($feeds as &$feed) {
                $feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->exception_log("JSON decode error for feed ID: {$feed['id']}. Error: " . json_last_error_msg());
                    $feed['images'] = [];
                }
                unset($feed['images_json']);
                $feed['user'] = [
                    'uid' => $feed['user_id'],
                    'nickname' => $feed['nickname'],
                    'avatar_url' => $feed['avatar_url']
                ];
                unset($feed['nickname'], $feed['avatar_url']);
            }

            // Return data with manually obtained total
            return ["status" => "ok", "data" => ["list" => $feeds, "total" => $total]];

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $errorTrace = $e->getTraceAsString();
            // Log the last attempted SQL if available
            $lastSql = is_array(Db::$lastSql) ? json_encode(Db::$lastSql) : Db::$lastSql;
            $this->exception_log("Error in get_feeds: {$errorMessage}
Last SQL: {$lastSql}
Trace: {$errorTrace}");
            // Restore standard user-facing error message
            return ["status" => "error", "msg" => "获取日记列表失败，请稍后重试"];
        }
    }


    /**
     * @apiName 获取日记详情
     * @method get_feed_detail
     * @POST
     * @param id int 日记ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feed_detail($id, $uid = 0, $token = "")
    {
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        $feedId = (int)$id;

         $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        dbConn();
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildFeedQueryClauses($currentUserId);

            // 合并参数
            $allParams = array_merge([':feedId' => $feedId], $queryClauses['params']);

            // 执行查询
            $query = Db()->table($queryClauses['from'])
                 ->select($queryClauses['select'])
                 ->where('feeds.id = :feedId') // 使用别名 feeds.
                 ->prepareParam($allParams);

            // 最后执行 fetch
            $feed = $query->fetch();

            if (empty($feed)) {
                return ["status" => "empty", "msg" => "日记不存在或已被删除"];
            }

            // 处理图片 JSON 和用户信息
            $feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
            if (json_last_error() !== JSON_ERROR_NONE) $feed['images'] = [];
            unset($feed['images_json']);
            $feed['user'] = [
                'uid' => $feed['user_id'],
                'nickname' => $feed['nickname'],
                'avatar_url' => $feed['avatar_url'] // 使用 select 中的别名
            ];
            unset($feed['nickname'], $feed['avatar_url']);

            // 可以考虑在此处获取评论列表 (参考 Huodong.php 的 get_pingjia)
            // $feed['comments'] = Db()->table('feed_comments')... ->fetchAll();

            return ["status" => "ok", "data" => $feed];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取日记详情失败"];
        }
    }


    /**
     * @apiName 发布动态/日记
     * @method publish_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param content string 内容
     * @param images array 图片URL数组 (可选)
     * @param location string 位置信息 (可选)
     * @param tags string 标签 (可选, 逗号分隔或数组)
     * @param privacy string 隐私设置 ('public', 'private') (可选, 默认 'public')
     * @param type string 类型 ('feed', 'diary') (可选, 默认 'feed')
     * @return {"status":"ok","msg":"发布成功","data":{"feed_id": N}} | {"status":"error","msg":"..."}
     */
    public function publish_feed($uid, $token, $content, $images = [], $location = "", $tags = "", $privacy = 'public', $type = 'feed')
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        $userId = (int)$uid;

        $content = trim($content);
        if (empty($content)) {
             return ["status" => "error", "msg" => "内容不能为空"];
        }
        if (mb_strlen($content) > 5000) { // 检查长度
             return ["status" => "error", "msg" => "内容过长"];
        }

        // 记录接收到的图片参数类型
        $this->exception_log("publish_feed received images param type: " . gettype($images));

        // 处理图片参数
        $validImages = [];

        // 如果是空值，直接设置为空数组
        if (empty($images)) {
            $images = [];
        }
        // 如果已经是数组，处理每个元素
        else if (is_array($images)) {
            foreach ($images as $img) {
                // 如果是字符串且是URL，添加到有效图片列表
                if (is_string($img) && check($img, "url")) {
                    $validImages[] = $img;
                }
                // 如果是对象或数组，检查是否有status和data属性
                else if (is_array($img) || is_object($img)) {
                    $imgArray = (array)$img;
                    // 如果有status属性且为ok，并且有data属性
                    if (isset($imgArray['status']) && $imgArray['status'] === 'ok' && isset($imgArray['data'])) {
                        if (is_string($imgArray['data']) && check($imgArray['data'], "url")) {
                            $validImages[] = $imgArray['data'];
                        }
                    }
                    // 如果有url属性
                    else if (isset($imgArray['url']) && is_string($imgArray['url']) && check($imgArray['url'], "url")) {
                        $validImages[] = $imgArray['url'];
                    }
                }
            }
        }
        // 如果是字符串，尝试解析
        else if (is_string($images)) {
            // 如果是JSON数组字符串
            if (substr($images, 0, 1) === '[' && substr($images, -1) === ']') {
                $decodedImages = json_decode($images, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedImages)) {
                    foreach ($decodedImages as $img) {
                        if (is_string($img) && check($img, "url")) {
                            $validImages[] = $img;
                        }
                    }
                }
            }
            // 如果是单个URL
            else if (check($images, "url")) {
                $validImages[] = $images;
            }
        }

        // 使用处理后的有效图片数组
        $images = $validImages;

        // Now $images should be an array (possibly empty)
        // Optional: Validate image URLs within the array
        foreach ($images as $img) {
            // Allow empty strings or null if that's possible from frontend
            if (!empty($img) && !check($img, "url")) {
                 $this->exception_log("Invalid image URL detected: " . htmlspecialchars($img));
                 // Decide whether to return error or just ignore the invalid URL
                 // return ["status" => "error", "msg" => "图片地址格式错误: " . htmlspecialchars($img)];
            }
        }
        // Filter out potentially empty/invalid URLs if choosing to ignore them
        $images = array_filter($images, function($url) { return !empty($url) && check($url, "url"); });

        $tagsString = is_array($tags) ? implode(',', $tags) : $tags;

        // Validate privacy setting
        $validPrivacy = ['public', 'private']; // Initially support public and private
        if (!in_array($privacy, $validPrivacy)) {
            $privacy = 'public'; // Default to public if invalid value provided
        }

        $data = [
            'user_id' => $userId,
            'type' => $type, // 添加类型字段
            'content' => ':content', // 使用预处理
            'images_json' => count($images) > 0 ? json_encode($images, JSON_UNESCAPED_UNICODE) : null,
            'location' => ':location',
            'tags' => ':tags',
            'like_count' => 0, // 初始值
            'comment_count' => 0, // 初始值
            'status' => 'published', // Default status
            'privacy' => $privacy,       // Save privacy setting
            'created_at' => date('Y-m-d H:i:s'), // 使用当前时间
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $prepareParam = [
            ':content' => htmlspecialchars($content), // 防 XSS
            ':location' => !empty($location) ? htmlspecialchars($location) : null,
            ':tags' => !empty($tagsString) ? htmlspecialchars($tagsString) : null
        ];

        dbConn();
        try {
            // 替换insertGetId为insert + lastInsertId
            $insertResult = Db()->table('feeds')->prepareParam($prepareParam)->insert($data);
            if (!$insertResult) {
                throw new \Exception("插入日记数据失败");
            }
            $feedId = Db()->lastInsertId();
            if (!$feedId) {
                throw new \Exception("获取插入ID失败");
            }

            $this->user_log($userId, "发布日记【{$feedId}】");
            return ["status" => "ok", "msg" => "发布成功", "data" => ['feed_id' => $feedId]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "发布失败，请稍后重试"];
        }
    }

     /**
     * @apiName 发布日卡 (管理员)
     * @method publish_card
     * @POST
     * @param uid int 管理员用户ID
     * @param token string 管理员token
     * @param card_date string 日期 YYYY-MM-DD
     * @param description string 描述
     * @param author string 作者 (可选)
     * @param background_image_url string 背景图URL (可选)
     * @return {"status":"ok","msg":"发布成功","data":{"card_id": N}} | {"status":"error","msg":"..."}
     */
    public function publish_card($uid, $token, $card_date, $description, $author = "", $background_image_url = "")
    {
        // 权限校验 (需要您根据项目实际情况实现 isAdmin 检查)
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        /* // 假设有一个 isAdmin 方法检查权限
        if (!$this->isAdmin($uid)) {
             return ["status" => "error", "msg" => "无权操作", "code" => 403];
        }
        */

        $description = trim($description);
        if (empty($card_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $card_date)) {
            return ["status" => "error", "msg" => "日期格式不正确"];
        }
        if (empty($description)) {
            return ["status" => "error", "msg" => "描述不能为空"];
        }
        if (!empty($background_image_url) && !check($background_image_url, "url")) {
             return ["status" => "error", "msg" => "背景图URL格式不正确"];
        }

        dbConn();
        try {
            // 检查日期是否已存在
            $existingCard = Db()->table('daily_cards')->where("card_date = :card_date")->prepareParam([':card_date' => $card_date])->fetch();
            if ($existingCard) {
                return ["status" => "error", "msg" => "该日期的卡片已存在"];
            }

            $data = [
                'card_date' => $card_date,
                'description' => ':description',
                'author' => ':author',
                'background_image_url' => ':background_image_url',
                'like_count' => 0,
                'comment_count' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
             $prepareParam = [
                ':description' => htmlspecialchars($description),
                ':author' => !empty($author) ? htmlspecialchars($author) : null,
                ':background_image_url' => !empty($background_image_url) ? htmlspecialchars($background_image_url) : null
            ];

            $cardId = Db()->table('daily_cards')->prepareParam($prepareParam)->insertGetId($data);
             if (!$cardId) {
                 throw new \Exception("插入日卡数据失败");
            }

            $this->user_log($uid, "发布日卡【{$cardId} - {$card_date}】");
            return ["status" => "ok", "msg" => "发布成功", "data" => ['card_id' => $cardId]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "发布日卡失败"];
        }
    }


    /**
     * @apiName 点赞/取消点赞 日记
     * @method like_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日记ID
     * @return {"status":"ok","msg":"点赞/取消成功","data":{"isLiked": boolean}} | {"status":"error","msg":"..."}
     */
    public function like_feed($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        $userId = (int)$uid;
        $feedId = (int)$id;

        dbConn();
        try {
            // 检查日记是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                 return ["status" => "error", "msg" => "日记不存在"];
            }
            // 检查是否已点赞
            $like = Db()->table('user_feed_likes')
                ->where('user_id = :userId AND feed_id = :feedId')
                ->prepareParam([':userId' => $userId, ':feedId' => $feedId])
                ->fetch();

            $isLikedAfterToggle = false;
            if ($like) {
                // 已点赞，取消点赞
                $deleteResult = Db()->table('user_feed_likes')->where('id = :likeId')->prepareParam([':likeId' => $like['id']])->del();
                if (!$deleteResult) throw new \Exception("取消点赞记录失败");

                // 更新日记点赞数 (避免负数)
                $updateSql = "UPDATE `feeds` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE id = :feedId";
                $updateResult = Db()->_exec($updateSql, [':feedId' => $feedId]);
                if ($updateResult === false) throw new \Exception("更新日记点赞数失败");

                $isLikedAfterToggle = false;
                $msg = "取消点赞成功";
            } else {
                // 未点赞，添加点赞
                $insertData = [
                    'user_id' => $userId,
                    'feed_id' => $feedId,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_feed_likes')->insert($insertData);
                 if (!$insertResult) throw new \Exception("添加点赞记录失败");

                // 更新日记点赞数
                $updateSql = "UPDATE `feeds` SET `like_count` = `like_count` + 1 WHERE id = :feedId";
                $updateResult = Db()->_exec($updateSql, [':feedId' => $feedId]);
                if ($updateResult === false) throw new \Exception("更新日记点赞数失败");

                $isLikedAfterToggle = true;
                $msg = "点赞成功";
            }
            $this->user_log($userId, ($isLikedAfterToggle ? '点赞' : '取消点赞') . "日记【{$feedId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isLiked' => $isLikedAfterToggle]];

        } catch (\Exception $e) {
            $this->exception_log("点赞日记失败: " . $e->getMessage() . " | feedId={$feedId}, userId={$userId}");
            return ["status" => "error", "msg" => "操作失败，请稍后重试"];
        }
    }


    /**
     * @apiName 评论日记
     * @method comment_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param feed_id int 日记ID
     * @param content string 评论内容
     * @param parent_id int 回复的评论ID (可选, 0 或 null 表示直接评论)
     * @return {"status":"ok","msg":"评论成功","data":{"comment_id": N}} | {"status":"error","msg":"..."}
     */
    public function comment_feed($uid, $token, $feed_id, $content, $parent_id = null)
    {
                $userId = (int)$uid;
                $feedId = (int)$feed_id;
                $parentId = $parent_id !== null ? (int)$parent_id : null;
                $this->exception_log("准备插入日记评论数据1: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));
         if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
         if (!check($feed_id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        if($parent_id == "null"){
            $parent_id = null;
        }
         // parent_id 可以为 null 或大于0的整数
         if ($parent_id !== null && (!is_numeric($parent_id) || intval($parent_id) <= 0)) {
             return ["status" => "error", "msg" => "回复参数错误"];
         }

        // 内容处理
        $content = trim($content);

        if (empty($content)) {
             return ["status" => "error", "msg" => "评论内容不能为空"];
        }
         if (mb_strlen($content) > 1000) { // 限制评论长度
             return ["status" => "error", "msg" => "评论内容过长"];
        }
        $this->exception_log("准备插入日记评论数据1: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));

        dbConn();
        $this->exception_log("准备插入日记评论数据2: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));

        try {
            // 检查日记是否存在
             $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
             if (!$feedExists) {
                  return ["status" => "error", "msg" => "评论的日记不存在"];
             }
             $this->exception_log("准备插入日记评论数据3: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));

             // 如果是回复，检查父评论是否存在于同一日记下
            if ($parentId !== null) { // 检查parentId是否不为null
                $parentComment = Db()->table('feed_comments')
                                   ->where('id = :parentId AND feed_id = :feedId')
                                   ->prepareParam([':parentId' => $parentId, ':feedId' => $feedId])
                                   ->fetch();
                if (!$parentComment) {
                     return ["status" => "error", "msg" => "回复的评论不存在或不属于该日记"];
                }
            }
            $this->exception_log("准备插入日记评论数据4: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));


            // 使用直接插入方式
            $data = [
                'feed_id' => $feedId,
                'user_id' => $userId,
                'content' => htmlspecialchars($content), // 防 XSS
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            if($parentId !== null){
                $data['parent_id'] = $parentId;
            }

            // 使用insert方法插入评论数据
            $insertResult = Db()->table('feed_comments')->insert($data);
            if (!$insertResult) {
                throw new \Exception("插入评论数据失败");
            }
            $this->exception_log("准备插入日记评论数据5: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));

            // 更新日记评论数
            $updateSql = "UPDATE `feeds` SET `comment_count` = `comment_count` + 1 WHERE id = :feedId";
            $updateResult = Db()->_exec($updateSql, [':feedId' => $feedId]);
            if ($updateResult === false) {
                throw new \Exception("更新日记评论数失败");
            }
            $this->exception_log("准备插入日记评论数据6: feedId={$feedId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));


            $this->user_log($userId, "评论日记【{$feedId}】");
            // 可以考虑返回新评论的更多信息，而不仅仅是ID
            return ["status" => "ok", "msg" => "评论成功"];

        } catch (\Exception $e) {
            // 记录详细错误信息
            $errorDetails = "评论日记失败: " . $e->getMessage() .
                           "\nFile: " . $e->getFile() .
                           "\nLine: " . $e->getLine() .
                           "\nTrace: " . $e->getTraceAsString() .
                           "\nParams: feedId={$feedId}, userId={$userId}, content长度=" . strlen($content);
            $this->exception_log($errorDetails);

            // 返回更具体的错误信息
            return ["status" => "error", "msg" => "评论失败: " . $e->getMessage()];
        }
    }

    // --- 新增：摘录相关方法 ---

    /**
     * @apiName 创建摘录
     * @method create_quote
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param content string 摘录内容
     * @param author string 作者 (可选)
     * @param source string 出处 (可选)
     * @param tags string 标签 (可选, 逗号分隔)
     * @param privacy string 隐私设置 ('public' 或 'private', 默认 'public')
     * @param images array 图片URL数组 (可选)
     * @return {"status":"ok","msg":"摘录成功","data":{"quote_id": N}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function create_quote($uid, $token, $content, $author = "", $source = "", $tags = "", $privacy = 'public', $images = [])
    {
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        $userId = (int)$uid;

        // 2. 内容验证
        $content = trim($content);
        if (empty($content)) {
            return ["status" => "error", "msg" => "摘录内容不能为空"];
        }
        if (mb_strlen($content) > 10000) { // 限制长度 (按需调整)
            return ["status" => "error", "msg" => "内容过长"];
        }

        // 3. 清理和验证其他可选参数
        $author = !empty($author) ? trim(htmlspecialchars($author)) : null;
        $source = !empty($source) ? trim(htmlspecialchars($source)) : null;
        $tags = !empty($tags) ? trim(htmlspecialchars($tags)) : null; // 保持为字符串

        // 4. 验证隐私设置
        $validPrivacy = ['public', 'private'];
        if (!in_array($privacy, $validPrivacy)) {
            $privacy = 'public'; // 默认公开
        }

        // 5. 处理图片数据
        $imagesJson = null;
        if (!empty($images)) {
            if (is_array($images)) {
                // 验证图片URL格式
                $validImages = [];
                foreach ($images as $imageUrl) {
                    if (is_string($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                        $validImages[] = $imageUrl;
                    }
                }
                if (!empty($validImages)) {
                    $imagesJson = json_encode($validImages, JSON_UNESCAPED_UNICODE);
                }
            } elseif (is_string($images)) {
                // 如果传入的是JSON字符串，验证格式
                $decodedImages = json_decode($images, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedImages)) {
                    $validImages = [];
                    foreach ($decodedImages as $imageUrl) {
                        if (is_string($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            $validImages[] = $imageUrl;
                        }
                    }
                    if (!empty($validImages)) {
                        $imagesJson = json_encode($validImages, JSON_UNESCAPED_UNICODE);
                    }
                }
            }
        }

        // 6. 准备插入数据
        $data = [
            'user_id' => $userId,
            'content' => htmlspecialchars($content), // 防 XSS
            'images_json' => $imagesJson,
            'author' => $author,
            'source' => $source,
            'tags' => $tags,
            'privacy' => $privacy,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 7. 执行插入
        dbConn();
        try {
            // 替换insertGetId为insert + lastInsertId
            $insertResult = Db()->table('quotes')->insert($data);
            if (!$insertResult) {
                throw new \Exception("插入摘录数据失败");
            }
            $quoteId = Db()->lastInsertId();
            if (!$quoteId) {
                throw new \Exception("获取插入ID失败");
            }

            $this->user_log($userId, "创建摘录【{$quoteId}】");
            return ["status" => "ok", "msg" => "摘录成功", "data" => ['quote_id' => $quoteId]];

        } catch (\Exception $e) {
            $this->exception_log("创建摘录失败: " . $e->getMessage());
            return ["status" => "error", "msg" => "摘录保存失败，请稍后重试"];
        }
    }

    // --- 后续添加 get_quotes, update_quote, delete_quote 等 ---

    /**
     * @apiName 点赞/取消点赞 日卡
     * @method like_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日卡ID
     * @return {"status":"ok","msg":"点赞/取消成功","data":{"isLiked": boolean}} | {"status":"error","msg":"..."}
     */
    public function like_card($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $userId = (int)$uid;
        $cardId = (int)$id;

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                 return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 检查是否已点赞
            $like = Db()->table('user_card_likes')
                ->where('user_id = :userId AND card_id = :cardId')
                ->prepareParam([':userId' => $userId, ':cardId' => $cardId])
                ->fetch();

            $isLikedAfterToggle = false;
            if ($like) {
                // 已点赞，取消点赞
                $deleteResult = Db()->table('user_card_likes')->where('id = :likeId')->prepareParam([':likeId' => $like['id']])->del();
                if (!$deleteResult) throw new \Exception("取消点赞记录失败");

                // 更新日卡点赞数 (避免负数)
                $updateSql = "UPDATE `daily_cards` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE id = :cardId";
                $updateResult = Db()->_exec($updateSql, [':cardId' => $cardId]);
                if ($updateResult === false) throw new \Exception("更新日卡点赞数失败");

                $isLikedAfterToggle = false;
                $msg = "取消点赞成功";

            } else {
                // 未点赞，添加点赞
                $insertData = [
                    'user_id' => $userId,
                    'card_id' => $cardId,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_card_likes')->insert($insertData);
                 if (!$insertResult) throw new \Exception("添加点赞记录失败");

                // 更新日卡点赞数
                $updateSql = "UPDATE `daily_cards` SET `like_count` = `like_count` + 1 WHERE id = :cardId";
                $updateResult = Db()->_exec($updateSql, [':cardId' => $cardId]);
                if ($updateResult === false) throw new \Exception("更新日卡点赞数失败");

                $isLikedAfterToggle = true;
                $msg = "点赞成功";
            }

            $this->user_log($userId, ($isLikedAfterToggle ? '点赞' : '取消点赞') . "日卡【{$cardId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isLiked' => $isLikedAfterToggle]];

        } catch (\Exception $e) {
            $this->exception_log("点赞日卡失败: " . $e->getMessage() . " | cardId={$cardId}, userId={$userId}");
            return ["status" => "error", "msg" => "操作失败，请稍后重试"];
        }
    }

    /**
     * @apiName 收藏/取消收藏 日卡
     * @method favorite_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日卡ID
     * @return {"status":"ok","msg":"收藏/取消成功","data":{"isFavorited": boolean}} | {"status":"error","msg":"..."}
     */
    public function favorite_card($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $userId = (int)$uid;
        $cardId = (int)$id;

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                 return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 检查是否已收藏
            $favorite = Db()->table('user_favorites')
                ->where("user_id = :userId AND item_id = :cardId AND item_type = 'card'")
                ->prepareParam([':userId' => $userId, ':cardId' => $cardId])
                ->fetch();

            $isFavoritedAfterToggle = false;
            if ($favorite) {
                // 已收藏，取消收藏
                $deleteResult = Db()->table('user_favorites')->where("id = :id")->prepareParam([':id' => $favorite['id']])->del();
                if (!$deleteResult) throw new \Exception("取消收藏记录失败");

                $isFavoritedAfterToggle = false;
                $msg = "取消收藏成功";

            } else {
                // 未收藏，添加收藏
                $insertData = [
                    'user_id' => $userId,
                    'item_id' => $cardId,
                    'item_type' => 'card',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_favorites')->insert($insertData);
                 if (!$insertResult) throw new \Exception("添加收藏记录失败");

                $isFavoritedAfterToggle = true;
                $msg = "收藏成功";
            }

            $this->user_log($userId, ($isFavoritedAfterToggle ? '收藏' : '取消收藏') . "日卡【{$cardId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isFavorited' => $isFavoritedAfterToggle]];

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "收藏操作失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 收藏/取消收藏 动态
     * @method favorite_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 动态ID
     * @return {"status":"ok","msg":"收藏/取消成功","data":{"isFavorited": boolean}} | {"status":"error","msg":"..."}
     */
    public function favorite_feed($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的动态ID"];
        }
        $userId = (int)$uid;
        $feedId = (int)$id;

        dbConn();
        try {
            // 检查动态是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                 return ["status" => "error", "msg" => "动态不存在"];
            }

            // 检查是否已收藏
            $favorite = Db()->table('user_favorites')
                ->where("user_id = :userId AND item_id = :feedId AND item_type = 'feed'")
                ->prepareParam([':userId' => $userId, ':feedId' => $feedId])
                ->fetch();

            $isFavoritedAfterToggle = false;
            if ($favorite) {
                // 已收藏，取消收藏
                $deleteResult = Db()->table('user_favorites')->where("id = :id")->prepareParam([':id' => $favorite['id']])->del();
                if (!$deleteResult) throw new \Exception("取消收藏记录失败");

                $isFavoritedAfterToggle = false;
                $msg = "取消收藏成功";

            } else {
                // 未收藏，添加收藏
                $insertData = [
                    'user_id' => $userId,
                    'item_id' => $feedId,
                    'item_type' => 'feed',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_favorites')->insert($insertData);
                 if (!$insertResult) throw new \Exception("添加收藏记录失败");

                $isFavoritedAfterToggle = true;
                $msg = "收藏成功";
            }

            $this->user_log($userId, ($isFavoritedAfterToggle ? '收藏' : '取消收藏') . "动态【{$feedId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isFavorited' => $isFavoritedAfterToggle]];

        } catch (\Exception $e) {
            return ["status" => "error", "msg" => "收藏操作失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 评论日卡
     * @method comment_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param card_id int 日卡ID
     * @param content string 评论内容
     * @param parent_id int 回复的评论ID (可选, 0 或 null 表示直接评论)
     * @return {"status":"ok","msg":"评论成功","data":{"comment_id": N}} | {"status":"error","msg":"..."}
     */
    public function comment_card($uid, $token, $card_id, $content, $parent_id = null)
    {
         if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
         if (!check($card_id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        if($parent_id == "null"){
            $parent_id = null;
        }
         // parent_id 可以为 null 或大于0的整数
         if ($parent_id !== null && (!is_numeric($parent_id) || intval($parent_id) <= 0)) {
             return ["status" => "error", "msg" => "回复参数错误"];
         }

        $userId = (int)$uid;
        $cardId = (int)$card_id;
        // 修复parent_id处理，确保为整数或null
        $parentId = $parent_id !== null ? (int)$parent_id : null; // 保持为整数或null
        $content = trim($content);

        if (empty($content)) {
             return ["status" => "error", "msg" => "评论内容不能为空"];
        }
         if (mb_strlen($content) > 1000) { // 限制评论长度
             return ["status" => "error", "msg" => "评论内容过长"];
        }

        dbConn();
        try {
            // 检查日卡是否存在
             $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
             if (!$cardExists) {
                  return ["status" => "error", "msg" => "评论的日卡不存在"];
             }

             // 如果是回复，检查父评论是否存在于同一日卡下
            if ($parentId !== null) { // 检查parentId是否不为null
                $parentComment = Db()->table('card_comments')
                                   ->where('id = :parentId AND card_id = :cardId')
                                   ->prepareParam([':parentId' => $parentId, ':cardId' => $cardId])
                                   ->fetch();
                if (!$parentComment) {
                     return ["status" => "error", "msg" => "回复的评论不存在或不属于该日卡"];
                }
            }

            // 记录调试日志
            $this->exception_log("准备插入评论数据: cardId={$cardId}, userId={$userId}, content长度=".strlen($content).", parentId=".($parentId ?? 'null'));

            // 使用直接插入方式
            $data = [
                'card_id' => $cardId,
                'user_id' => $userId,
                'content' => htmlspecialchars($content), // 防 XSS
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            if($parentId !== null){
                $data['parent_id'] = $parentId;
            }
            // 使用insert方法插入评论数据
            $insertResult = Db()->table('card_comments')->insert($data);
            if (!$insertResult) {
                throw new \Exception("插入评论数据失败");
            }

            // 更新日卡评论数
            $updateSql = "UPDATE `daily_cards` SET `comment_count` = `comment_count` + 1 WHERE id = :cardId";
            $updateResult = Db()->_exec($updateSql, [':cardId' => $cardId]);
            if ($updateResult === false) {
                throw new \Exception("更新日卡评论数失败");
            }

            $this->user_log($userId, "评论日卡【{$cardId}】");
            // 可以考虑返回新评论的更多信息，而不仅仅是ID
            return ["status" => "ok", "msg" => "评论成功",];

        } catch (\Exception $e) {
            // 记录详细错误信息
            $errorDetails = "评论日卡失败: " . $e->getMessage() .
                           "\nFile: " . $e->getFile() .
                           "\nLine: " . $e->getLine() .
                           "\nTrace: " . $e->getTraceAsString() .
                           "\nParams: cardId={$cardId}, userId={$userId}, content长度=" . strlen($content);
            $this->exception_log($errorDetails);

            // 返回更具体的错误信息
            return ["status" => "error", "msg" => "评论失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 获取日卡评论列表
     * @method get_card_comments
     * @POST
     * @param card_id int 日卡ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param sort_type string 排序方式 (默认 'latest', 可选 'hot')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_card_comments($card_id, $page = 1, $page_size = 20, $uid = 0, $token = "", $sort_type = 'latest')
    {
        if (!check($card_id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
            return ["status" => "error", "msg" => "分页参数错误"];
        }

        $cardId = (int)$card_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        $sort_type = in_array($sort_type, ['latest', 'hot']) ? $sort_type : 'latest';

        // 验证用户身份（可选）
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 构建查询
            $fromClause = 'card_comments cc LEFT JOIN user u ON u.uid = cc.user_id';
            $selectFields = 'cc.id, cc.card_id, cc.user_id, cc.content, cc.parent_id, cc.like_count, cc.created_at, u.nickname, u.avatar';

            // 获取评论总数
            $total = Db()->table('card_comments')->where("card_id = :cardId")->prepareParam([':cardId' => $cardId])->count();

            // 根据排序类型决定排序规则 - 修复排序逻辑
            $orderBy = $sort_type === 'hot' ? "cc.like_count DESC, cc.created_at DESC" : "cc.created_at DESC";

            // 获取评论列表
            $comments = Db()->table($fromClause)
                ->select($selectFields)
                ->where("cc.card_id = :cardId")
                ->prepareParam([':cardId' => $cardId])
                ->order($orderBy)
                ->page($page, $page_size);

            if (empty($comments)) {
                return ["status" => "empty", "msg" => "暂无评论"];
            }

            // 处理评论数据
            foreach ($comments as &$comment) {
                $comment['user'] = [
                    'uid' => $comment['user_id'],
                    'nickname' => $comment['nickname'],
                    'avatar' => $comment['avatar']
                ];
                unset($comment['nickname'], $comment['avatar']);

                // 如果是回复评论，获取父评论信息
                if (!empty($comment['parent_id'])) {
                    $parentComment = Db()->table('card_comments cc LEFT JOIN user u ON u.uid = cc.user_id')
                        ->select('cc.id, cc.content, cc.user_id, u.nickname')
                        ->where("cc.id = :parentId")
                        ->prepareParam([':parentId' => $comment['parent_id']])
                        ->fetch();

                    if ($parentComment) {
                        $comment['reply_to'] = [
                            'id' => $parentComment['id'],
                            'user_id' => $parentComment['user_id'],
                            'nickname' => $parentComment['nickname'],
                            'content' => mb_substr($parentComment['content'], 0, 20) . (mb_strlen($parentComment['content']) > 20 ? '...' : '')
                        ];
                    }
                }
            }

            return ["status" => "ok", "data" => ["list" => $comments, "total" => $total]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取评论失败"];
        }
    }

    /**
     * @apiName 获取日记评论列表
     * @method get_feed_comments
     * @POST
     * @param feed_id int 日记ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param sort_type string 排序方式 (默认 'latest', 可选 'hot')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feed_comments($feed_id, $page = 1, $page_size = 20, $uid = 0, $token = "", $sort_type = 'latest')
    {
        if (!check($feed_id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日记ID"];
        }
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
            return ["status" => "error", "msg" => "分页参数错误"];
        }

        $feedId = (int)$feed_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        $sort_type = in_array($sort_type, ['latest', 'hot']) ? $sort_type : 'latest';

        // 验证用户身份（可选）
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 检查日记是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                return ["status" => "error", "msg" => "日记不存在"];
            }

            // 构建查询
            $fromClause = 'feed_comments fc LEFT JOIN user u ON u.uid = fc.user_id';
            $selectFields = 'fc.id, fc.feed_id, fc.user_id, fc.content, fc.parent_id, fc.like_count, fc.created_at, u.nickname, u.avatar';

            // 获取评论总数
            $total = Db()->table('feed_comments')->where("feed_id = :feedId")->prepareParam([':feedId' => $feedId])->count();

            // 根据排序类型决定排序规则 - 修复排序逻辑
            $orderBy = $sort_type === 'hot' ? "fc.like_count DESC, fc.created_at DESC" : "fc.created_at DESC";

            // 获取评论列表
            $comments = Db()->table($fromClause)
                ->select($selectFields)
                ->where("fc.feed_id = :feedId")
                ->prepareParam([':feedId' => $feedId])
                ->order($orderBy)
                ->page($page, $page_size);

            if (empty($comments)) {
                return ["status" => "empty", "msg" => "暂无评论"];
            }

            // 处理评论数据
            foreach ($comments as &$comment) {
                $comment['user'] = [
                    'uid' => $comment['user_id'],
                    'nickname' => $comment['nickname'],
                    'avatar' => $comment['avatar']
                ];
                unset($comment['nickname'], $comment['avatar']);

                // 如果是回复评论，获取父评论信息
                if (!empty($comment['parent_id'])) {
                    $parentComment = Db()->table('feed_comments fc LEFT JOIN user u ON u.uid = fc.user_id')
                        ->select('fc.id, fc.content, fc.user_id, u.nickname')
                        ->where("fc.id = :parentId")
                        ->prepareParam([':parentId' => $comment['parent_id']])
                        ->fetch();

                    if ($parentComment) {
                        $comment['reply_to'] = [
                            'id' => $parentComment['id'],
                            'user_id' => $parentComment['user_id'],
                            'nickname' => $parentComment['nickname'],
                            'content' => mb_substr($parentComment['content'], 0, 20) . (mb_strlen($parentComment['content']) > 20 ? '...' : '')
                        ];
                    }
                }
            }

            return ["status" => "ok", "data" => ["list" => $comments, "total" => $total]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取评论失败"];
        }
    }

    /**
     * 处理未定义的方法
     */
    public function _empty()
    {
        return ["status" => "error", "msg" => "请求的方法不存在"];
        // 或者使用助手函数: return response_error('请求的方法不存在', null, 404);
    }

    // 如果需要 isAdmin 权限检查，可以在这里或 BaseController 中实现
    /*
    protected function isAdmin($userId) {
        // 实现管理员检查逻辑，例如查询用户角色表
        dbConn();
        $role = Db()->table('user')->where('id = :userId', [':userId' => $userId])->getColumn('role');
        return $role === 'admin'; // 假设 admin 角色表示管理员
    }
    */

    /*
    * @apiName 点赞评论
    * @method like_comment
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param comment_id string 评论ID
    * @param comment_type string 评论类型：feed=动态评论，card=日卡评论，quote=摘录评论
    * @return {"status":"ok","msg":"点赞成功"}
    */
    public function like_comment($uid, $token, $comment_id, $comment_type)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($comment_id) ||
            !check($comment_id, "intgt0") ||
            empty($comment_type) ||
            !in_array($comment_type, ['feed', 'card', 'quote'])
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $comment_id = (int)$comment_id;
        dbConn();

        try {
            // 记录操作开始的日志
            $this->exception_log("用户{$uid}尝试操作{$comment_type}评论点赞，评论ID：{$comment_id}");
            
            // 检查是否已经点赞
            $existing = Db()->table("comment_likes")->where("user_id={$uid} AND comment_id={$comment_id} AND comment_type='{$comment_type}'")->fetch();

            if ($existing) {
                // 取消点赞
                Db()->table("comment_likes")->where("user_id={$uid} AND comment_id={$comment_id} AND comment_type='{$comment_type}'")->del();

                // 更新评论表的点赞数
                $table_map = [
                    'feed' => 'feed_comments',
                    'card' => 'card_comments',
                    'quote' => 'quote_comments'
                ];

                if (isset($table_map[$comment_type])) {
                    // 使用原生SQL执行点赞数减1操作
                    $tableName = $table_map[$comment_type];
                    $sql = "UPDATE `{$tableName}` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE `id` = :comment_id";
                    Db()->_exec($sql, [':comment_id' => $comment_id]);
                }
                
                // 记录取消点赞成功日志
                $this->exception_log("用户{$uid}取消{$comment_type}评论点赞成功，评论ID：{$comment_id}");

                return ["status" => "ok", "msg" => "取消点赞成功", "action" => "unlike"];
            } else {
                // 添加点赞
                Db()->table("comment_likes")->insert([
                    "user_id" => $uid,
                    "comment_id" => $comment_id,
                    "comment_type" => $comment_type,
                    "created_at" => date('Y-m-d H:i:s')
                ]);

                // 更新评论表的点赞数
                $table_map = [
                    'feed' => 'feed_comments',
                    'card' => 'card_comments',
                    'quote' => 'quote_comments'
                ];

                if (isset($table_map[$comment_type])) {
                    // 使用原生SQL执行点赞数加1操作
                    $tableName = $table_map[$comment_type];
                    $sql = "UPDATE `{$tableName}` SET `like_count` = `like_count` + 1 WHERE `id` = :comment_id";
                    Db()->_exec($sql, [':comment_id' => $comment_id]);
                }
                
                // 记录点赞成功日志
                $this->exception_log("用户{$uid}对{$comment_type}评论点赞成功，评论ID：{$comment_id}");

                return ["status" => "ok", "msg" => "点赞成功", "action" => "like"];
            }
        } catch (\Exception $e) {
            // 记录异常日志
            $this->exception_log("评论点赞操作异常：用户{$uid}，评论类型{$comment_type}，评论ID{$comment_id}，错误：" . $e->getMessage());
            return ["status" => "error", "msg" => "操作失败"];
        }
    }

    /*
    * @apiName 获取评论点赞状态
    * @method get_comment_like_status
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param comment_ids string 评论ID列表，逗号分隔
    * @param comment_type string 评论类型：feed=动态评论，card=日卡评论，quote=摘录评论
    * @return {"status":"ok","data":{"liked_comments":[1,2,3]}}
    */
    public function get_comment_like_status($uid, $token, $comment_ids, $comment_type)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($comment_ids) ||
            empty($comment_type) ||
            !in_array($comment_type, ['feed', 'card', 'quote'])
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $comment_ids_array = explode(',', $comment_ids);
        $comment_ids_array = array_map('intval', $comment_ids_array);
        $comment_ids_str = implode(',', $comment_ids_array);

        dbConn();

        try {
            $liked_comments = Db()->table("comment_likes")
                ->select("comment_id")
                ->where("user_id={$uid} AND comment_type='{$comment_type}' AND comment_id IN ({$comment_ids_str})")
                ->fetchAll();

            $liked_ids = array_column($liked_comments, 'comment_id');
            $liked_ids = array_map('intval', $liked_ids);

            return ["status" => "ok", "data" => ["liked_comments" => $liked_ids]];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取点赞状态失败"];
        }
    }

    public function __destruct()
    {
        // 如果需要执行清理操作
    }

    /**
     * @apiName 删除动态
     * @method delete_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param feed_id int 动态ID
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function delete_feed($uid, $token, $feed_id)
    {
        // 添加详细日志，帮助排查问题
        $this->exception_log("删除动态请求 - uid: {$uid}, feed_id: {$feed_id}");
        
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            $this->exception_log("删除动态参数错误 - uid或token无效");
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            $this->exception_log("删除动态授权失败 - 登录信息验证失败");
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($feed_id, "intgt0")) {
            $this->exception_log("删除动态参数错误 - 动态ID无效: {$feed_id}");
            return ["status" => "error", "msg" => "无效的动态ID"];
        }
        
        $userId = (int)$uid;
        $feedId = (int)$feed_id;
        
        dbConn();
        try {
            // 检查feeds表是否存在
            $tableExists = Db()->_fetch("SHOW TABLES LIKE 'feeds'");
            if (!$tableExists) {
                $this->exception_log("删除动态失败 - 表不存在: feeds");
                return ["status" => "error", "msg" => "数据库表结构错误"];
            }
            
            // 先查询动态是否存在
            $feed = Db()->table('feeds')
                ->where("id = :feedId")
                ->prepareParam([':feedId' => $feedId])
                ->fetch();
                
            if (!$feed) {
                $this->exception_log("删除动态失败 - 动态不存在: 动态ID {$feedId}");
                return ["status" => "error", "msg" => "动态不存在"];
            }
            
            // 检查是否是用户自己的动态
            if ($feed['user_id'] != $userId) {
                $this->exception_log("删除动态失败 - 无权删除: 动态用户ID {$feed['user_id']}, 请求用户ID {$userId}");
                return ["status" => "error", "msg" => "您无权删除此动态"];
            }
            
            // 开始事务
            Db()->begin();
            
            // 删除该动态的所有评论
            try {
                Db()->table('feed_comments')
                    ->where('feed_id = :feedId')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->exception_log("已删除动态评论 - 动态ID: {$feedId}");
            } catch (\Exception $e) {
                $this->exception_log("删除动态评论失败: " . $e->getMessage());
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除该动态的所有点赞记录
            try {
                Db()->table('user_feed_likes')
                    ->where('feed_id = :feedId')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->exception_log("已删除动态点赞记录 - 动态ID: {$feedId}");
            } catch (\Exception $e) {
                $this->exception_log("删除动态点赞记录失败: " . $e->getMessage());
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除该动态的所有收藏记录
            try {
                Db()->table('user_favorites')
                    ->where('item_id = :feedId AND item_type = "feed"')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->exception_log("已删除动态收藏记录 - 动态ID: {$feedId}");
            } catch (\Exception $e) {
                $this->exception_log("删除动态收藏记录失败: " . $e->getMessage());
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除动态本身
            $deleteResult = Db()->table('feeds')
                ->where('id = :feedId AND user_id = :userId')
                ->prepareParam([':feedId' => $feedId, ':userId' => $userId])
                ->del();
                
            if (!$deleteResult) {
                throw new \Exception("删除动态失败");
            }
            
            // 提交事务
            Db()->commit();
            
            $this->user_log($userId, "删除动态【{$feedId}】成功");
            return ["status" => "ok", "msg" => "删除成功"];
            
        } catch (\Exception $e) {
            // 回滚事务
            Db()->rollback();
            
            $this->exception_log("删除动态失败: " . $e->getMessage());
            return ["status" => "error", "msg" => "删除失败，请稍后重试: " . $e->getMessage()];
        }
    }
    
    /**
     * @apiName 删除评论
     * @method delete_comment
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param comment_id int 评论ID
     * @param comment_type string 评论类型：feed=动态评论，card=日卡评论
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function delete_comment($uid, $token, $comment_id, $comment_type)
    {
        // 添加详细日志，帮助排查问题
        $this->exception_log("删除评论请求 - uid: {$uid}, comment_id: {$comment_id}, comment_type: {$comment_type}");
        
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            $this->exception_log("删除评论参数错误 - uid或token无效");
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            $this->exception_log("删除评论授权失败 - 登录信息验证失败");
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($comment_id, "intgt0")) {
            $this->exception_log("删除评论参数错误 - 评论ID无效: {$comment_id}");
            return ["status" => "error", "msg" => "无效的评论ID"];
        }
        if (empty($comment_type) || !in_array($comment_type, ['feed', 'card'])) {
            $this->exception_log("删除评论参数错误 - 评论类型无效: {$comment_type}");
            return ["status" => "error", "msg" => "无效的评论类型"];
        }
        
        $userId = (int)$uid;
        $commentId = (int)$comment_id;
        
        dbConn();
        try {
            // 获取表名
            $tableName = $comment_type . '_comments';
            
            // 检查表是否存在
            $tableExists = Db()->_fetch("SHOW TABLES LIKE '{$tableName}'");
            if (!$tableExists) {
                $this->exception_log("删除评论失败 - 表不存在: {$tableName}");
                return ["status" => "error", "msg" => "评论类型无效"];
            }
            
            // 先查询评论是否存在
            $comment = Db()->table($tableName)
                ->where("id = :commentId")
                ->prepareParam([':commentId' => $commentId])
                ->fetch();
                
            if (!$comment) {
                $this->exception_log("删除评论失败 - 评论不存在: 评论ID {$commentId}, 表 {$tableName}");
                return ["status" => "error", "msg" => "评论不存在"];
            }
            
            // 检查是否是用户自己的评论
            if ($comment['user_id'] != $userId) {
                $this->exception_log("删除评论失败 - 无权删除: 评论用户ID {$comment['user_id']}, 请求用户ID {$userId}");
                return ["status" => "error", "msg" => "您无权删除此评论"];
            }
            
            // 开始事务
            Db()->begin();
            
            // 获取父级内容的ID，以便更新评论计数
            $parentIdColumn = $comment_type . '_id';
            if (!isset($comment[$parentIdColumn])) {
                $this->exception_log("删除评论错误 - 评论缺少父级ID字段: {$parentIdColumn}");
                return ["status" => "error", "msg" => "评论数据结构错误"];
            }
            
            $parentId = $comment[$parentIdColumn];
            
            // 检查父级内容是否存在
            $parentTable = $comment_type === 'feed' ? 'feeds' : 'daily_cards';
            $parent = Db()->table($parentTable)
                ->where("id = :parentId")
                ->prepareParam([':parentId' => $parentId])
                ->fetch();
                
            if (!$parent) {
                $this->exception_log("删除评论警告 - 父级内容不存在: 父级ID {$parentId}, 表 {$parentTable}");
                // 继续处理，不阻止删除评论
            }
            
            // 删除评论点赞记录
            try {
                Db()->table('comment_likes')
                    ->where("comment_id = :commentId AND comment_type = :commentType")
                    ->prepareParam([':commentId' => $commentId, ':commentType' => $comment_type])
                    ->del();
            } catch (\Exception $e) {
                $this->exception_log("删除评论点赞记录失败: " . $e->getMessage());
                // 继续处理，不阻止删除评论本身
            }
                
            // 删除评论本身
            $deleteResult = Db()->table($tableName)
                ->where('id = :commentId AND user_id = :userId')
                ->prepareParam([':commentId' => $commentId, ':userId' => $userId])
                ->del();
                
            if (!$deleteResult) {
                throw new \Exception("删除评论失败");
            }
            
            // 更新父级内容的评论计数（如果父级存在）
            if ($parent) {
                $sql = "UPDATE `{$parentTable}` SET `comment_count` = GREATEST(0, `comment_count` - 1) WHERE id = :parentId";
                Db()->_exec($sql, [':parentId' => $parentId]);
            }
            
            // 提交事务
            Db()->commit();
            
            $this->user_log($userId, "删除{$comment_type}评论【{$commentId}】成功");
            return ["status" => "ok", "msg" => "删除成功"];
            
        } catch (\Exception $e) {
            // 回滚事务
            Db()->rollback();
            
            $this->exception_log("删除评论失败: " . $e->getMessage());
            return ["status" => "error", "msg" => "删除失败，请稍后重试: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 获取摘录列表 (分页)
     * @method get_quotes
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param user_id int 要筛选的用户ID (可选, 0 表示不筛选)
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_quotes($page = 1, $page_size = 20, $uid = 0, $token = "", $user_id = 0)
    {
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
         if (!check($user_id, "integt0")) { // 允许为 0
             return ["status" => "error", "msg" => "用户筛选参数错误"];
        }

        $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        $filterUserId = (int)$user_id;

        dbConn();
        try {
            // 构建查询条件
            $baseWhereConditions = [];
            $prepareParam = [];

            if ($filterUserId > 0) {
                $baseWhereConditions[] = "quotes.user_id = :filterUserId";
                $prepareParam[':filterUserId'] = $filterUserId;
            }

            // 隐私筛选：只显示公开的摘录，或者当前用户自己的摘录
            if ($currentUserId > 0) {
                $baseWhereConditions[] = "(quotes.privacy = 'public' OR quotes.user_id = :currentUserId)";
                $prepareParam[':currentUserId'] = $currentUserId;
            } else {
                $baseWhereConditions[] = "quotes.privacy = 'public'";
            }

            // 构建FROM和SELECT子句
            $fromClause = 'quotes LEFT JOIN user u ON u.uid = quotes.user_id';
            $selectFields = 'quotes.id, quotes.user_id, quotes.content, quotes.images_json, quotes.author, quotes.source, quotes.tags, quotes.privacy, quotes.created_at, u.nickname, u.avatar as avatar_url';

            // 构建WHERE子句
            $whereClauseString = "";
            if (!empty($baseWhereConditions)) {
                 $whereClauseString = " WHERE " . implode(" AND ", $baseWhereConditions);
            }

            // 计算总数
            $countSql = "SELECT COUNT(1) as totalCount FROM quotes LEFT JOIN user u ON u.uid = quotes.user_id" . $whereClauseString;
            $countResult = Db::_fetch($countSql, $prepareParam);
            $total = $countResult ? (int)$countResult['totalCount'] : 0;

            if ($total === 0) {
                return ["status" => "empty", "msg" => "暂无摘录数据"];
            }

            // 计算分页
            $limitOffset = ($page - 1) * $page_size;
            $limitRowCount = $page_size;
            $limitClause = "LIMIT {$limitOffset}, {$limitRowCount}";

            // 构建最终SQL
            $orderByClause = "ORDER BY quotes.created_at DESC";
            $sql = "SELECT {$selectFields} FROM {$fromClause}{$whereClauseString} {$orderByClause} {$limitClause}";

            // 执行查询
            $quotes = Db::_fetchAll($sql, $prepareParam);

            if (empty($quotes)) {
                return ["status" => "empty", "msg" => "暂无摘录数据"];
            }

            // 处理结果
            foreach ($quotes as &$quote) {
                // 处理图片数据
                if (!empty($quote['images_json'])) {
                    $images = json_decode($quote['images_json'], true);
                    $quote['images'] = is_array($images) ? $images : [];
                } else {
                    $quote['images'] = [];
                }
                unset($quote['images_json']);

                $quote['user'] = [
                    'uid' => $quote['user_id'],
                    'nickname' => $quote['nickname'],
                    'avatar_url' => $quote['avatar_url']
                ];
                unset($quote['nickname'], $quote['avatar_url']);
            }

            return ["status" => "ok", "data" => ["list" => $quotes, "total" => $total]];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "获取摘录列表失败"];
        }
    }
}

// 移除之前添加的 response_success/response_error 函数定义
// 假设这些函数由 core/Controller 或其他全局文件提供
// 如果项目中没有，需要取消注释或将它们移到合适的位置
/*
if (!function_exists('response_success')) {
    function response_success($msg = '操作成功', $data = [], $code = 0)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => $code, 'msg' => $msg, 'data' => $data], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
}

if (!function_exists('response_error')) {
    function response_error($msg = '操作失败', $data = [], $code = 1)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => $code, 'msg' => $msg, 'data' => $data], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
}
*/

?>
