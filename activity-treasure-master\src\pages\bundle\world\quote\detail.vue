<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getQuoteDetail, likeQuote, favoriteQuote, getQuoteComments, postQuoteComment } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth';

// 状态管理
const quote = ref(null);
const loading = ref(true);
const quoteId = ref('');
const comments = ref([]);
const commentText = ref('');
const isSubmittingComment = ref(false);

// 获取URL参数
onLoad((options) => {
  if (options.id) {
    quoteId.value = options.id;
    loadQuoteDetail();
    loadComments();
  } else {
    uni.showToast({ title: '参数错误', icon: 'none' });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 加载摘录详情
const loadQuoteDetail = async () => {
  try {
    loading.value = true;
    
    const res = await getQuoteDetail({
      id: quoteId.value,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      quote.value = res.data;
    } else {
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  } catch (error) {
    console.error('加载摘录详情失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
  }
};

// 加载评论
const loadComments = async () => {
  try {
    const res = await getQuoteComments({
      quote_id: quoteId.value,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      comments.value = res.data.list || [];
    }
  } catch (error) {
    console.error('加载评论失败:', error);
  }
};

// 点赞摘录
const handleLike = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  try {
    const res = await likeQuote({
      id: quoteId.value,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.value.is_liked = !quote.value.is_liked;
      quote.value.like_count = quote.value.is_liked ?
        (quote.value.like_count || 0) + 1 :
        (quote.value.like_count || 1) - 1;

      uni.showToast({
        title: quote.value.is_liked ? '点赞成功' : '取消点赞',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('点赞失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 收藏摘录
const handleFavorite = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  try {
    const res = await favoriteQuote({
      id: quoteId.value,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.value.is_favorited = !quote.value.is_favorited;
      quote.value.favorite_count = quote.value.is_favorited ?
        (quote.value.favorite_count || 0) + 1 :
        (quote.value.favorite_count || 1) - 1;

      uni.showToast({
        title: quote.value.is_favorited ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('收藏失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 发布评论
const submitComment = async () => {
  if (!store().$state.userInfo?.uid) {
    uni.showToast({ title: '请先登录', icon: 'none' });
    return;
  }

  if (!commentText.value.trim()) {
    uni.showToast({ title: '请输入评论内容', icon: 'none' });
    return;
  }

  if (isSubmittingComment.value) return;

  isSubmittingComment.value = true;

  try {
    const res = await postQuoteComment({
      quote_id: quoteId.value,
      content: commentText.value.trim(),
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 创建新评论对象
      const newComment = {
        id: res.data?.comment_id || Date.now(),
        content: commentText.value.trim(),
        user: {
          nickname: store().$state.userInfo.nickname,
          avatar_url: store().$state.userInfo.avatar
        },
        created_at: new Date().toISOString()
      };

      comments.value.unshift(newComment);
      commentText.value = '';

      uni.showToast({ title: '评论成功', icon: 'success' });
    } else {
      uni.showToast({ title: res.msg || '评论失败', icon: 'none' });
    }
  } catch (error) {
    console.error('评论失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  } finally {
    isSubmittingComment.value = false;
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 微信小程序分享 - 使用新的分享方式
// onShareAppMessage(() => {
//   if (quote.value) {
//     return {
//       title: `分享一段美好的文字：${quote.value.content.substring(0, 30)}...`,
//       path: `/pages/bundle/world/quote/detail?id=${quoteId.value}`,
//       imageUrl: ''
//     };
//   }
//
//   return {
//     title: '分享摘录',
//     path: `/pages/bundle/world/quote/detail?id=${quoteId.value}`
//   };
// });

// 使用新的分享方式
const handleShare = () => {
  if (quote.value) {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 0,
      title: `分享一段美好的文字：${quote.value.content.substring(0, 30)}...`,
      summary: quote.value.content,
      href: `/pages/bundle/world/quote/detail?id=${quoteId.value}`,
      success: () => {
        uni.showToast({ title: '分享成功', icon: 'success' });
      },
      fail: (err) => {
        console.error('分享失败:', err);
        uni.showToast({ title: '分享失败', icon: 'none' });
      }
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<template>
  <view class="quote-detail-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <u-icon name="arrow-left" size="22" color="#333"></u-icon>
      </view>
      <view class="navbar-title">摘录详情</view>
      <view class="navbar-right">
        <!-- 分享按钮已注释，使用微信右上角分享 -->
        <!-- <view class="share-btn" @click="handleShare">
          <u-icon name="share" size="20" color="#6AC086"></u-icon>
        </view> -->
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view scroll-y class="main-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 摘录内容 -->
      <view v-else-if="quote" class="quote-detail">
        <!-- 摘录正文 -->
        <view class="quote-content">
          <text class="quote-text">{{ quote.content }}</text>
        </view>

        <!-- 作者和出处 -->
        <view v-if="quote.author || quote.source" class="quote-meta">
          <text v-if="quote.author" class="quote-author">— {{ quote.author }}</text>
          <text v-if="quote.source" class="quote-source">《{{ quote.source }}》</text>
        </view>

        <!-- 用户信息 -->
        <view class="quote-user">
          <image 
            :src="quote.user?.avatar_url || '/static/default-avatar.png'" 
            class="user-avatar"
            mode="aspectFill"
          ></image>
          <view class="user-info">
            <text class="user-nickname">{{ quote.user?.nickname || '匿名用户' }}</text>
            <text class="quote-time">{{ formatTime(quote.created_at) }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="quote-actions">
          <view class="action-btn" @click="handleLike">
            <u-icon 
              :name="quote.is_liked ? 'heart-fill' : 'heart'" 
              :color="quote.is_liked ? '#ff4757' : '#666'" 
              size="24"
            ></u-icon>
            <text class="action-text">{{ quote.like_count || 0 }}</text>
          </view>
          
          <view class="action-btn" @click="handleFavorite">
            <u-icon 
              :name="quote.is_favorited ? 'star-fill' : 'star'" 
              :color="quote.is_favorited ? '#ffa502' : '#666'" 
              size="24"
            ></u-icon>
            <text class="action-text">{{ quote.favorite_count || 0 }}</text>
          </view>
          
          <view class="action-btn">
            <u-icon name="chat" color="#666" size="24"></u-icon>
            <text class="action-text">{{ comments.length }}</text>
          </view>
        </view>

        <!-- 评论区域 -->
        <view class="comments-section">
          <view class="comments-header">
            <text class="comments-title">评论 ({{ comments.length }})</text>
          </view>

          <!-- 评论列表 -->
          <view v-if="comments.length > 0" class="comments-list">
            <view 
              v-for="comment in comments" 
              :key="comment.id" 
              class="comment-item"
            >
              <image 
                :src="comment.user?.avatar_url || '/static/default-avatar.png'" 
                class="comment-avatar"
                mode="aspectFill"
              ></image>
              <view class="comment-content">
                <text class="comment-user">{{ comment.user?.nickname || '匿名' }}</text>
                <text class="comment-text">{{ comment.content }}</text>
                <text class="comment-time">{{ formatTime(comment.created_at) }}</text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="comments-empty">
            <text class="empty-text">暂无评论，快来抢沙发吧~</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 评论输入框 -->
    <view class="comment-input-section">
      <view class="comment-input-wrapper">
        <input 
          v-model="commentText"
          class="comment-input"
          placeholder="写下你的想法..."
          :maxlength="200"
        />
        <view 
          class="comment-submit"
          :class="{ 'disabled': !commentText.trim() || isSubmittingComment }"
          @click="submitComment"
        >
          <text class="submit-text">{{ isSubmittingComment ? '发送中...' : '发送' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.quote-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
  padding-top: var(--status-bar-height, 44rpx);
  border-bottom: 1rpx solid rgba(237, 237, 237, 0.8);
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;

  .navbar-left,
  .navbar-right {
    width: 120rpx;
    display: flex;
    align-items: center;
    height: 88rpx;
  }

  .navbar-left {
    justify-content: flex-start;
  }

  .navbar-right {
    justify-content: flex-end;

    .share-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx;
      cursor: pointer;
    }
  }

  .navbar-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1a1a1a;
  }
}

.main-content {
  flex: 1;
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx));
  padding-bottom: 120rpx; /* 为评论输入框留出空间 */
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.quote-detail {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.quote-content {
  margin-bottom: 32rpx;
  text-align: center;
  
  .quote-text {
    font-size: 36rpx;
    line-height: 1.8;
    color: #333;
    font-style: italic;
    font-weight: 300;
  }
}

.quote-meta {
  margin-bottom: 32rpx;
  text-align: right;
  
  .quote-author {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
  }
  
  .quote-source {
    display: block;
    font-size: 26rpx;
    color: #999;
  }
}

.quote-user {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .user-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .user-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .quote-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.quote-actions {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 32rpx;
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx;
    border-radius: 12rpx;
    transition: background-color 0.2s ease;
    
    &:active {
      background-color: #f5f5f5;
    }
    
    .action-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.comments-section {
  .comments-header {
    margin-bottom: 24rpx;
    
    .comments-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  .comments-list {
    .comment-item {
      display: flex;
      margin-bottom: 24rpx;
      
      .comment-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
        flex-shrink: 0;
      }
      
      .comment-content {
        flex: 1;
        
        .comment-user {
          display: block;
          font-size: 26rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .comment-text {
          display: block;
          font-size: 28rpx;
          line-height: 1.6;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .comment-time {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
  
  .comments-empty {
    text-align: center;
    padding: 60rpx 0;
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.comment-input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
  
  .comment-input-wrapper {
    display: flex;
    align-items: center;
    gap: 20rpx;
    
    .comment-input {
      flex: 1;
      height: 80rpx;
      padding: 0 24rpx;
      background: #f8f9fa;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
      outline: none;
    }
    
    .comment-submit {
      padding: 20rpx 32rpx;
      background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
      border-radius: 40rpx;
      
      &.disabled {
        opacity: 0.6;
        pointer-events: none;
      }
      
      .submit-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
      }
    }
  }
}
</style>
