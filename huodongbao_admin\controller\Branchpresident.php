<?php
namespace controller;
use core\Controller;
use core\Db;

/*
 * @className 分会长管理
 */
class BranchPresident extends Controller {

    public function __construct() {
        parent::__construct();
        $this->auth();
    }

    /*
     * @name 分会长申请列表
     * @method applications
     */
    public function applications() {
        try {
            $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
            $page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'], "intgt0") ? (int)$_REQUEST['page_size'] : 20;

            assign("page_size", $page_size);
            assign("page", $page);

            dbConn();

            $where = "1";
            $prepareParam = [];
            $pageParams = ["page" => $page, "page_size" => $page_size];

            // 状态筛选
            if (isset($_REQUEST['status']) && check($_REQUEST['status'], "integt0")) {
                $status = (int)$_REQUEST['status'];
                $where .= " AND status={$status}";
                $pageParams['status'] = $_REQUEST['status'];
                assign("status", $status);
            }

            // 申请人搜索（先获取用户ID，然后在主查询中使用）
            if (!empty($_REQUEST['user_name'])) {
                $users = Db()->table("user")->select("uid")->where("nickname LIKE :user_name")->prepareParam([":user_name" => "%" . trim($_REQUEST['user_name']) . "%"])->fetchAll();
                $user_ids = array_column($users, 'uid');
                if (!empty($user_ids)) {
                    $where .= " AND user_id IN (" . implode(',', $user_ids) . ")";
                } else {
                    $where .= " AND user_id = -1"; // 没有匹配的用户，返回空结果
                }
                $pageParams['user_name'] = $_REQUEST['user_name'];
                assign("user_name", $_REQUEST['user_name']);
            }

            // 分会名称搜索
            if (!empty($_REQUEST['branch_name'])) {
                $where .= " AND branch_name LIKE :branch_name";
                $prepareParam[":branch_name"] = "%" . trim($_REQUEST['branch_name']) . "%";
                $pageParams['branch_name'] = $_REQUEST['branch_name'];
                assign("branch_name", $_REQUEST['branch_name']);
            }

            // 查询申请列表
            $data = Db()->table("branch_president_applications")->where($where)->prepareParam($prepareParam)->order("application_time DESC")->page($page, $page_size);

            // 补充用户信息
            if (!empty($data)) {
                foreach ($data as &$application) {
                    $user = Db()->table("user")->select("nickname, mobile")->where("uid={$application['user_id']}")->fetch();
                    $application['user_nickname'] = $user['nickname'] ?? '';
                    $application['user_mobile'] = $user['mobile'] ?? '';

                    // 格式化时间
                    $application['application_time_formatted'] = date('Y-m-d H:i:s', strtotime($application['application_time']));
                    if (!empty($application['review_time'])) {
                        $application['review_time_formatted'] = date('Y-m-d H:i:s', strtotime($application['review_time']));
                    }
                }
            }

            assign("data", $data);
            assign("pageParams", $pageParams);

            // 记录操作日志
            $this->root_log("查看分会长申请列表");

            return view(['header','branchpresident/applications','footer']);

        } catch (\Exception $e) {
            $this->exception_log("分会长申请列表加载失败: " . $e->getMessage());
            assign("msg", "数据加载失败，请稍后重试");
            return view(['header','error','footer']);
        }
    }

    /*
     * @name 申请详情
     * @method application_detail
     */
    public function application_detail() {
        try {
            if (!isset($_REQUEST['id']) || !check($_REQUEST['id'], "intgt0")) {
                alert("参数错误");
                return;
            }

            $id = (int)$_REQUEST['id'];

            dbConn();

            // 获取申请详情
            $application = Db()->table("branch_president_applications")->where("id={$id}")->fetch();

            if (empty($application)) {
                alert("申请记录不存在");
                return;
            }

            // 补充用户信息
            $user = Db()->table("user")->select("nickname, mobile, avatar, time as register_time")->where("uid={$application['user_id']}")->fetch();
            $application['user_nickname'] = $user['nickname'] ?? '';
            $application['user_mobile'] = $user['mobile'] ?? '';
            $application['user_avatar'] = $user['avatar'] ?? '';
            $application['user_register_time'] = $user['register_time'] ?? '';

            // 格式化时间
            $application['application_time_formatted'] = date('Y-m-d H:i:s', strtotime($application['application_time']));
            if (!empty($application['review_time'])) {
                $application['review_time_formatted'] = date('Y-m-d H:i:s', strtotime($application['review_time']));
            }

            // 如果已审核，获取审核人信息
            if (!empty($application['reviewer_id'])) {
                $reviewer = Db()->table("root")->select("r_name")->where("rid={$application['reviewer_id']}")->fetch();
                $application['reviewer_name'] = $reviewer['r_name'] ?? '';
            }

            assign("application", $application);

            // 记录操作日志
            $this->root_log("查看分会长申请详情，申请ID: {$id}");

            return view(['header','branchpresident/application_detail','footer']);

        } catch (\Exception $e) {
            $this->exception_log("分会长申请详情加载失败: " . $e->getMessage());
            alert("数据加载失败，请稍后重试");
            return;
        }
    }

    /*
     * @name 审核申请
     * @method review_application
     */
    public function review_application() {
        try {
            if (!isset($_REQUEST['id']) || !check($_REQUEST['id'], "intgt0")) {
                alert("参数错误");
                return;
            }

            if (!isset($_REQUEST['status']) || !in_array($_REQUEST['status'], [1, 2])) {
                alert("审核状态参数错误");
                return;
            }

            $id = (int)$_REQUEST['id'];
            $status = (int)$_REQUEST['status'];
            $comment = isset($_REQUEST['comment']) ? trim($_REQUEST['comment']) : '';

            // 记录操作日志
            $action = $status == 1 ? '通过' : '拒绝';
            $this->root_log("审核分会长申请，申请ID: {$id}，操作: {$action}");

            // 调用API进行审核
            $result = $this->callAPI('admin/branch_president_review', [
                'uid' => $_SESSION['root_info']['rid'], // 管理员ID
                'token' => 'admin_token', // 管理员token，可以配置
                'application_id' => $id,
                'status' => $status,
                'comment' => $comment
            ]);

            if ($result && $result['status'] === 'ok') {
                alert("审核完成", "success", "/branchpresident/applications");
            } else {
                $error_msg = $result['msg'] ?? "审核失败";
                $this->exception_log("分会长申请审核失败: {$error_msg}");
                alert($error_msg);
            }

        } catch (\Exception $e) {
            $this->exception_log("分会长申请审核异常: " . $e->getMessage());
            alert("审核失败，请稍后重试");
        }
    }

    /*
     * @name 分会列表
     * @method branches
     */
    public function branches() {
        try {
            $page = isset($_REQUEST['page']) && check($_REQUEST['page'], "intgt0") ? (int)$_REQUEST['page'] : 1;
            $page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'], "intgt0") ? (int)$_REQUEST['page_size'] : 20;

            assign("page_size", $page_size);
            assign("page", $page);

            dbConn();

            $where = "branch_id > 0"; // 排除总会
            $prepareParam = [];
            $pageParams = ["page" => $page, "page_size" => $page_size];

            // 分会名称搜索
            if (!empty($_REQUEST['branch_name'])) {
                $where .= " AND branch_name LIKE :branch_name";
                $prepareParam[":branch_name"] = "%" . trim($_REQUEST['branch_name']) . "%";
                $pageParams['branch_name'] = $_REQUEST['branch_name'];
                assign("branch_name", $_REQUEST['branch_name']);
            }

            // 查询分会列表
            $data = Db()->table("user_branch")->where($where)->prepareParam($prepareParam)->order("branch_id ASC")->page($page, $page_size);

            // 补充负责人信息和统计成员数量
            if (!empty($data)) {
                foreach ($data as &$branch) {
                    // 获取负责人信息
                    if (!empty($branch['branch_leader'])) {
                        $leader = Db()->table("user")->select("nickname, mobile, role_type")->where("uid={$branch['branch_leader']}")->fetch();
                        $branch['leader_nickname'] = $leader['nickname'] ?? '';
                        $branch['leader_mobile'] = $leader['mobile'] ?? '';
                        $branch['leader_role_type'] = $leader['role_type'] ?? '';
                    } else {
                        $branch['leader_nickname'] = '';
                        $branch['leader_mobile'] = '';
                        $branch['leader_role_type'] = '';
                    }

                    // 统计成员数量
                    $member_count = Db()->table("user")->where("branch_id={$branch['branch_id']}")->count();
                    $branch['member_count'] = $member_count;

                    // 格式化创建时间
                    if (!empty($branch['created_at'])) {
                        $branch['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($branch['created_at']));
                    }
                }
            }

            assign("data", $data);
            assign("pageParams", $pageParams);

            // 获取统计数据
            $stats = [];

            // 分会长数量
            $president_count = Db()->table("user")
                ->where("role_type='1' AND branch_id IS NOT NULL AND branch_id > 0")
                ->count();
            $stats['president_count'] = $president_count;

            // 待审核申请数量
            $pending_applications = Db()->table("branch_president_applications")
                ->where("status=0")
                ->count();
            $stats['pending_applications'] = $pending_applications;

            assign("stats", $stats);

            // 记录操作日志
            $this->root_log("查看分会列表");

            return view(['header','branchpresident/branches','footer']);

        } catch (\Exception $e) {
            $this->exception_log("分会列表加载失败: " . $e->getMessage());
            assign("msg", "数据加载失败，请稍后重试");
            return view(['header','error','footer']);
        }
    }



    /*
     * @name 分会详情
     * @method branch_detail
     */
    public function branch_detail() {
        if (!isset($_REQUEST['id']) || !check($_REQUEST['id'], "intgt0")) {
            alert("参数错误");
            return;
        }

        $branch_id = (int)$_REQUEST['id'];

        try {
            dbConn();

            // 获取分会信息
            $branch = Db()->table("user_branch")->where("branch_id={$branch_id}")->fetch();
            if (empty($branch)) {
                alert("分会不存在");
                return;
            }

            // 获取分会长信息
            $president = Db()->table("user")->select("uid, nickname, mobile, time")->where("branch_id={$branch_id} AND role_type='1'")->fetch();
            if (!empty($president)) {
                $branch['president_info'] = $president;
            }

            // 统计分会成员数量
            $member_count = Db()->table("user")->where("branch_id={$branch_id}")->count();
            $branch['member_count'] = $member_count;

            // 统计分会活动数量
            $activity_count = Db()->table("huodong")->where("reviewed_by_president={$president['uid']}")->count();
            $branch['activity_count'] = $activity_count;

            assign("branch", $branch);

            return view(['header','branchpresident/branch_detail','footer']);

        } catch (\Exception $e) {
            $this->exception_log("分会详情加载失败: " . $e->getMessage());
            alert("数据加载失败，请稍后重试");
            return;
        }
    }

    /*
     * @name 更改分会长状态
     * @method change_status
     */
    public function change_status() {
        responseType("json");

        if (!isset($_REQUEST['id']) || !check($_REQUEST['id'], "intgt0")) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        if (!isset($_REQUEST['status']) || !in_array($_REQUEST['status'], [0, 1])) {
            return ["status" => "error", "msg" => "状态参数错误"];
        }

        $id = (int)$_REQUEST['id'];
        $status = (int)$_REQUEST['status'];

        try {
            dbConn();

            // 检查用户是否存在且是分会长
            $user = Db()->table("user")->select("uid, nickname, role_type")->where("uid={$id} AND role_type='1'")->fetch();
            if (empty($user)) {
                return ["status" => "error", "msg" => "分会长不存在"];
            }

            // 更新状态（这里假设有一个status字段，如果没有可以添加或使用其他字段）
            $result = Db()->table("user")->where("uid={$id}")->update(["is_dongjie" => $status == 0 ? 1 : 0]);

            if ($result) {
                $action = $status == 1 ? '启用' : '停用';
                $this->root_log("更改分会长状态，用户ID: {$id}，操作: {$action}");
                return ["status" => "ok", "msg" => $action . "成功"];
            } else {
                return ["status" => "error", "msg" => "操作失败"];
            }

        } catch (\Exception $e) {
            $this->exception_log("更改分会长状态失败: " . $e->getMessage());
            return ["status" => "error", "msg" => "操作失败，请稍后重试"];
        }
    }

    /*
     * @name 调用API接口
     * @method callAPI
     */
    private function callAPI($endpoint, $params = []) {
        try {
            // 从配置中获取API基础URL
            $api_base_url = "https://api.linqingkeji.com/";
            $url = $api_base_url . $endpoint;

            // 记录API调用日志
            $this->root_log("调用API: {$endpoint}");

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'User-Agent: HuodongbaoAdmin/1.0'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $this->exception_log("API调用CURL错误: {$error}");
                return ['status' => 'error', 'msg' => 'API调用失败: 网络错误'];
            }

            if ($httpCode !== 200) {
                $this->exception_log("API调用HTTP错误: HTTP {$httpCode}");
                return ['status' => 'error', 'msg' => "API调用失败: HTTP {$httpCode}"];
            }

            if ($response) {
                $result = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $result;
                } else {
                    $this->exception_log("API响应JSON解析失败: " . json_last_error_msg());
                    return ['status' => 'error', 'msg' => 'API响应格式错误'];
                }
            }

            return ['status' => 'error', 'msg' => 'API无响应'];

        } catch (\Exception $e) {
            $this->exception_log("API调用异常: " . $e->getMessage());
            return ['status' => 'error', 'msg' => 'API调用异常'];
        }
    }

    /*
     * @name 获取统计数据
     * @method getStatistics
     */
    private function getStatistics() {
        try {
            dbConn();

            $stats = [];

            // 申请统计
            $stats['total_applications'] = Db()->table("branch_president_applications")->count();
            $stats['pending_applications'] = Db()->table("branch_president_applications")->where("status=0")->count();
            $stats['approved_applications'] = Db()->table("branch_president_applications")->where("status=1")->count();
            $stats['rejected_applications'] = Db()->table("branch_president_applications")->where("status=2")->count();

            // 分会统计
            $stats['total_branches'] = Db()->table("user_branch")->where("branch_id > 0")->count();
            $stats['total_presidents'] = Db()->table("user")->where("role_type='1' AND branch_id > 0")->count();

            // 活动统计
            $stats['total_reviewed_activities'] = Db()->table("huodong")->where("president_review_status IS NOT NULL")->count();

            return $stats;

        } catch (\Exception $e) {
            $this->exception_log("获取统计数据失败: " . $e->getMessage());
            return [];
        }
    }
}
