<?php
namespace controller;
//用于前后端分离专门处理的公共控制器
class View{

	public function __construct(){

	}
	//$params['tpls'] = "header|sys@accesslog|footer"
	public function page($tpls){
		responseType("html");
		$pages = [];
		$params = \core\Route::$params;
		$tpls = $params["tpls"];
		if(strpos($tpls,"|") !== false){
			$items = explode("|",$tpls);
			foreach($items as $item){
				if(strpos($item,"@") !== false){
					$pages[] = str_ireplace("@","/",$item);
				}else{
					$pages[] = $item;
				}
			}
		}
		return view($pages);
	}

	public function __destruct(){

	}
}
