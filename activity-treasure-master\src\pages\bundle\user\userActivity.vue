<script setup>
import { watch, ref, reactive } from "vue";
import {
  userget_user_likes,
  userget_user_favorites,
  userget_user_published,
  userget_user_comments
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, navto } from "@/utils";

const activityType = ref("");
const pageTitle = ref("");
const currentTab = ref("feed"); // 当前选中的tab: feed, card, quote
const goods = ref([]);

// 调用mescroll的hook
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

// tab选项配置
const tabOptions = [
  { key: "feed", label: "动态" },
  { key: "card", label: "日卡" },
  { key: "quote", label: "摘录" },
  { key: "diary", label: "日记" }
];

onLoad((options) => {
  activityType.value = options.type || "likes";

  // 设置页面标题
  const titleMap = {
    likes: "我赞过的",
    favorites: "我收藏的",
    published: "我发布的",
    comments: "我评论的"
  };
  pageTitle.value = titleMap[activityType.value] || "我的活动";
});

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 切换tab
const switchTab = (tabKey) => {
  if (currentTab.value !== tabKey) {
    currentTab.value = tabKey;
    goods.value = [];
    // 重新加载数据
    const mescroll = getMescroll();
    if (mescroll) {
      mescroll.resetUpScroll();
    }
  }
};

// 上拉加载的回调
const upCallback = async (mescroll) => {
  try {
    let res;
    const params = {
      page: mescroll.num,
      page_size: mescroll.size,
      type: getApiType() // 根据当前tab获取对应的API类型
    };

    console.log(`API调用参数:`, params, `活动类型: ${activityType.value}`);

    switch (activityType.value) {
      case "likes":
        res = await userget_user_likes(params);
        break;
      case "favorites":
        res = await userget_user_favorites(params);
        break;
      case "published":
        res = await userget_user_published(params);
        break;
      case "comments":
        res = await userget_user_comments(params);
        break;
      default:
        res = { status: "error", msg: "未知类型" };
    }

    console.log(`API响应结果:`, res);

    if (res.status === "ok") {
      let curPageData = [];

      // 根据当前tab和活动类型处理数据
      if (currentTab.value === "feed") {
        if (activityType.value === "likes") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "favorites") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "published") {
          curPageData = res.data.feeds.map(item => ({ ...item, type: "feed" }));
        } else if (activityType.value === "comments") {
          curPageData = res.data.feed_comments.map(item => ({ ...item, type: "feed_comment" }));
        }
      } else if (currentTab.value === "card") {
        if (activityType.value === "likes") {
          curPageData = res.data.cards.map(item => ({ ...item, type: "card" }));
        } else if (activityType.value === "favorites") {
          curPageData = res.data.cards.map(item => ({ ...item, type: "card" }));
        } else if (activityType.value === "published") {
          curPageData = []; // 日卡不支持发布
        } else if (activityType.value === "comments") {
          curPageData = res.data.card_comments.map(item => ({ ...item, type: "card_comment" }));
        }
      } else if (currentTab.value === "quote") {
        if (activityType.value === "likes") {
          curPageData = res.data.quotes ? res.data.quotes.map(item => ({ ...item, type: "quote" })) : [];
        } else if (activityType.value === "favorites") {
          curPageData = res.data.quotes ? res.data.quotes.map(item => ({ ...item, type: "quote" })) : [];
        } else if (activityType.value === "published") {
          curPageData = res.data.quotes.map(item => ({ ...item, type: "quote" }));
        } else if (activityType.value === "comments") {
          curPageData = res.data.quote_comments ? res.data.quote_comments.map(item => ({ ...item, type: "quote_comment" })) : [];
        }
      } else if (currentTab.value === "diary") {
        if (activityType.value === "likes") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "favorites") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "published") {
          curPageData = res.data.diaries ? res.data.diaries.map(item => ({ ...item, type: "diary" })) : [];
        } else if (activityType.value === "comments") {
          curPageData = res.data.diary_comments ? res.data.diary_comments.map(item => ({ ...item, type: "diary_comment" })) : [];
        }
      }

      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    } else if (res.status === "empty" || res === "n") {
      // 🔧 修复：服务器返回空数据时不显示错误提示
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);
    } else {
      // 🔧 修复：只在真正的错误时显示提示，空数据不显示
      if (res.status !== "empty" && res !== "n") {
        console.error("获取数据失败:", res);
        // 只在网络错误或服务器错误时显示提示
        if (res.msg && !res.msg.includes('暂无') && !res.msg.includes('没有')) {
          uni.$u.toast(res.msg);
        }
      }
      mescroll.endErr();
    }
  } catch (error) {
    console.error("获取用户活动错误:", error);
    // 🔧 修复：只在真正的网络错误时显示提示
    if (error.name === 'NetworkError' || error.message.includes('network') || error.message.includes('timeout')) {
      uni.$u.toast("网络连接失败，请检查网络后重试");
    }
    mescroll.endErr();
  }
};

// 根据当前tab获取API类型参数
const getApiType = () => {
  const typeMap = {
    feed: "feed",
    card: "card",
    quote: "quote",
    diary: "diary"
  };
  return typeMap[currentTab.value] || "";
};

// 点击项目跳转
const handleItemClick = (item) => {
  if (item.type === "feed" || item.type === "feed_comment") {
    const feedId = item.feed?.id || item.id;
    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}`);
  } else if (item.type === "card" || item.type === "card_comment") {
    const cardId = item.card?.id || item.id;
    navto(`/pages/bundle/world/card/detail?cardId=${cardId}`);
  } else if (item.type === "quote" || item.type === "quote_comment") {
    const quoteId = item.quote?.id || item.id;
    navto(`/pages/bundle/world/quote/detail?quoteId=${quoteId}`);
  } else if (item.type === "diary" || item.type === "diary_comment") {
    const diaryId = item.diary?.id || item.id;
    navto(`/pages/bundle/world/diary/detail?diaryId=${diaryId}`);
  }
};

// 获取显示内容
const getDisplayContent = (item) => {
  if (item.type === "feed") {
    return item.feed?.content || item.content || "动态内容";
  } else if (item.type === "card") {
    return item.card?.content || item.content || "日卡内容";
  } else if (item.type === "quote") {
    return item.content || "摘录内容";
  } else if (item.type === "diary") {
    return item.diary?.content || item.content || "日记内容";
  } else if (item.type === "feed_comment") {
    return item.content || "评论内容";
  } else if (item.type === "card_comment") {
    return item.content || "评论内容";
  } else if (item.type === "quote_comment") {
    return item.content || "评论内容";
  } else if (item.type === "diary_comment") {
    return item.content || "评论内容";
  }
  return "内容";
};

// 获取引用内容（用于评论类型）
const getReferencedContent = (item) => {
  if (item.type === "feed_comment" && item.feed) {
    return item.feed.display_content || item.feed.content;
  } else if (item.type === "card_comment" && item.card) {
    return item.card.display_content || item.card.content;
  } else if (item.type === "quote_comment" && item.quote) {
    return item.quote.display_content || item.quote.content;
  } else if (item.type === "diary_comment" && item.diary) {
    return item.diary.display_content || item.diary.content;
  }
  return null;
};

// 获取显示图片
const getDisplayImage = (item) => {
  if (item.type === "feed") {
    const images = item.feed?.images || item.images || [];
    return images.length > 0 ? images[0] : null;
  } else if (item.type === "card") {
    return item.card?.image_url || item.image_url;
  } else if (item.type === "diary") {
    const images = item.diary?.images || item.images || [];
    return images.length > 0 ? images[0] : null;
  }
  return null;
};

// 获取类型标签
const getTypeLabel = (item) => {
  const typeMap = {
    feed: "动态",
    card: "日卡",
    quote: "摘录",
    diary: "日记",
    feed_comment: "动态评论",
    card_comment: "日卡评论",
    quote_comment: "摘录评论",
    diary_comment: "日记评论"
  };
  return typeMap[item.type] || "内容";
};
</script>

<template>
  <view class="user-activity-page">
    <!-- 标题栏 -->
    <view class="activity-header">
      <myTitle
        bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        height="200rpx"
        :title="pageTitle"
        color="#ffffff"
        :blod="true"
      ></myTitle>
    </view>

    <!-- Tab选项卡 -->
    <view class="tab-container">
      <view class="tab-wrapper">
        <view
          class="tab-item"
          v-for="tab in tabOptions"
          :key="tab.key"
          :class="{ active: currentTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          <text class="tab-text">{{ tab.label }}</text>
          <view class="tab-indicator" v-if="currentTab === tab.key"></view>
        </view>
      </view>
    </view>

    <!-- 内容列表 -->
    <view class="activity-container">
      <mescroll-uni
        class="activity-list"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
        :height="height"
      >
        <!-- 空状态 -->
        <view v-if="goods.length === 0" class="empty-state">
          <image :src="`${store().$state.url}empty.png`" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无{{pageTitle.replace('我', '')}}内容</text>
        </view>

        <!-- 内容列表 -->
        <view v-else>
          <view
            class="activity-item"
            v-for="(val, i) in goods"
            :key="i"
            @click="handleItemClick(val)"
          >
            <view class="activity-content">
              <!-- 图片 -->
              <view class="activity-image" v-if="getDisplayImage(val)">
                <image
                  class="content-image"
                  :src="getDisplayImage(val)"
                  mode="aspectFill"
                  lazy-load
                ></image>
              </view>

              <!-- 内容信息 -->
              <view class="activity-info" :class="{ 'no-image': !getDisplayImage(val) }">
                <view class="activity-header-info">
                  <view class="activity-type">
                    <text class="type-label">{{ getTypeLabel(val) }}</text>
                  </view>
                  <view class="activity-time">
                    <u-text
                      size="20rpx"
                      color="#999"
                      :text="val.created_at || val.time || ''"
                    ></u-text>
                  </view>
                </view>

                <view class="activity-title">
                  <u-text
                    size="28rpx"
                    bold
                    color="#333"
                    lines="2"
                    :text="getDisplayContent(val)"
                  ></u-text>
                </view>

                <!-- 引用内容（评论类型显示） -->
                <view class="referenced-content" v-if="val.type.includes('comment') && getReferencedContent(val)">
                  <view class="reference-label">引用内容：</view>
                  <u-text
                    size="24rpx"
                    color="#666"
                    lines="1"
                    :text="getReferencedContent(val)"
                  ></u-text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="activity-action">
                <u-icon name="arrow-right" color="#999" size="32rpx"></u-icon>
              </view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
/* 页面容器 */
.user-activity-page {
  min-height: 100vh;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 标题栏 */
.activity-header {
  position: relative;
  z-index: 10;
}

/* Tab选项卡 */
.tab-container {
  position: fixed;
  top: 200rpx; /* 调整与标题栏底部对齐 */
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1rpx solid rgba(106, 192, 134, 0.1);
  padding: 12rpx 30rpx; /* 增加上下内边距，提高可点击区域 */
  z-index: 20;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tab-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 50rpx;
}

.tab-item {
  position: relative;
  padding: 24rpx 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 30rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.tab-item.active .tab-text {
  color: #6AC086;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 2rpx;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 40rpx;
    opacity: 1;
  }
}

/* 内容容器 */
.activity-container {
  padding: 30rpx;
  margin-top: 280rpx; /* 调整顶部边距，确保内容不被tab遮挡 */
}

/* 内容列表 */
.activity-list {
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 内容项 */
.activity-item {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.activity-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.activity-content {
  display: flex;
  align-items: flex-start;
}

/* 图片 */
.activity-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.content-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 内容信息 */
.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.activity-info.no-image {
  margin-left: 0;
}

.activity-header-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.activity-type {
  display: flex;
  align-items: center;
}

.type-label {
  font-size: 20rpx;
  color: #6AC086;
  background: rgba(106, 192, 134, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
}

.activity-title {
  margin-bottom: 8rpx;
}

.activity-extra {
  opacity: 0.8;
}

/* 引用内容 */
.referenced-content {
  margin-top: 12rpx;
  padding: 12rpx;
  background: rgba(106, 192, 134, 0.05);
  border-radius: 8rpx;
  border-left: 3rpx solid #6AC086;
}

.reference-label {
  font-size: 20rpx;
  color: #6AC086;
  margin-bottom: 4rpx;
  font-weight: 500;
}

/* 操作按钮 */
.activity-action {
  padding-left: 20rpx;
  align-self: center;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .activity-container {
    padding: 20rpx;
  }

  .activity-item {
    padding: 20rpx;
  }

  .activity-image {
    width: 100rpx;
    height: 100rpx;
    margin-right: 16rpx;
  }
}

/* 动画效果 */
.activity-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
