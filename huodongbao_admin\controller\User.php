<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 用户 
*/
class User extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 查看
	* @method index
	*/			
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(isset($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $_REQUEST['uid'];
		}
		if(isset($_REQUEST['p_uid']) && check($_REQUEST['p_uid'],"integt0")){
			$p_uid = (int)$_REQUEST['p_uid'];
			$where .= " AND p_uid={$p_uid}";
			$pageParams['p_uid'] = $_REQUEST['p_uid'];
		}
		if(isset($_REQUEST['sex']) && check($_REQUEST['sex'],"integt0")){
			$sex = (int)$_REQUEST['sex'];
			$where .= " AND sex={$sex}";
			$pageParams['sex'] = $_REQUEST['sex'];
			assign("sex",$sex);
		}
		if(!empty($_REQUEST['nickname'])){
			$where .= " AND nickname LIKE :nickname";
			$prepareParam[":nickname"] = "%".trim($_REQUEST['nickname'])."%";
			$pageParams['nickname'] = $_REQUEST['nickname'];
		}
		if(!empty($_REQUEST['mobile'])){
			$where .= " AND mobile LIKE :mobile";
			$prepareParam[":mobile"] = "%".trim($_REQUEST['mobile'])."%";
			$pageParams['mobile'] = $_REQUEST['mobile'];
		}
		if(isset($_REQUEST['is_huiyuan']) && check($_REQUEST['is_huiyuan'],"integt0")){
			$is_huiyuan = (int)$_REQUEST['is_huiyuan'];
			$where .= " AND is_huiyuan={$is_huiyuan}";
			$pageParams['is_huiyuan'] = $_REQUEST['is_huiyuan'];
			assign("is_huiyuan",$is_huiyuan);
		}
		if(isset($_REQUEST['is_dongjie']) && check($_REQUEST['is_dongjie'],"integt0")){
			$is_dongjie = (int)$_REQUEST['is_dongjie'];
			$where .= " AND is_dongjie={$is_dongjie}";
			$pageParams['is_dongjie'] = $_REQUEST['is_dongjie'];
			assign("is_dongjie",$is_dongjie);
		}
		//
		$sort_arr = [
			"1"=>['by'=>"uid DESC","name"=>"注册倒序"],
			"2"=>['by'=>"open_times DESC,uid DESC","name"=>"人气"],
		];
		$sort_k = !empty($_REQUEST['sort_k']) && isset($sort_arr[$_REQUEST['sort_k']]) ? $_REQUEST['sort_k'] : "1";
		$pageParams['sort_k'] = $sort_k;
		assign("sort_k",$sort_k);
		assign("sort_arr",$sort_arr);
		$sort = $sort_arr[$sort_k];
		//
		$data = Db()->table("user")->where($where)->prepareParam($prepareParam)->order($sort['by'])->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as &$row){
				$p_user = Db()->table("user")->select("uid,nickname,mobile")->where("uid={$row['p_uid']}")->fetch();
				$row['p_user'] = $p_user;
				$row['label_num'] = Db()->table("user_label")->where("uid={$row['uid']}")->count();

				// 获取分会信息
				if (!empty($row['branch_id'])) {
					$branch_info = Db()->table("user_branch")->select("branch_name")->where("branch_id={$row['branch_id']}")->fetch();
					$row['branch_name'] = $branch_info ? $branch_info['branch_name'] : '';
				} else {
					$row['branch_name'] = '';
				}
			}
		}
		assign("data",$data);
		//dump($data);exit;
		//
		$sex_arr = [0=>"未知",1=>"男",2=>"女"];
		assign("sex_arr",$sex_arr);
		//
		return view(['header','user/index','footer']);
	}
	/*
	* @name 获取相册
	* @method get_imgs
	*/			
	public function get_imgs($uid){
		responseType("json");
		$uid = intval($uid);
		dbConn();
		$data = Db()->table("user_img")->select("img_url as src")->where("uid={$uid}")->fetchAll();
		$i = 0;
		foreach($data as &$row){
			$row['alt'] = "";
			$row['pid'] = $i;
			$row['thumb'] = $row['src'];
			$i++;
		}
		return ["title"=>"用户照片","id"=>123,"start"=>0,"data"=>$data];
	}
	/*
	* @name 编辑
	* @method edit
	*/			
	public function edit(){
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("user/index"));
			exit;
		}
		dbConn();
		$id = (int)$_REQUEST['id'];
		if(
			isset($_POST['is_dongjie']) &&
			in_array($_POST['is_dongjie'],[0,1])
		){
			Db()->begin();
			try{
				$data = [
					"is_dongjie"=>(int)$_POST['is_dongjie'],
				];
				$prepareParam = [
				];
				if(!empty($_POST['mobile'])){
					$data['mobile'] = ":mobile";
					$prepareParam[":mobile"] = htmlspecialchars(trim($_POST['mobile']));
				}
				if(!empty($_POST['nickname'])){
					$data['nickname'] = ":nickname";
					$prepareParam[":nickname"] = htmlspecialchars(trim($_POST['nickname']));
				}
				if(!empty($_POST['avatar']) && check($_POST['avatar'],"url")){
					$data['avatar'] = ":avatar";
					$prepareParam[":avatar"] = htmlspecialchars(trim($_POST['avatar']));
				}
				if(isset($_POST['sex']) && in_array($_POST['sex'],[0,1,2])){
					$data['sex'] = intval($_POST['sex']);
				}
				if(isset($_POST['is_huiyuan']) && in_array($_POST['is_huiyuan'],[0,1])){
					$data['is_huiyuan'] = intval($_POST['is_huiyuan']);
				}
				if(isset($_POST['huiyuan_end_time']) && check($_POST['huiyuan_end_time'],"datetime")){
					$data['huiyuan_end_time'] = $_POST['huiyuan_end_time'];
				}
				if(isset($_POST['birthday']) && check($_POST['birthday'],"date")){
					$data['birthday'] = $_POST['birthday'];
				}
				if(!empty($_POST['password'])){
					$data['password'] = password_hash(config("sys.user_salt").$_POST['password'],PASSWORD_BCRYPT);
					$data['token'] = "0000";
				}
				if($data['is_dongjie'] == 1){
					$data['token'] = "0000";
				}

				// 🔧 修复：角色编辑处理（避免嵌套事务）
				$role_change_needed = false;
				$role_change_data = null;

				// 修复：role_type为varchar类型，确保类型安全比较
				if(isset($_POST['role_type']) && in_array($_POST['role_type'], ['0','1','2','3','4','5'])){
					$new_role_type = trim($_POST['role_type']);
					$current_user = Db()->table("user")->where("uid={$id}")->fetch();

					if(trim($current_user['role_type']) != $new_role_type){
						// 标记需要角色转换，但在事务外处理
						$role_change_needed = true;
						$role_change_data = [
							'user_id' => $id,
							'old_role' => trim($current_user['role_type']),
							'new_role' => $new_role_type,
							'post_data' => $_POST
						];
						$data['role_type'] = $new_role_type;
					}
				}
				$res = Db()->table("user")->prepareParam($prepareParam)->where("uid={$id}")->update($data);
				$this->root_log("编辑用户信息,uid:{$id}");
				Db()->commit();

				// 🔧 修复：在主事务完成后处理角色转换
				if($role_change_needed && $role_change_data){
					try {
						$this->handleRoleChange(
							$role_change_data['user_id'],
							$role_change_data['old_role'],
							$role_change_data['new_role'],
							$role_change_data['post_data']
						);
					} catch(\Exception $e) {
						// 角色转换失败，但基本信息已保存
						assign("alertTpl",false);
						assign("alertMsg","基本信息保存成功，但角色转换失败：" . $e->getMessage());
						// 跳转到表单显示
						goto show_form;
					}
				}

				$params = $_GET;
				unset($params['id']);
				header("location:".url("user/index",$params));
				exit;
			}catch(\Exception $e){
				Db()->rollback();
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}

		show_form: // 🔧 修复：添加标签用于错误处理跳转
		$data = Db()->table("user")->where("uid={$id}")->fetch();

		// 🆕 新增：获取分会信息
		if (!empty($data['branch_id'])) {
			$branch_info = Db()->table("user_branch")->select("branch_name")->where("branch_id={$data['branch_id']}")->fetch();
			$data['branch_name'] = $branch_info ? $branch_info['branch_name'] : '';
		} else {
			$data['branch_name'] = '';
		}

		assign("data",$data);
		//
		return view(['header','user/edit','footer']);
	}

	/**
	 * 🔧 P0-3修复：处理用户角色转换（完整的角色转换路径处理）
	 * @param int $user_id 用户ID
	 * @param string $old_role 原角色
	 * @param string $new_role 新角色
	 * @param array $post_data POST数据
	 */
	private function handleRoleChange($user_id, $old_role, $new_role, $post_data) {
		// 🔧 修复：添加完整的事务控制
		Db::begin();
		try {
			$role_names = [
				'0' => '管理员',
				'1' => '分会长',
				'2' => '普通用户',
				'3' => '场地与活动第三方',
				'4' => '城市分会长',
				'5' => '场地第三方-不管理分会'
			];

			// 🔧 修复：使用命名参数避免PDO参数绑定错误
			$current_user = Db::_fetch("SELECT uid, role_type, branch_id, assignment_type FROM user WHERE uid=:uid FOR UPDATE", [':uid' => $user_id]);
			if (!$current_user) {
				throw new \Exception("用户不存在");
			}

			// 修复：role_type为varchar类型，确保类型安全比较
			if (trim($current_user['role_type']) == (string)$new_role) {
				throw new \Exception("用户已经是{$role_names[$new_role]}，无需重复设置");
			}

			// 修复：根据新角色类型调用相应的处理方法（varchar类型）
			switch ($new_role) {
				case '0': // 管理员
					$this->handleAdminRoleChange($user_id, $old_role, $current_user);
					break;
				case '1': // 分会长
				case '4': // 城市分会长
					$this->handleBranchPresidentRoleChange($user_id, $old_role, $new_role, $post_data, $current_user);
					break;
				case '2': // 普通用户
					$this->handleNormalUserRoleChange($user_id, $old_role, $current_user);
					break;
				case '3': // 场地与活动第三方（需要分会）
					$this->handleBranchPresidentRoleChange($user_id, $old_role, $new_role, $post_data, $current_user);
					break;
				case '5': // 场地第三方-不管理分会
					$this->handleVenueProviderRoleChange($user_id, $old_role, $new_role, $current_user);
					break;
				default:
					throw new \Exception("不支持的角色类型：{$new_role}");
			}

			// 🔧 优化：记录角色变更日志（包含role_type='3'）
			$log_message = "角色变更：{$role_names[$old_role]} → {$role_names[$new_role]}";
			if (in_array($new_role, ['1', '3', '4']) && !empty($post_data['branch_name'])) {
				$log_message .= "，分会：{$post_data['branch_name']}";
			}

			$this->root_log($log_message . "，用户ID：{$user_id}");

			// 🔧 修复：提交事务
			Db::commit();

		} catch (\Exception $e) {
			// 🔧 修复：回滚事务
			Db::rollback();
			throw new \Exception("角色转换失败：" . $e->getMessage());
		}
	}

	/**
	 * 🆕 P0-3新增：处理管理员角色转换
	 */
	private function handleAdminRoleChange($user_id, $old_role, $current_user) {
		// 管理员角色转换：清理分会关联（如果有）
		if (!empty($current_user['branch_id']) && in_array($old_role, ['1', '4'])) {
			$this->cleanupBranchAssociation($user_id, $current_user);
		}
		// 管理员无需特殊处理，保持现有权限
	}

	/**
	 * 🔧 优化：处理分会长角色转换（支持role_type='3'）
	 */
	private function handleBranchPresidentRoleChange($user_id, $old_role, $new_role, $post_data, $current_user) {
		$branch_name = trim($post_data['branch_name'] ?? '');

		// 🔧 优化：根据角色类型调整验证逻辑
		$role_names = [
			'1' => '分会长',
			'3' => '场地与活动第三方',
			'4' => '城市分会长'
		];

		if (empty($branch_name)) {
			$role_name = $role_names[$new_role] ?? '该角色';
			throw new \Exception("{$role_name}必须设置分会名称");
		}

		// 验证分会名称
		$this->validateBranchName($branch_name, $user_id);

		if (!empty($current_user['branch_id'])) {
			// 更新现有分会
			$this->updateExistingBranch($current_user['branch_id'], $branch_name, $user_id, $post_data);
			$branch_id = $current_user['branch_id'];
		} else {
			// 创建新分会
			$branch_id = $this->createNewBranch($branch_name, $user_id, $post_data);
		}

		// 更新用户分会关联
		$this->updateUserBranchAssociation($user_id, $branch_id, $current_user);

		// 创建申请记录
		$this->createBranchPresidentApplication($user_id, $branch_name, $post_data, $branch_id);
	}

	/**
	 * 🆕 P0-3新增：处理普通用户角色转换
	 */
	private function handleNormalUserRoleChange($user_id, $old_role, $current_user) {
		// 普通用户角色转换：清理分会关联（如果原来是分会长）
		if (!empty($current_user['branch_id']) && in_array($old_role, ['1', '4'])) {
			$this->cleanupBranchAssociation($user_id, $current_user);
		}
		// 普通用户无需特殊处理
	}

	/**
	 * 🆕 P0-3新增：处理场地提供商角色转换
	 */
	private function handleVenueProviderRoleChange($user_id, $old_role, $new_role, $current_user) {
		// 场地第三方-不管理分会(5)：清理分会关联
		if ($new_role == '5' && !empty($current_user['branch_id']) && in_array($old_role, ['1', '4'])) {
			$this->cleanupBranchAssociation($user_id, $current_user);
		}
		// 场地与活动第三方(3)：保持分会关联（如果有）
		// 无需特殊处理，保持现有状态
	}

	/**
	 * 🔧 P1-2修复：验证分会名称（与现有申请流程完全一致）
	 */
	private function validateBranchName($branch_name, $user_id) {
		// 🔧 修复：使用与Branchpresident.php完全相同的验证逻辑

		// 1. 检查分会名称是否重复（与apply方法第65-73行逻辑一致）
		$existing_branch = Db()->table("user_branch")
			->select("branch_id")
			->where("branch_name=:branch_name")
			->prepareParam([":branch_name" => $branch_name])
			->fetch();

		if (!empty($existing_branch)) {
			throw new \Exception("分会名称已存在，请选择其他名称");
		}

		// 2. 检查申请表中是否有重复的分会名称（与apply方法第75-83行逻辑一致）
		$existing_application_name = Db()->table("branch_president_applications")
			->select("id")
			->where("branch_name=:branch_name AND status!=2 AND user_id!=:user_id")
			->prepareParam([":branch_name" => $branch_name, ":user_id" => $user_id])
			->fetch();

		if (!empty($existing_application_name)) {
			throw new \Exception("该分会名称已有人申请，请选择其他名称");
		}
	}

	/**
	 * 🆕 P0-3新增：更新现有分会
	 */
	private function updateExistingBranch($branch_id, $branch_name, $user_id, $post_data) {
		// 🔧 P1-1修复：计算当前分会成员数量
		$member_count = Db::_fetch("SELECT COUNT(*) as count FROM user WHERE branch_id=?", [$branch_id])['count'];

		$branch_data = [
			'branch_name' => $branch_name,
			'branch_leader' => $user_id,
			'branch_location' => $post_data['branch_location'] ?? '',
			'branch_members' => $member_count // 🆕 新增：更新成员数量
		];
		$update_result = Db()->table("user_branch")->where("branch_id={$branch_id}")->update($branch_data);
		if ($update_result === false) {
			throw new \Exception("更新分会信息失败");
		}
	}

	/**
	 * 🔧 优化：创建新分会（支持role_type='3'）
	 */
	private function createNewBranch($branch_name, $user_id, $post_data) {
		// 🔧 修复：使用安全的分会ID生成方式
		$branch_id = $this->generateSafeBranchId();

		// 获取用户信息
		$user_info = Db()->table("user")
			->select("mobile, avatar, role_type")
			->where("uid = :uid")
			->prepareParam([":uid" => $user_id])
			->fetch();

		// 🔧 优化：根据角色类型确定分会类型
		$branch_type = 'normal'; // 默认普通分会
		if ($user_info['role_type'] == '3') {
			$branch_type = 'venue_partner'; // 场地与活动第三方分会
		} elseif ($user_info['role_type'] == '4') {
			$branch_type = 'city_branch'; // 城市分会长分会
		}

		// 创建新分会（包含分会长手机号和二维码）
		$branch_data = [
			'branch_id' => $branch_id,
			'branch_name' => $branch_name,
			'branch_location' => $post_data['branch_location'] ?? '',
			'branch_leader' => $user_id,
			'branch_leader_mobile' => $user_info['mobile'] ?? '',
			'branch_leader_qr_image' => $post_data['wechat_qr_image'] ?? '',
			'branch_members' => 1,
			'assignment_counter' => 0,
			'system_assigned_count' => 0,
			'branch_type' => $branch_type,
			'created_at' => date('Y-m-d H:i:s')
		];

		$result = Db()->table("user_branch")->insert($branch_data);
		if (!$result) {
			throw new \Exception("创建分会失败");
		}

		// 记录分会长变更日志
		$this->logBranchChange($branch_id, null, $user_id, 'assign', '管理员直接设置', $_SESSION['admin_uid'] ?? 0, 'admin');

		return $branch_id;
	}

	/**
	 * 🆕 P0-3新增：更新用户分会关联
	 */
	private function updateUserBranchAssociation($user_id, $branch_id, $current_user) {
		$old_branch_id = $current_user['branch_id'];

		// 🔧 修复：更新用户信息，保持系统分配标记
		$user_update_data = ['branch_id' => $branch_id];
		// 保持原有的assignment_type标记
		if (!empty($current_user['assignment_type'])) {
			$user_update_data['assignment_type'] = $current_user['assignment_type'];
		}

		$user_update_result = Db()->table("user")->where("uid={$user_id}")->update($user_update_data);
		if ($user_update_result === false) {
			throw new \Exception("更新用户分会信息失败");
		}

		// 🆕 P1-1新增：更新相关分会的成员数量
		if (!empty($old_branch_id) && $old_branch_id != $branch_id) {
			$this->updateBranchMemberCount($old_branch_id); // 更新原分会成员数量
		}
		if (!empty($branch_id)) {
			$this->updateBranchMemberCount($branch_id); // 更新新分会成员数量
		}
	}

	/**
	 * 🆕 P0-3新增：创建分会长申请记录
	 */
	private function createBranchPresidentApplication($user_id, $branch_name, $post_data, $branch_id) {
		// 插入分会长申请记录（状态为已通过）
		$application_data = [
			'user_id' => $user_id,
			'branch_name' => $branch_name,
			'branch_description' => '管理员直接设置',
			'branch_location' => $post_data['branch_location'] ?? '',
			'application_reason' => '管理员直接设置为分会长',
			'application_time' => date('Y-m-d H:i:s'),
			'status' => 1, // 已通过
			'reviewer_id' => $_SESSION['admin_uid'] ?? 0,
			'review_time' => date('Y-m-d H:i:s'),
			'review_comment' => '管理员直接设置',
			'created_branch_id' => $branch_id
		];

		Db()->table("branch_president_applications")->insert($application_data);
	}

	/**
	 * 🆕 P0-3新增：清理分会关联
	 */
	private function cleanupBranchAssociation($user_id, $current_user) {
		// 检查分会是否还有其他成员
		$branch_members_count = Db::_fetch("SELECT COUNT(*) as count FROM user WHERE branch_id=? AND uid!=?", [$current_user['branch_id'], $user_id])['count'];

		if ($branch_members_count > 0) {
			// 有其他成员，只清除用户的分会关联，但保留分会
			$user_update_data = ['branch_id' => null];
			// 保持原有的assignment_type标记
			if (!empty($current_user['assignment_type'])) {
				$user_update_data['assignment_type'] = $current_user['assignment_type'];
			}
			Db()->table("user")->where("uid={$user_id}")->update($user_update_data);

			// 更新分会信息，移除分会长
			Db()->table("user_branch")->where("branch_id={$current_user['branch_id']}")->update([
				'branch_leader' => 0, // 暂时没有分会长
				'branch_members' => $branch_members_count
			]);
		} else {
			// 没有其他成员，删除分会
			Db()->table("user_branch")->where("branch_id={$current_user['branch_id']}")->delete();
			$user_update_data = ['branch_id' => null];
			// 保持原有的assignment_type标记
			if (!empty($current_user['assignment_type'])) {
				$user_update_data['assignment_type'] = $current_user['assignment_type'];
			}
			Db()->table("user")->where("uid={$user_id}")->update($user_update_data);
		}
	}

	/**
	 * 🆕 P1-1新增：更新分会成员数量
	 * @param int $branch_id 分会ID
	 */
	private function updateBranchMemberCount($branch_id) {
		if (empty($branch_id)) return;

		$member_count = Db::_fetch("SELECT COUNT(*) as count FROM user WHERE branch_id=?", [$branch_id])['count'];
		Db()->table("user_branch")->where("branch_id={$branch_id}")->update(['branch_members' => $member_count]);
	}

	/**
	 * 🔧 修复：安全的分会ID生成方法
	 * @return int 新的分会ID
	 */
	private function generateSafeBranchId() {
		// 🔧 修复：使用更安全的方式获取下一个分会ID
		$max_branch_id = Db()->table("user_branch")->max("branch_id");
		$new_branch_id = ($max_branch_id ? (int)$max_branch_id : 0) + 1;

		// 🔧 修复：检查ID是否已存在，避免冲突
		$existing = Db()->table("user_branch")->where("branch_id={$new_branch_id}")->fetch();
		while ($existing) {
			$new_branch_id++;
			$existing = Db()->table("user_branch")->where("branch_id={$new_branch_id}")->fetch();
		}

		return $new_branch_id;
	}

	/**
	 * 🆕 新增：记录分会长变更日志
	 */
	private function logBranchChange($branch_id, $old_leader_id, $new_leader_id, $change_type, $change_reason, $operator_id, $operator_type) {
		$log_data = [
			'branch_id' => $branch_id,
			'old_leader_id' => $old_leader_id,
			'new_leader_id' => $new_leader_id,
			'change_type' => $change_type,
			'change_reason' => $change_reason,
			'operator_id' => $operator_id,
			'operator_type' => $operator_type,
			'created_at' => date('Y-m-d H:i:s')
		];

		Db()->table("branch_change_log")->insert($log_data);

		// 发送分会通知
		$this->sendBranchNotification($branch_id, $change_type, $old_leader_id, $new_leader_id);
	}

	/**
	 * 🆕 新增：发送分会通知
	 */
	private function sendBranchNotification($branch_id, $change_type, $old_leader_id, $new_leader_id) {
		// 获取分会信息
		$branch_info = Db()->table("user_branch")
			->select("branch_name")
			->where("branch_id = :branch_id")
			->prepareParam([":branch_id" => $branch_id])
			->fetch();

		if (empty($branch_info)) {
			return;
		}

		$branch_name = $branch_info['branch_name'];
		$notification_data = [];

		switch ($change_type) {
			case 'assign':
				// 新任命分会长通知
				if ($new_leader_id) {
					$new_leader_info = Db()->table("user")
						->select("nickname")
						->where("uid = :uid")
						->prepareParam([":uid" => $new_leader_id])
						->fetch();

					$notification_data = [
						'type' => 'branch_leader_change',
						'title' => '分会长变更通知',
						'content' => "您所在的分会 {$branch_name} 的分会长已变更为 " . ($new_leader_info['nickname'] ?? '新分会长') . "，请知悉。"
					];
				}
				break;

			case 'remove':
				// 分会长移除通知
				$notification_data = [
					'type' => 'branch_leader_change',
					'title' => '分会长变更通知',
					'content' => "您所在的分会 {$branch_name} 的分会长已被移除，分会暂时无分会长管理。"
				];
				break;

			case 'transfer':
				// 分会长转移通知
				if ($new_leader_id) {
					$new_leader_info = Db()->table("user")
						->select("nickname")
						->where("uid = :uid")
						->prepareParam([":uid" => $new_leader_id])
						->fetch();

					$notification_data = [
						'type' => 'branch_leader_change',
						'title' => '分会长变更通知',
						'content' => "您所在的分会 {$branch_name} 的分会长已转移给 " . ($new_leader_info['nickname'] ?? '新分会长') . "，请知悉。"
					];
				}
				break;
		}

		if (!empty($notification_data)) {
			// 插入分会通知（target_type='branch'，只有该分会成员能收到）
			$insert_data = [
				'uid' => 0, // 系统通知
				'type' => $notification_data['type'],
				'title' => $notification_data['title'],
				'content' => $notification_data['content'],
				'related_id' => $branch_id,
				'branch_id' => $branch_id,
				'target_type' => 'branch',
				'is_global' => 0,
				'created_at' => date('Y-m-d H:i:s')
			];

			Db()->table("user_notifications")->insert($insert_data);
		}
	}

	/*
	* @name 冻结/解冻
	* @method dongjie
	*/
	public function dongjie($ac,$ids){
		dbConn();
		responseType("json");
		$ac = trim($ac);
		$ids = trim($ids);
		$ids_arr = explode(",",$ids);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(empty($ids_arr)){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		if($ac == "dongjie"){
			$sql = "UPDATE `user` SET is_dongjie=1,token='0000' WHERE uid IN (" . implode(",",$ids_arr) . ")";
		}else if($ac == "cancel_dongjie"){
			$sql = "UPDATE `user` SET is_dongjie=0 WHERE uid IN (" . implode(",",$ids_arr) . ")";
		}else{
			return ["status"=>"error","msg"=>"参数错误"];
		}
		Db()->begin();
		try{
			$rowCount = Db()->_exec($sql);
			$this->root_log("批量冻结/解冻用户信息,{$ids} | {$ac}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"更新数量：{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}
	/*
	* @name 重置密码
	* @method reset_password
	*/	
	public function reset_password(){
		dbConn();
		responseType("json");
		$ids = trim($_POST['ids']);
		$ids_arr = explode(",",$ids);
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(empty($ids_arr)){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		if(!isset($_POST['password']) || empty($_POST['password'])){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$ac = trim($_POST['ac']);
		$password = password_hash(config("sys.user_salt").$_POST['password'],PASSWORD_BCRYPT);
		$sql = "UPDATE `user` SET password=:password,token='0000' WHERE uid IN (" . implode(",",$ids_arr) . ")";
		Db()->begin();
		try{
			$rowCount = Db()->_exec($sql,[":password"=>$password]);
			$this->root_log("批量调整用户级别,{$ids}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"更新数量：{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			return ["status"=>"error","msg"=>$e->getMessage()];
		}		
	}

	/*
	* @name 查看日志
	* @method log
	*/		
	public function log(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['ip'])){
			$ip = "%".trim($_REQUEST['ip'])."%";
			$where .= " AND `ip` LIKE :ip";
			$prepareParam[':ip'] = $ip;
			$pageParams['ip'] = $_REQUEST['ip'];;
		}
		if(!empty($_REQUEST['keyword'])){
			$keyword = "%".trim($_REQUEST['keyword'])."%";
			$where .= " AND `msg` LIKE :keyword";
			$prepareParam[':keyword'] = $keyword;
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_log")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		return view(['header','user/log','footer']);
	}
	
	/*
	* @name 获取标签信息
	* @method get_labels
	*/	
	public function get_labels($uid){
		if(!check($uid,"integt0")){
			die("<p>参数错误</p>");
		}
		$uid = intval($uid);
		dbConn();
		$data = Db()->table("user_label")->where("uid={$uid}")->fetchAll();
		if(empty($data)){
			die("<p>暂无数据</p>");
		}
		$str = "";
		foreach($data as $row){
			$str .= "{$row['label']}<br />";
		}
		echo $str;exit;
	}

	/*
	* @name 充值记录
	* @method chongzhi_order
	*/		
	public function chongzhi_order(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['order_id'])){
			$where .= " AND `order_id`=:order_id";
			$prepareParam[':order_id'] = $_REQUEST['order_id'];
			$pageParams['order_id'] = $_REQUEST['order_id'];;
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $status;
			assign("status",$status);
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_chongzhi_order")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		$status_arr = [0=>"未支付",1=>"已支付",2=>"已取消",3=>"退款中",4=>"退款成功",5=>"退款失败"];
		assign("status_arr",$status_arr);
		//
		return view(['header','user/chongzhi_order','footer']);
	}
	/*
	* @name 购买会员
	* @method huiyuan_order
	*/		
	public function huiyuan_order(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['order_id'])){
			$where .= " AND `order_id`=:order_id";
			$prepareParam[':order_id'] = $_REQUEST['order_id'];
			$pageParams['order_id'] = $_REQUEST['order_id'];;
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $status;
			assign("status",$status);
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_huiyuan_order")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		$status_arr = [0=>"未支付",1=>"已支付",2=>"已取消",3=>"退款中",4=>"退款成功",5=>"退款失败"];
		assign("status_arr",$status_arr);
		//
		return view(['header','user/huiyuan_order','footer']);
	}
	/*
	* @name 提现订单
	* @method tixian_order
	*/		
	public function tixian_order(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['bank_id']) && check($_REQUEST['bank_id'],"intgt0")){
			$bank_id = (int)$_REQUEST['bank_id'];
			$where .= " AND bank_id={$bank_id}";
			$pageParams['bank_id'] = $bank_id;
		}
		if(isset($_REQUEST['status']) && check($_REQUEST['status'],"integt0")){
			$status = (int)$_REQUEST['status'];
			$where .= " AND status={$status}";
			$pageParams['status'] = $status;
			assign("status",$status);
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_tixian")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
				//
				$bank = Db()->table("user_bank")->where("id={$v['bank_id']}")->fetch();
				$data[$k]['bank_info'] = $bank;
				//
			}
		}
		assign("data",$data);
		//
		$status_arr = [0=>"未处理",1=>"已通过",2=>"已拒绝"];
		assign("status_arr",$status_arr);
		//
		return view(['header','user/tixian_order','footer']);
	}
	/*
	* @name 提现审核
	* @method tixian_shenhe
	*/	
	public function tixian_shenhe($ac,$ids,$msg=""){
		dbConn();
		responseType("json");
		$ids_arr = explode(",",trim($ids));
		foreach($ids_arr as $id){
			if(!check($id,"intgt0")){
				return ["status"=>"error","msg"=>"参数错误"];
			}
		}
		if(!in_array($ac,["tongguo","bohui"])){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$ids_str = implode(",",$ids_arr);
		//
		$data = Db()->table("user_tixian")->select("id,uid,money,status")->where("id IN ({$ids_str}) AND status=0")->fetchAll();
		if(empty($data)){
			return ["status"=>"error","msg"=>"未找到可以审核数据"];
		}
		//
		Db()->begin();
		try{
			$success_num = 0;
			if($ac == "tongguo"){
				foreach($data as $row){
					$sql = "UPDATE `user_tixian` SET `status`=1 WHERE `id`={$row['id']} AND status=0";
					$rowCount = Db()->_exec($sql);
					if($rowCount){
						$success_num ++;
						//
						$sql = "UPDATE `user_tixian` SET `rid`={$_SESSION['root_info']['rid']},`chuli_time`='".DATETIME."' WHERE `id`={$row['id']}";
						Db()->_exec($sql);
						// 注意：已删除总提现字段，不再需要更新user表的统计字段

						// 🟡 新增：如果是佣金提现，更新佣金状态为已提现
						$tixian_info = Db()->table("user_tixian")->select("source_type,commission_ids")->where("id={$row['id']}")->fetch();
						if (!empty($tixian_info) && $tixian_info['source_type'] == 'commission' && !empty($tixian_info['commission_ids'])) {
							$commission_ids = json_decode($tixian_info['commission_ids'], true);
							if (!empty($commission_ids)) {
								$commission_ids_str = implode(',', array_map('intval', $commission_ids));
								$current_time = date('Y-m-d H:i:s');
								$sql = "UPDATE `user_yongjin_log` SET `status`=3, `withdraw_apply_id`={$row['id']}, `withdraw_time`='{$current_time}' WHERE id IN ({$commission_ids_str}) AND status=2";
								$updated_count = Db()->_exec($sql);

								// 记录状态变更日志
								foreach ($commission_ids as $commission_id) {
									$log_data = [
										'commission_id' => $commission_id,
										'old_status' => 2,
										'new_status' => 3,
										'operator_id' => $_SESSION['root_info']['rid'],
										'operator_type' => 'admin',
										'remark' => "管理员审核通过提现申请，提现ID: {$row['id']}"
									];
									Db()->table("commission_status_logs")->insert($log_data);
								}

								// 记录操作日志
								$this->root_log("佣金提现审核通过，提现ID: {$row['id']}，更新了{$updated_count}条佣金记录状态为已提现");
							}
						}
						//
					}
				}
			}else if($ac == "bohui"){
				foreach($data as $row){
					$sql = "UPDATE `user_tixian` SET `status`=2 WHERE `id`={$row['id']} AND status=0";
					$rowCount = Db::_exec($sql);
					if($rowCount){
						$success_num ++;
						//
						$sql = "UPDATE `user_tixian` SET `rid`={$_SESSION['root_info']['rid']},`chuli_time`='".DATETIME."',beizhu=:beizhu WHERE `id`={$row['id']}";
						Db::_exec($sql,[":beizhu"=>$msg]);

						// 🟡 新增：检查是否为佣金提现
						$tixian_info = Db()->table("user_tixian")->select("source_type,commission_ids")->where("id={$row['id']}")->fetch();
						if (!empty($tixian_info) && $tixian_info['source_type'] == 'commission' && !empty($tixian_info['commission_ids'])) {
							// 佣金提现被驳回，恢复佣金状态为可提取
							$commission_ids = json_decode($tixian_info['commission_ids'], true);
							if (!empty($commission_ids)) {
								$commission_ids_str = implode(',', array_map('intval', $commission_ids));
								$current_time = date('Y-m-d H:i:s');
								$sql = "UPDATE `user_yongjin_log` SET `status`=1, `withdraw_apply_id`=NULL, `reject_time`='{$current_time}' WHERE id IN ({$commission_ids_str}) AND status=2";
								$updated_count = Db()->_exec($sql);

								// 记录状态变更日志
								foreach ($commission_ids as $commission_id) {
									$log_data = [
										'commission_id' => $commission_id,
										'old_status' => 2,
										'new_status' => 1,
										'operator_id' => $_SESSION['root_info']['rid'],
										'operator_type' => 'admin',
										'remark' => "管理员驳回提现申请，恢复可提取状态，提现ID: {$row['id']}，驳回原因: {$msg}"
									];
									Db()->table("commission_status_logs")->insert($log_data);
								}

								// 记录操作日志
								$this->root_log("佣金提现审核驳回，提现ID: {$row['id']}，恢复了{$updated_count}条佣金记录为可提取状态，驳回原因: {$msg}");
							}
						} else {
							// 余额提现被驳回，恢复用户余额
							$sql = "UPDATE `user` SET `money`=`money`+{$row['money']} WHERE uid={$row['uid']}";
							Db()->_exec($sql);
							//
							$shengyu = Db()->table("user")->where("uid={$row['uid']}")->getColumn("money",0);
							$zhangdan = [
								"uid"=>$row['uid'],
								"money"=>$row['money'],
								"type"=>7,
								"shengyu"=>$shengyu,
								"msg"=>"提现驳回:{$row['id']}",
							];
							Db()->table("user_zhangdan")->insert($zhangdan);
						}
						//
					}
				}
			}else{
				throw new \Exception("未知操作类型");
			}
			$this->root_log("审核提现:{$ids} => {$ac} ，影响数量: {$success_num}");
			Db()->commit();
			return ["status"=>"ok","msg"=>"影响数量: {$success_num}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 佣金记录
	* @method yongjin_log
	*/		
	public function yongjin_log(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['type']) && check($_REQUEST['type'],"intgt0")){
			$type = (int)$_REQUEST['type'];
			$where .= " AND type={$type}";
			$pageParams['type'] = $type;
			assign("type",$type);
		}
		if(!empty($_REQUEST['order_id'])){
			$where .= " AND order_id=:order_id";
			$prepareParam[":order_id"] = $_REQUEST['order_id'];
			$pageParams['order_id'] = $_REQUEST['order_id'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_yongjin_log")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		$type_arr = [1=>"会员佣金",2=>"活动佣金",3=>"商品佣金"];
		assign("type_arr",$type_arr);
		//
		return view(['header','user/yongjin_log','footer']);
	}
	/*
	* @name 账单记录
	* @method zhangdan_log
	*/		
	public function zhangdan_log(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['type']) && check($_REQUEST['type'],"intgt0")){
			$type = (int)$_REQUEST['type'];
			$where .= " AND type={$type}";
			$pageParams['type'] = $type;
			assign("type",$type);
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND msg LIKE :keyword";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_zhangdan")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		$type_arr = [1=>"充值",2=>"活动费用结算",3=>"活动佣金结算",4=>"商品佣金结算",5=>"会员佣金结算",6=>"提现申请",7=>"提现驳回",8=>"活动费用支付",9=>"活动费用退还",10=>"抽奖中奖",11=>"商品支付",12=>"会员支付",13=>"商品退款",14=>"大礼包支付",15=>"大礼包退款"];
		assign("type_arr",$type_arr);
		//
		return view(['header','user/zhangdan_log','footer']);
	}
	/*
	* @name 收货地址
	* @method addr
	*/		
	public function addr(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND (sheng LIKE :keyword OR shi LIKE :keyword OR qu LIKE :keyword OR addr LIKE :keyword OR mobile LIKE :keyword OR username LIKE :keyword)";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_addr")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		return view(['header','user/addr','footer']);
	}
	/*
	* @name 收款账户
	* @method bank
	*/		
	public function bank(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['uid']) && check($_REQUEST['uid'],"intgt0")){
			$uid = (int)$_REQUEST['uid'];
			$where .= " AND uid={$uid}";
			$pageParams['uid'] = $uid;
		}
		if(!empty($_REQUEST['keyword'])){
			$where .= " AND (bank_name LIKE :keyword OR bank_num LIKE :keyword OR username LIKE :keyword)";
			$prepareParam[":keyword"] = "%".$_REQUEST['keyword']."%";
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-10 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("user_bank")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		if(!empty($data)){
			foreach($data as $k=>$v){
				$sql = "SELECT uid,nickname FROM `user` WHERE `uid`={$v['uid']} LIMIT 1";
				$res = Db()->_fetch($sql);
				$data[$k]["user"] = $res ? $res : [];
			}
		}
		assign("data",$data);
		//
		return view(['header','user/bank','footer']);
	}
	/*
	* @name 充值数额
	* @method chongzhi_config
	*/		
	public function chongzhi_config($ac=""){
		dbConn();
		//
		if(!empty($ac)){
			responseType("json");
			if($ac == "add"){
				if(
					empty($_POST['money']) || 
					!check($_POST['money'],"intgt0") ||
					empty($_POST['daozhang_money']) ||
					!check($_POST['daozhang_money'],"intgt0") ||
					!isset($_POST['label'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$data = [
					"money"=>number_format($_POST['money'],2,".",""),
					"daozhang_money"=>number_format($_POST['daozhang_money'],2,".",""),
					"label"=>mb_substr(htmlspecialchars($_POST['label']),0,20),
				];
				Db()->table("user_chongzhi")->insert($data);
				return ["status"=>"ok","msg"=>"操作成功"];
			}else if($ac == "update"){
				if(
					empty($_POST['id']) || 
					!check($_POST['id'],"intgt0") || 
					empty($_POST['money']) || 
					!check($_POST['money'],"intgt0") ||
					empty($_POST['daozhang_money']) ||
					!check($_POST['daozhang_money'],"intgt0") ||
					!isset($_POST['label'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$id = intval($_POST['id']);
				$data = [
					"money"=>number_format($_POST['money'],2,".",""),
					"daozhang_money"=>number_format($_POST['daozhang_money'],2,".",""),
					"label"=>mb_substr(htmlspecialchars($_POST['label']),0,20),
				];
				$rowCount = Db()->table("user_chongzhi")->where("id={$id}")->update($data);	
				if($rowCount){
					return ["status"=>"ok","msg"=>"操作成功"];
				}
			}else if($ac == "del"){
				if(
					empty($_POST['ids'])
				){
					return ["status"=>"error","msg"=>"参数错误"];
				}
				$ids_arr = explode(",",$_POST['ids']);
				foreach($ids_arr as $v){
					if(!check($v,"intgt0")){
						return ["status"=>"error","msg"=>"参数错误"];
					}
				}
				$ids_str = implode(",",$ids_arr);
				$rowCount = Db()->table("user_chongzhi")->where("id IN ({$ids_str})")->del();	
				if($rowCount){
					return ["status"=>"ok","msg"=>"操作成功"];
				}
			}else{
				return ["status"=>"error","msg"=>"参数错误"];
			}
			return ["status"=>"error","msg"=>"操作失败"];
		}
		//
		$data = Db()->table("user_chongzhi")->order("id ASC")->fetchAll();
		assign("data",$data);
		//
		return view(['header','user/chongzhi_config','footer']);
	}
	/*
	* @name 获取充值配置信息
	* @method get_chongzhi_config_info
	*/	
	public function get_chongzhi_config_info($id){
		responseType("json");
		if(!check($id,"integt0")){
			return ["status"=>"error","msg"=>"获取信息失败"];
		}
		$id = intval($id);
		dbConn();
		$info = Db()->table("user_chongzhi")->where("id={$id}")->fetch();
		if(empty($info)){
			return ["status"=>"error","msg"=>"获取信息失败"];		
		}
		return ["status"=>"ok","data"=>$info];
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
