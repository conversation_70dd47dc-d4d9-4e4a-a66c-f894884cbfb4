<script setup>
import { ref, reactive, computed } from 'vue';
import { publishDiary, upload_img } from '@/api/index.js';
import { store } from '@/store';
import { requireLogin } from '@/utils/auth';
import customNavbar from '@/components/customNavbar.vue';

// 状态管理
const content = ref(''); // 日记内容
const images = ref([]); // 图片列表
const location = ref(null); // 位置信息
const privacy = ref('public'); // 隐私设置
const isSubmitting = ref(false); // 提交状态

// 隐私选项
const privacyOptions = [
  { label: '公开', value: 'public' },
  { label: '私密', value: 'private' }
];

// 计算最大上传图片数量（会员4张，非会员1张）
const maxImageCount = computed(() => {
    const userInfo = store().$state.userInfo;
    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员
    return isVip ? 4 : 1;
});

// 处理图片上传
const handleAfterRead = async (event) => {
  // 将新选择的文件添加到列表，并标记为上传中
  let lists = [].concat(event.file);
  let fileListLen = images.value.length;

  lists.map((item) => {
    images.value.push({
      ...item,
      status: 'uploading',
      message: '上传中'
    });
  });

  // 依次上传新选择的文件
  for (let i = 0; i < lists.length; i++) {
    const currentFileIndex = fileListLen + i;
    try {
      const res = await upload_img(lists[i].url);

      if (res.status === 'ok' && res.data) {
        let item = images.value[currentFileIndex];
        if (item) {
          images.value.splice(currentFileIndex, 1, {
            ...item,
            status: 'success',
            message: '',
            url: res.data
          });
        }
      } else {
        if (images.value[currentFileIndex]) {
          images.value[currentFileIndex].status = 'failed';
          images.value[currentFileIndex].message = res.msg || '上传失败';
        }
        uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });
      }
    } catch (error) {
      if (images.value[currentFileIndex]) {
        images.value[currentFileIndex].status = 'failed';
        images.value[currentFileIndex].message = '上传失败';
      }
      uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });
    }
  }
};

// 删除图片
const handleDeletePic = (event) => {
  images.value.splice(event.index, 1);
};

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      location.value = {
        name: res.name,
        address: res.address,
        latitude: res.latitude,
        longitude: res.longitude
      };
    },
    fail: () => {
      uni.showToast({ title: '选择位置失败', icon: 'none' });
    }
  });
};

// 清除位置
const clearLocation = () => {
  location.value = null;
};

// 发布日记
const handleSubmit = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再发布日记')) {
    return;
  }

  if (!content.value.trim() && images.value.filter(img => img.status === 'success').length === 0) {
    uni.showToast({ title: '请输入日记内容或添加图片', icon: 'none' });
    return;
  }

  if (isSubmitting.value) return;

  isSubmitting.value = true;

  try {
    // 上传图片
    const uploadedImageUrls = [];
    for (const img of images.value) {
      if (img.status === 'success' && img.url) {
        uploadedImageUrls.push(img.url);
      }
    }

    // 准备参数 - 与后端API保持一致
    const params = {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      content: content.value.trim(),
      images: uploadedImageUrls, // 传递数组，与动态页面保持一致
      location: location.value ? JSON.stringify(location.value) : '',
      tags: '', // 日记暂不支持标签，但保持字段一致性
      privacy: privacy.value,
      type: 'diary' // 标识为日记类型
    };

    const res = await publishDiary(params);

    if (res.status === 'ok') {
      uni.showToast({ title: '发布成功', icon: 'success' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    } else if (res.status === 'relogin') {
      uni.showToast({ title: '请先登录', icon: 'none' });
    } else {
      uni.showToast({ title: res.msg || '发布失败', icon: 'none' });
    }
  } catch (error) {
    uni.showToast({ title: '发布失败，请重试', icon: 'none' });
  } finally {
    isSubmitting.value = false;
  }
};

// 返回
const goBack = () => {
  uni.navigateBack();
};
</script>

<template>
  <view class="diary-post-container">
    <!-- 统一导航栏 -->
    <customNavbar
      title="写日记"
      backIcon="close"
      @back="goBack"
    />

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 日记内容输入 -->
      <view class="content-section">
        <textarea
          v-model="content"
          class="content-textarea"
          placeholder="记录今天的心情和想法..."
          auto-height
          :maxlength="2000"
          show-confirm-bar
        ></textarea>
        <view class="char-count">
          <text class="count-text">{{ content.length }}/2000</text>
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="image-section">
        <u-upload
          :fileList="images"
          @afterRead="handleAfterRead"
          @delete="handleDeletePic"
          name="file"
          multiple
          :maxCount="maxImageCount"
          :previewImage="true"
          width="200rpx"
          height="200rpx"
          uploadIconColor="#ccc"
        ></u-upload>
        <view v-if="maxImageCount === 1" class="upload-tip">
          <text class="tip-text">非会员最多上传1张图片，升级会员可上传4张</text>
        </view>
      </view>

      <!-- 位置信息 -->
      <view class="location-section">
        <view class="section-header">
          <u-icon name="map" size="16" color="#6AC086"></u-icon>
          <text class="section-title">位置</text>
        </view>
        <view v-if="location" class="location-info">
          <view class="location-content">
            <text class="location-name">{{ location.name }}</text>
            <text class="location-address">{{ location.address }}</text>
          </view>
          <u-icon name="close" size="16" color="#999" @click="clearLocation"></u-icon>
        </view>
        <view v-else class="location-empty" @click="chooseLocation">
          <text class="location-placeholder">添加位置</text>
          <u-icon name="arrow-right" size="14" color="#999"></u-icon>
        </view>
      </view>

      <!-- 隐私设置 -->
      <view class="privacy-section">
        <view class="section-header">
          <u-icon name="lock" size="16" color="#6AC086"></u-icon>
          <text class="section-title">隐私设置</text>
        </view>
        <u-radio-group v-model="privacy" placement="row">
          <u-radio
            v-for="item in privacyOptions"
            :key="item.value"
            :label="item.label"
            :name="item.value"
            activeColor="#6AC086"
          ></u-radio>
        </u-radio-group>
      </view>
    </scroll-view>

    <!-- 发布按钮 - 固定在页面底部右侧 -->
    <view class="publish-button-container">
      <view
        class="publish-btn"
        :class="{ 'disabled': isSubmitting || (!content.trim() && images.filter(img => img.status === 'success').length === 0) }"
        @click="handleSubmit"
      >
        <u-icon name="checkmark" size="44rpx" color="#ffffff" v-if="!isSubmitting"></u-icon>
        <u-loading-icon v-if="isSubmitting" color="#ffffff" size="40rpx"></u-loading-icon>
        <text class="publish-text" v-if="!isSubmitting">发布</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.diary-post-container {
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.custom-navbar {
  background: #ffffff;
  padding-top: calc(var(--status-bar-height) + 60rpx);
  border-bottom: 1rpx solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);

  .navbar-content {
    height: calc(44px + 40rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;

    .navbar-left {
      width: 120rpx;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .navbar-title {
      .title-text {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }
    }

    .navbar-right {
      width: 120rpx;
      display: flex;
      justify-content: flex-end;

      .publish-btn {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
        border-radius: 40rpx;
        position: relative;
        box-shadow: 0 4rpx 8rpx rgba(106, 192, 134, 0.2);

        &.disabled {
          opacity: 0.6;
          pointer-events: none;
        }

        .publish-icon {
          width: 40rpx;
          height: 40rpx;
        }

        .loading-icon {
          position: absolute;
          width: 32rpx;
          height: 32rpx;
          border: 3rpx solid #ffffff;
          border-top-color: transparent;
          border-radius: 50%;
          animation: loading 0.8s linear infinite;
        }
      }

      @keyframes loading {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }
  }
}

.content-area {
  flex: 1;
  padding: 24rpx;
  padding-top: 16rpx;
  padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */
  overflow-y: auto;
}

.content-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  
  .content-textarea {
    width: 100%;
    min-height: 300rpx;
    font-size: 32rpx;
    line-height: 1.6;
    color: #333333;
    border: none;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  }
  
  .char-count {
    display: flex;
    justify-content: flex-end;
    margin-top: 16rpx;
    
    .count-text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.image-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;

  .upload-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #ddd;
    border-radius: 12rpx;

    .upload-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #999;
    }
  }

  .upload-tip {
    margin-top: 16rpx;
    padding: 12rpx 16rpx;
    background-color: #fff3cd;
    border: 1rpx solid #ffeaa7;
    border-radius: 8rpx;

    .tip-text {
      font-size: 24rpx;
      color: #856404;
      line-height: 1.4;
    }
  }
}

.location-section, .privacy-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  
  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    
    .section-title {
      margin-left: 12rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    }
  }
}

.location-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .location-content {
    flex: 1;
    
    .location-name {
      display: block;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .location-address {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.location-empty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .location-placeholder {
    font-size: 30rpx;
    color: #999;
  }
}

/* 发布按钮容器 */
.publish-button-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 1000;
}

/* 发布按钮样式 */
.publish-btn {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 60rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  transition: all 0.3s ease;

  .publish-text {
    font-size: 24rpx;
    color: #ffffff;
    margin-top: 8rpx;
    font-weight: 500;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>
