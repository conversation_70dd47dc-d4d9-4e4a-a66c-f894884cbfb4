<template>
  <view class="page">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="运营佣金"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <!-- 佣金统计卡片 -->
      <view class="commission-stats">
        <view class="stats-item">
          <view class="stats-number">{{ totalCommission }}</view>
          <view class="stats-label">累计佣金</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ monthCommission }}</view>
          <view class="stats-label">本月佣金</view>
        </view>
      </view>
      
      <!-- 筛选器 -->
      <view class="filter-container">
        <view class="filter-item">
          <u-button
            :type="filterType === 'all' ? 'primary' : 'info'"
            size="small"
            @click="setFilter('all')"
            customStyle="border-radius: 30rpx; margin-right: 20rpx;"
            :customTextStyle="filterType === 'all' ? 'color: #ffffff' : 'color: #6AC086'"
          >
            全部
          </u-button>
          <u-button
            :type="filterType === 'invite' ? 'primary' : 'info'"
            size="small"
            @click="setFilter('invite')"
            customStyle="border-radius: 30rpx; margin-right: 20rpx;"
            :customTextStyle="filterType === 'invite' ? 'color: #ffffff' : 'color: #6AC086'"
          >
            邀请佣金
          </u-button>
          <u-button
            :type="filterType === 'operation' ? 'primary' : 'info'"
            size="small"
            @click="setFilter('operation')"
            customStyle="border-radius: 30rpx;"
            :customTextStyle="filterType === 'operation' ? 'color: #ffffff' : 'color: #6AC086'"
          >
            运营佣金
          </u-button>
        </view>
      </view>
      
      <!-- 佣金记录列表 -->
      <view class="commission-list" v-if="commissionList.length > 0">
        <view 
          class="commission-item"
          v-for="item in commissionList"
          :key="item.id"
        >
          <view class="item-header">
            <view class="commission-type">
              <text class="type-badge" :class="getTypeBadgeClass(item.commission_type)">
                {{ getTypeText(item.commission_type) }}
              </text>
              <text class="commission-amount">+{{ item.money }}</text>
            </view>
            <view class="commission-time">{{ formatTime(item.time) }}</view>
          </view>
          
          <view class="item-content" v-if="item.commission_type === 'operation'">
            <view class="commission-detail">
              <text class="detail-label">创建月份：</text>
              <text class="detail-value">{{ formatMonth(item.time) }}</text>
            </view>
            <!-- 🆕 新增：系统分配标识 -->
            <view class="commission-detail" v-if="isSystemAssignedCommission(item)">
              <text class="detail-label">来源：</text>
              <text class="detail-value system-assigned">系统分配用户运营佣金</text>
            </view>
          </view>
          
          <view class="item-content" v-else-if="item.user && item.user.nickname">
            <view class="commission-detail">
              <text class="detail-label">来源用户：</text>
              <text class="detail-value">{{ item.user.nickname }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <u-empty
          mode="list"
          text="暂无佣金记录"
          textColor="#999999"
          textSize="28"
        ></u-empty>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && !loading">
        <u-button
          type="primary"
          :loading="loadingMore"
          @click="loadMore"
          customStyle="background: #6AC086; border: none; border-radius: 50rpx;"
        >
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </u-button>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <u-loading-page 
      :loading="loading" 
      loading-text="加载中..."
      bg-color="#f8f9fa"
    ></u-loading-page>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { requireLogin } from "@/utils/auth";
import { branch_presidentget_commission } from "@/api";
import { store } from "@/store";

// 数据状态
const commissionList = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;
const filterType = ref('all'); // all, invite, operation

// 统计数据
const totalCommission = ref('0.00');
const monthCommission = ref('0.00');

// 页面加载
onLoad(() => {
  if (!requireLogin()) {
    return;
  }
  
  loadCommissionData();
});

onShow(() => {
  // 每次显示页面时刷新数据
  refreshData();
});

// 加载佣金数据
const loadCommissionData = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1;
      hasMore.value = true;
      loading.value = true;
    } else if (!hasMore.value) {
      return;
    } else {
      // 第一次加载时也要设置loading状态
      loading.value = true;
    }
    
    const userInfo = store().$state.userInfo;

    // 验证用户登录状态
    if (!userInfo || !userInfo.uid || !userInfo.token) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const params = {
      uid: userInfo.uid,
      token: userInfo.token,
      page: currentPage.value,
      page_size: pageSize
    };

    // 添加佣金类型筛选
    if (filterType.value !== 'all') {
      params.commission_type = filterType.value;
    }

    console.log('加载佣金数据，参数:', params);

    const res = await branch_presidentget_commission(params);

    console.log('佣金数据API响应:', res);
    
    if (res.status === 'ok') {
      const newCommissions = res.data || [];
      
      if (isRefresh) {
        commissionList.value = newCommissions;
      } else {
        commissionList.value.push(...newCommissions);
      }
      
      // 判断是否还有更多数据
      hasMore.value = newCommissions.length === pageSize;
      
      // 计算统计数据（仅在刷新时计算）
      if (isRefresh) {
        calculateStats();
      }
      
    } else if (res.status === 'empty') {
      if (isRefresh) {
        commissionList.value = [];
      }
      hasMore.value = false;
    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: res.msg || '加载失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('加载佣金数据失败:', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 🆕 新增：格式化时间戳为月份
const formatMonth = (timestamp) => {
  if (!timestamp) return '未知';
  const date = new Date(timestamp * 1000);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
};

// 计算统计数据
const calculateStats = () => {
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式
  let total = 0;
  let monthTotal = 0;
  
  commissionList.value.forEach(item => {
    const amount = parseFloat(item.money) || 0;
    total += amount;
    
    // 计算本月佣金
    if (item.time && item.time.startsWith(currentMonth)) {
      monthTotal += amount;
    }
  });
  
  totalCommission.value = total.toFixed(2);
  monthCommission.value = monthTotal.toFixed(2);
};

// 设置筛选器
const setFilter = (type) => {
  if (filterType.value !== type) {
    filterType.value = type;
    refreshData();
  }
};

// 刷新数据
const refreshData = () => {
  loadCommissionData(true);
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadingMore.value = true;
    currentPage.value++;
    loadCommissionData();
  }
};

// 获取类型文本
const getTypeText = (type) => {
  switch (type) {
    case 'invite':
      return '邀请佣金';
    case 'operation':
      return '运营佣金';
    default:
      return '邀请佣金';
  }
};

// 获取类型徽章样式
const getTypeBadgeClass = (type) => {
  switch (type) {
    case 'invite':
      return 'type-invite';
    case 'operation':
      return 'type-operation';
    default:
      return 'type-invite';
  }
};

// 🆕 新增：判断是否为系统分配佣金
const isSystemAssignedCommission = (item) => {
  return item.remark && item.remark.includes('系统分配用户运营佣金');
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style lang="scss" scoped>
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.commission-stats {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-around;
  color: #ffffff;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 26rpx;
  opacity: 0.8;
}

.filter-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  display: flex;
  align-items: center;
}

.commission-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.commission-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.commission-type {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.type-badge {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  color: #ffffff;
}

.type-invite {
  background: #6AC086;
}

.type-operation {
  background: #ff6b6b;
}

.commission-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #6AC086;
}

.commission-time {
  font-size: 24rpx;
  color: #999999;
}

.item-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.commission-detail {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666666;
  margin-right: 10rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333333;
}

// 🆕 新增：系统分配标识样式
.system-assigned {
  color: #FF9800 !important;
  font-weight: bold;
  position: relative;

  &::before {
    content: '🤖';
    margin-right: 8rpx;
  }
}

.empty-state {
  margin-top: 100rpx;
}

.load-more {
  margin-top: 40rpx;
  text-align: center;
}
</style>
