cat > /home/<USER>/default/huodongbao_admin/debug_test.php << 'EOF'
<?php
// 启用所有错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== PHP Debug Test ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Current Directory: " . __DIR__ . "\n";

// 测试1: 检查核心文件是否存在
echo "\n=== Test 1: Check Core Files ===\n";
$coreFiles = [
    'core/init.php',
    'core/func.php', 
    'controller/BranchPresident.php'
];

foreach ($coreFiles as $file) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✓ $file exists (" . filesize($fullPath) . " bytes)\n";
    } else {
        echo "✗ $file NOT FOUND\n";
    }
}

// 测试2: 尝试加载core/init.php
echo "\n=== Test 2: Load core/init.php ===\n";
try {
    require_once "core/init.php";
    echo "✓ core/init.php loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Error loading core/init.php: " . $e->getMessage() . "\n";
    exit(1);
} catch (Error $e) {
    echo "✗ Fatal error loading core/init.php: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试3: 检查自动加载器函数
echo "\n=== Test 3: Check Autoloader ===\n";
if (function_exists('autoload')) {
    echo "✓ autoload function exists\n";
} else {
    echo "✗ autoload function NOT FOUND\n";
}

// 测试4: 尝试手动加载BranchPresident类
echo "\n=== Test 4: Manual Load BranchPresident ===\n";
$classFile = __DIR__ . '/controller/BranchPresident.php';
if (file_exists($classFile)) {
    try {
        require_once $classFile;
        echo "✓ BranchPresident.php included successfully\n";
        
        if (class_exists('controller\\BranchPresident')) {
            echo "✓ controller\\BranchPresident class exists\n";
        } else {
            echo "✗ controller\\BranchPresident class NOT FOUND after include\n";
        }
    } catch (Exception $e) {
        echo "✗ Error including BranchPresident.php: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "✗ Fatal error including BranchPresident.php: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ BranchPresident.php file not found\n";
}

echo "\n=== Test Complete ===\n";
EOF