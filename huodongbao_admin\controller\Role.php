<?php
/*
 * @className 用户接口 
 * @ignore
*/
/*
* @name 注册 
* @method login
* @ignore
*/
namespace controller;
use core\Controller;
use core\Db;
class Role extends Controller{
	//config
	private $path = BASE_PATH . "controller";
	public function __construct(){
		parent::__construct();
	}
	
	//生成权限列表
	public function index(){
		responseType("json");
		$files = scandir($this->path);
		$data = [];
		foreach($files as $file){
			if(
				substr($file,0,1) == "." || 
				substr($file,-4,4) != ".php" 
			)continue;
			$fileName = substr($file,0,-4);
			$code = file_get_contents($this->path."/".$file);
			preg_match_all('@/\*.*?\*/@s',$code,$match);
			if(empty($match[0]))continue;
			$className = "";
			foreach($match[0] as $block){
				//
				$ignore = false;
				$block = str_ireplace("\r\n","\n",$block);
				$block = str_ireplace("\r","",$block);
				$lines = explode("\n",$block);
				foreach($lines as $line){
					if(stripos($line,"@ignore") !== false){
						$ignore = true;
						break;
					}
				}
				if($ignore) continue;
				//
				$is_find_className = false;
				foreach($lines as $line){
					if(stripos($line,"@className") !== false){
						$className = str_ireplace("@className","",$line);
						$className = trim(str_ireplace("*","",$className));
						$is_find_className = true;
						break;
					}
				}
				if($is_find_className)break;
			}
			if(empty($className))continue;
			
			foreach($match[0] as $block){
				//
				$ignore = false;
				$block = str_ireplace("\r\n","\n",$block);
				$block = str_ireplace("\r","",$block);
				$lines = explode("\n",$block);
				foreach($lines as $line){
					if(stripos($line,"@ignore") !== false){
						$ignore = true;
						break;
					}
				}
				if($ignore) continue;
				//
				$name = "";
				$method = "";
				foreach($lines as $line){
					if(stripos($line,"@name") !== false){
						$name = str_ireplace("@name","",$line);
						$name = trim(str_ireplace("*","",$name));
					}
					if(stripos($line,"@method") !== false){
						$method = str_ireplace("@method","",$line);
						$method = trim(str_ireplace("*","",$method));
					}
				}
				if(!empty($name) && !empty($method)){
					$data[] = ["module_name"=>$className,"role_name"=>$name,"role"=>$fileName.".".$method];
				}
			}
		}
		if(!empty($data)){
			dbConn();
			$sql = "INSERT INTO `root_role` (`role`,`role_name`,`module_name`) VALUES (:role,:role_name,:module_name) ";
			$sql .= "ON DUPLICATE KEY UPDATE `role_name`=:role_name,`module_name`=:module_name";
			$sth = Db()->prepare($sql);
			$sth->bindParam(":role",$role,\PDO::PARAM_STR);
			$sth->bindParam(":role_name",$role_name,\PDO::PARAM_STR);
			$sth->bindParam(":module_name",$module_name,\PDO::PARAM_STR);
			foreach($data as $v){
				$role = htmlspecialchars(strtolower($v['role']));
				$role_name = htmlspecialchars($v['role_name']);
				$module_name = htmlspecialchars($v['module_name']);
				$sth->execute();
			}
		}
		return ["status"=>"ok","msg"=>"生成权限完成"];
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}