<script setup>
import { ref, computed, onMounted } from 'vue';
import { store } from '@/store';
import { navto } from '@/utils';
import { getBranchLeaderInfo } from '@/api';
import CustomTabBar from '../components/CustomTabBar.vue';

// 状态管理
const branchLeaderInfo = ref({
  has_branch: false,
  has_leader: false,
  has_qr_code: false,
  branch_name: '',
  leader_name: '',
  leader_qr_image: '',
  message: ''
});

// 通知功能列表
const notificationFeatures = [
  {
    id: 'messages',
    icon: 'bell-fill',
    title: '通知消息',
    description: '查看系统通知和消息',
    url: '/pages/bundle/user/notifications',
    hasNotification: true
  },
  {
    id: 'contact-leader',
    icon: 'phone-fill',
    title: '联系分会长',
    description: '联系您所在分会的分会长',
    isBranchContact: true
  },
  {
    id: 'apply-president',
    icon: 'star-fill',
    title: '申请分会长',
    description: '申请成为分会长，管理分会事务',
    url: '/pages/branch_president/apply'
  }
];

// 计算是否显示申请分会长功能
const shouldShowApplyPresident = computed(() => {
  const userInfo = store().$state.userInfo;
  const roleType = parseInt(userInfo?.role_type || 0);
  // 只有普通用户(role_type=2)才显示申请分会长功能
  return roleType === 2;
});

// 过滤后的功能列表
const filteredFeatures = computed(() => {
  return notificationFeatures.filter(feature => {
    if (feature.id === 'apply-president') {
      return shouldShowApplyPresident.value;
    }
    return true;
  });
});

// 获取分会长信息
const fetchBranchLeaderInfo = async () => {
  try {
    const userInfo = store().$state.userInfo;
    if (!userInfo?.uid || !userInfo?.token) {
      console.log('用户未登录，无法获取分会长信息');
      return;
    }

    const res = await getBranchLeaderInfo({
      uid: userInfo.uid,
      token: userInfo.token
    });

    if (res.status === 'ok') {
      // 直接使用后端返回的数据结构
      branchLeaderInfo.value = {
        has_branch: res.data.has_branch || false,
        has_leader: res.data.has_leader || false,
        has_qr_code: res.data.has_qr_code || false,
        branch_name: res.data.branch_name || '',
        leader_name: res.data.leader_name || '',
        leader_qr_image: res.data.leader_qr_image || '',
        message: res.data.message || ''
      };
    } else {
      console.error('获取分会长信息失败:', res.msg);
    }
  } catch (error) {
    console.error('获取分会长信息异常:', error);
  }
};

// 处理功能点击
const handleFeatureClick = (feature) => {
  if (feature.url) {
    // 直接跳转到指定页面
    navto(feature.url);
  } else if (feature.isBranchContact) {
    // 联系分会长功能
    handleContactLeader();
  }
};

// 联系分会长功能
const handleContactLeader = () => {
  if (!branchLeaderInfo.value.has_branch) {
    uni.showModal({
      title: '提示',
      content: '您还没有加入任何分会，无法联系分会长。',
      showCancel: false
    });
    return;
  }

  if (!branchLeaderInfo.value.has_leader) {
    uni.showModal({
      title: '提示',
      content: branchLeaderInfo.value.message || '暂无分会长信息',
      showCancel: false
    });
    return;
  }

  // 检查是否有二维码
  if (!branchLeaderInfo.value.has_qr_code || !branchLeaderInfo.value.leader_qr_image) {
    uni.showModal({
      title: '提示',
      content: '分会长暂未设置微信二维码，请联系管理员。',
      showCancel: false
    });
    return;
  }

  // 显示分会长二维码
  showQRCodeModal();
};

// 显示二维码弹窗
const showQRCodeModal = () => {
  const leaderInfo = branchLeaderInfo.value;

  uni.showModal({
    title: '联系分会长',
    content: `分会长：${leaderInfo.leader_name}\n长按识别二维码添加微信`,
    confirmText: '查看二维码',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 预览二维码图片
        uni.previewImage({
          urls: [leaderInfo.leader_qr_image],
          current: leaderInfo.leader_qr_image,
          longPressActions: {
            itemList: ['保存到相册'],
            success: (data) => {
              if (data.tapIndex === 0) {
                // 保存图片到相册
                uni.saveImageToPhotosAlbum({
                  filePath: leaderInfo.leader_qr_image,
                  success: () => {
                    uni.showToast({
                      title: '已保存到相册',
                      icon: 'success'
                    });
                  },
                  fail: () => {
                    uni.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                });
              }
            }
          }
        });
      }
    }
  });
};

onMounted(() => {
  fetchBranchLeaderInfo();
});
</script>

<template>
  <view class="notifications-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-content">
        <text class="page-title">通知中心</text>
        <text class="page-subtitle">消息通知与分会服务</text>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="features-container">
      <view 
        v-for="feature in filteredFeatures" 
        :key="feature.id"
        class="feature-item"
        @click="handleFeatureClick(feature)"
      >
        <!-- 图标容器 -->
        <view class="feature-icon-container">
          <u-icon 
            :name="feature.icon" 
            size="48rpx" 
            color="#6AC086"
          ></u-icon>
          <!-- 通知红点 -->
          <view 
            v-if="feature.hasNotification" 
            class="notification-badge"
          ></view>
        </view>

        <!-- 内容区域 -->
        <view class="feature-content">
          <text class="feature-title">{{ feature.title }}</text>
          <text class="feature-description">{{ feature.description }}</text>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <CustomTabBar :current="3" />
  </view>
</template>

<style lang="scss" scoped>
/* 统一设计变量 */
:root {
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --radius-card: 20rpx;
  --color-bg-page: #f8f9fa;
  --color-bg-card: #ffffff;
  --color-text-title: #333333;
  --color-text-body: #666666;
  --color-text-caption: #999999;
  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.notifications-page {
  min-height: 100vh;
  background-color: var(--color-bg-page);
  padding-bottom: 200rpx; /* 为底部导航栏留出空间 */
}

.page-header {
  background: linear-gradient(135deg, #6AC086 0%, #88D7A0 100%);
  padding: 80rpx var(--spacing-lg) 60rpx;
  margin-bottom: var(--spacing-lg);

  .header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .page-title {
    font-size: 48rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16rpx;
  }

  .page-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.features-container {
  padding: 0 var(--spacing-lg);
}

.feature-item {
  display: flex;
  align-items: center;
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  min-height: 88rpx; /* 确保最小触摸区域 */

  &:active {
    transform: scale(0.98);
    background-color: #f5f5f5;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.feature-icon-container {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, rgba(106, 192, 134, 0.1) 0%, rgba(136, 215, 160, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}

.notification-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--color-text-title);
  margin-bottom: 8rpx;
}

.feature-description {
  font-size: 26rpx;
  color: var(--color-text-body);
  line-height: 1.4;
}
</style>
