<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad, onShareAppMessage, onShareTimeline, onBackPress } from '@dcloudio/uni-app';
import { getFeedDetail, likeFeed, favoriteFeed, commentFeed, getFeedComments, likeComment, getCommentLikeStatus, deleteFeed, deleteComment } from '@/api/index.js';
import { store } from '@/store';
import { generateShareImage, saveImageToAlbum } from '@/utils/painterShare';
import SharePopup from '@/components/share-popup/share-popup.vue';
import { handleShare as handleShareFromUtils, createFeedShareConfig, handleWechatShare, hideShareMenu, isShareMenuShow } from '@/utils/uniShareConfig';
import { requireLogin } from '@/utils/auth';

// 添加调试日志
console.log('日记详情页面加载');

// --- 状态变量 ---
const feedDetail = ref(null); // 动态详情数据
const isLoading = ref(true); // 加载状态
const showComments = ref(false); // 是否显示评论区
const commentText = ref(''); // 评论文本
const comments = ref([]); // 评论列表
const commentsLoading = ref(false); // 评论加载状态
const commentsPage = ref(1); // 评论页码
const hasMoreComments = ref(true); // 是否有更多评论
const commentsRefreshing = ref(false); // 评论下拉刷新状态
const replyTo = ref(null); // 回复对象
const imageList = ref([]); // 图片列表
const showShareImagePreview = ref(false); // 是否显示分享图片预览
const shareImagePath = ref(''); // 分享图片路径
const sortType = ref('latest'); // 评论排序方式: 'latest'最新 或 'hot'最热

// 分享相关状态
const showSharePopup = ref(false);
const currentShareData = ref(null);

// --- 计算属性 ---
const commentPlaceholder = computed(() => {
  return replyTo.value ? `回复 ${replyTo.value.nickname}` : '写下你的评论...';
});

// 检查当前用户是否为内容创建者
const isContentCreator = computed(() => {
  if (!feedDetail.value || !store().$state.userInfo?.uid) return false;
  return feedDetail.value.user_id === store().$state.userInfo.uid;
});

// 检查当前用户是否为评论创建者
const isCommentCreator = (comment) => {
  if (!comment || !store().$state.userInfo?.uid) return false;
  return comment.user_id === store().$state.userInfo.uid;
};

// --- 生命周期钩子 ---
onLoad(async (options) => {
  const feedId = options?.feedId;
  if (!feedId) {
    uni.showToast({ title: '无法加载动态详情', icon: 'none' });
    uni.navigateBack();
    return;
  }

  try {
    isLoading.value = true;
    await loadFeedDetail(feedId);

    // 显示评论区 - 移到加载完成后执行
    if (options?.showComments === 'true') {
      showComments.value = true;
      // 直接加载评论
      loadComments(true);
    }
  } catch (error) {
    console.error('加载动态详情失败:', error);
    uni.showToast({ title: '加载失败，请重试', icon: 'none' });
  } finally {
    isLoading.value = false;
  }
});

onMounted(() => {
  // 预加载评论数据
  if (feedDetail.value?.id) {
    loadComments(true);
  }
});

// 分享配置
onShareAppMessage(() => {
  try {
    if (!feedDetail.value) {
      console.warn('动态信息未加载完成，使用默认分享信息');
      return {
        title: '分享一条精彩动态',
        path: '/pages/bundle/world/feed/index',
        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
      };
    }

    return {
      title: feedDetail.value.content ?
        (feedDetail.value.content.length > 30 ?
          feedDetail.value.content.substring(0, 30) + '...' :
          feedDetail.value.content) :
        '分享一条精彩动态',
      path: `/pages/bundle/world/feed/detail?feedId=${feedDetail.value.id}`,
      imageUrl: (feedDetail.value.images && feedDetail.value.images.length > 0) ?
        feedDetail.value.images[0] :
        (store().$state.config?.img_config?.app_logo?.val || '')
    };
  } catch (error) {
    console.error('动态分享配置失败:', error);
    return {
      title: '分享一条精彩动态',
      path: '/pages/bundle/world/feed/index',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  }
});

onShareTimeline(() => {
  if (!feedDetail.value) return {};
  return {
    title: feedDetail.value.content.substring(0, 30) + '...',
    query: `feedId=${feedDetail.value.id}`,
    imageUrl: feedDetail.value.images && feedDetail.value.images.length > 0 ? feedDetail.value.images[0] : ''
  };
});

// 处理返回按键，确保uni-share菜单能正确关闭
onBackPress(({ from }) => {
  console.log('返回按键触发，来源:', from);
  if (from === 'backbutton' && isShareMenuShow()) {
    hideShareMenu();
    return true; // 阻止默认返回行为
  }
  return false; // 允许默认返回行为
});

// --- 方法 ---
// 加载动态详情
const loadFeedDetail = async (feedId) => {
  try {
    const res = await getFeedDetail({
      id: feedId,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      feedDetail.value = res.data;

      // 从全局状态同步点赞和收藏状态
      const globalLikeState = store().getFeedLikeState(feedDetail.value.id);
      const globalFavoriteState = store().getFeedFavoriteState(feedDetail.value.id);

      if (globalLikeState) {
        feedDetail.value.isLiked = globalLikeState.isLiked;
        feedDetail.value.likeCount = globalLikeState.likeCount;
      }

      if (globalFavoriteState !== null) {
        feedDetail.value.isFavorited = globalFavoriteState;
      }

      // 处理图片列表 - 修复图片显示问题
      console.log('原始动态数据:', feedDetail.value);

      // 尝试多种可能的图片字段
      let images = [];
      if (feedDetail.value.images_json) {
        try {
          images = JSON.parse(feedDetail.value.images_json);
          console.log('从images_json解析图片:', images);
        } catch (e) {
          console.error('解析images_json失败:', e);
        }
      } else if (feedDetail.value.images) {
        // 如果images字段是字符串，尝试解析
        if (typeof feedDetail.value.images === 'string') {
          try {
            images = JSON.parse(feedDetail.value.images);
            console.log('从images字符串解析图片:', images);
          } catch (e) {
            // 如果不是JSON，可能是单个URL
            images = [feedDetail.value.images];
            console.log('将images作为单个URL:', images);
          }
        } else if (Array.isArray(feedDetail.value.images)) {
          images = feedDetail.value.images;
          console.log('直接使用images数组:', images);
        }
      }

      imageList.value = Array.isArray(images) ? images.filter(img => img && img.trim()) : [];
      console.log('最终图片列表:', imageList.value);
    } else {
      throw new Error(res.msg || '获取动态详情失败');
    }
  } catch (error) {
    console.error('Error loading feed detail:', error);
    throw error;
  }
};

// 加载评论
const loadComments = async (reset = false) => {
  if (commentsLoading.value || (!hasMoreComments.value && !reset)) return;

  if (!feedDetail.value?.id) return;

  try {
    commentsLoading.value = true;

    if (reset) {
      commentsPage.value = 1;
      comments.value = [];
    }

    // 输出请求参数用于调试
    console.log('获取评论请求参数:', {
      feed_id: feedDetail.value.id,
      page: commentsPage.value,
      page_size: 10,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      sort_type: sortType.value
    });

    const res = await getFeedComments({
      feed_id: feedDetail.value.id,
      page: commentsPage.value,
      page_size: 10,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      sort_type: sortType.value
    });

    console.log('评论API响应:', res);

    if (res.status === 'ok') {
      const commentsList = res.data.list || [];

      // 为每个评论添加点赞状态
      if (commentsList.length > 0 && store().$state.userInfo?.uid) {
        await loadCommentLikeStatus(commentsList);
      }

      if (reset) {
        comments.value = commentsList;
      } else {
        comments.value = [...comments.value, ...commentsList];
      }

      hasMoreComments.value = commentsList.length === 10;
      commentsPage.value++;
    } else if (res.status === 'empty') {
      hasMoreComments.value = false;
    }
  } catch (error) {
    console.error('加载评论失败:', error);
    uni.showToast({ title: '加载评论失败', icon: 'none' });
  } finally {
    commentsLoading.value = false;
    commentsRefreshing.value = false; // 🔧 修复：确保下拉刷新状态重置
  }
};

// 🆕 新增：评论区下拉刷新
const onCommentsRefresh = () => {
  commentsRefreshing.value = true;
  loadComments(true);
};

// 加载评论点赞状态
const loadCommentLikeStatus = async (commentsList) => {
  try {
    const commentIds = commentsList.map(comment => comment.id).join(',');
    const res = await getCommentLikeStatus({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      comment_ids: commentIds,
      comment_type: 'feed'
    });

    if (res.status === 'ok') {
      const likedComments = res.data.liked_comments || [];
      commentsList.forEach(comment => {
        // 优先使用全局状态，如果没有则使用API返回的状态
        const globalLikeState = store().getCommentLikeState(comment.id);
        if (globalLikeState) {
          comment.isLiked = globalLikeState.isLiked;
          comment.like_count = globalLikeState.likeCount;
        } else {
          comment.isLiked = likedComments.includes(comment.id);
          comment.like_count = comment.like_count || 0;
        }
      });
    }
  } catch (error) {
    console.error('加载评论点赞状态失败:', error);
  }
};

// 处理评论点赞
const handleCommentLike = async (comment) => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞评论')) {
    return;
  }

  // 乐观更新UI
  const originalLikedState = comment.isLiked;
  const originalLikeCount = comment.like_count || 0;

  comment.isLiked = !comment.isLiked;
  comment.like_count = comment.isLiked ? originalLikeCount + 1 : originalLikeCount - 1;

  try {
    // 记录请求参数便于调试
    const requestParams = {
      id: comment.id,  // 这里会被API中转换为comment_id
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      comment_type: 'feed'  // 确保传递评论类型
    };
    console.log('发送评论点赞请求:', requestParams);

    const res = await likeComment(requestParams);
    console.log('评论点赞响应:', res);

    if (res.status !== 'ok') {
      // 恢复原状态
      comment.isLiked = originalLikedState;
      comment.like_count = originalLikeCount;
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    } else {
      // 点赞成功，更新全局状态
      store().updateCommentLike(comment.id, comment.isLiked, comment.like_count);
      uni.showToast({
        title: comment.isLiked ? '点赞成功' : '已取消点赞',
        icon: 'success',
        duration: 1500
      });
    }
  } catch (error) {
    // 恢复原状态
    comment.isLiked = originalLikedState;
    comment.like_count = originalLikeCount;
    console.error('评论点赞失败:', error);
    uni.showToast({ title: '操作失败，请重试', icon: 'none' });
  }
};

// 处理点赞
const handleLike = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  if (!feedDetail.value) return;

  // 乐观更新UI
  const originalLikedState = feedDetail.value.isLiked;
  const originalLikeCount = feedDetail.value.likeCount;

  feedDetail.value.isLiked = !feedDetail.value.isLiked;
  feedDetail.value.likeCount += feedDetail.value.isLiked ? 1 : -1;

  try {
    console.log('发送日记点赞请求:', {
      id: feedDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    const res = await likeFeed({
      id: feedDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    console.log('日记点赞响应:', res);

    if (res.status !== 'ok') {
      // 恢复原状态
      feedDetail.value.isLiked = originalLikedState;
      feedDetail.value.likeCount = originalLikeCount;
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    } else {
      // 操作成功，更新全局状态
      store().updateFeedLike(feedDetail.value.id, feedDetail.value.isLiked, feedDetail.value.likeCount);
      uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
    }
  } catch (error) {
    // 恢复原状态
    feedDetail.value.isLiked = originalLikedState;
    feedDetail.value.likeCount = originalLikeCount;
    console.error('点赞失败:', error);
    uni.showToast({ title: '操作失败，请重试', icon: 'none' });
  }
};

// 处理收藏
const handleFavorite = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  if (!feedDetail.value) return;

  // 乐观更新UI
  const originalFavoritedState = feedDetail.value.isFavorited || false;
  feedDetail.value.isFavorited = !feedDetail.value.isFavorited;

  try {
    const res = await favoriteFeed({
      id: feedDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status !== 'ok') {
      // 恢复原状态
      feedDetail.value.isFavorited = originalFavoritedState;
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    } else {
      // 操作成功，更新全局状态
      store().updateFeedFavorite(feedDetail.value.id, feedDetail.value.isFavorited);
      uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
    }
  } catch (error) {
    // 恢复原状态
    feedDetail.value.isFavorited = originalFavoritedState;
    console.error('收藏失败:', error);
    uni.showToast({ title: '操作失败，请重试', icon: 'none' });
  }
};

// 切换评论区显示
const toggleComments = () => {
  showComments.value = !showComments.value;
  if (showComments.value) {
    loadComments(true);
  }
};

// 设置回复对象
const setReplyTo = (comment) => {
  replyTo.value = comment ? { id: comment.id, nickname: comment.nickname } : null;
  // 聚焦输入框
  if (comment) {
    setTimeout(() => {
      uni.pageScrollTo({
        scrollTop: 9999,
        duration: 300
      });
    }, 100);
  }
};

// 提交评论
const submitComment = async () => {
  if (!commentText.value.trim()) {
    uni.showToast({ title: '评论内容不能为空', icon: 'none' });
    return;
  }

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再评论')) {
    return;
  }

  try {
    // 确保parent_id是数字类型或null，修复外键约束错误
    const parentId = replyTo.value?.id ? Number(replyTo.value.id) : null;

    console.log('发送日记评论请求:', {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      feed_id: feedDetail.value.id,
      content: commentText.value,
      parent_id: parentId
    });

    const res = await commentFeed({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      feed_id: feedDetail.value.id,
      content: commentText.value,
      parent_id: parentId // 确保是数字类型或null
    });

    console.log('日记评论响应:', res);

    if (res.status === 'ok') {
      uni.showToast({ title: '评论成功', icon: 'success' });
      commentText.value = '';
      replyTo.value = null;

      // 重新加载评论
      loadComments(true);

      // 更新评论计数
      feedDetail.value.commentCount = (feedDetail.value.commentCount || 0) + 1;
    } else {
      uni.showToast({ title: res.msg || '评论失败', icon: 'none' });
    }
  } catch (error) {
    console.error('提交评论失败:', error);
    uni.showToast({ title: '评论失败，请重试', icon: 'none' });
  }
};

// 预览图片
const previewImage = (index) => {
  if (!imageList.value || imageList.value.length === 0) return;

  uni.previewImage({
    current: imageList.value[index],
    urls: imageList.value
  });
};

// 统一资源清理管理器
const resourceManager = {
  canvasContexts: new Set(),
  timers: new Set(),
  eventListeners: new Set(),

  // 注册Canvas上下文
  registerCanvas(canvasId) {
    this.canvasContexts.add(canvasId);
  },

  // 注册定时器
  registerTimer(timerId) {
    this.timers.add(timerId);
  },

  // 注册事件监听器
  registerEventListener(listener) {
    this.eventListeners.add(listener);
  },

  // 清理所有资源
  cleanup() {
    // 清理Canvas资源
    this.canvasContexts.forEach(canvasId => {
      try {
        uni.createSelectorQuery()
          .select(`#${canvasId}`)
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0] && res[0].node) {
              const canvas = res[0].node;
              const ctx = canvas.getContext('2d');
              if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                // 释放Canvas内存
                canvas.width = 0;
                canvas.height = 0;
              }
            }
          });
      } catch (error) {
        console.warn(`清理Canvas ${canvasId} 失败:`, error);
      }
    });

    // 清理定时器
    this.timers.forEach(timerId => {
      clearTimeout(timerId);
      clearInterval(timerId);
    });

    // 清理事件监听器
    this.eventListeners.forEach(listener => {
      if (typeof listener === 'function') {
        listener();
      }
    });

    // 清空所有集合
    this.canvasContexts.clear();
    this.timers.clear();
    this.eventListeners.clear();
  }
};

// 关闭页面
const closePage = () => {
  // 使用统一资源管理器清理所有资源
  resourceManager.cleanup();
  uni.navigateBack();
};

// 处理分享按钮点击 - 使用uni-share重构
const handleShareClick = async () => {
  if (!feedDetail.value) {
    uni.showToast({
      title: '内容加载中，请稍后',
      icon: 'none'
    });
    return;
  }

  console.log('开始分享动态，数据:', feedDetail.value);

  try {
    // 使用uni-share处理分享
    await handleShareFromUtils(feedDetail.value, 'feed',
      (result) => {
        console.log('动态分享成功:', result);
        // 分享成功回调
        handleShareSuccess(result);
      },
      (error) => {
        console.error('动态分享失败:', error);
        // 分享失败回调
        handleShareError(error);
      }
    );
  } catch (error) {
    console.error('分享处理失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  }
};

// 保持原有的handleShare函数用于兼容性
const handleShare = handleShareClick;

// 处理分享成功
const handleShareSuccess = (result) => {
  console.log('分享成功:', result);
  uni.showToast({
    title: '分享成功',
    icon: 'success'
  });
};

// 处理分享错误
const handleShareError = (error) => {
  console.error('分享失败:', error);
  uni.showToast({
    title: '分享失败',
    icon: 'none'
  });
};



// 分享给微信好友
const shareToWechatFriend = () => {
  // 在小程序环境中
  // #ifdef MP-WEIXIN
  try {
    // 显示分享菜单，让用户选择分享方式
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage']
    });

    uni.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });
  } catch (error) {
    console.error('显示分享菜单失败:', error);
    uni.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
  // #endif

  // 在非微信小程序环境中
  // #ifndef MP-WEIXIN
  // 先生成分享图片
  shareAsImage(true).then(imagePath => {
    // 准备分享内容
    const summary = feedDetail.value?.content || '分享一条日记';
    const shortSummary = summary.length > 50 ? summary.substring(0, 50) + '...' : summary;
    const imageUrl = imagePath || (feedDetail.value?.images && feedDetail.value.images.length > 0 ? feedDetail.value.images[0] : '');

    // 尝试使用系统分享
    if (uni.canIUse('shareWithSystem')) {
      uni.shareWithSystem({
        summary: shortSummary,
        imageUrl: imageUrl,
        href: `https://example.com/feed?id=${feedDetail.value?.id}`,
        success() {
          console.log('分享成功');
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail(err) {
          console.log('分享失败:', err);
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 复制链接到剪贴板
      uni.setClipboardData({
        data: `https://example.com/feed?id=${feedDetail.value?.id}`,
        success: function() {
          uni.showToast({
            title: '链接已复制，请粘贴分享',
            icon: 'none'
          });
        }
      });
    }
  }).catch(error => {
    console.error('生成分享图片失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  });
  // #endif
};

// 分享到朋友圈
const shareToWechatMoments = () => {
  // 在微信小程序环境中使用正确的分享方式
  // #ifdef MP-WEIXIN
  try {
    // 显示分享菜单，包含朋友圈选项
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    uni.showToast({
      title: '请点击右上角分享到朋友圈',
      icon: 'none'
    });
  } catch (error) {
    console.error('显示分享菜单失败:', error);
    uni.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
  // #endif

  // 在非微信小程序环境中，生成图片分享
  // #ifndef MP-WEIXIN
  shareAsImage();
  // #endif
};

// 生成分享图片
const shareAsImage = async (skipPreview = false) => {
  uni.showLoading({
    title: '生成分享图片中...'
  });

  try {
    // 使用Painter生成动态分享图片
    const imagePath = await generateShareImage({
      template: 'feed',
      content: feedDetail.value?.content || '分享一条动态',
      images: feedDetail.value?.images || [],
      author: feedDetail.value?.user?.nickname,
      authorAvatar: feedDetail.value?.user?.avatar,
      date: feedDetail.value?.created_at ? new Date(feedDetail.value.created_at).toLocaleDateString() : null,
      location: feedDetail.value?.location,
      watermark: '分享自MindfulMeetUp & 小聚会'
    });

    uni.hideLoading();
    console.log('Painter生成动态分享图片成功:', imagePath);

    if (skipPreview) {
      return imagePath; // 直接返回图片路径，不显示预览
    }

    // 在微信小程序中使用官方图片分享API
    // #ifdef MP-WEIXIN
    try {
      await wx.showShareImageMenu({
        path: imagePath
      });

      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('图片分享失败:', error);
      // 如果官方API失败，显示图片预览
      shareImagePath.value = imagePath;
      showShareImagePreview.value = true;
    }
    // #endif

    // 在非微信小程序环境中显示图片预览
    // #ifndef MP-WEIXIN
    shareImagePath.value = imagePath;
    showShareImagePreview.value = true;
    // #endif

    return imagePath;
  } catch (error) {
    uni.hideLoading();
    console.error('生成分享图片失败:', error);
    uni.showToast({
      title: '生成分享图片失败',
      icon: 'none'
    });
    throw error;
  }
};

// 保存分享图片
const saveShareImage = async (imagePath) => {
  try {
    // 确保图片路径正确
    if (!imagePath) {
      console.error('图片路径为空:', imagePath);
      uni.showToast({
        title: '图片路径无效，无法保存',
        icon: 'none'
      });
      return;
    }

    // 检查是否是有效的图片路径
    const validImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const hasValidExtension = validImageExtensions.some(ext =>
      imagePath.toLowerCase().includes(ext)
    );

    if (!hasValidExtension && !imagePath.startsWith('wxfile://') && !imagePath.startsWith('http')) {
      console.error('图片路径格式无效:', imagePath);
      uni.showToast({
        title: '图片格式不支持',
        icon: 'none'
      });
      return;
    }

    // 如果是网络图片但不是https开头，尝试转换为https
    if (imagePath.startsWith('http:')) {
      console.warn('检测到http协议图片，尝试转换为https');
      imagePath = imagePath.replace('http:', 'https:');
    }

    console.log('准备保存图片，路径:', imagePath);
    await saveImageToAlbum(imagePath);
    uni.showToast({
      title: '图片已保存到相册',
      icon: 'success'
    });
  } catch (error) {
    console.error('保存图片失败:', error);

    // 检查是否是权限问题
    if (error.errMsg && error.errMsg.includes('authorize')) {
      uni.showModal({
        title: '提示',
        content: '需要授权访问相册才能保存图片',
        confirmText: '去授权',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting();
          }
        }
      });
    } else {
      uni.showToast({
        title: '保存图片失败',
        icon: 'none'
      });
    }
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题，将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const date = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - date;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// 改变评论排序方式
const changeSortType = (type) => {
  if (sortType.value === type) return;
  
  sortType.value = type;
  console.log('切换排序方式为:', type);
  loadComments(true);
};

// 处理删除动态
const handleDeleteFeed = () => {
  if (!isContentCreator.value) {
    uni.showToast({ title: '您无权删除此动态', icon: 'none' });
    return;
  }

  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这条动态吗？',
    confirmColor: '#6AC086',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' });
          
          const params = {
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token,
            feed_id: feedDetail.value.id
          };
          
          console.log('删除动态请求参数:', JSON.stringify(params));
          
          const result = await deleteFeed(params);
          
          console.log('删除动态返回结果:', JSON.stringify(result));
          
          uni.hideLoading();
          
          if (result.status === 'ok') {
            uni.showToast({ title: '删除成功', icon: 'success' });
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('删除动态失败:', error);
          uni.showToast({ title: '删除失败，请稍后重试', icon: 'none' });
        }
      }
    }
  });
};

// 处理删除评论
const handleDeleteComment = (comment) => {
  if (!isCommentCreator(comment)) {
    uni.showToast({ title: '您无权删除此评论', icon: 'none' });
    return;
  }

  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这条评论吗？',
    confirmColor: '#6AC086',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' });
          
          const params = {
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token,
            comment_id: comment.id,
            comment_type: 'feed'  // 这里明确指定为feed类型评论
          };
          
          console.log('删除评论请求参数:', JSON.stringify(params));
          
          const result = await deleteComment(params);
          
          console.log('删除评论返回结果:', JSON.stringify(result));
          
          uni.hideLoading();
          
          if (result.status === 'ok') {
            uni.showToast({ title: '删除成功', icon: 'success' });
            // 从评论列表中移除该评论
            const index = comments.value.findIndex(item => item.id === comment.id);
            if (index !== -1) {
              comments.value.splice(index, 1);
            }
            // 更新评论计数
            if (feedDetail.value) {
              feedDetail.value.comment_count = Math.max(0, (feedDetail.value.comment_count || 0) - 1);
            }
          } else {
            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('删除评论出错:', error);
          uni.showToast({ title: '删除失败，请稍后重试', icon: 'none' });
        }
      }
    }
  });
};
</script>



<template>
  <view class="feed-detail-container">
    <!-- 顶部导航栏 -->
    <view class="feed-header">
      <view class="back-button" @click="closePage">
        <u-icon name="arrow-left" color="#333" size="20"></u-icon>
      </view>
      <text class="header-title">动态详情</text>
      <!-- 分享按钮已注释，使用微信右上角分享 -->
      <!-- <view class="share-button" @click="handleShare">
        <u-icon name="share" color="#333" size="20"></u-icon>
      </view> -->
    </view>

    <!-- Canvas 2D API分享图片生成 -->
    <view class="hidden-canvas-container">
      <canvas
        type="2d"
        id="share-canvas"
        style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"
      ></canvas>
    </view>



    <!-- 分享图片预览 -->
    <view v-if="showShareImagePreview" class="share-image-preview">
      <view class="share-image-container">
        <image :src="shareImagePath" mode="widthFix" class="share-preview-image"></image>
        <view class="share-preview-actions">
          <button class="share-action-btn" @click="shareToWechatFriend">发送给朋友</button>
          <button class="share-action-btn" @click="shareToWechatMoments">分享到朋友圈</button>
          <button class="share-action-btn" @click="saveShareImage(shareImagePath)">保存到相册</button>
          <button class="share-action-btn cancel" @click="showShareImagePreview = false">取消</button>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-state">
      <u-loading-icon mode="circle" size="30" color="#333"></u-loading-icon>
    </view>

    <!-- 内容区域 -->
    <scroll-view v-else-if="feedDetail" class="feed-content-scroll" scroll-y>
      <!-- 用户信息 -->
      <view class="user-info">
        <image class="user-avatar" :src="feedDetail.user?.avatar_url || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-meta">
          <text class="user-nickname">{{ feedDetail.user?.nickname || '用户' }}</text>
          <text class="post-time">{{ formatTime(feedDetail.created_at) }}</text>
        </view>
        <!-- 添加删除按钮，仅在当前用户是内容创建者时显示 -->
        <view v-if="isContentCreator" class="delete-btn" @click="handleDeleteFeed">
          <u-icon name="trash" color="#999" size="18"></u-icon>
          <text class="delete-text">删除</text>
        </view>
      </view>

      <!-- 日记内容 -->
      <view class="feed-content">
        <text class="feed-text">{{ feedDetail.content }}</text>
      </view>

      <!-- 图片列表 -->
      <view v-if="imageList.length > 0" class="image-grid" :class="[`grid-${Math.min(imageList.length, 4)}`]">
        <view
          v-for="(img, index) in imageList"
          :key="index"
          class="image-item"
          @click="previewImage(index)"
        >
          <image :src="img" mode="aspectFill" class="feed-image"></image>
        </view>
      </view>

      <!-- 位置信息 -->
      <view v-if="feedDetail.location" class="location-info">
        <u-icon name="map" size="16" color="#999"></u-icon>
        <text class="location-text">{{ feedDetail.location }}</text>
      </view>

      <!-- 标签 -->
      <view v-if="feedDetail.tags" class="tags-container">
        <view
          v-for="(tag, index) in (typeof feedDetail.tags === 'string' ? feedDetail.tags.split(',') : feedDetail.tags)"
          :key="index"
          class="tag-item"
        >
          <text class="tag-text">#{{ tag }}</text>
        </view>
      </view>

      <!-- 操作栏 -->
      <view class="feed-action-bar">
        <view class="action-item" @click="handleLike">
          <image :src="feedDetail.isLiked ? '/static/dianzanqianhou.svg' : '/static/dianzanqian.svg'" 
                 style="width: 44rpx; height: 44rpx"
                 :style="{ filter: feedDetail.isLiked ? 'none' : 'opacity(0.7)' }"></image>
          <text class="action-text" :class="{ 'liked': feedDetail.isLiked }">{{ feedDetail.likeCount || 0 }}</text>
        </view>
        <view class="action-item" @click="toggleComments">
          <u-icon name="chat" color="#999" size="20"></u-icon>
          <text class="action-text">{{ feedDetail.commentCount || 0 }}</text>
        </view>
        <view class="action-item" @click="handleFavorite">
          <image :src="feedDetail.isFavorited ? '/static/shoucanghou.svg' : '/static/shoucangqian.svg'" 
                 style="width: 44rpx; height: 44rpx"
                 :style="{ filter: feedDetail.isFavorited ? 'none' : 'opacity(0.7)' }"></image>
          <text class="action-text">收藏</text>
        </view>
        <!-- 分享按钮已注释，使用微信右上角分享 -->
        <!-- <view class="action-item" @click="handleShare">
          <u-icon name="share" color="#999" size="20"></u-icon>
          <text class="action-text">分享</text>
        </view> -->
      </view>

      <!-- 评论区 -->
      <view v-if="showComments" class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 ({{ feedDetail.commentCount || 0 }})</text>
          <view class="comment-sort-options">
            <text 
              class="sort-option" 
              :class="{ 'active': sortType === 'latest' }" 
              @click="changeSortType('latest')"
            >最新</text>
            <text 
              class="sort-option" 
              :class="{ 'active': sortType === 'hot' }" 
              @click="changeSortType('hot')"
            >最热</text>
          </view>
        </view>

        <!-- 评论列表 -->
        <scroll-view
          class="comments-list"
          scroll-y
          @scrolltolower="loadComments"
          refresher-enabled
          :refresher-triggered="commentsRefreshing"
          @refresherrefresh="onCommentsRefresh"
        >
          <view v-if="comments.length === 0 && !commentsLoading" class="empty-comments">
            <text>暂无评论，快来发表第一条评论吧</text>
          </view>

          <view v-for="(comment, index) in comments" :key="comment.id" class="comment-item">
            <image class="comment-avatar" :src="comment.user?.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-nickname">{{ comment.user?.nickname || '用户' }}</text>
                <text class="comment-time">{{ formatTime(comment.created_at) }}</text>
                <!-- 添加删除按钮，仅在当前用户是评论创建者时显示 -->
                <view v-if="isCommentCreator(comment)" class="comment-delete-btn" @click.stop="handleDeleteComment(comment)">
                  <u-icon name="trash" color="#999" size="14"></u-icon>
                </view>
              </view>
              <view class="comment-text">
                <text v-if="comment.parent_id" class="reply-target">回复 @{{ comment.reply_to?.nickname || '用户' }}：</text>
                <text>{{ comment.content }}</text>
              </view>
              <view class="comment-actions">
                <view class="comment-like-btn" @click="handleCommentLike(comment)">
                  <u-icon
                    :name="comment.isLiked ? 'thumb-up-fill' : 'thumb-up'"
                    :color="comment.isLiked ? '#6AC086' : '#999'"
                    size="14"
                  ></u-icon>
                  <text class="like-count" :class="{ 'liked': comment.isLiked }">{{ comment.like_count || 0 }}</text>
                </view>
                <text class="reply-btn" @click="setReplyTo({id: comment.id, nickname: comment.user?.nickname || '用户'})">回复</text>
              </view>
            </view>
          </view>

          <view v-if="commentsLoading" class="loading-more">
            <u-loading-icon mode="circle" size="24" color="#999"></u-loading-icon>
          </view>

          <view v-if="hasMoreComments && !commentsLoading" class="load-more" @click="loadComments">
            <text>加载更多</text>
          </view>
        </scroll-view>
      </view>
    </scroll-view>

    <!-- 评论输入框 -->
    <view v-if="showComments" class="comment-input-area">
      <view v-if="replyTo" class="reply-info">
        <text>回复：{{ replyTo.nickname }}</text>
        <u-icon name="close" size="12" color="#999" @click="setReplyTo(null)"></u-icon>
      </view>
      <view class="input-wrapper">
        <input
          class="comment-input"
          v-model="commentText"
          :placeholder="commentPlaceholder"
          confirm-type="send"
          @confirm="submitComment"
        />
        <button class="send-btn" @click="submitComment">发送</button>
      </view>
    </view>

    <!-- 分享弹窗 -->
    <share-popup
      :show="showSharePopup"
      title="分享动态"
      :share-data="currentShareData"
      :show-member-invite="store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1"
      @close="showSharePopup = false"
      @share-success="handleShareSuccess"
      @share-error="handleShareError"
    />

  </view>
</template>

<style lang="scss" scoped>
/* 分享弹窗样式 */
.share-popup {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  width: 80%;
  max-width: 600rpx;
  margin: 0 auto;
}

.share-options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.share-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  background-color: #f5f5f5;
}

.share-icon .u-icon {
  font-size: 48rpx;
}

.share-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.share-cancel {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  text-align: center;
  color: #666;
  font-size: 28rpx;
}

.feed-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.hidden-canvas-container {
  position: fixed;
  left: -9999px;
  top: -9999px;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
}

.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  padding-bottom: 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(88rpx + var(--status-bar-height));
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.back-button, .share-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.loading-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feed-content-scroll {
  flex: 1;
  background-color: #fff;
  padding-top: calc(88rpx + var(--status-bar-height) + 20rpx);
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 1px solid #f0f0f0;
}

.user-meta {
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.post-time {
  font-size: 26rpx;
  color: #999999;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.feed-content {
  padding: 30rpx;
}

.feed-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.8;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.image-grid {
  padding: 0 30rpx;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5rpx;
  margin-top: 20rpx;
}

.grid-1 .image-item {
  width: 100%;
  padding: 5rpx;
}

.grid-2 .image-item, .grid-4 .image-item {
  width: 50%;
  padding: 5rpx;
}

.grid-3 .image-item {
  width: 33.33%;
  padding: 5rpx;
}

.feed-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.location-info {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  color: #999;
}

.location-text {
  font-size: 24rpx;
  margin-left: 10rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx 20rpx;
}

.tag-item {
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #576b95; /* 微信蓝色 */
}

.feed-action-bar {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  transition: background-color 0.3s;
}

.action-item:active {
  background-color: #f5f5f5;
}

.action-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.action-text.liked {
  color: #ff6b81;
}

.share-button {
  background: none;
  border: none;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.share-button:active {
  background-color: #f5f5f5;
}

.comments-section {
  padding: 30rpx;
  background-color: #fff;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #f5f5f5;
}

.comments-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333;
}

.comment-sort-options {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.sort-option {
  padding: 4rpx 20rpx;
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.sort-option.active {
  color: #fff;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  box-shadow: 0 4rpx 10rpx rgba(106, 192, 134, 0.3);
}

.comments-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.empty-comments {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.comment-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 1px solid #f0f0f0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
}

.comment-nickname {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 12rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
  flex: 1;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.reply-target {
  color: #576b95; /* 微信蓝色 */
  margin-right: 10rpx;
  font-weight: 500;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}

.comment-like-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  transition: background-color 0.3s;
}

.comment-like-btn:active {
  background-color: rgba(106, 192, 134, 0.1);
}

.like-count {
  font-size: 22rpx;
  color: #999;
  min-width: 20rpx;
  text-align: center;
}

.like-count.liked {
  color: #6AC086;
  font-weight: 500;
}

.reply-btn {
  font-size: 24rpx;
  color: #576b95; /* 微信蓝色 */
  padding: 4rpx 10rpx;
}

.loading-more, .load-more {
  padding: 20rpx 0;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

.comment-input-area {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.reply-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.input-wrapper {
  display: flex;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-btn {
  margin-left: 20rpx;
  background-color: #576b95; /* 微信蓝色 */
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.share-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

.share-image-container {
  width: 80%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-preview-image {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.share-preview-actions {
  width: 100%;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-action-btn {
  background-color: #576b95;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.share-action-btn.cancel {
  background-color: #f5f5f5;
  color: #333;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 添加删除按钮样式 */
.delete-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
  margin-left: auto;
}

.delete-text {
  font-size: 22rpx;
  color: #999;
  margin-left: 4rpx;
}

.comment-delete-btn {
  padding: 6rpx;
  margin-left: 10rpx;
}
</style>