<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto } from "@/utils";

const props = defineProps({
  w: { type: String || Number, default: "5" }, // 宽度
  h: { type: String || Number, default: "25" }, // 高度
  bg: { type: String, default: "#333" },
  r: { type: String || Number, default: "2" },
  title: { type: String, default: "标题" },
  size: { type: String || Number, default: "34" },
  c: { type: String, default: "#000" },
  mr: { type: String || Number, default: "10" },
  right: { type: Boolean, default: false },
  label: { type: String, default: "" },
  iconSize: { type: Number || String, default: 15 },
  url: { type: String, default: "" },
});
</script>
<template>
  <view class="myLine df aic">
    <view
      :style="{
        width: w + 'rpx',
        height: h + 'rpx',
        background: bg,
        borderRadius: r + 'rpx',
        marginRight: mr + 'rpx',
      }"
    ></view>
    <view
      class="f1"
      :style="{
        fontSize: size + 'rpx',
        color: c,
        fontWeight: size >= 28 ? 'bold' : 'normal',
      }"
    >
      {{ title }}
    </view>
    <u-icon
      v-if="right"
      :label="label"
      label-pos="left"
      label-color="#AAAAAA"
      label-size="20rpx"
      :size="iconSize"
      color="#AAAAAA"
      name="arrow-right"
      @click="navto(url)"
    ></u-icon>
    <slot></slot>
  </view>
</template>

<style scoped lang="less"></style>
