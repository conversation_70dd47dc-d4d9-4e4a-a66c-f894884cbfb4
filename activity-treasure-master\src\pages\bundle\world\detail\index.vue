<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getCardDetail, getFeedDetail } from '@/api/index.js'; // 导入两种详情API
import { store } from '@/store'; // 导入store

// --- State Refs ---
const contentDetail = ref(null); // 内容详情数据（可能是日卡或日记）
const isLoading = ref(true); // 加载状态标志
const contentType = ref('card'); // 内容类型：'card'或'feed'
const showComments = ref(false); // 是否显示评论区

// --- Computed Properties ---
// 背景图片样式
const backgroundStyle = computed(() => {
  let imageUrl = null;

  // 根据内容类型获取不同的图片URL
  if (contentType.value === 'card') {
    imageUrl = contentDetail.value?.background_image_url;
  } else if (contentType.value === 'feed') {
    // 对于日记，使用第一张图片作为背景，如果有的话
    imageUrl = contentDetail.value?.images && contentDetail.value.images.length > 0
      ? contentDetail.value.images[0]
      : null;
  }

  // 使用默认背景
  const defaultBg = 'linear-gradient(to bottom, #444, #111)';
  return {
    backgroundImage: imageUrl ? `url(${imageUrl})` : defaultBg,
  };
});



// --- Lifecycle Hooks ---
/**
 * 页面加载时触发，获取路由参数
 * @param {object} options - 页面加载参数
 */
onLoad(async (options) => {
  // 检查内容类型
  if (options?.type === 'feed') {
    contentType.value = 'feed';
  } else {
    contentType.value = 'card';
  }

  // 检查是否显示评论
  if (options?.showComments === 'true') {
    showComments.value = true;
  }

  // 获取内容ID
  const contentId = options?.feedId || options?.cardId;

  if (!contentId) {
    console.error('No content ID provided for detail page');
    isLoading.value = false;
    uni.showToast({ title: '无法加载详情', icon: 'none' });
    uni.navigateBack(); // 返回上一页
    return;
  }

  console.log(`Loading ${contentType.value} detail for ID:`, contentId);

  try {
    isLoading.value = true;

    // 根据内容类型调用不同的API
    if (contentType.value === 'card') {
      // 获取日卡详情
      const res = await getCardDetail({
        id: contentId,
        uid: store().$state.userInfo?.uid || 0,
        token: store().$state.userInfo?.token || ''
      });

      if (res.status === 'ok') {
        contentDetail.value = res.data;
      } else {
        throw new Error(res.msg || '获取日卡详情失败');
      }
    } else if (contentType.value === 'feed') {
      // 获取日记详情
      const res = await getFeedDetail({
        id: contentId,
        uid: store().$state.userInfo?.uid || 0,
        token: store().$state.userInfo?.token || ''
      });

      if (res.status === 'ok') {
        contentDetail.value = res.data;
      } else {
        throw new Error(res.msg || '获取日记详情失败');
      }
    }
  } catch (error) {
    console.error('Error loading content detail:', error);
    uni.showToast({ title: error.message || '加载详情失败', icon: 'none' });

    // 不使用模拟数据，保持空状态
    contentDetail.value = null;
  } finally {
    isLoading.value = false;
  }
});

// --- Functions ---
/**
 * 关闭当前页面
 */
const closePage = () => {
    uni.navigateBack();
}

// --- Action Handlers ---
const handleLike = () => {
    uni.showToast({ title: '点赞功能待实现', icon: 'none' });
}

const handleComment = () => {
    // 切换评论区显示状态
    showComments.value = !showComments.value;
}

const handleShare = () => {
    uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
    });
}

// 预览图片
const previewImage = (index) => {
    if (!contentDetail.value?.images || contentDetail.value.images.length === 0) return;

    uni.previewImage({
        current: index,
        urls: contentDetail.value.images
    });
}



</script>

<template>
  <!-- Page container with dynamic background -->
  <view class="detail-page-container" :style="backgroundStyle" @click="closePage">
    <!-- Background overlay -->
    <view class="detail-background-overlay"></view>

    <!-- Loading state -->
    <view v-if="isLoading" class="loading-state-detail">
        <u-loading-icon mode="circle" size="30" color="#ffffff"></u-loading-icon>
    </view>

    <!-- Content area, prevent event bubbling -->
    <view v-else-if="contentDetail" class="detail-content-wrapper" @click.stop>
      <!-- 日卡内容 -->
      <view v-if="contentType === 'card'" class="detail-text-content">
        <text class="detail-description">{{ contentDetail.description }}</text>
        <text class="detail-author">{{ contentDetail.author ? '~ ' + contentDetail.author + ' ~' : '' }}</text>
      </view>

      <!-- 动态内容 -->
      <view v-else-if="contentType === 'feed'" class="feed-content-wrapper">
        <!-- 用户信息 -->
        <view class="feed-user-info">
          <image class="user-avatar" :src="contentDetail.user?.avatar_url || '/static/default-avatar.png'"></image>
          <text class="user-name">{{ contentDetail.user?.nickname || '用户' }}</text>
        </view>

        <!-- 动态内容 -->
        <view class="feed-text-content">
          <text class="feed-text">{{ contentDetail.content }}</text>
        </view>

        <!-- 图片列表 -->
        <view v-if="contentDetail.images && contentDetail.images.length > 0" class="feed-image-list">
          <image
            v-for="(img, index) in contentDetail.images"
            :key="index"
            :src="img"
            class="feed-image"
            mode="aspectFill"
            @click.stop="previewImage(index)"
          ></image>
        </view>

        <!-- 评论区 -->
        <view v-if="showComments" class="comments-section">
          <view class="comments-header">
            <text class="comments-title">评论</text>
          </view>

          <!-- 评论列表 -->
          <view class="comments-list">
            <!-- 暂无评论提示 -->
            <view v-if="!contentDetail.comments || contentDetail.comments.length === 0" class="no-comments">
              <text>暂无评论，快来发表第一条评论吧</text>
            </view>

            <!-- 评论项 -->
            <view
              v-for="(comment, index) in contentDetail.comments"
              :key="index"
              class="comment-item"
            >
              <image class="comment-avatar" :src="comment.user?.avatar_url || '/static/default-avatar.png'"></image>
              <view class="comment-content">
                <text class="comment-username">{{ comment.user?.nickname || '用户' }}</text>
                <text class="comment-text">{{ comment.content }}</text>
                <text class="comment-time">{{ comment.created_at }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- Action bar at the bottom -->
      <view class="detail-action-bar">
          <view class="action-icon" @click.stop="handleComment">
            <u-icon name="chat" size="24" color="#fff"></u-icon>
          </view>
          <view class="action-icon" @click.stop="handleLike">
             <u-icon name="heart" size="24" color="#fff"></u-icon>
          </view>
          <view class="action-icon" @click.stop="handleShare">
            <u-icon name="share" size="24" color="#fff"></u-icon>
          </view>
      </view>
    </view>

    <!-- Close button in top right, prevent event bubbling -->
     <view class="close-button" @click.stop="closePage">
         <u-icon name="close" color="#FFFFFF" size="20"></u-icon> <!-- Slightly smaller icon -->
     </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  position: relative;
  background-size: cover;       // Cover the entire container
  background-position: center; // Center the background image
  background-repeat: no-repeat;
  overflow: hidden;             // Prevent content overflow
  animation: fadeIn 0.4s ease-out forwards;
}

// 日记内容样式
.feed-content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 30rpx;
  overflow-y: auto;
  color: #FFFFFF;
}

.feed-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

.feed-text-content {
  margin: 20rpx 0;
}

.feed-text {
  font-size: 32rpx;
  line-height: 1.6;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

.feed-image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx 0;
}

.feed-image {
  width: 220rpx;
  height: 220rpx;
  margin: 5rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

// 评论区样式
.comments-section {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.comments-header {
  margin-bottom: 20rpx;
}

.comments-title {
  font-size: 30rpx;
  font-weight: bold;
}

.comments-list {
  margin-bottom: 20rpx;
}

.no-comments {
  padding: 20rpx 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 28rpx;
}

.comment-item {
  display: flex;
  margin-bottom: 20rpx;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.comment-content {
  flex: 1;
}

.comment-username {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 5rpx;
}

.comment-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.detail-background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3); // Slightly darker overlay
  z-index: 1; // Below content, above background image
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading-state-detail {
    position: absolute; // Position loading indicator centrally
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5; // Above overlay
}

.detail-content-wrapper {
  position: relative; // Use relative for positioning children
  z-index: 2; // Above overlay
  width: 100%;
  height: 100%;
  display: flex; // Use flex to position text and action bar
  flex-direction: column;
  padding: var(--status-bar-height, 20px) 0 0; // Top padding for status bar
  box-sizing: border-box;
  animation: fadeInUp 0.4s ease-out 0.1s forwards; // Delayed entrance for content
  opacity: 0; // Start hidden for animation
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.detail-text-content {
  flex: 1; // Take available space
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx; // Padding for text content
  color: #FFFFFF;
  text-align: center;
  overflow-y: auto; // Allow scrolling if content is too long
}

.detail-description {
  // font-family: 'Source Han Serif SC', serif;
  font-size: 38rpx; // Slightly smaller than before
  line-height: 1.8;
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.6); // Enhance shadow
}

.detail-author {
  // font-family: '阿里巴巴普惠体', sans-serif;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85); // Slightly brighter author
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
  margin-top: 20rpx;
}

.detail-action-bar {
    height: 120rpx; // Slightly taller action bar
    width: 100%;
    flex-shrink: 0; // Prevent shrinking
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 50rpx;
    box-sizing: border-box;
    background: linear-gradient(to top, rgba(0,0,0,0.4), transparent); // Slightly darker gradient
    z-index: 4;
}

.action-icon {
    display: flex;
    align-items: center;
    // Add some padding if needed for touch targets
    padding: 10rpx;
}

.close-button {
    position: absolute;
    top: calc(var(--status-bar-height, 20px) + 15rpx); // Adjust top position
    right: 25rpx;
    width: 60rpx;
    height: 60rpx;
    background-color: rgba(0, 0, 0, 0.3); // Add a subtle background
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10; // Ensure it's on top
    box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

</style>