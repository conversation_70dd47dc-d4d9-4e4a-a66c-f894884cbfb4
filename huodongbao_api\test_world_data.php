<?php
/**
 * 测试世界模块数据
 */

require_once 'core/init.php';

// 连接数据库
dbConn();

echo "<h2>世界模块数据测试</h2>\n";

// 测试feeds表数据
echo "<h3>1. 测试feeds表数据</h3>\n";
try {
    $feedsSql = "SELECT id, user_id, type, content, status, privacy, created_at FROM feeds ORDER BY created_at DESC LIMIT 10";
    $feeds = Db::_fetchAll($feedsSql);
    
    echo "<p>feeds表记录数: " . count($feeds) . "</p>\n";
    if (!empty($feeds)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>用户ID</th><th>类型</th><th>内容</th><th>状态</th><th>隐私</th><th>创建时间</th></tr>\n";
        foreach ($feeds as $feed) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($feed['id']) . "</td>";
            echo "<td>" . htmlspecialchars($feed['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($feed['type']) . "</td>";
            echo "<td>" . htmlspecialchars(mb_substr($feed['content'], 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($feed['status']) . "</td>";
            echo "<td>" . htmlspecialchars($feed['privacy']) . "</td>";
            echo "<td>" . htmlspecialchars($feed['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>feeds表中没有数据</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>查询feeds表失败: " . $e->getMessage() . "</p>\n";
}

// 测试quotes表数据
echo "<h3>2. 测试quotes表数据</h3>\n";
try {
    $quotesSql = "SELECT id, user_id, content, author, source, privacy, created_at FROM quotes ORDER BY created_at DESC LIMIT 10";
    $quotes = Db::_fetchAll($quotesSql);
    
    echo "<p>quotes表记录数: " . count($quotes) . "</p>\n";
    if (!empty($quotes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>用户ID</th><th>内容</th><th>作者</th><th>来源</th><th>隐私</th><th>创建时间</th></tr>\n";
        foreach ($quotes as $quote) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($quote['id']) . "</td>";
            echo "<td>" . htmlspecialchars($quote['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars(mb_substr($quote['content'], 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($quote['author'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($quote['source'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($quote['privacy']) . "</td>";
            echo "<td>" . htmlspecialchars($quote['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>quotes表中没有数据</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>查询quotes表失败: " . $e->getMessage() . "</p>\n";
}

// 测试用户表数据
echo "<h3>3. 测试用户表数据</h3>\n";
try {
    $userSql = "SELECT uid, nickname, avatar, role_type, branch_id FROM user WHERE uid = 215";
    $user = Db::_fetch($userSql);

    if ($user) {
        echo "<p>用户ID 215 完整信息:</p>\n";
        echo "<ul>\n";
        echo "<li>UID: " . htmlspecialchars($user['uid']) . "</li>\n";
        echo "<li>昵称: " . htmlspecialchars($user['nickname']) . "</li>\n";
        echo "<li>头像: " . htmlspecialchars($user['avatar'] ?? '无') . "</li>\n";
        echo "<li>角色类型: " . htmlspecialchars($user['role_type'] ?? '无') . "</li>\n";
        echo "<li>分会ID: " . htmlspecialchars($user['branch_id'] ?? '无') . "</li>\n";
        echo "</ul>\n";

        // 检查分会长判断逻辑
        if ($user['role_type'] == '1' && !empty($user['branch_id'])) {
            echo "<p style='color: green;'>✅ 符合分会长条件</p>\n";
        } else {
            echo "<p style='color: red;'>❌ 不符合分会长条件</p>\n";
            echo "<p>判断条件：role_type='1' 且 branch_id 不为空</p>\n";
            echo "<p>当前值：role_type='{$user['role_type']}', branch_id='{$user['branch_id']}'</p>\n";
        }
    } else {
        echo "<p style='color: red;'>找不到用户ID 215</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>查询用户表失败: " . $e->getMessage() . "</p>\n";
}

// 测试API调用
echo "<h3>4. 测试API调用</h3>\n";

// 模拟调用get_feeds API
echo "<h4>4.1 测试get_feeds API</h4>\n";
try {
    require_once 'controller/World.php';
    $worldController = new \controller\World();
    
    // 测试获取日记列表
    $result = $worldController->get_feeds(1, 10, 215, '', 0, 'latest', 'diary');
    echo "<p>get_feeds API结果:</p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>调用get_feeds API失败: " . $e->getMessage() . "</p>\n";
}

// 模拟调用get_quotes API
echo "<h4>4.2 测试get_quotes API</h4>\n";
try {
    $result = $worldController->get_quotes(1, 20, 215, '');
    echo "<p>get_quotes API结果:</p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>调用get_quotes API失败: " . $e->getMessage() . "</p>\n";
}

echo "<h3>测试完成</h3>\n";
echo "<p>请检查以上结果，确认数据和API是否正常工作。</p>\n";
?>
