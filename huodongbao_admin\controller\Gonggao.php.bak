<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 公告管理 
*/
class Gonggao extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['title'])){
			$where .= " AND title LIKE :title";
			$prepareParam[":title"]="%".$_REQUEST['title']."%";
			$pageParams['title'] = $_REQUEST['title'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-1 years"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		$data = Db()->table("gonggao")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		return view(['header','gonggao/index','footer']);
	}
	/*
	* @name 添加
	* @method add
	*/		
	public function add(){
		dbConn();
		if(
			!empty($_POST['title']) && 
			!empty($_POST['contents'])
		){
			$data = [
				"title"=>":title",
				"contents"=>":contents",
			];
			$prepareParam = [
				":title"=>htmlspecialchars($_POST['title']),
				":contents"=>htmlspecialchars($_POST['contents']),
			];
			if(!empty($_POST['img']) && check($_POST['img'],"url")){
				$data['img_url'] = htmlspecialchars($_POST['img']);
			}			
			try{
				$res = Db()->table("gonggao")->prepareParam($prepareParam)->insert($data);
				if($res){
					$insert_id = Db()->insertId();
					$this->root_log("添加公告,id:[ {$insert_id} ]");
					assign("alertTpl",true);
					header("location:".url("gonggao/index/alert/{$insert_id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		return view(['header','gonggao/add','footer']);
	}
	/*
	* @name 编辑
	* @method edit
	*/	
	public function edit(){
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("gonggao/index"));
		}
		$id = (int)$_REQUEST['id'];
		dbConn();
		if(
			!empty($_POST['title']) && 
			!empty($_POST['contents'])
		){
			$data = [
				"title"=>":title",
				"contents"=>":contents",
				"time"=>DATETIME,
			];
			$prepareParam = [
				":title"=>htmlspecialchars($_POST['title']),
				":contents"=>htmlspecialchars($_POST['contents']),
			];
			if(!empty($_POST['img']) && check($_POST['img'],"url")){
				$data['img_url'] = htmlspecialchars($_POST['img']);
			}			
			try{
				$res = Db()->table("gonggao")->prepareParam($prepareParam)->where("id={$id}")->update($data);
				if($res){
					$this->root_log("编辑公告,id:{$id}");
					assign("alertTpl",true);
					header("location:".url("gonggao/index/alert/{$id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$data = Db()->table("gonggao")->where("id={$id}")->fetch();
		if(empty($data))header("location:".url("gonggao/index"));
		if(isset($data['contents']))$data['contents'] = Db()->quote(htmlspecialchars_decode($data['contents']));
		assign("data",$data);
		return view(['header','gonggao/edit','footer']);
	}	
	/*
	* @name 删除
	* @method del
	*/	
	public function del(){
		responseType("json");
		if(empty($_POST['ids']))return ["status"=>"error","msg"=>"参数错误"];
		$ids = trim($_POST['ids']);
		$ids_arr = explode(",",$ids);
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		foreach($ids_arr as &$v){
			if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
		}
		$ids = implode(",",$ids_arr);
		if(empty($ids))return ["status"=>"error","msg"=>"参数错误"];
		try{
			$sql = "DELETE FROM `gonggao` WHERE id IN ({$ids})";
			$rowCount = Db()->_exec($sql);
			if($rowCount){
				$this->root_log("删除公告,ids[{$ids}]");
				return ["status"=>"ok","msg"=>"影响数量：{$rowCount}"];
			}else{
				return ["status"=>"error","msg"=>"影响数量为零行"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	public function _empty(){
		
	}
	function __destruct(){

	}
}
