import { BaseUrl, gaodeKey } from "./BaseUrl";
import { store } from "@/store";
import { navto } from "@/utils";
import { isValidAuth } from "@/utils/auth";
import cacheManager, { CACHE_TYPES, setCache, getCache } from "./cacheManager";

// 请求去重管理
const pendingRequests = new Map();
const pendingRequestsTime = new Map(); // 跟踪请求开始时间

// 生成请求缓存键
// 修复：优化缓存键生成，避免不同用户/业务场景的缓存冲突
const generateCacheKey = (url, data) => {
  const cleanData = data || {};

  // 提取关键参数用于缓存键生成，确保不同查询条件生成不同的键
  const keyParams = {
    uid: cleanData.uid || 'guest', // 改为guest，避免与实际API参数混淆
    list_type: cleanData.list_type,
    category: cleanData.category,
    user_id: cleanData.user_id,
    type_id: cleanData.type_id,
    shi_id: cleanData.shi_id,
    qu_id: cleanData.qu_id,
    sort: cleanData.sort,
    keyword: cleanData.keyword,
    page: cleanData.page,
    page_size: cleanData.page_size,
    date: cleanData.date,
    huodong_date: cleanData.huodong_date,
    baoming_date: cleanData.baoming_date,
    is_tuijian: cleanData.is_tuijian,
    baoming_status: cleanData.baoming_status,
    huodong_status: cleanData.huodong_status,
    // 添加时间戳确保不同时间的请求有不同的键（分钟级精度）
    time_key: Math.floor(Date.now() / (1000 * 60))
  };

  // 移除undefined值，只保留有效参数
  const validParams = Object.fromEntries(
    Object.entries(keyParams).filter(([_, value]) => value !== undefined)
  );

  // 生成更精确的缓存键
  const paramString = Object.keys(validParams).length > 0
    ? JSON.stringify(validParams)
    : 'default';

  return `api_${url}_${paramString}`;
};

// 智能缓存配置 - 只缓存静态资源，移除动态数据缓存
const cacheConfig = {
  // 移除活动列表缓存，确保数据实时性
  // "huodong/get_list": { ... },

  // 保留静态资源缓存
  "lunbotu/index": {
    ttl: 30 * 60 * 1000,
    type: CACHE_TYPES.STORAGE,
    priority: 3
  },
  "config/app": {
    ttl: 60 * 60 * 1000,
    type: CACHE_TYPES.STORAGE,
    priority: 3
  },

  // 移除活动分类缓存，确保分类数据实时性
  // "huodong/get_type": { ... },

  // 移除World模块缓存，确保内容实时性
  // "world/get_cards": { ... },
  // "world/get_feeds": { ... }
};

// 定义公开API列表 - 这些API无需登录也可访问
const publicApis = [
  "huodong/get_list",
  "lunbotu/index",
  "config/app",
  "config/get_china",
  "config/pop",
  "user/login",
  "huodong/get_type",
  // "huodong/get_info", // 移除：活动详情需要真实认证以获取用户状态
  "huodong/get_pingjia", // 添加活动评价API
  "huodong/get_baoming_list_public", // 添加公开报名列表API
  "config/get_deep_link", // 添加深链接API
  "world/get_cards",  // 日卡列表公开访问
  "world/get_card_detail", // 日卡详情可公开访问
  "world/get_feeds",  // 动态列表可公开访问
  "world/get_feed_detail", // 动态详情可公开访问
  "user/get_trial_info", // 获取体验会员分享信息可公开访问
  "dalibao/get_info" // 大礼包详情可公开访问
];

// 需要登录但不自动跳转登录页的API - 这些API会返回relogin状态，但让页面自行决定如何提示用户
const authApisWithoutRedirect = [
  "world/get_daily_cards", // 获取日期范围内的日卡
  "user/get_other_user_info", // 获取他人信息
  "user/get_guanzhu_list", // 获取关注列表
  "user/get_fans_list", // 获取粉丝列表
  "user/guanzhu_check", // 检查是否关注
  "userguanzhu_check", // 添加用户关注检查API
  "user/claim_trial_member", // 领取体验会员
  "user/get_share_records", // 获取分享记录
  "user/get_unread_count", // 获取未读通知数量
  "user/get_daijiesuan_status" // 获取账户待结算状态
];

// 需要登录并自动跳转登录页的API - 这些是核心操作类API
const authApisWithRedirect = [
  "huodong/add_huodong", // 发布活动
  "huodong/add_baoming", // 活动报名
  "huodong/cancel_baoming", // 取消报名
  "huodong/shoucang_add", // 收藏活动
  "huodong/shoucang_del", // 取消收藏
  "huodong/zan_add", // 点赞活动
  "huodong/zan_del", // 取消点赞
  "huodong/add_pingjia", // 添加评价
  "world/publish_feed", // 发布动态
  "world/publish_card", // 发布日卡
  "world/like_feed", // 点赞动态
  "world/like_card", // 点赞日卡
  "world/favorite_feed", // 收藏动态
  "world/favorite_card", // 收藏日卡
  "world/comment_feed", // 评论动态
  "world/comment_card", // 评论日卡
  "world/like_comment", // 点赞评论
  "world/create_quote", // 创建摘录
  "world/get_quotes", // 获取摘录列表
  "world/delete_feed", // 删除动态
  "world/delete_comment", // 删除评论
  "user/update", // 更新用户信息
  "user/guanzhu_add", // 添加关注
  "user/guanzhu_del", // 取消关注
  "user/create_trial_share" // 创建体验会员分享链接（需要管理员权限）
];

// 判断API是否公开
const isPublicApi = (url) => {
  return publicApis.some(api => url.includes(api));
};

// 判断API是否需要登录但不自动跳转
const isAuthApiWithoutRedirect = (url) => {
  return authApisWithoutRedirect.some(api => url.includes(api));
};

// 判断API是否需要登录并自动跳转
const isAuthApiWithRedirect = (url) => {
  return authApisWithRedirect.some(api => url.includes(api));
};

uni.addInterceptor("request", {
  invoke: (invoke) => {
    // 排除特定URL
    if (
      invoke.url != "https://api.linqingkeji.com/user/update" &&
      invoke.url != "https://api.linqingkeji.com/user/update_mobile"
    ) {
      if (invoke.url.indexOf(BaseUrl) != -1 && invoke.method === "POST") {
        // 判断是否是内部API请求
        const apiPath = invoke.url.replace(BaseUrl, '');
        const isPublicRequest = isPublicApi(apiPath);
        const isAuthWithoutRedirect = isAuthApiWithoutRedirect(apiPath);
        const isAuthWithRedirect = isAuthApiWithRedirect(apiPath);



        // 修复：优先检查是否为公开请求，公开请求始终使用默认认证信息
        if (isPublicRequest) {
          console.log('公开API请求，使用默认token和uid', {
            apiPath,
            url: invoke.url,
            userLoggedIn: isValidAuth()
          });
          invoke.data.token = "00000000000000000000000000000000";
          invoke.data.uid = "1";
          // 标记为公开请求，用于响应拦截器处理
          invoke._isPublicRequest = true;
        }
        // 如果不是公开请求且用户已登录，添加真实的token和uid
        else if (isValidAuth()) {
          console.log('用户已登录，添加真实token和uid', {
            apiPath,
            uid: store().$state.userInfo.uid,
            tokenLength: store().$state.userInfo.token.length,
            isPublicRequest,
            isAuthWithoutRedirect,
            isAuthWithRedirect
          });
          invoke.data.token = store().$state.userInfo.token;
          invoke.data.uid = store().$state.userInfo.uid;
        }
        // 如果用户未登录且不是公开请求
        else {
          // 对于需要登录的API请求，标记登录需求类型
          if (!invoke.url.includes("user/login")) {
            // 标记是否需要自动跳转登录页
            invoke._requiresAuth = true;
            invoke._requiresRedirect = isAuthWithRedirect;
          }
        }
      }
    }
  }
});

uni.addInterceptor("navigateTo", {
  invoke: (invoke) => {
    if (uni.$u.page() != invoke.url) return true;
    else return false;
  }
});

/**
 @@param {request} request 封装请求
 */
const requset = {
  request(options) {
    return new Promise((resolve, reject) => {
      // 确保options.data存在
      if (!options.data) {
        options.data = {};
      }

      // 生成缓存键
      const cacheKey = generateCacheKey(options.url, options.data);

      // 极简防抖机制：只阻止200ms内的完全相同请求，避免误杀正常请求
      if (pendingRequests.has(cacheKey)) {
        const requestStartTime = pendingRequestsTime.get(cacheKey) || Date.now();
        const elapsedTime = Date.now() - requestStartTime;

        // 只在200ms内阻止重复请求，确保用户操作响应及时
        if (elapsedTime < 200) {
          console.log(`防抖阻止重复请求 [${cacheKey}]，已等待 ${elapsedTime}ms`);
          return pendingRequests.get(cacheKey);
        } else {
          // 超过200ms，清理旧请求，允许新请求
          console.log(`清理旧请求 [${cacheKey}]，允许新请求`);
          pendingRequests.delete(cacheKey);
          pendingRequestsTime.delete(cacheKey);
        }
      }

      // 暂时禁用智能缓存检查，确保动态数据实时性
      // const config = cacheConfig[options.url];
      // if (config) {
      //   const cached = getCache(cacheKey, { type: config.type });
      //   if (cached) {
      //     return Promise.resolve(cached);
      //   }
      // }

      // 添加请求超时处理
      let isTimeout = false;
      let timer = setTimeout(() => {
        isTimeout = true;
        pendingRequests.delete(cacheKey);
        pendingRequestsTime.delete(cacheKey);
        console.error(`请求超时 [${cacheKey}]，已清理缓存`);
        reject(new Error('请求超时'));
      }, 15000); // 15秒超时



      // 如果是需要授权的API且用户未登录，则直接返回需要登录的错误
      if (options._requiresAuth && (!store().$state.userInfo?.token || !store().$state.userInfo?.uid)) {
        clearTimeout(timer);
        const requiresAuthError = {
          status: "relogin",
          msg: "请先登录后再操作"
        };
        // 对于需要登录鉴权的请求操作，会返回relogin，但不会自动跳转
        resolve(requiresAuthError);
        return;
      }

      // 特定API强制使用form-urlencoded格式
      const forceFormUrlencodedApis = [
        "huodong/add_huodong",
        "huodong/update_huodong"
      ];

      const shouldForceFormUrlencoded = forceFormUrlencodedApis.some(api => options.url.includes(api));

      // 检查是否包含数组参数，如果有则使用JSON格式（除非强制使用form-urlencoded）
      const hasArrayParam = options.data && Object.values(options.data).some(value => Array.isArray(value));
      const contentType = shouldForceFormUrlencoded
        ? "application/x-www-form-urlencoded"
        : (hasArrayParam ? "application/json" : "application/x-www-form-urlencoded");



      // 确保请求URL不为空
      if (!options.url) {
        clearTimeout(timer);

        reject(new Error('请求URL为空'));
        return;
      }

      // 记录请求开始时间
      pendingRequestsTime.set(cacheKey, Date.now());
      console.log(`开始新请求 [${cacheKey}]，当前时间: ${new Date().toISOString()}`);

      // 将请求Promise加入待处理队列
      pendingRequests.set(cacheKey, new Promise((requestResolve, requestReject) => {
        uni.request({
          url: BaseUrl + options.url,
          method: options.method || 'GET',
          data: options.data,
          header: {
            "content-type": contentType
          },
          success: async (res) => {
            if (isTimeout) return; // 如果已超时，不处理响应
            clearTimeout(timer); // 清除超时定时器
            pendingRequests.delete(cacheKey); // 从待处理队列中移除
            pendingRequestsTime.delete(cacheKey); // 清除时间戳记录

            const elapsedTime = Date.now() - (pendingRequestsTime.get(cacheKey) || Date.now());
            console.log(`请求成功 [${cacheKey}]，耗时: ${elapsedTime}ms`);

          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            console.error(`HTTP错误: ${res.statusCode}`);

            // 记录错误到控制台
              if (options.url && (options.url.includes('comment_card') || options.url.includes('comment_feed') ||
                  options.url.includes('like_card') || options.url.includes('like_feed'))) {
              console.error('API调用失败:', {
                url: options.url,
                statusCode: res.statusCode,
                data: options.data
              });
            }

            return reject(new Error(`HTTP错误: ${res.statusCode}`));
          }

          // 检查响应数据
          if (!res.data) {
            console.error('响应数据为空');
            return reject(new Error('响应数据为空'));
          }

          const { status, msg } = res.data;

          if (status === "relogin") {
            // 获取API路径用于调试
            const apiPath = options.url.replace(BaseUrl, '');
            const isPublicRequest = isPublicApi(apiPath);
            const isAuthWithoutRedirect = isAuthApiWithoutRedirect(apiPath);
            const isAuthWithRedirect = isAuthApiWithRedirect(apiPath);
            const userInfo = store().$state.userInfo;

            console.log('API返回relogin状态，详细信息:', {
              apiPath,
              isPublicRequest,
              isAuthWithoutRedirect,
              isAuthWithRedirect,
              hasUserInfo: !!(userInfo && userInfo.uid && userInfo.token),
              uid: userInfo?.uid,
              tokenLength: userInfo?.token?.length
            });

            // 特殊处理：如果是公开API返回relogin，可能是后端逻辑问题
            if (options._isPublicRequest || isPublicRequest) {
              console.warn('公开API返回relogin状态，这可能是后端逻辑问题', {
                apiPath,
                isPublicRequest,
                hasDefaultToken: options.data?.token === "00000000000000000000000000000000"
              });

              // 对于公开API，如果返回relogin但使用的是默认token，直接返回数据
              if (options.data?.token === "00000000000000000000000000000000") {
                console.log('公开API使用默认token，忽略relogin状态');
                console.log('原始响应数据:', res.data);

                // 修复：如果响应是字符串，尝试提取JSON
                let responseData = res.data;
                if (typeof responseData === 'string') {
                  console.log('检测到字符串响应，提取JSON...');
                  try {
                    const jsonStart = responseData.indexOf('{"status"');
                    if (jsonStart !== -1) {
                      const jsonStr = responseData.substring(jsonStart);
                      responseData = JSON.parse(jsonStr);
                      console.log('JSON解析成功，返回解析后的数据:', responseData);
                    }
                  } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                  }
                }

                return resolve(responseData);
              }
            }

            // 如果用户确实已登录但API返回relogin，可能是token过期或其他问题
            if (userInfo && userInfo.uid && userInfo.token) {
              console.warn('用户已登录但API返回relogin，可能是token过期或权限问题');

              // 对于不需要跳转的API，直接返回状态让页面处理
              if (isAuthWithoutRedirect) {
                console.log('API不需要自动跳转，返回relogin状态供页面处理');
                return resolve(res.data);
              }
            }

            console.log('需要重新登录');

            // 修改登录跳转逻辑，仅对标记了需要跳转的API执行跳转
            if (options._requiresRedirect || isAuthWithRedirect) {
              // 对特定操作跳转登录页
              if (!store().flag) {
                store().flag = true;
                console.log('跳转到登录页');
                navto("/pages/bundle/common/login");
                setTimeout(() => (store().flag = false), 1500);
              }
            }
            // 对于其他API，只返回需要登录的状态，由页面自行决定是否显示登录提示
            return resolve(res.data);
          } else if (status === "empty") {
            console.log('服务器返回空数据');
            return resolve("n");
          } else if (status === "error") {
            console.error('服务器返回错误:', msg);

            // 特殊处理缺少uid参数的错误
            if (msg && msg.includes('Missing parameter [ uid ]')) {
              console.log('检测到缺少uid参数，可能是用户未登录');
              // 返回特殊的登录状态，让调用方处理
              return resolve({
                status: "relogin",
                msg: "请先登录后再操作"
              });
            }

            return resolve(res.data); // 返回原始响应，让调用方处理
          }

          // 暂时禁用智能缓存存储，确保数据实时性
          // const config = cacheConfig[options.url];
          // if (config && res.data.status === 'ok') {
          //   setCache(cacheKey, res.data, {
          //     ttl: config.ttl,
          //     type: config.type,
          //     priority: config.priority
          //   });
          // }

          requestResolve(res.data);
          resolve(res.data);
        },
        fail: (err) => {
          if (isTimeout) return; // 如果已超时，不处理错误
          clearTimeout(timer); // 清除超时定时器
          pendingRequests.delete(cacheKey); // 从待处理队列中移除
          pendingRequestsTime.delete(cacheKey); // 清除时间戳记录

          console.error(`请求失败 [${cacheKey}]:`, err);
          requestReject(err);
          reject(err);
        },
        complete: () => {
          // 可以在这里添加通用的完成处理逻辑
        }
      });
      }));

      // 返回待处理队列中的Promise
      return pendingRequests.get(cacheKey);
    });
  },
  getCoordinate(e) {
    return new Promise((rs, rj) => {
      uni.request({
        url: `https://restapi.amap.com/v3/assistant/coordinate/convert?locations=${e.locations}&coordsys=gps&output=JSON&key=${gaodeKey}`,
        success: (res) => rs(res.data),
        fail: (error) => rj(error)
      });
    });
  },
  getAddr(e) {
    return new Promise((rs, rj) => {
      uni.request({
        url: `https://restapi.amap.com/v3/geocode/regeo?output=JSON&location=${
          e.location
        }&key=${gaodeKey}&extensions=${e.extensions ? e.extensions : "base"}`,
        success: (res) => rs(res.data),
        fail: (error) => rj(error)
      });
    });
  },
  // 获取二进制
  getArrayBuffer(options) {
    return new Promise((resolve, reject) => {
      // 检查是否包含数组参数，如果有则使用JSON格式
      const hasArrayParam = Object.values(options.data).some(value => Array.isArray(value));
      const contentType = hasArrayParam
        ? "application/json"
        : "application/x-www-form-urlencoded";

      console.log(`ArrayBuffer请求内容类型: ${contentType}, 是否包含数组: ${hasArrayParam}`);

      uni.request({
        url: BaseUrl + options.url,
        method: options.method,
        data: options.data,
        header: {
          "content-type": contentType
        },
        responseType: "arraybuffer",
        success: (res) => {
          const { status, msg } = res.data;
          if (status === "relogin") {
            uni.$u.toast("请重新登录");
            uni.$u.debounce(navto("/pages/bundle/common/login"), 1000);
          } else if (status === "error") {
            uni.$u.toast(msg);
          } else if (status === "empty") {
            uni.$u.toast("暂无数据");
            return resolve("n");
          } else {
            msg ? uni.$u.toast(msg) : "";
          }
          resolve(res.data);
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },
  upload(options) {
    return new Promise((resolve, reject) => {
      // 添加请求超时处理
      let isTimeout = false;
      let timer = setTimeout(() => {
        isTimeout = true;
        console.error('上传请求超时:', options.url);
        reject(new Error('上传请求超时'));
      }, 30000); // 30秒超时（上传可能需要更长时间）

      console.log(`发起上传请求: ${options.url}, 文件路径: ${options.data}`);

      // 确保formData包含用户信息
      const formData = options.formData || {};
      if (store().$state.userInfo?.uid) {
        formData.uid = store().$state.userInfo.uid;
      }
      if (store().$state.userInfo?.token) {
        formData.token = store().$state.userInfo.token;
      }

      uni.uploadFile({
        url: BaseUrl + options.url,
        filePath: options.data,
        name: options.name,
        formData: formData,
        success: (res) => {
          if (isTimeout) return; // 如果已超时，不处理响应
          clearTimeout(timer); // 清除超时定时器

          console.log(`上传成功: ${options.url}, 状态码: ${res.statusCode}, 响应:`, res.data);

          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            console.error(`上传HTTP错误: ${res.statusCode}`);
            return reject(new Error(`上传HTTP错误: ${res.statusCode}`));
          }

          try {
            const parsedData = JSON.parse(res.data);
            const { status, msg } = parsedData;

            if (status === "relogin") {
              console.log('上传需要重新登录');
              uni.showToast({ title: "请先登录", icon: "none" });
              setTimeout(() => navto("/pages/bundle/common/login"), 1000);
              return resolve(parsedData);
            } else if (status === "error") {
              console.error('上传服务器返回错误:', msg);
              uni.showToast({ title: msg || "上传失败", icon: "none" });
              return resolve(parsedData);
            } else if (status === "empty") {
              console.log('上传服务器返回空数据');
              uni.showToast({ title: "暂无数据", icon: "none" });
              return resolve("n");
            }

            resolve(parsedData);
          } catch (error) {
            console.error('解析上传响应失败:', error);
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (err) => {
          if (isTimeout) return; // 如果已超时，不处理错误
          clearTimeout(timer); // 清除超时定时器

          console.error(`上传失败: ${options.url}`, err);
          reject(err);
        }
      });
    });
  },
  u(url, data, name, options) {
    data = data ? data : {};
    if (!options) {
      options = {};
    }
    options.name = name;
    options.url = url;
    options.data = data;
    // 使用传入的 formData，如果没有则使用空对象
    options.formData = options.formData || {};
    return this.upload(options);
  },
  aB(url, data, method, options) {
    data = data ? data : {};
    if (!options) {
      options = {};
    }
    options.url = url;
    options.data = data;
    options.method = method ? method : "POST";
    return this.getArrayBuffer(options);
  },
  g(url, data, options) {
    if (!options) {
      options = {};
    }
    options.url = url;
    options.data = data;
    options.method = "GET";
    return this.request(options);
  },
  p(url, data, options) {
    data = data ? data : {};
    if (!options) {
      options = {};
    }
    options.url = url;
    options.data = data;
    options.method = "POST";
    return this.request(options);
  },
  put(url, data, options) {
    data = data ? data : {};
    if (!options) {
      options = {};
    }
    options.url = url;
    options.data = data;
    options.method = "PUT";
    return this.request(options);
  },
  delete(url, data, options) {
    data = data ? data : {};
    // data.token = store().$state.token;
    // data.uid = store().$state.uid;
    if (!options) {
      options = {};
    }
    options.url = url;
    options.data = data;
    options.method = "DELETE";
    return this.request(options);
  }
};
export default requset;
