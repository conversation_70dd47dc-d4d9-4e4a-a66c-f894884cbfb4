<template>
  <view class="page">
    <myTitle
        bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        height="200rpx"
        title="会员服务协议"
        :backShow="true"
        color="#ffffff"
        :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <view class="agreement-content">
        
        <!-- 会员权益 -->
        <view class="section">
          <view class="section-title">会员权益</view>
          <view class="section-content">
            <view class="benefit-item">
              <view class="benefit-title"># 无限次报名活动</view>
              <view class="benefit-desc">不限城市、不限次数、不限类型，线上线下均可报名，自主发起活动</view>
            </view>
            
            <view class="benefit-item">
              <view class="benefit-title">平台上发起活动</view>
              <view class="benefit-desc">自定主题、时间、地点（也可选择官方合作场地），在会员社群</view>
            </view>
            
            <view class="benefit-item">
              <view class="benefit-title">会员社群</view>
              <view class="benefit-desc">加入同城会员社群，活动小群等读书社群</view>
            </view>
            
            <view class="benefit-item">
              <view class="benefit-title">参与制定活动主题</view>
              <view class="benefit-desc">在群内制定主题/任务，官管官方发起活动</view>
            </view>
          </view>
        </view>

        <!-- 体验不满意可全额退款 -->
        <view class="section">
          <view class="section-title">体验不满意可全额退款</view>
          <view class="section-content">
            <view class="refund-item">
              <text>新用户注册30天内，日报名参加活动 1场，</text>
            </view>
            <view class="refund-item">
              <text>不满意</text>
            </view>
            <view class="refund-item">
              <text>可退款，超过30天或超过1场则无法退款</text>
            </view>
          </view>
        </view>

        <!-- 享受平台其他功能 -->
        <view class="section">
          <view class="section-title">享受平台其他功能</view>
          <view class="section-content">
            <view class="feature-desc">
              如笔记、推书、文章等内容创作，加入建议读书小组等
            </view>
          </view>
        </view>

        <!-- 服务条款 -->
        <view class="section">
          <view class="section-title">服务条款</view>
          <view class="section-content">
            <view class="terms-item">
              <view class="terms-number">1.</view>
              <view class="terms-text">会员服务期限为购买时选择的期限，到期后需重新购买。</view>
            </view>
            
            <view class="terms-item">
              <view class="terms-number">2.</view>
              <view class="terms-text">会员权益仅限本人使用，不得转让或借用他人。</view>
            </view>
            
            <view class="terms-item">
              <view class="terms-number">3.</view>
              <view class="terms-text">平台保留对会员服务内容进行调整的权利，调整前会提前通知用户。</view>
            </view>
            
            <view class="terms-item">
              <view class="terms-number">4.</view>
              <view class="terms-text">如发现恶意使用会员权益或违反平台规定，平台有权取消会员资格。</view>
            </view>
            
            <view class="terms-item">
              <view class="terms-number">5.</view>
              <view class="terms-text">退款申请需在购买后30天内提出，超期不予受理。</view>
            </view>
            
            <view class="terms-item">
              <view class="terms-number">6.</view>
              <view class="terms-text">本协议的解释权归平台所有，如有争议以平台最终解释为准。</view>
            </view>
          </view>
        </view>

        <!-- 联系我们 -->
        <view class="section">
          <view class="section-title">联系我们</view>
          <view class="section-content">
            <view class="contact-item">
              <text>如有任何问题或建议，请联系我们的客服团队。</text>
            </view>
            <view class="contact-item">
              <text>客服邮箱：<EMAIL></text>
            </view>
            <view class="contact-item">
              <text>客服电话：400-123-4567</text>
            </view>
          </view>
        </view>

        <!-- 生效时间 -->
        <view class="section">
          <view class="section-title">协议生效</view>
          <view class="section-content">
            <view class="effective-date">
              <text>本协议自用户点击"我已阅读"按钮时生效。</text>
            </view>
            <view class="effective-date">
              <text>最后更新时间：{{ new Date().toLocaleDateString() }}</text>
            </view>
          </view>
        </view>

      </view>
      
      <!-- 底部按钮 -->
      <view class="bottom-actions">
        <u-button
            text="我已阅读并同意"
            shape="circle"
            color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
            :customStyle="{
              width: '100%',
              height: '88rpx',
              fontSize: '32rpx',
              fontWeight: '600',
              boxShadow: '0 4rpx 16rpx rgba(106, 192, 134, 0.3)'
            }"
            @click="agreeAndClose"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
import myTitle from "@/components/myTitle.vue";

export default {
  components: {
    myTitle
  },
  data() {
    return {};
  },
  methods: {
    agreeAndClose() {
      // 返回上一页
      uni.navigateBack();
    }
  }
};
</script>

<style scoped lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
  padding-bottom: 120rpx;
}

.content-container {
  padding: 40rpx 30rpx 0;
  margin-top: 40rpx;
}

.agreement-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(106, 192, 134, 0.1);
}

.section {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #6AC086;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid rgba(106, 192, 134, 0.2);
}

.section-content {
  line-height: 1.6;
}

.benefit-item {
  margin-bottom: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.refund-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.terms-item {
  display: flex;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.terms-number {
  font-size: 26rpx;
  color: #6AC086;
  font-weight: 600;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.terms-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.contact-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.effective-date {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 8rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid rgba(106, 192, 134, 0.1);
  box-shadow: 0 -4rpx 16rpx rgba(106, 192, 134, 0.08);
}
</style>
