<?php
namespace controller;
use core\Controller;
use core\Db;
class Output extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	//无验证文档下载
	public function document_download(){
		if(empty($_GET['id']) || !check($_GET['id'],"intgt0")){
			die("404");exit();
		}
		$id = (int)$_GET['id'];
		dbConn();
		$info = Db()->table("files")->where("id={$id}")->fetch();
		if(empty($info)){
			die("404");exit();
		}
		$path = BASE_PATH . $info['path'];
		if(!file_exists($path)){
			die("404");exit();
		}
		//
		$sql = "UPDATE `files` SET `downtimes`=`downtimes`+1 WHERE id={$id}";
		Db()->_exec($sql);
		//
		header('Content-disposition: attachment; filename='.$info['filename']); //文件名
		header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
		header("Content-type: application/octet-stream");
		header('Content-Encoding: none');
	    header('Content-Length: '. filesize($path)); //告诉浏览器，文件大小
		@readfile($path);
	}

	public function _empty(){
		echo 1111;
	}
	function __destruct(){

	}
}
