<script setup>
import { ref, onMounted } from 'vue';
import { getQuotes, likeQuote } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth';
import customNavbar from '@/components/customNavbar.vue';

// 状态管理
const quotes = ref([]);
const loading = ref(true);
const refreshing = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 修复：添加摘录缓存机制，避免重复加载
const quoteCache = ref(new Map()); // 缓存摘录数据
const quoteCacheKey = 'quotes_list'; // 摘录缓存键

// 生成缓存键
const generateQuoteCacheKey = () => {
  const uid = store().$state.userInfo?.uid || 'anonymous';
  return `${quoteCacheKey}_user_${uid}`;
};

// 加载摘录列表 - 修复：添加缓存机制
const loadQuotes = async (page = 1, isRefresh = false) => {
  try {
    if (isRefresh) {
      refreshing.value = true;
      currentPage.value = 1;
    } else if (page > 1) {
      loadingMore.value = true;
    } else {
      loading.value = true;

      // 检查缓存（仅第一页且非刷新时）
      if (page === 1 && !isRefresh) {
        const cacheKey = generateQuoteCacheKey();
        const cachedData = quoteCache.value.get(cacheKey);
        if (cachedData && cachedData.timestamp && (Date.now() - cachedData.timestamp < 5 * 60 * 1000)) {
          quotes.value = cachedData.data;
          currentPage.value = cachedData.page;
          hasMore.value = cachedData.hasMore;
          loading.value = false;
          console.log(`从缓存恢复摘录数据，共 ${quotes.value.length} 条`);
          return;
        }
      }
    }

    const res = await getQuotes({
      page: page,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      const newQuotes = res.data.list || [];

      if (isRefresh || page === 1) {
        quotes.value = newQuotes;

        // 缓存第一页数据
        if (page === 1) {
          const cacheKey = generateQuoteCacheKey();
          quoteCache.value.set(cacheKey, {
            data: [...newQuotes],
            page: page,
            hasMore: newQuotes.length === pageSize,
            timestamp: Date.now()
          });
          console.log(`缓存摘录数据，共 ${newQuotes.length} 条`);
        }
      } else {
        quotes.value = [...quotes.value, ...newQuotes];
      }

      hasMore.value = newQuotes.length === pageSize;
      currentPage.value = page;
    } else if (res.status === 'empty') {
      if (isRefresh || page === 1) {
        quotes.value = [];
      }
      hasMore.value = false;
    }
  } catch (error) {
    console.error('加载摘录失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
    loadingMore.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  loadQuotes(1, true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadQuotes(currentPage.value + 1);
  }
};

// 查看摘录详情
const viewQuote = (quote) => {
  navto(`/pages/bundle/world/quote/detail?id=${quote.id}`);
};

// 点赞摘录
const handleLike = async (quote, event) => {
  event.stopPropagation(); // 阻止事件冒泡

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  try {
    const res = await likeQuote({
      id: quote.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.is_liked = !quote.is_liked;
      quote.like_count = quote.is_liked ? (quote.like_count || 0) + 1 : (quote.like_count || 1) - 1;

      uni.showToast({
        title: quote.is_liked ? '点赞成功' : '取消点赞',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('点赞失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 收藏摘录
const handleFavorite = async (quote, event) => {
  event.stopPropagation(); // 阻止事件冒泡

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  try {
    const res = await favoriteQuote({
      id: quote.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.is_favorited = !quote.is_favorited;
      quote.favorite_count = quote.is_favorited ? (quote.favorite_count || 0) + 1 : (quote.favorite_count || 1) - 1;

      uni.showToast({
        title: quote.is_favorited ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('收藏失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - time;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadQuotes();
});
</script>

<template>
  <view class="quote-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 摘录列表 -->
    <scroll-view 
      v-else
      class="quote-scroll"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <!-- 空状态 -->
      <view v-if="quotes.length === 0" class="empty-container">
        <u-empty mode="list" text="还没有摘录哦" description="收藏美好的文字片段"></u-empty>
      </view>

      <!-- 瀑布流摘录列表 -->
      <view v-else class="quote-waterfall">
        <view
          v-for="columnIndex in 2"
          :key="columnIndex"
          class="quote-column"
        >
          <view
            v-for="(quote, index) in quotes.filter((_, i) => i % 2 === (columnIndex - 1))"
            :key="quote.id"
            class="quote-card"
            @click="viewQuote(quote)"
          >
            <!-- 摘录内容 -->
            <view class="quote-content">
              <text class="quote-text">{{ quote.content }}</text>
            </view>

            <!-- 作者和出处 -->
            <view v-if="quote.author || quote.source" class="quote-meta">
              <text v-if="quote.author" class="quote-author">— {{ quote.author }}</text>
              <text v-if="quote.source" class="quote-source">《{{ quote.source }}》</text>
            </view>

            <!-- 底部信息 -->
            <view class="quote-footer">
              <!-- 用户信息 -->
              <view class="user-info">
                <image
                  :src="quote.user?.avatar_url || '/static/default-avatar.png'"
                  class="user-avatar"
                  mode="aspectFill"
                ></image>
                <text class="user-nickname">{{ quote.user?.nickname || '匿名' }}</text>
              </view>

              <!-- 私密标识 -->
              <view v-if="quote.privacy === 'private'" class="privacy-badge">
                <u-icon name="lock" size="10" color="#999"></u-icon>
              </view>
            </view>

            <!-- 时间和操作按钮 -->
            <view class="quote-bottom">
              <view class="quote-time">
                <text class="time-text">{{ formatTime(quote.created_at) }}</text>
              </view>

              <!-- 操作按钮 -->
              <view class="quote-actions">
                <view class="action-btn" @click="handleLike(quote, $event)">
                  <u-icon
                    :name="quote.is_liked ? 'heart-fill' : 'heart'"
                    :color="quote.is_liked ? '#ff4757' : '#999'"
                    size="16"
                  ></u-icon>
                  <text class="action-count">{{ quote.like_count || 0 }}</text>
                </view>

                <view class="action-btn" @click="handleFavorite(quote, $event)">
                  <u-icon
                    :name="quote.is_favorited ? 'star-fill' : 'star'"
                    :color="quote.is_favorited ? '#ffa502' : '#999'"
                    size="16"
                  ></u-icon>
                  <text class="action-count">{{ quote.favorite_count || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="circle" size="20" color="#6AC086"></u-loading-icon>
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="!hasMore && quotes.length > 0" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.quote-container {
  height: 100%;
  background-color: var(--color-background, #f8f9fa);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .loading-text {
    margin-top: var(--spacing-md, 20rpx);
    font-size: var(--font-size-md, 28rpx);
    color: var(--color-text-secondary, #666);
  }
}

.quote-scroll {
  height: 100%;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.quote-waterfall {
  display: flex;
  padding: var(--spacing-md, 20rpx);
  gap: var(--spacing-md, 20rpx);
}

.quote-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md, 20rpx);
}

.quote-card {
  background: var(--color-surface, #ffffff);
  border-radius: var(--radius-md, 16rpx);
  padding: var(--spacing-lg, 24rpx);
  box-shadow: var(--shadow-sm, 0 2rpx 12rpx rgba(0, 0, 0, 0.05));
  border-left: 4rpx solid var(--color-primary, #6AC086);
}

.quote-content {
  margin-bottom: 20rpx;
  
  .quote-text {
    font-size: 30rpx;
    line-height: 1.8;
    color: #333;
    font-style: italic;
  }
}

.quote-meta {
  margin-bottom: 20rpx;
  
  .quote-author {
    display: block;
    font-size: 26rpx;
    color: #666;
    text-align: right;
    margin-bottom: 8rpx;
  }
  
  .quote-source {
    display: block;
    font-size: 24rpx;
    color: #999;
    text-align: right;
  }
}

.quote-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-avatar {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      margin-right: 12rpx;
    }
    
    .user-nickname {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .privacy-badge {
    padding: 4rpx 8rpx;
    background: #f5f5f5;
    border-radius: 12rpx;
  }
}

.quote-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .quote-time {
    .time-text {
      font-size: 22rpx;
      color: #999;
    }
  }

  .quote-actions {
    display: flex;
    gap: 24rpx;

    .action-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx;
      border-radius: 12rpx;
      transition: background-color 0.2s ease;

      &:active {
        background-color: #f5f5f5;
      }

      .action-count {
        font-size: 20rpx;
        color: #999;
        min-width: 20rpx;
      }
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-more-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
