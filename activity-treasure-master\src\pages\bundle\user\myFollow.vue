<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_guanzhu_list, userguanzhu_del } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, navto, callPhone } from "@/utils";
import { requireLogin } from "@/utils/auth";

const goods = ref([
  { num: 0, money: 20, zongMoney: 0 },
  { num: 0, money: 30, zongMoney: 0 },
]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_guanzhu_list({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || []; // 当前页数据
      if (mescroll.num == 1) goods.value = []; // 第一页需手动制空列表
      goods.value = goods.value.concat(curPageData); //追加新数据
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr(); // 请求失败, 结束加载
    });
};
const cancelFollow = async (val, i) => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再取消关注')) {
    return;
  }

  const res = await userguanzhu_del({ to_uid: val.to_user.uid });
  if (res.status === "ok") goods.value.splice(i, 1);
  else uni.$u.toast(res.msg);
};
</script>
<template>
  <view class="page">
    <myTitle img="cJianbianTopBg.png" height="320rpx" title="我的关注"></myTitle>
    <!-- <u-gap height="320rpx" bg-color=""></u-gap> -->
    <view class="pa px30 w" style="top: 178rpx">
      <mescroll-uni
        class="list"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
        :height="height"
      >
        <view class="mb10 pt20 pb20 px30 w b6f r30" v-for="(val, i) in goods" :key="i">
          <view class="mb20 df aic">
            <view class="df aic f1" @click="navto(`/pages/bundle/msg/personage?to_uid=${val.to_user?.uid}`)">
              <u-avatar
                size="128rpx"
                :src="val.to_user?.avatar"
                mode="aspectFill"
              ></u-avatar>
              <view class="df fdc jcsb ml20">
                <u-text size="32rpx" bold :text="val.to_user?.nickname"></u-text>
                <u-text size="22rpx" :text="val.to_user?.gexingqianming"></u-text>
              </view>
            </view>
            <u-button
              color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
              :customStyle="{
                width: '140rpx',
                height: '60rpx',
                borderRadius: '16rpx',
                color: '#000',
                fontSize: '20rpx',
                scale: '.8',
                transformOrigin: 'center',
              }"
              text="取消关注"
              @click.stop="cancelFollow(val, i)"
            ></u-button>
          </view>
          <view class="df aic jcsb mb20 x26 fb" style="margin-left: 150rpx">
            <view class="df aic">
              {{ val.huodong_canyu }}
              <u-text
                margin="0 0 0 10rpx"
                color="#AAAAAA"
                size="20rpx"
                text="Ta参加的活动"
              ></u-text>
            </view>
            <view class="df aic">
              {{ val.huodong_faqi }}
              <u-text
                margin="0 0 0 10rpx"
                color="#AAAAAA"
                size="20rpx"
                text="Ta举办的活动"
              ></u-text>
            </view>
          </view>
          <u-album
            v-if="val.imgs_url?.length > 0"
            :urls="val.imgs_url"
            keyName="img_url"
            maxCount="3"
            multipleSize="201rpx"
            singleSize="600rpx"
            radius="20rpx"
          ></u-album>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
:v-deep u-album__row__wrapper data-v-6fcabaad {
  border-radius: 30rpx;
}
</style>
