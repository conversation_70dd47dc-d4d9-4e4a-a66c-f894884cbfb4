<template>
  <view class="placeholder-container">
    <u-loading-icon mode="circle" size="20"></u-loading-icon>
    <text class="placeholder-text">日卡加载中...</text>
  </view>
</template>

<script setup>
// No script logic needed for simple placeholder
</script>

<style lang="scss" scoped>
.placeholder-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  height: 300rpx; // Example height
  color: #999;
  font-size: 26rpx;
}
.placeholder-text {
    margin-top: 15rpx;
}
</style> 