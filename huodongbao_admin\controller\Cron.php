<?php
namespace controller;
use core\Controller;
use core\Db;
//定时任务
class Cron extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	//导出数据库
	public function db_dump(){//数据库定时备份，每小时一次
		if(!IS_CLI){
			echo "只支持命令行访问";
			exit;
		}
		$path = BASE_PATH . config("sys.databak");
		if(!is_dir($path))@mkdir($path, 0777, true);
		$config = \core\Config::getDatabaseConfig();
		foreach($config as $k=>$v){
			if($v["host"] == "127.0.0.1" || $v["host"] == "localhost"){
				shell_exec(BASE_PATH . "databak.sh {$v['user']} {$v['password']} {$v['database']} {$path}");
				shell_exec("cd {$path} && rm -f {$v['database']}_".date("Y_m_d",strtotime("-3 day"))."*");
			}
		}
		echo "done";
	}
	
	//每天凌晨自动更新
	public function huiyuan_update(){
		if(!IS_CLI){
			echo "只支持命令行访问";
			exit;
		}
		dbConn();
		Db::begin();
		try{
			// 修改：排除分会长（role_type='1'），分会长永久免费会员
			$sql = "UPDATE `user` SET `is_huiyuan`=0 WHERE `is_huiyuan`=1 AND `huiyuan_end_time`<NOW() AND `role_type`!='1'";
			$rowCount = Db::_exec($sql);
			Db::commit();
			_log_("会员到期更新完成【{$rowCount}】（已排除分会长）");
			echo "会员到期更新完成【{$rowCount}】（已排除分会长）\n";
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log("会员到期更新失败：" . $e->getMessage());
			_log_("会员到期更新失败：" . $e->getMessage());
			echo "会员到期更新失败：" . $e->getMessage() . "\n";
		}
		//
	}
	public function _empty(){
		echo __CLASS__." -> _empty";
	}
	function __destruct(){

	}
}
