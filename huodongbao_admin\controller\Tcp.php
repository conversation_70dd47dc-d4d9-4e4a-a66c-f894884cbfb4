<?php
namespace controller;
use \Swoole\Process;
use \Swoole\Coroutine;
use \Swoole\Coroutine\Server\Connection;
class Tcp extends \core\Swoole{
	
	static public function init(){
		if(!IS_CLI){
			echo "只支持命令行访问\n";
			exit;
		}
		//fd对应的登录信息
		$login_fd = [
			["column"=>"fd","type"=>"int","len"=>10],
			["column"=>"uid","type"=>"int","len"=>10],
			["column"=>"channels","type"=>"string","len"=>500],//加入的频道，逗号隔开
		];
		self::createTable("login_fd",$login_fd);
		
		//uid换fd，一对多
		$login_uid = [
			["column"=>"fds","type"=>"string","len"=>100],
		];
		self::createTable("login_uid",$login_uid);
		//多进程管理模块
		$pool = new Process\Pool(1);
		//让每个OnWorkerStart回调都自动创建一个协程
		$pool->set(['enable_coroutine' => true]);
		$pool->on('workerStart', function ($pool, $id) {
			$server_config = [
				'daemonize' => config("sys.swoole.daemonize"),
				'heartbeat_check_interval'=>60,
				'heartbeat_idle_time'=>600,
				'log_file'=>parent::$base_dir . "socket.err.log",
				'open_eof_check' => true,
				'package_eof' => '\r\n',
			];
			$ssl_key_file = config("sys.swoole.ssl_key_file");
			$ssl_cert_file = config("sys.swoole.ssl_cert_file");
			$ssl = false;
			if(!empty($ssl_key_file) && !empty($ssl_cert_file)){
				$server_config['ssl_key_file'] = $ssl_key_file;
				$server_config['ssl_cert_file'] = $ssl_cert_file;
				$server_config['ssl_method'] = \SWOOLE_SSLv23_SERVER_METHOD;
				$ssl = true;
			}
			$server = new \Swoole\Coroutine\Server('0.0.0.0', config("sys.swoole.socket_port"), $ssl, true);
			$server->set($server_config);
			//收到15信号关闭服务
			Process::signal(SIGTERM, function () use ($server) {
				$server->shutdown();
			});

			//接收到新的连接请求 并自动创建一个协程
			$server->handle(function (Connection $conn) {
				$conn_id = spl_object_id($conn);
				(parent::$links)[$conn_id] = $conn;
				self::connectd($conn_id);
				while(true){
					$input = $conn->recv();
					if ($input === '' || $input === false) {
						$conn->close();
						break;
					}
					//echo $input . "\n";
					$res = explode('\r\n',trim($input,'\r\n'));
					if(!empty($res)){
						foreach($res as $row){
							go(function()use ($row,$conn,$conn_id){
								$data = json_decode($row,true);
								if(empty($data) || !is_array($data) || !isset($data['url'])){
									parent::write($conn,["type"=>"error","msg"=>"params error"]);
								}
								$data['conn'] = $conn;
								$data['conn_id'] = $conn_id;
								parent::route($conn,$data);								
							});
						}
					}
				}
				self::disconnectd($conn_id);
			});
			parent::$tcpServer = $server;
			//开始监听端口
			$server->start();
		});
		$pool->start();
	}
}