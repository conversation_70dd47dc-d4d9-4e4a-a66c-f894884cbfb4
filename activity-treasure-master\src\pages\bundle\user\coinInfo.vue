<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_zhangdan } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
import { getItem } from "@/utils";

const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_zhangdan({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};
</script>
<template>
  <view class="page">
    <view class="py30 df aic" style="background-color: #eeeeee">
      <u-text
        margin="0 10rpx"
        align="center"
        color="#333"
        size="22rpx"
        text="变动类型"
      ></u-text>
      <u-text
        margin="0 10rpx"
        align="center"
        color="#333"
        size="22rpx"
        text="变动日期"
      ></u-text>
      <u-text
        margin="0 10rpx"
        align="center"
        color="#333"
        size="22rpx"
        text="变动金额(元)"
      ></u-text>
      <u-text
        margin="0 10rpx"
        align="center"
        color="#333"
        size="22rpx"
        text="剩余金额(元)"
      ></u-text>
    </view>
    <mescroll-uni
      class="list"
      :height="height"
      :up="{
        page: {
          num: 0,
          size: 20,
          time: null,
        },
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <view class="py30 df aic borderBottom" v-for="(val, i) in goods" :key="i">
        <u-text
          margin="0 10rpx"
          align="center"
          color="#333"
          size="22rpx"
          :text="
            getItem(
              [
                '充值',
                '活动费用结算',
                '活动佣金结算',
                '商品佣金结算',
                '会员佣金结算',
                '提现申请',
                '提现驳回',
                '活动费用支付',
                '活动费用退还',
                '抽奖中奖',
                '商品支付',
                '会员支付',
                '商品退款',
                '大礼包支付',
                '大礼包退款',
              ],
              val.type - 1
            )
          "
        ></u-text>
        <u-text
          margin="0 10rpx"
          align="center"
          color="#333"
          size="22rpx"
          :text="val.time"
        ></u-text>
        <u-text
          margin="0 10rpx"
          align="center"
          color="#FF2A00"
          size="22rpx"
          :text="val.money"
        ></u-text>
        <u-text
          margin="0 10rpx"
          align="center"
          color="#FF2A00"
          size="22rpx"
          :text="val.shengyu"
        ></u-text>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less"></style>
