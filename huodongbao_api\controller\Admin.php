<?php
namespace controller;
use core\Controller;
use core\Db;

/*
 * @className 管理员功能
 */
class Admin extends Controller {

    public function __construct() {
        parent::__construct();
    }

    /*
     * @apiName 审核分会长申请
     * @method branch_president_review
     * @POST
     * @param application_id string 申请编号
     * @param status string 审核状态：1=通过，2=拒绝
     * @param comment string 审核意见（可选）
     * @return {"status":"ok","msg":"审核完成"}
     */
    public function branch_president_review($application_id, $status, $comment = '') {
        // 参数验证
        if (
            empty($application_id) || !check($application_id, "intgt0") ||
            !in_array($status, [1, 2])
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $application_id = (int)$application_id;
        $status = (int)$status;

        // 这里应该有管理员权限验证，暂时简化处理
        // 在实际使用中需要根据具体的管理员认证方式进行验证

        dbConn();

        // 获取申请信息
        $application = Db()->table("branch_president_applications")
            ->select("id,user_id,branch_name,branch_description,branch_location,status")
            ->where("id={$application_id}")
            ->fetch();

        if (empty($application)) {
            return ["status" => "error", "msg" => "申请记录不存在"];
        }

        if ($application['status'] != 0) {
            return ["status" => "error", "msg" => "该申请已经审核过了"];
        }

        Db::begin();
        try {
            // 更新申请状态
            $update_data = [
                "status" => $status,
                "review_time" => date("Y-m-d H:i:s"),
                "review_comment" => $comment
            ];

            if ($status == 1) { // 审核通过
                // 获取新的分会ID
                $max_branch_id = Db()->table("user_branch")->max("branch_id");
                $new_branch_id = $max_branch_id + 1;

                // 🔧 修复：创建新分会（包含新增字段）
                // 获取申请人信息
                $applicant_info = Db()->table("user")
                    ->select("mobile")
                    ->where("uid={$application['user_id']}")
                    ->fetch();

                // 获取申请中的微信二维码（如果有）
                $application_detail = Db()->table("branch_president_applications")
                    ->select("wechat_qr_image")
                    ->where("id={$application_id}")
                    ->fetch();

                $branch_data = [
                    "branch_id" => $new_branch_id,
                    "branch_name" => $application['branch_name'],
                    "branch_location" => $application['branch_location'],
                    "branch_leader" => $application['user_id'],
                    "branch_leader_mobile" => $applicant_info['mobile'] ?? '',
                    "branch_leader_qr_image" => $application_detail['wechat_qr_image'] ?? '',
                    "branch_members" => 1,
                    "assignment_counter" => 0,
                    "system_assigned_count" => 0,
                    "branch_type" => 'normal',
                    "created_at" => date("Y-m-d H:i:s")
                ];

                Db()->table("user_branch")->insert($branch_data);

                // 更新用户为分会长，并设置为永久会员
                $user_update = [
                    "role_type" => '1',
                    "branch_id" => $new_branch_id,
                    "is_huiyuan" => 1,
                    "huiyuan_end_time" => '2099-12-31 23:59:59' // 设置为永久会员
                ];

                Db()->table("user")->where("uid={$application['user_id']}")->update($user_update);

                // 记录创建的分会ID
                $update_data["created_branch_id"] = $new_branch_id;
            }

            // 更新申请记录
            Db()->table("branch_president_applications")->where("id={$application_id}")->update($update_data);

            Db::commit();

            $status_text = $status == 1 ? "通过" : "拒绝";
            return [
                "status" => "ok",
                "msg" => "审核完成：{$status_text}"
            ];

        } catch (\Exception $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }


}
