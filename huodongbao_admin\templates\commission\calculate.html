<fieldset class="table-search-fieldset">
    <legend>运营佣金计算</legend>
    <div style="margin: 20px;">
        <div class="layui-row">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-calculator"></i> 佣金计算设置
                    </div>
                    <div class="layui-card-body">
                        <form class="layui-form" action="/commission/calculate" method="post">
                            <div class="layui-form-item">
                                <label class="layui-form-label">计算月份</label>
                                <div class="layui-input-block">
                                    <input type="text" name="year_month" id="year_month" placeholder="请选择年月" class="layui-input" required lay-verify="required">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="calculate">
                                        <i class="layui-icon layui-icon-ok"></i> 开始计算
                                    </button>
                                    <a href="/commission/index" class="layui-btn layui-btn-primary">
                                        <i class="layui-icon layui-icon-return"></i> 返回
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-tips"></i> 操作说明
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-text">
                            <h3>佣金计算规则：</h3>
                            <ul>
                                <li>运营佣金按月计算，每个分会长每月可获得固定金额的运营佣金</li>
                                <li>佣金金额根据系统配置的默认运营佣金标准确定</li>
                                <li>如有个性化佣金设置，将优先使用个性化设置的金额</li>
                                <li>同一月份只能计算一次，重复计算将被系统拒绝</li>
                                <li>计算完成后，佣金将自动发放到分会长账户</li>
                            </ul>
                            
                            <h3>注意事项：</h3>
                            <ul>
                                <li>请确保选择的月份是正确的，计算后无法撤销</li>
                                <li>建议在每月初进行上月佣金的计算和发放</li>
                                <li>计算过程中请勿关闭页面或进行其他操作</li>
                                <li>如遇到问题，请联系技术支持</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-chart"></i> 当前统计
                    </div>
                    <div class="layui-card-body">
                        <div class="stat-item">
                            <div class="stat-label">分会长总数</div>
                            <div class="stat-value" id="president_count">加载中...</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">默认运营佣金</div>
                            <div class="stat-value" id="default_commission">加载中...</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">预计发放总额</div>
                            <div class="stat-value" id="estimated_total">加载中...</div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-log"></i> 最近计算记录
                    </div>
                    <div class="layui-card-body">
                        <div id="recent_records">
                            <div style="text-align: center; color: #999; padding: 20px;">
                                加载中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</fieldset>

<script>
layui.use(['form', 'laydate', 'layer'], function(){
    var form = layui.form;
    var laydate = layui.laydate;
    var layer = layui.layer;
    
    // 年月选择器
    laydate.render({
        elem: '#year_month',
        type: 'month',
        format: 'yyyy-MM',
        max: 0, // 不能选择未来月份
        value: getLastMonth() // 默认选择上个月
    });
    
    // 表单提交
    form.on('submit(calculate)', function(data){
        var yearMonth = data.field.year_month;
        
        layer.confirm('确定要计算 ' + yearMonth + ' 的运营佣金吗？<br><br><span style="color: red;">注意：计算后无法撤销！</span>', {
            icon: 3,
            title: '确认计算'
        }, function(index){
            layer.close(index);
            
            // 显示进度条
            var loadIndex = layer.load(2, {
                content: '正在计算佣金，请稍候...'
            });
            
            // 提交表单
            $.post('/commission/calculate', {
                year_month: yearMonth
            }, function(response) {
                layer.close(loadIndex);
                
                if(response.indexOf('成功') > -1) {
                    layer.alert(response, {
                        icon: 1,
                        title: '计算完成'
                    }, function() {
                        window.location.href = '/commission/records';
                    });
                } else {
                    layer.alert(response, {
                        icon: 2,
                        title: '计算失败'
                    });
                }
            }).fail(function() {
                layer.close(loadIndex);
                layer.msg('网络错误，请稍后重试', {icon: 2});
            });
        });
        
        return false;
    });
    
    // 获取上个月
    function getLastMonth() {
        var date = new Date();
        date.setMonth(date.getMonth() - 1);
        var year = date.getFullYear();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        return year + '-' + month;
    }
    
    // 加载统计数据
    function loadStats() {
        // 这里可以通过AJAX加载统计数据
        // 暂时使用模拟数据
        $('#president_count').text('5 人');
        $('#default_commission').text('¥50.00');
        $('#estimated_total').text('¥250.00');
        
        // 加载最近记录
        $('#recent_records').html(`
            <div class="record-item">
                <div class="record-month">2024-01</div>
                <div class="record-info">已计算 5 人，¥250.00</div>
            </div>
            <div class="record-item">
                <div class="record-month">2023-12</div>
                <div class="record-info">已计算 4 人，¥200.00</div>
            </div>
        `);
    }
    
    // 页面加载完成后加载数据
    loadStats();
});
</script>

<style>
.layui-card {
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.stat-value {
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.record-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
    border-bottom: none;
}

.record-month {
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.record-info {
    font-size: 12px;
    color: #999;
}

.layui-text ul li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.layui-text h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #333;
}

.layui-text h3:first-child {
    margin-top: 0;
}
</style>
