<script setup>
import { ref, computed, defineProps, defineEmits, watch, nextTick } from 'vue';
import { saveImageToAlbum } from '@/utils/painterShare';
import { showShareMenu } from '@/utils/uniShareConfig';
import { createCardShareConfig, createFeedShareConfig, createDefaultShareConfig } from '@/utils/painterConfig';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '分享'
  },
  shareData: {
    type: Object,
    default: () => ({})
  },
  showMemberInvite: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'select', 'share-success', 'share-error']);

// Painter 相关状态
const isGeneratingImage = ref(false);
const generatedImagePath = ref('');
const painterConfig = ref(null);
const generationTimer = ref(null);
const GENERATION_TIMEOUT = 30000; // 30秒超时

// 配置保护机制 - 防止Vue响应式系统意外清理
const configLocked = ref(false);
const isRenderingInProgress = ref(false);
const configProtectionTimer = ref(null);
const lastValidConfig = ref(null);

// 基础分享选项
const baseShareOptions = [
  { id: 'wechat', name: '微信', icon: '/static/weixin.svg' },
  { id: 'moments', name: '朋友圈', icon: '/static/pengyouquan.svg' },
  { id: 'image', name: '分享图片', icon: '/static/kapian.svg' },
  { id: 'save', name: '保存图片', icon: '/static/baocun.svg' }
];

// 动态分享选项（包含体验会员邀请）
const shareOptions = computed(() => {
  const options = [...baseShareOptions];

  // 如果显示体验会员邀请
  if (props.showMemberInvite) {
    options.push({
      id: 'member-invite',
      name: '邀请体验',
      icon: '/static/gift.svg'
    });
  }

  return options;
});

// 处理选项点击
const handleSelect = async (option) => {
  try {
    switch (option.id) {
      case 'wechat':
        await shareToWeChat();
        break;
      case 'moments':
        await shareToMoments();
        break;
      case 'image':
        await generateAndShowShareImage();
        break;
      case 'save':
        await generateAndSaveImage();
        break;
      case 'member-invite':
        await shareMemberInvite();
        break;
      case 'uni-share':
        await shareWithUniShare();
        break;
      default:
        emit('select', option);
    }
  } catch (error) {
    console.error('分享操作失败:', error);
    emit('share-error', error);

    // 提供更友好的错误提示
    const errorMessage = error.message || '分享失败';
    uni.showModal({
      title: '分享失败',
      content: errorMessage,
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试，重新调用相同的操作
          handleSelect(option);
        }
      }
    });
  }
};

// 微信好友分享
const shareToWeChat = async () => {
  // #ifdef MP-WEIXIN
  try {
    // 根据官方文档，直接提示用户点击右上角分享
    uni.showToast({
      title: '请点击右上角分享给好友',
      icon: 'none',
      duration: 2000
    });

    emit('share-success', { type: 'wechat' });
    handleClose();
  } catch (error) {
    console.error('微信分享失败:', error);
    throw error;
  }
  // #endif

  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '仅支持微信小程序',
    icon: 'none'
  });
  // #endif
};

// 朋友圈分享
const shareToMoments = async () => {
  // #ifdef MP-WEIXIN
  try {
    // 朋友圈分享需要跳转到详情页，因为需要onShareTimeline配置
    uni.showToast({
      title: '请点击右上角分享到朋友圈',
      icon: 'none',
      duration: 2000
    });

    emit('share-success', { type: 'moments' });
    handleClose();
  } catch (error) {
    console.error('朋友圈分享失败:', error);
    throw error;
  }
  // #endif

  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '仅支持微信小程序',
    icon: 'none'
  });
  // #endif
};

// 生成并显示分享图片
const generateAndShowShareImage = async () => {
  try {
    if (isGeneratingImage.value) {
      console.warn('图片正在生成中，请勿重复操作');
      return;
    }

    console.log('=== 开始分享图片生成流程 ===');
    isGeneratingImage.value = true;
    generatedImagePath.value = ''; // 清空保存模式标记
    uni.showLoading({ title: '生成图片中...' });

    const config = painterData.value;
    if (!config) {
      throw new Error('无法生成分享配置');
    }

    console.log('✅ Painter配置生成成功:', JSON.stringify(config, null, 2));

    // 验证配置的关键字段
    if (!config.width || !config.height) {
      throw new Error(`Painter配置缺少尺寸信息: width=${config.width}, height=${config.height}`);
    }

    if (!config.views || !Array.isArray(config.views)) {
      throw new Error('Painter配置缺少views数组');
    }

    console.log('✅ Painter配置验证通过，views数量:', config.views.length);

    // 启动超时计时
    startGenerationTimer();
    console.log('✅ 超时计时器已启动，超时时间:', GENERATION_TIMEOUT, 'ms');

    // 启用配置保护机制
    console.log('🛡️ 启用配置保护机制...');
    configLocked.value = true;
    isRenderingInProgress.value = true;

    // 设置保护超时 - 5秒后自动解除保护
    configProtectionTimer.value = setTimeout(() => {
      console.log('🛡️ 配置保护超时，自动解除保护');
      configLocked.value = false;
      isRenderingInProgress.value = false;
    }, 5000);

    // 暂时注释内存清理，避免可能的冲突
    // performMemoryCleanup();

    // 设置Painter配置，触发图片生成
    console.log('🎨 设置Painter配置，触发图片生成...');
    console.log('🎨 配置中的背景图片URL:', config.views?.[0]?.url);

    // 确保不会被意外清理
    const configToSet = { ...config };
    lastValidConfig.value = configToSet; // 保存最后有效配置
    painterConfig.value = configToSet;

    console.log('🎨 Painter配置已设置，等待组件渲染...');
    console.log('🛡️ 配置保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);

    // 添加延迟确保组件正确渲染
    setTimeout(() => {
      console.log('🎨 Painter组件渲染检查...');
      console.log('🎨 当前painterConfig状态:', !!painterConfig.value);
      console.log('🎨 当前isGeneratingImage状态:', isGeneratingImage.value);
      console.log('🛡️ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);

      if (!painterConfig.value) {
        console.error('❌ Painter配置被意外清理！');
        console.error('❌ 尝试恢复配置...');

        // 重新启用保护机制
        configLocked.value = true;
        isRenderingInProgress.value = true;

        // 恢复配置
        painterConfig.value = configToSet;

        console.log('✅ 配置已恢复，重新启动保护机制');
        console.log('🛡️ 恢复后保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);

        // 重新设置保护超时
        if (configProtectionTimer.value) {
          clearTimeout(configProtectionTimer.value);
        }
        configProtectionTimer.value = setTimeout(() => {
          console.log('🛡️ 恢复后配置保护超时，自动解除保护');
          configLocked.value = false;
          isRenderingInProgress.value = false;
        }, 5000);
      }
    }, 100);

    // 添加 Painter 组件状态检查（小程序兼容版本）
    setTimeout(() => {
      console.log('🔍 深度检查 Painter 组件状态...');

      // 小程序环境检查 - 简化版本，避免DOM API调用
      console.log('🔍 小程序环境，跳过DOM检查');

      // 直接使用 uni-app API 检查组件
      try {
        const query = uni.createSelectorQuery();
        query.selectAll('painter').boundingClientRect((rects) => {
          console.log('🔍 小程序中找到的 Painter 组件数量:', rects ? rects.length : 0);

          if (!rects || rects.length === 0) {
            console.warn('⚠️ 小程序中未找到 Painter 组件，可能渲染失败');
            triggerPainterRerender();
          } else {
            console.log('✅ Painter 组件在小程序中存在');
            rects.forEach((rect, index) => {
              console.log(`🔍 Painter 组件 ${index + 1} 位置:`, rect);
            });
          }
        }).exec();
      } catch (error) {
        console.error('🔍 组件检查失败:', error);
        // 如果检查失败，直接尝试重新渲染
        triggerPainterRerender();
      }
    }, 500);

// 触发 Painter 重新渲染的函数
const triggerPainterRerender = () => {
  console.log('🔄 尝试强制重新渲染 Painter 组件...');
  const tempConfig = painterConfig.value;
  painterConfig.value = null;

  nextTick(() => {
    painterConfig.value = tempConfig;
    console.log('🔄 Painter 组件已重新渲染');
  });
};

  } catch (error) {
    console.error('❌ 生成分享图片失败:', error);
    cleanupResources(true, 'generate-error');

    const errorMessage = error.message || '生成分享图片失败';
    uni.showToast({
      title: errorMessage.length > 20 ? '生成图片失败' : errorMessage,
      icon: 'none',
      duration: 3000
    });
    throw error;
  }
};

// 保存生成的图片到相册
const saveGeneratedImageToAlbum = async (imagePath) => {
  try {
    await saveImageToAlbum(imagePath);

    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });

    emit('share-success', { type: 'save', path: imagePath });
    handleClose();
  } catch (error) {
    console.error('保存图片失败:', error);

    const errorMessage = error.message || '保存图片失败';
    uni.showToast({
      title: errorMessage.length > 20 ? '保存失败' : errorMessage,
      icon: 'none',
      duration: 3000
    });
    throw error;
  }
};

// 生成并保存图片
const generateAndSaveImage = async () => {
  try {
    if (isGeneratingImage.value) {
      console.warn('图片正在生成中，请勿重复操作');
      return;
    }

    // 设置保存模式标志
    isGeneratingImage.value = true;
    generatedImagePath.value = 'save-mode'; // 标记为保存模式
    uni.showLoading({ title: '生成图片中...' });

    const config = painterData.value;
    if (!config) {
      throw new Error('无法生成分享配置');
    }

    console.log('开始生成保存图片:', config);

    // 启动超时计时
    startGenerationTimer();

    // 暂时注释内存清理，避免可能的冲突
    // performMemoryCleanup();

    // 设置Painter配置，触发图片生成
    console.log('🎨 保存模式 - 配置中的背景图片URL:', config.views?.[0]?.url);
    painterConfig.value = { ...config };

  } catch (error) {
    console.error('生成保存图片失败:', error);
    cleanupResources(true, 'save-error');

    const errorMessage = error.message || '生成保存图片失败';
    uni.showToast({
      title: errorMessage.length > 20 ? '生成图片失败' : errorMessage,
      icon: 'none',
      duration: 3000
    });
    throw error;
  }
};

// 体验会员分享邀请
const shareMemberInvite = async () => {
  try {
    // #ifdef MP-WEIXIN
    // 根据官方文档，直接提示用户点击右上角分享
    uni.showToast({
      title: '请点击右上角分享体验券',
      icon: 'none',
      duration: 2000
    });
    // #endif

    emit('share-success', { type: 'member-invite' });
    handleClose();
  } catch (error) {
    console.error('体验会员分享失败:', error);
    throw error;
  }
};

// 使用uni-share进行分享
const shareWithUniShare = async () => {
  try {
    console.log('开始uni-share分享，数据:', props.shareData);

    // 根据模板类型创建分享配置
    let shareConfig;
    if (props.shareData.template === 'card') {
      // 使用 uniShareConfig 中的函数创建 uni-share 配置
      const { createCardShareConfig: createUniCardConfig } = await import('@/utils/uniShareConfig');
      shareConfig = createUniCardConfig(props.shareData);
    } else if (props.shareData.template === 'feed' || props.shareData.template === 'dynamic') {
      // 使用 uniShareConfig 中的函数创建 uni-share 配置
      const { createFeedShareConfig: createUniFeedConfig } = await import('@/utils/uniShareConfig');
      shareConfig = createUniFeedConfig(props.shareData);
    } else {
      // 自定义分享配置
      shareConfig = {
        content: {
          type: 0,
          href: props.shareData.href || '',
          title: props.shareData.content || '分享内容',
          summary: `${props.shareData.author || '匿名用户'}分享了内容`,
          imageUrl: props.shareData.image || ''
        },
        menus: [
          {
            "img": "/static/app-plus/sharemenu/wechatfriend.png",
            "text": "微信好友",
            "share": {
              "provider": "weixin",
              "scene": "WXSceneSession"
            }
          },
          {
            "img": "/static/app-plus/sharemenu/wechatmoments.png",
            "text": "微信朋友圈",
            "share": {
              "provider": "weixin",
              "scene": "WXSceneTimeline"
            }
          },
          {
            "img": "/static/app-plus/sharemenu/copyurl.png",
            "text": "复制链接",
            "share": "copyurl"
          }
        ],
        cancelText: "取消分享"
      };
    }

    // 显示uni-share分享菜单
    const result = await showShareMenu(shareConfig);

    if (result.success) {
      console.log('uni-share分享成功:', result);
      uni.showToast({ title: '分享成功', icon: 'success' });
      emit('share-success', { type: 'uni-share', result });
      handleClose();
    } else {
      console.log('用户取消分享');
    }
  } catch (error) {
    console.error('uni-share分享失败:', error);
    uni.showToast({ title: '分享失败', icon: 'none' });
    emit('share-error', error);
  }
};

// 内存清理函数
const performMemoryCleanup = () => {
  console.log('🧹 开始内存清理...');

  try {
    // 强制垃圾回收（如果支持）
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      console.log('🧹 触发微信小程序垃圾回收');
      wx.triggerGC();
    }

    // 清理可能的内存泄漏
    if (typeof global !== 'undefined' && global.gc) {
      console.log('🧹 触发全局垃圾回收');
      global.gc();
    }

    console.log('🧹 内存清理完成');
  } catch (error) {
    console.error('🧹 内存清理失败:', error);
  }
};

// 清理资源函数 - 添加保护机制
const cleanupResources = (forceCleanup = false, reason = 'unknown') => {
  console.log('🧹 清理资源请求 - 原因:', reason, '强制清理:', forceCleanup);
  console.log('🧹 当前状态 - configLocked:', configLocked.value, 'isRenderingInProgress:', isRenderingInProgress.value);
  console.log('🧹 调用栈:', new Error().stack);

  // 保护机制：如果配置被锁定且不是强制清理，则拒绝清理
  if (configLocked.value && !forceCleanup) {
    console.log('🛡️ 配置被保护，拒绝清理 - 原因:', reason);
    return;
  }

  // 保护机制：如果正在渲染且不是强制清理，则延迟清理
  if (isRenderingInProgress.value && !forceCleanup) {
    console.log('🛡️ 正在渲染中，延迟清理 - 原因:', reason);
    setTimeout(() => {
      cleanupResources(false, `delayed-${reason}`);
    }, 1000);
    return;
  }

  console.log('🧹 开始执行资源清理...');

  if (generationTimer.value) {
    clearTimeout(generationTimer.value);
    generationTimer.value = null;
  }

  if (configProtectionTimer.value) {
    clearTimeout(configProtectionTimer.value);
    configProtectionTimer.value = null;
  }

  isGeneratingImage.value = false;
  configLocked.value = false;
  isRenderingInProgress.value = false;

  // 清理 Painter 配置，触发组件销毁
  if (painterConfig.value) {
    console.log('🧹 清理 Painter 配置');
    painterConfig.value = null;
  }

  // 清理生成的图片路径
  if (generatedImagePath.value && generatedImagePath.value !== 'save-mode') {
    generatedImagePath.value = '';
  }

  uni.hideLoading();

  // 执行内存清理
  performMemoryCleanup();

  console.log('🧹 资源清理完成');
};

// 超时处理函数
const handleGenerationTimeout = () => {
  console.error('⏰ 图片生成超时，超时时间:', GENERATION_TIMEOUT, 'ms');
  console.error('⏰ 当前状态 - isGeneratingImage:', isGeneratingImage.value);
  console.error('⏰ 当前状态 - painterConfig:', !!painterConfig.value);
  console.error('⏰ 当前状态 - generatedImagePath:', generatedImagePath.value);
  console.error('⏰ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);

  // 超时时强制清理
  cleanupResources(true, 'timeout');

  uni.showToast({
    title: '图片生成超时，请重试',
    icon: 'none',
    duration: 3000
  });
};



// 开始超时计时
const startGenerationTimer = () => {
  if (generationTimer.value) {
    console.log('⏰ 清理之前的超时计时器');
    clearTimeout(generationTimer.value);
  }

  console.log('⏰ 启动新的超时计时器，超时时间:', GENERATION_TIMEOUT, 'ms');
  generationTimer.value = setTimeout(() => {
    handleGenerationTimeout();
  }, GENERATION_TIMEOUT);
};

// 计算 Painter 配置
const painterData = computed(() => {
  console.log('🔧 计算 Painter 配置，shareData:', props.shareData);

  if (!props.shareData) {
    console.warn('⚠️ shareData 为空，无法生成 Painter 配置');
    return null;
  }

  try {
    console.log('🔧 根据模板类型生成配置，template:', props.shareData.template);

    let config;
    switch (props.shareData.template) {
      case 'card':
        console.log('🔧 使用日卡模板生成配置');
        config = createCardShareConfig(props.shareData);
        break;
      case 'feed':
      case 'dynamic':
        console.log('🔧 使用动态模板生成配置');
        config = createFeedShareConfig(props.shareData);
        break;
      default:
        console.log('🔧 使用默认模板生成配置');
        config = createDefaultShareConfig(props.shareData);
    }

    console.log('✅ Painter 配置生成成功，配置预览:', {
      width: config?.width,
      height: config?.height,
      viewsCount: config?.views?.length,
      background: config?.background
    });

    return config;
  } catch (error) {
    console.error('❌ 生成Painter配置失败:', error);
    console.error('❌ 错误详情:', error.stack);
    return null;
  }
});

// Painter 成功回调
const onPainterSuccess = (event) => {
  console.log('🎉 Painter success event 触发:', event);
  console.log('🎉 Event detail:', event.detail);
  console.log('🎉 Event 完整结构:', JSON.stringify(event, null, 2));

  // 解除配置保护
  console.log('🛡️ 解除配置保护 - 成功回调');
  configLocked.value = false;
  isRenderingInProgress.value = false;

  if (configProtectionTimer.value) {
    clearTimeout(configProtectionTimer.value);
    configProtectionTimer.value = null;
  }

  // 清理超时计时器
  if (generationTimer.value) {
    console.log('✅ 清理超时计时器');
    clearTimeout(generationTimer.value);
    generationTimer.value = null;
  }

  isGeneratingImage.value = false;
  uni.hideLoading();

  const tempFilePath = event.detail?.path || event.detail?.tempFilePath || event.path;
  console.log('🎉 提取的图片路径:', tempFilePath);

  if (tempFilePath) {
    console.log('✅ 图片生成成功，路径:', tempFilePath);

    // 验证图片文件是否有效
    console.log('🔍 开始验证图片文件...');
    uni.getImageInfo({
      src: tempFilePath,
      success: (res) => {
        console.log('✅ 图片验证成功:', res);
        console.log('✅ 图片尺寸:', res.width, 'x', res.height);

        // 根据模式决定是分享还是保存
        if (generatedImagePath.value === 'save-mode') {
          console.log('💾 进入保存模式');
          saveGeneratedImageToAlbum(tempFilePath);
        } else {
          console.log('📤 进入分享模式');
          shareGeneratedImage(tempFilePath);
        }

        generatedImagePath.value = tempFilePath;
      },
      fail: (err) => {
        console.error('❌ 图片验证失败:', err);
        cleanupResources(true, 'image-validation-failed');
        uni.showToast({
          title: '生成的图片无效',
          icon: 'none'
        });
      }
    });
  } else {
    console.error('❌ 图片生成成功但未获取到路径');
    console.error('❌ Event 详情:', event);
    cleanupResources(true, 'no-image-path');
    uni.showToast({
      title: '图片生成失败',
      icon: 'none'
    });
  }
};

// Painter 失败回调
const onPainterFail = (event) => {
  console.error('❌ Painter组件生成失败 event 触发:', event);
  console.error('❌ Event detail:', event.detail);
  console.error('❌ Event 完整结构:', JSON.stringify(event, null, 2));

  // 解除配置保护
  console.log('🛡️ 解除配置保护 - 失败回调');
  configLocked.value = false;
  isRenderingInProgress.value = false;

  if (configProtectionTimer.value) {
    clearTimeout(configProtectionTimer.value);
    configProtectionTimer.value = null;
  }

  // 清理超时计时器
  if (generationTimer.value) {
    console.log('✅ 清理超时计时器');
    clearTimeout(generationTimer.value);
    generationTimer.value = null;
  }

  const errorMsg = event.detail?.error?.message ||
                   event.detail?.message ||
                   event.detail?.errMsg ||
                   event.errMsg ||
                   '图片生成失败';

  console.error('❌ 分享图片生成失败，错误信息:', errorMsg);

  // 尝试降级到传统 Canvas API
  if (painterConfig.value && !painterConfig.value._fallbackAttempted) {
    console.log('🔄 尝试降级到传统 Canvas API...');
    painterConfig.value._fallbackAttempted = true;

    // 重新设置配置，触发重试
    setTimeout(() => {
      console.log('🔄 开始降级重试...');
      const config = { ...painterConfig.value };
      delete config._fallbackAttempted;
      painterConfig.value = null;

      // 使用传统 Canvas API 重试
      setTimeout(() => {
        console.log('🔄 设置传统 Canvas API 配置...');
        painterConfig.value = config;
      }, 100);
    }, 500);

    return;
  }

  console.error('❌ 所有重试方案都已失败，清理资源');
  cleanupResources(true, 'all-retries-failed');

  uni.showToast({
    title: '图片生成失败',
    icon: 'none'
  });
};

// 分享生成的图片
const shareGeneratedImage = (imagePath) => {
  try {
    // #ifdef MP-WEIXIN
    // 微信小程序使用官方分享API
    uni.showShareImageMenu({
      path: imagePath,
      success: () => {
        console.log('分享图片成功');
        emit('share-success', { type: 'image', path: imagePath });
        handleClose();
      },
      fail: (error) => {
        console.error('分享图片失败:', error);
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 其他平台显示图片预览
    uni.previewImage({
      urls: [imagePath],
      current: imagePath
    });
    emit('share-success', { type: 'image', path: imagePath });
    handleClose();
    // #endif
  } catch (error) {
    console.error('分享图片失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  }
};

// 关闭弹窗
const handleClose = () => {
  console.log('🚪 弹窗关闭请求');
  console.log('🛡️ 当前保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);
  console.log('🎨 当前生成状态 - isGeneratingImage:', isGeneratingImage.value);

  // 如果正在生成图片，询问用户是否确认关闭
  if (isGeneratingImage.value && (configLocked.value || isRenderingInProgress.value)) {
    console.log('⚠️ 正在生成图片，延迟关闭');
    uni.showModal({
      title: '提示',
      content: '图片正在生成中，确定要关闭吗？',
      success: (res) => {
        if (res.confirm) {
          console.log('👤 用户确认关闭，强制清理资源');
          cleanupResources(true, 'user-confirmed-close');
          generatedImagePath.value = '';
          emit('close');
        } else {
          console.log('👤 用户取消关闭');
        }
      }
    });
    return;
  }

  // 正常关闭流程
  console.log('🚪 执行正常关闭流程');
  cleanupResources(true, 'popup-close');
  generatedImagePath.value = '';
  emit('close');
};

// 监听 Painter 配置变化，防止意外清理
watch(painterConfig, (newVal, oldVal) => {
  // 如果配置被清理但应该受保护
  if (!newVal && oldVal && (configLocked.value || isRenderingInProgress.value)) {
    console.warn('🛡️ 检测到配置被意外清理，尝试恢复');
    console.log('🛡️ 保护状态 - locked:', configLocked.value, 'rendering:', isRenderingInProgress.value);

    if (lastValidConfig.value) {
      console.log('🔄 使用最后有效配置恢复');
      // 延迟恢复，避免立即被再次清理
      setTimeout(() => {
        if (!painterConfig.value && (configLocked.value || isRenderingInProgress.value)) {
          painterConfig.value = { ...lastValidConfig.value };
          console.log('✅ 配置已自动恢复');
        }
      }, 50);
    }
  }
}, { deep: true });

// Painter 组件事件处理
const onPainterDidShow = () => {
  console.log('🎨 Painter didShow 事件 - 组件已显示');
  console.log('🎨 Painter 组件状态检查');
  console.log('🎨 - painterConfig:', !!painterConfig.value);
  console.log('🎨 - isGeneratingImage:', isGeneratingImage.value);
  console.log('🎨 - configLocked:', configLocked.value);
};

const onPainterViewUpdate = (e) => {
  console.log('🎨 Painter viewUpdate 事件:', e);
  console.log('🎨 ViewUpdate 详情:', JSON.stringify(e, null, 2));
};

const onPainterTouchStart = (e) => {
  console.log('🎨 Painter touchStart 事件:', e);
};

const onPainterTouchMove = (e) => {
  console.log('🎨 Painter touchMove 事件:', e);
};

const onPainterTouchEnd = (e) => {
  console.log('🎨 Painter touchEnd 事件:', e);
};
</script>

<template>
  <view class="share-popup-wrapper" :class="{ 'show': show }" @click.self="handleClose">
    <view class="share-popup" :class="{ 'show': show }">
      <view class="share-popup-header">
        <text class="share-popup-title">{{ title }}</text>
        <view class="share-popup-close" @click="handleClose">
          <u-icon name="close" size="24" color="#999"></u-icon>
        </view>
      </view>

      <view class="share-options">
        <view
          v-for="option in shareOptions"
          :key="option.id"
          class="share-option-item"
          @click="handleSelect(option)"
        >
          <image class="share-option-icon" :src="option.icon" mode="aspectFit"></image>
          <text class="share-option-name">{{ option.name }}</text>
        </view>
      </view>
    </view>

    <!-- Painter组件用于生成分享图片 -->
    <painter
      v-if="painterConfig"
      :palette="painterConfig"
      :use2D="true"
      :LRU="false"
      :dirty="false"
      :disableAction="true"
      :widthPixels="750"
      @imgOK="onPainterSuccess"
      @imgErr="onPainterFail"
      @didShow="onPainterDidShow"
      @viewUpdate="onPainterViewUpdate"
      @touchStart="onPainterTouchStart"
      @touchMove="onPainterTouchMove"
      @touchEnd="onPainterTouchEnd"
      :customStyle="'position: fixed; top: -9999px; left: -9999px; width: 750rpx; height: 1334rpx; z-index: -1;'"
    />
  </view>
</template>

<style lang="scss" scoped>
.share-popup-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.share-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;

  &.show {
    transform: translateY(0);
  }
}

.share-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.share-popup-close {
  padding: 10rpx;
}

.share-options {
  display: flex;
  flex-direction: row; // 改为横排
  justify-content: space-around; // 均匀分布
  padding: 30rpx 0;
  border-top: 1rpx solid #f0f0f0; // 添加顶部边框
}

.share-option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10rpx;
  transition: all 0.3s ease;
}

.share-option-item:active {
  transform: scale(1.15);
}

.share-option-icon {
  width: 80rpx; // 固定大小
  height: 80rpx; // 固定大小
  margin-bottom: 16rpx;
  transition: all 0.2s ease;
}

.share-option-item:active .share-option-icon {
  filter: brightness(1.1) contrast(1.1);
}

.share-option-name {
  font-size: 24rpx;
  color: #333;
}
</style>
