<?php
namespace model;
use core\Db;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Crypto\AesGcm;
use WeChatPay\Formatter;
use WeChatPay\Builder;
use WeChatPay\Util\PemUtil;
class Wechat{
	
	public function __construct(){
		
	}
	
	//获取应用AccessToken
	static public function getAccessToken($is_online=false){
		//
		if(empty($is_online)){
			$access_token = \core\Cache::getCache("access_token");
			if(!empty($access_token))return $access_token;
		}
		//
		$appid = config("app.weixin_xiaochengxu_appid");
		$appsecret = config("app.weixin_xiaochengxu_appsecret");
		$url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
		$res = curl($url);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['access_token']) && isset($data['expires_in'])){
			\core\Cache::setCache("access_token",$data['access_token'],$data['expires_in'] - 10);
			return $data['access_token'];
		}else{
			return false;
		}
	}
	
	//小程序登录
	static public function getOpenid($code,$debug=false){
		$appid = config("app.weixin_xiaochengxu_appid");
		$appsecret = config("app.weixin_xiaochengxu_appsecret");
		$url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appid}&secret={$appsecret}&js_code={$code}&grant_type=authorization_code";
		$res = curl($url);
		//dump($res);exit;
		$data = json_decode($res['body'],true);
		if(isset($data['openid']) && isset($data['unionid'])){//绑定小程序到开放平台后才有unionid
			return $data;
		}else{
			if($debug === false)return false;
			return $data;
		}
	}
	
	//小程序获取手机号
	static public function getPhoneNumber($code,$debug=false){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return false;
			}
		}
		$url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={$access_token}";
		$params = ["code"=>$code];
		$res = curl($url,$params,true);
		$data = json_decode($res['body'],true);
		if(isset($data['errcode']) && $data['errcode'] == 0 && isset($data['phone_info']) && isset($data['phone_info']['purePhoneNumber'])){
			return $data['phone_info']['purePhoneNumber'];
		}else{
			if($debug === false)return false;
			return $data;
		}
	}

	//获取小程序码
	static public function get_xiaochengxuma($scene="",$page="pages/index/index",$width=280,$debug=false){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return false;
			}
		}
		$url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$access_token}";
		$params = [
			"scene"=>$scene,//参数值
			"page"=>$page,//页面
			"width"=>$width,//宽度
		];
		$res = curl($url,$params,true);
		//dump($res);
		if(stripos($res['header'],"Content-Type") !== false && stripos($res['header'],"image") !== false){
			return $res['body'];
		}
		if($debug === false)return false;
		return $res['body'];
	}
	
	//获取小程序URL LINK(可以理解为深度链接)
	static public function get_url_link($path="",$query=""){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return false;
			}
		}
		$url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={$access_token}";
		$params = [
			"path"=>$path,
			"query"=>$query,
		];
		$res = curl($url,$params,true);
		//dump($res);
		$data = json_decode($res['body'],true);
		if(isset($data['errcode']) && $data['errcode'] == 0 && isset($data['url_link'])){
			return $data['url_link'];
		}
		return false;
	}
	
	//获取调起支付签名
	static public function getPaySign($prepay_id){
		$appid = config("app.weixin_xiaochengxu_appid");
		$timeStamp = time();
		$nonceStr = makeCode(32,true);
		$package = "prepay_id={$prepay_id}";
		
		$message = $appid."\n".$timeStamp."\n".$nonceStr."\n".$package."\n";	
		
		openssl_sign($message, $raw_sign, file_get_contents(config("app.weixin_mchid_api_cert_pem")), 'sha256WithRSAEncryption');
		$paySign = base64_encode($raw_sign);
		$signType = "RSA";
		
		$data = [
			"timeStamp"=>$timeStamp,
			"nonceStr"=>$nonceStr,
			"package"=>$package,
			"signType"=>$signType,
			"paySign"=>$paySign,
		];
		return $data;
	}
	
	//服务端请求支付生成签名
	static private function getSign($url,$body,$http_method = "POST",$timestamp = 0,$nonce = ""){
		$url_parts = parse_url($url);
		$canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?{$url_parts['query']}" : ""));
		if(empty($timestamp)){
			$timestamp = time();
		}
		if(empty($nonce)){
			$nonce = makeCode(32,true);
		}
		$message = $http_method."\n".$canonical_url."\n".$timestamp."\n".$nonce."\n".$body."\n";
		openssl_sign($message, $raw_sign, file_get_contents(config("app.weixin_mchid_api_cert_pem")), 'sha256WithRSAEncryption');
		$sign = base64_encode($raw_sign);
		$schema = 'WECHATPAY2-SHA256-RSA2048';
		$token = sprintf('mchid="%s",serial_no="%s",nonce_str="%s",timestamp="%d",signature="%s"',config("app.weixin_mchid"),config("app.weixin_mchid_api_cert_serial"),  $nonce, $timestamp, $sign);
		return "Authorization: " . $schema . " " . $token;
	}
	
	//微信支付H5下单 生成支付url V3 //微信外部网站
	static public function h5($money_fen,$out_trade_no,$description="商品描述",$attach="自定义数据"){
		$url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/h5';
		$data = [
			"appid"=>config("app.weixin_gongzhonghao_appid"),
			"mchid"=>config("app.weixin_mchid"),
			"description"=>$description,//商品描述
			"out_trade_no"=>$out_trade_no,//商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一示例值：1217752501201407033233368018
			"notify_url"=>HTTP_HOST . "/pay/weixinpay_notify",//异步接收微信支付结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数。 公网域名必须为https，如果是走专线接入，使用专线NAT IP或者私有回调域名可使用http示例值：https://www.weixin.qq.com/wxpay/pay.php
			"attach"=>$attach,//自定义数据
			"amount"=>[
				"total"=>$money_fen,//订单总金额，单位为分。示例值：100
				"currency"=>"CNY"
			],
			"scene_info"=>[
				"payer_client_ip"=>IP,//用户的客户端IP，支持IPv4和IPv6两种格式的IP地址
				"h5_info"=>["type"=>"Wap"]
			]
		];
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,json_encode($data)),
		];
		$res = curl($url,$data,true,$header);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"微信h5下单请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['h5_url'])){
			return ["status"=>"ok","url"=>$data['h5_url']];
		}
		$msg = isset($data['code']) ? $data['code'] : "";
		$msg .= isset($data['message']) ? "【".$data['message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//微信支付JSAPI下单 生成预支付参数 V3 //公众号|小程序
	static public function jsapi($money_fen,$openid,$out_trade_no,$description="商品描述",$attach="自定义数据"){
		$url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi';
		$data = [
			"appid"=>config("app.weixin_xiaochengxu_appid"),
			"mchid"=>config("app.weixin_mchid"),
			"description"=>$description,//商品描述
			"out_trade_no"=>$out_trade_no,//商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一示例值：1217752501201407033233368018
			"notify_url"=>HTTP_HOST . "/pay/weixinpay_notify",//异步接收微信支付结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数。 公网域名必须为https，如果是走专线接入，使用专线NAT IP或者私有回调域名可使用http示例值：https://www.weixin.qq.com/wxpay/pay.php
			"attach"=>$attach,//自定义数据
			"amount"=>[
				"total"=>$money_fen,//订单总金额，单位为分。示例值：100
				"currency"=>"CNY"
			],
			"payer"=>[
				"openid"=>$openid,//用户在直连商户appid下的唯一标识。 下单前需获取到用户的Openid
			]
		];
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,json_encode($data)),
		];
		$res = curl($url,$data,true,$header);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"微信jsapi下单请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['prepay_id'])){
			return ["status"=>"ok","prepay_id"=>$data['prepay_id']];
		}
		$msg = isset($data['code']) ? $data['code'] : "";
		$msg .= isset($data['message']) ? "【".$data['message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}

	//微信支付App下单 生成预支付参数 V3 //App
	static public function app($money_fen,$out_trade_no,$description="商品描述",$attach="自定义数据"){
		$url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/app';
		$data = [
			"appid"=>config("app.weixin_xiaochengxu_appid"),//APPID的应用属性，直连模式下该id应为APP应用的id
			"mchid"=>config("app.weixin_mchid"),
			"description"=>$description,//商品描述
			"out_trade_no"=>$out_trade_no,//商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一示例值：1217752501201407033233368018
			"notify_url"=>HTTP_HOST . "/pay/weixinpay_notify",//异步接收微信支付结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数。 公网域名必须为https，如果是走专线接入，使用专线NAT IP或者私有回调域名可使用http示例值：https://www.weixin.qq.com/wxpay/pay.php
			"attach"=>$attach,//自定义数据
			"amount"=>[
				"total"=>$money_fen,//订单总金额，单位为分。示例值：100
				"currency"=>"CNY"
			],
		];
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,json_encode($data)),
		];
		$res = curl($url,$data,true,$header);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"微信app下单请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['prepay_id'])){
			return ["status"=>"ok","prepay_id"=>$data['prepay_id']];
		}
		$msg = isset($data['code']) ? $data['code'] : "";
		$msg .= isset($data['message']) ? "【".$data['message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//微信支付查询订单 V3 商户订单号
	static public function get_transactions($order_id){
		$mchid = config("app.weixin_mchid");
		$url = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{$order_id}?mchid={$mchid}";
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,"","GET"),
		];
		$res = curl($url,null,false,$header);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"微信查询请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(isset($data['trade_state']) && $data['trade_state'] == "SUCCESS"){
			$data = [
				"trade_state"=>$data['trade_state'],
				"success_time"=>$data['success_time'],
				"trade_state_desc"=>$data['trade_state_desc'],
				"transaction_id"=>$data['transaction_id'],
				"trade_type"=>$data['trade_type'],
				"payer_total"=>$data['amount']['payer_total'],
				"total"=>$data['amount']['total'],
			];
			return ["status"=>"ok","data"=>$data];
		}
		return false;
	}

	//微信支付申请退款 V3
	static public function tuikuan($money_fen,$total,$out_trade_no,$reason="协商退款"){
		$url = 'https://api.mch.weixin.qq.com/v3/refund/domestic/refunds';
		$data = [
			"out_trade_no"=>$out_trade_no,//商户订单号
			"out_refund_no"=>$out_trade_no,//商户退款单号
			"reason"=>$reason,//
			"notify_url"=>"https://api.huodongbaobao.com/pay/weixinpay_tuikuan_notify",//退款结果回调url
			"amount"=>[
				"refund"=>$money_fen,//退款金额，单位为分。示例值：100
				"total"=>$total,//原支付交易的订单总金额，单位为分。示例值：100
				"currency"=>"CNY"
			],
		];
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,json_encode($data)),
		];
		$res = curl($url,$data,true,$header);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"微信申请退款请求失败"];
		}
		$data = json_decode($res['body'],true);
		if(
			isset($data['refund_id']) &&
			isset($data['status']) &&
			!empty($data['refund_id']) &&
			!empty($data['status']) 
		){
			$res_status = $data['status'];//$data['status']枚举值：SUCCESS：退款成功 CLOSED：退款关闭 PROCESSING：退款处理中 ABNORMAL：退款异常
			if($res_status == "SUCCESS"){
				return ["status"=>"ok","data"=>"success"];
			}else if($res_status == "PROCESSING"){
				return ["status"=>"ok","data"=>"processing"];
			}else{
				return ["status"=>"error","msg"=>"退款异常"];
			}
		}
		$msg = isset($data['code']) ? $data['code'] : "";
		$msg .= isset($data['message']) ? "【".$data['message']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//微信支付回调签名验证和数据解密 V3
	static public function get_notify($res){
		$inWechatpaySignature = $_SERVER['HTTP_WECHATPAY_SIGNATURE'];// 请根据实际情况获取
		$inWechatpayTimestamp = $_SERVER['HTTP_WECHATPAY_TIMESTAMP'];// 请根据实际情况获取
		$inWechatpaySerial = $_SERVER['HTTP_WECHATPAY_SERIAL'];// 请根据实际情况获取
		$inWechatpayNonce = $_SERVER['HTTP_WECHATPAY_NONCE'];// 请根据实际情况获取
		$inBody = $res;// 请根据实际情况获取，例如: file_get_contents('php://input');

		$apiv3Key = config("app.weixin_mchid_v3key");// 在商户平台上设置的APIv3密钥
		
		// 根据通知的平台证书序列号，查询本地平台证书文件，
		// 假定为 `/path/to/wechatpay/inWechatpaySerial.pem`
		$platformPublicKeyInstance = openssl_pkey_get_public("file://" . config("app.weixin_mchid_pingtai_public_pem"));

		// 检查通知时间偏移量，允许5分钟之内的偏移
		if(abs(time() - (int)$inWechatpayTimestamp) > 300){
			return false;
		}
		//验证签名
		$joinedByLineFeed = implode("\n", array_merge([$inWechatpayTimestamp], [$inWechatpayNonce],[$inBody],['']));
		$verifiedStatus = openssl_verify($joinedByLineFeed, base64_decode($inWechatpaySignature), $platformPublicKeyInstance, OPENSSL_ALGO_SHA256);
		if($verifiedStatus === false || $verifiedStatus !== 1){
			return false;
		}
		//解密数据
		$inBodyArray = json_decode($inBody, true);
		$ciphertext = $inBodyArray['resource']['ciphertext'];
		$nonce = $inBodyArray['resource']['nonce'];
		$aad = $inBodyArray['resource']['associated_data'];
		$ciphertext = base64_decode($ciphertext);
		$tailLength = -16;
		$authTag = substr($ciphertext, $tailLength);
		$inBodyResource = openssl_decrypt(substr($ciphertext, 0, $tailLength), 'aes-256-gcm', $apiv3Key, OPENSSL_RAW_DATA, $nonce, $authTag, $aad);
		if(empty($inBodyResource)){
			return false;
		}
		$inBodyResourceArray = json_decode($inBodyResource, true);
		//
		return $inBodyResourceArray;
	}
	
	//获取微信平台证书列表
	static public function get_weixin_pingtai_cert(){
		$url = "https://api.mch.weixin.qq.com/v3/certificates";
		$header = [
			"Accept: application/json",
			"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			self::getSign($url,"","GET"),
		];
		$res = curl($url,null,false,$header);//dump($res);
		if(!isset($res['body'])){
			return ["status"=>"error","msg"=>"请求失败"];
		}
		$data = json_decode($res['body'],true);
		//
		if(!isset($data['data'])){
			$msg = isset($data['code']) ? $data['code'] : "";
			$msg .= isset($data['message']) ? "【".$data['message']."】" : "";
			return ["status"=>"error","msg"=>$msg];
		}
		
		$apiv3Key = config("app.weixin_mchid_v3key");// 在商户平台上设置的APIv3密钥
		//验签 第一次跳过验签
		$weixin_mchid_pingtai_public_pem = config("app.weixin_mchid_pingtai_public_pem");
		if(file_exists($weixin_mchid_pingtai_public_pem)){
			//
			$header = [];
			$lines = explode("\r\n", $res['header']);
			foreach ($lines as $line) {
				if(strstr($line,":") === false){
					continue;
				}
				list($key, $value) = explode(": ", $line);
				$header[$key] = $value;
			}
			//dump($header);			
			//
			$inWechatpaySignature = $header['Wechatpay-Signature'];// 请根据实际情况获取
			$inWechatpayTimestamp = $header['Wechatpay-Timestamp'];// 请根据实际情况获取
			$inWechatpaySerial = $header['Wechatpay-Serial'];// 请根据实际情况获取
			$inWechatpayNonce = $header['Wechatpay-Nonce'];// 请根据实际情况获取
			$inBody = $res['body'];// 请根据实际情况获取，例如: file_get_contents('php://input');
			// 根据通知的平台证书序列号，查询本地平台证书文件，
			// 假定为 `/path/to/wechatpay/inWechatpaySerial.pem`
			$platformPublicKeyInstance = openssl_pkey_get_public("file://" . config("app.weixin_mchid_pingtai_public_pem"));

			// 检查通知时间偏移量，允许5分钟之内的偏移
			if(abs(time() - (int)$inWechatpayTimestamp) > 300){
				return ["status"=>"error","msg"=>"时间偏移量超过五分钟"];
			}
			//验证签名
			$joinedByLineFeed = implode("\n", array_merge([$inWechatpayTimestamp], [$inWechatpayNonce],[$inBody],['']));
			$verifiedStatus = openssl_verify($joinedByLineFeed, base64_decode($inWechatpaySignature), $platformPublicKeyInstance, OPENSSL_ALGO_SHA256);
			if($verifiedStatus === false || $verifiedStatus !== 1){
				return ["status"=>"error","msg"=>"验签失败"];
			}
		}
		//解密数据
		$result = [];
		foreach($data['data'] as $row){
			if(strtotime($row['expire_time']) <= time()){
				continue;
			}
			$ciphertext = $row['encrypt_certificate']['ciphertext'];
			$nonce = $row['encrypt_certificate']['nonce'];
			$aad = $row['encrypt_certificate']['associated_data'];
			$ciphertext = base64_decode($ciphertext);
			$tailLength = -16;
			$authTag = substr($ciphertext, $tailLength);
			$dataResource = openssl_decrypt(substr($ciphertext, 0, $tailLength), 'aes-256-gcm', $apiv3Key, OPENSSL_RAW_DATA, $nonce, $authTag, $aad);
			if(empty($dataResource)){
				return false;
			}
			//
			$result[] = [
				'serial_no' => $row['serial_no'],
				'cert' => $dataResource,
				'expire_time' => date("Y-m-d H:i:s",strtotime($row['expire_time'])),
			];
		}
		if(!file_exists($weixin_mchid_pingtai_public_pem)){
			file_put_contents($weixin_mchid_pingtai_public_pem,$result[0]['cert']);
		}
		return $result;
		//
	}
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
