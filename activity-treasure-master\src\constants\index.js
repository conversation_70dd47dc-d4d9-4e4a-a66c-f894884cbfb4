/**
 * 应用常量定义
 * 统一管理应用中的魔法数字和常量值
 */

// 时间相关常量
export const TIME_CONSTANTS = {
  // 毫秒
  ONE_SECOND: 1000,
  ONE_MINUTE: 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
  ONE_WEEK: 7 * 24 * 60 * 60 * 1000,
  
  // 缓存过期时间
  CACHE_EXPIRE_SHORT: 2 * 60 * 1000, // 2分钟
  CACHE_EXPIRE_MEDIUM: 5 * 60 * 1000, // 5分钟
  CACHE_EXPIRE_LONG: 30 * 60 * 1000, // 30分钟
  
  // 草稿保存相关
  DRAFT_EXPIRE_TIME: 7 * 24 * 60 * 60 * 1000, // 7天
  AUTO_SAVE_DELAY: 2000, // 2秒防抖
  
  // 倒计时
  SPLASH_COUNTDOWN: 3000, // 3秒
  TOAST_DURATION: 2000 // 2秒
};

// 尺寸相关常量
export const SIZE_CONSTANTS = {
  // 图片尺寸
  MAX_IMAGE_SIZE: 1200, // 最大图片尺寸
  THUMBNAIL_SIZE: 200, // 缩略图尺寸
  AVATAR_SIZE: 100, // 头像尺寸
  
  // 文件大小限制（字节）
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_IMAGE_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_VIDEO_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  
  // 列表分页
  PAGE_SIZE: 20, // 默认分页大小
  MAX_PAGE_SIZE: 100, // 最大分页大小
  
  // 输入限制
  MAX_TITLE_LENGTH: 50, // 标题最大长度
  MAX_CONTENT_LENGTH: 5000, // 内容最大长度
  MAX_COMMENT_LENGTH: 500, // 评论最大长度
  MIN_PASSWORD_LENGTH: 6, // 密码最小长度
  MAX_PASSWORD_LENGTH: 20 // 密码最大长度
};

// 数值相关常量
export const NUMBER_CONSTANTS = {
  // 活动相关
  MAX_PARTICIPANTS: 10000, // 最大参与人数
  MIN_PARTICIPANTS: 1, // 最小参与人数
  MAX_ACTIVITY_PRICE: 99999, // 最大活动价格
  MIN_ACTIVITY_PRICE: 0, // 最小活动价格
  
  // 评分相关
  MIN_RATING: 1, // 最小评分
  MAX_RATING: 5, // 最大评分
  
  // 重试次数
  MAX_RETRY_COUNT: 3, // 最大重试次数
  
  // 压缩质量
  IMAGE_COMPRESSION_QUALITY: 0.8, // 图片压缩质量
  
  // 性能阈值
  PERFORMANCE_WARNING_THRESHOLD: 3000, // 性能警告阈值（毫秒）
  API_TIMEOUT: 10000 // API超时时间（毫秒）
};

// 状态码常量
export const STATUS_CODES = {
  SUCCESS: 'ok',
  ERROR: 'error',
  LOADING: 'loading',
  IDLE: 'idle',
  
  // HTTP状态码
  HTTP_OK: 200,
  HTTP_CREATED: 201,
  HTTP_BAD_REQUEST: 400,
  HTTP_UNAUTHORIZED: 401,
  HTTP_FORBIDDEN: 403,
  HTTP_NOT_FOUND: 404,
  HTTP_INTERNAL_ERROR: 500
};

// 用户角色常量
export const USER_ROLES = {
  ADMIN: 0, // 管理员
  BRANCH_MANAGER: 1, // 分店管理员
  MEMBER: 2, // 会员
  REGULAR_USER: 3 // 普通用户
};

// 活动类型常量
export const ACTIVITY_TYPES = {
  OFFLINE: 0, // 线下活动
  ONLINE: 1, // 线上活动
  HYBRID: 2 // 混合活动
};

// 支付类型常量
export const PAYMENT_TYPES = {
  FREE: 0, // 免费
  ONLINE_PAY: 1, // 线上支付
  OFFLINE_PAY: 2 // 线下支付
};

// 文件类型常量
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt'],
  AUDIO: ['mp3', 'wav', 'aac']
};

// 正则表达式常量
export const REGEX_PATTERNS = {
  PHONE: /^1[3-9]\d{9}$/, // 手机号
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // 邮箱
  URL: /^https?:\/\/.+/, // URL
  CHINESE: /[\u4e00-\u9fa5]/, // 中文字符
  NUMBER: /^\d+$/, // 纯数字
  DECIMAL: /^\d+(\.\d+)?$/, // 小数
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/ // 密码（字母+数字）
};

// 存储键名常量
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  TOKEN: 'token',
  WORLD_STATE: 'worldState',
  ACTIVITY_STATE: 'activityState',
  OFFLINE_DATA: 'offlineData',
  ERROR_LOG: 'errorLog',
  PERFORMANCE_LOG: 'performanceLog',
  DRAFT_PREFIX: 'draft_',
  CACHE_PREFIX: 'cache_'
};

// 事件名称常量
export const EVENT_NAMES = {
  STATE_CHANGE: 'stateChange',
  USER_LOGIN: 'userLogin',
  USER_LOGOUT: 'userLogout',
  NETWORK_CHANGE: 'networkChange',
  PAGE_LOAD: 'pageLoad',
  ERROR_OCCURRED: 'errorOccurred'
};

// 动画相关常量
export const ANIMATION_CONSTANTS = {
  DURATION_FAST: 200, // 快速动画
  DURATION_NORMAL: 300, // 正常动画
  DURATION_SLOW: 500, // 慢速动画
  
  EASING_EASE: 'ease',
  EASING_EASE_IN: 'ease-in',
  EASING_EASE_OUT: 'ease-out',
  EASING_EASE_IN_OUT: 'ease-in-out'
};

// 颜色常量
export const COLOR_CONSTANTS = {
  PRIMARY: '#6AC086', // 主色调
  SUCCESS: '#52c41a', // 成功色
  WARNING: '#faad14', // 警告色
  ERROR: '#ff4d4f', // 错误色
  INFO: '#1890ff', // 信息色
  
  TEXT_PRIMARY: '#333333', // 主要文字
  TEXT_SECONDARY: '#666666', // 次要文字
  TEXT_DISABLED: '#999999', // 禁用文字
  
  BACKGROUND: '#f8f9fa', // 背景色
  SURFACE: '#ffffff', // 表面色
  BORDER: '#dee2e6' // 边框色
};

// 默认配置常量
export const DEFAULT_CONFIG = {
  APP_NAME: 'MindfulMeetUp',
  APP_SLOGAN: '小聚会，大精彩',
  DEFAULT_AVATAR: '/static/images/default-avatar.png',
  DEFAULT_COVER: '/static/images/default-cover.png',
  EMPTY_IMAGE: '/static/images/empty.png'
};

// 环境相关常量
export const ENV_CONSTANTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

// 平台相关常量
export const PLATFORM_CONSTANTS = {
  MP_WEIXIN: 'mp-weixin', // 微信小程序
  H5: 'h5', // H5
  APP: 'app' // APP
};

// 导出所有常量的集合
export const CONSTANTS = {
  TIME: TIME_CONSTANTS,
  SIZE: SIZE_CONSTANTS,
  NUMBER: NUMBER_CONSTANTS,
  STATUS: STATUS_CODES,
  USER_ROLES,
  ACTIVITY_TYPES,
  PAYMENT_TYPES,
  FILE_TYPES,
  REGEX: REGEX_PATTERNS,
  STORAGE: STORAGE_KEYS,
  EVENTS: EVENT_NAMES,
  ANIMATION: ANIMATION_CONSTANTS,
  COLORS: COLOR_CONSTANTS,
  DEFAULT: DEFAULT_CONFIG,
  ENV: ENV_CONSTANTS,
  PLATFORM: PLATFORM_CONSTANTS
};
