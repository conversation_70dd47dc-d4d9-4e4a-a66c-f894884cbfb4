<script setup>
import { watch, ref, reactive } from "vue";
import {
  htmlindex,
  useradd_huiyuan_order,
  userfenxiang_event,
  payyue_pay,
  payweixin_pay,
  payget_weixinpay_sign,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import iptInfo from "@/components/iptInfo.vue";
import {store} from "@/store";
import {pay, back, getUserInfo} from "@/utils";

const membershipBenefits = ref(''); // 会员权益 HTML
const agreementChecked = ref(false); // 协议复选框状态
const memberExpireTime = ref(''); // 会员到期时间

const show = ref(false);
const popupShow = ref(false);
const ipt = ref(true);
const actionsList = ref([
  /*{
    name: "余额支付",
    color: "#ffaa7f",
    fontSize: "18",
  },*/
  {
    name: "微信支付",
    color: "#ffaa7f",
    fontSize: "18",
  },
]);
const payQuery = ref({
  type: 3,
  order_id: null,
  money: store().$state.config.config?.huiyuan_price?.val,
});

onShareTimeline(async () => {
  const res = await userfenxiang_event({
    type: 1,
    item_id: store().$state.userInfo?.uid,
  });
  return {
    title: store().$state.config.config?.app_name?.val,
    imageUrl: store().$state.config.img_config?.app_logo?.val,
    path: `/pages/bundle/user/vip?pid=${store().$state.userInfo?.uid}`,
  };
});
onShareAppMessage(async () => {
  const res = await userfenxiang_event({
    type: 1,
    item_id: store().$state.userInfo?.uid,
  });
  return {
    title: store().$state.config.config?.app_name?.val,
    imageUrl: store().$state.config.img_config?.app_logo?.val,
    path: `/pages/bundle/user/vip?pid=${store().$state.userInfo?.uid}`,
  };
});

onLoad(async (e) => {
  if (e?.pid) store().changePid(e.pid);
  try {
    // 获取会员权益
    const benefitsRes = await htmlindex({ type: 7 });
    membershipBenefits.value = benefitsRes.status === 'ok' ? benefitsRes.data.contents : '<p>会员权益加载失败，请稍后重试。</p>';

    // 获取会员到期时间
    if (store().$state.userInfo?.is_huiyuan && store().$state.userInfo?.huiyuan_end_time) {
      memberExpireTime.value = store().$state.userInfo.huiyuan_end_time;
    }
  } catch (error) {
    console.error('加载页面数据失败:', error);
    uni.$u.toast('加载信息失败，请稍后重试');
    membershipBenefits.value = '<p>会员权益加载失败，请稍后重试。</p>';
  }
});

const handlePayClick = async () => {
  if (!agreementChecked.value) {
    uni.showToast({
      title: '请先阅读并同意会员服务协议',
      icon: 'none'
    });
    return;
  }

  if (store().$state.userInfo.is_huiyuan) {
    return;
  }

  // 显示加载提示
  uni.showLoading({
    title: '处理中...',
    mask: true
  });

  try {
    console.log('开始创建会员订单...');
    // 创建会员订单
    const vipRes = await useradd_huiyuan_order();
    console.log('会员订单创建结果:', vipRes);

    if (vipRes.status === "ok") {
      // 更新订单信息
      payQuery.value.order_id = vipRes.order_id;
      payQuery.value.money = vipRes.money;
      payQuery.value.type = 3;

      console.log('准备调用微信支付，参数:', JSON.stringify(payQuery.value));

      // 直接调用微信支付
      const wxRes = await payweixin_pay(payQuery.value);
      console.log('微信支付下单结果:', wxRes);

      if (wxRes.status === "ok") {
        console.log('获取到prepay_id:', wxRes.prepay_id);

        try {
          // 获取支付签名
          const signRes = await payget_weixinpay_sign({ prepay_id: wxRes.prepay_id });
          console.log('获取支付签名结果:', signRes);

          const payRes = await pay(signRes);
          if (payRes.errMsg === "requestPayment:ok") {
            // 支付成功
            uni.$u.toast('支付成功！');

            // 刷新用户信息
            await getUserInfo();

            // 延迟跳转回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            // 支付失败
            uni.$u.toast('支付失败，请稍后重试');
          }

          // 关闭加载提示
          uni.hideLoading();

        } catch (signError) {
          uni.hideLoading();
          console.error('获取支付签名异常:', signError);
          uni.$u.toast('获取支付参数失败，请稍后重试');
        }
      } else {
        uni.hideLoading();
        console.error('创建微信支付订单失败:', wxRes);
        uni.$u.toast(wxRes.msg || '创建微信支付订单失败');
      }
    } else {
      uni.hideLoading();
      console.error('创建会员订单失败:', vipRes);
      uni.$u.toast(vipRes.msg || '创建会员订单失败');
    }
  } catch(error) {
    uni.hideLoading();
    console.error('支付处理异常:', error);
    uni.$u.toast('支付处理异常，请稍后重试');
  }
};
// 保留selectClick函数但不再使用，以防其他地方有引用
const selectClick = async () => {
  // 直接调用handlePayClick
  handlePayClick();
};

const openAgreement = () => {
  uni.navigateTo({
    url: '/pages/bundle/user/membershipAgreement'
  });
};

const viewFullIntro = () => {
  uni.showToast({ title: '功能待实现', icon: 'none' });
};
</script>
<template>
  <view class="page">
    <myTitle
        height="178"
        title="我的会员"
        bgColor="rgba(255, 255, 255, 0.95)"
        color="#333"
        backColor="#333"
    ></myTitle>

    <view class="page-content">

      <!-- 已开通会员显示 -->
      <view v-if="store().$state.userInfo?.is_huiyuan" class="vip-card member-active">
        <view class="card-header">
          <u-icon name="checkmark-circle-fill" color="#ffffff" size="40rpx"></u-icon>
          <text class="card-title">会员已开通</text>
        </view>
        <view class="member-info">
          <text class="member-status">尊享会员权益</text>
          <text class="expire-time" v-if="memberExpireTime">到期时间：{{ memberExpireTime }}</text>
        </view>
        <image class="paw-print" src="/static/paw-print.png" mode="aspectFit"></image>
      </view>

      <!-- 未开通会员显示 -->
      <view v-else class="vip-card">
        <view class="card-header">
          <text class="card-title">六个月会员卡</text>
        </view>
        <view class="card-price">
          <text class="price-currency">￥</text>
          <text class="price-amount">{{ store().$state.config.config?.huiyuan_price?.val || 'N/A' }}</text>
          <text class="price-unit">/六个月</text>
        </view>
         <image class="paw-print" src="/static/paw-print.png" mode="aspectFit"></image>
      </view>

      <view class="action-section">
        <view class="agreement-line">
          <u-checkbox-group v-model="agreementChecked">
            <u-checkbox name="agree" shape="circle" :checked="agreementChecked" @change="agreementChecked = !agreementChecked"></u-checkbox>
          </u-checkbox-group>
          <view class="agreement-text">
             我已阅读并同意
             <text class="link" @click="openAgreement">《会员服务协议》</text>
          </view>
        </view>

        <u-button
          class="open-button"
          shape="circle"
          :text="store().$state.userInfo.is_huiyuan ? '您已是会员' : '立即开通'"
          :disabled="store().$state.userInfo.is_huiyuan"
          @click="handlePayClick"
        ></u-button>
      </view>

      <view class="benefits-section">
        <view class="section-title">
          <view class="title-icon"></view>
          <text>会员权益</text>
        </view>
        <view class="benefits-content">
           <u-parse :content="membershipBenefits"></u-parse>
        </view>
      </view>

    </view>



    <!-- 支付方式选择弹窗已移除，直接使用微信支付 -->

    <iptInfo v-model:popupShow="popupShow" @submit="submit" :ipt="ipt"></iptInfo>

  </view>
</template>

<style scoped lang="less">
/* 导航栏样式优化 */
:deep(.title) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 999 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

:deep(.title .pr) {
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

/* 确保导航栏文字可见 */
:deep(.title .x32) {
  color: #333 !important;
  font-weight: 600 !important;
}

/* 确保返回按钮可见 */
:deep(.title .u-icon) {
  color: #333 !important;
}

.page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
  display: flex;
  flex-direction: column;
  padding-bottom: 180rpx;
  box-sizing: border-box;
  position: relative;
}

.page-content {
  width: 690rpx;
  margin: 0 auto;
  padding: 0 30rpx;
  padding-top: 200rpx; /* 使用padding-top替代margin-top，确保导航栏不被遮挡 */
  z-index: 1;
  flex-grow: 1;
  position: relative;
}

.vip-card {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(106, 192, 134, 0.3);
  position: relative;
  overflow: hidden;
  color: #fff;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-left: 12rpx;
    }
  }

  .card-price {
    display: flex;
    align-items: baseline;
    .price-currency {
      font-size: 30rpx;
      margin-right: 4rpx;
    }
    .price-amount {
      font-size: 60rpx;
      font-weight: bold;
      margin-right: 8rpx;
    }
    .price-unit {
      font-size: 28rpx;
    }
  }

  .member-info {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    .member-status {
      font-size: 28rpx;
      font-weight: 600;
    }
    .expire-time {
      font-size: 24rpx;
      opacity: 0.9;
    }
  }

  .paw-print {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    width: 120rpx;
    height: 120rpx;
    opacity: 0.2;
  }
}

.member-active {
  background: linear-gradient(135deg, #D19C69 0%, #E6B87D 100%) !important;
  box-shadow: 0 8rpx 20rpx rgba(209, 156, 105, 0.3) !important;
}

.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;

  .agreement-line {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    width: 100%;

    .agreement-text {
      font-size: 24rpx;
      color: #666;
      margin-left: 10rpx;

      .link {
        color: #6AC086;
        text-decoration: underline;
      }
    }
  }

  .open-button {
    width: 600rpx !important;
    height: 88rpx !important;
    background: linear-gradient(90deg, #88D7A0 0%, #6AC086 100%) !important;
    color: #fff !important;
    font-size: 30rpx !important;
    font-weight: bold;
    border-radius: 44rpx !important;
    box-shadow: 0 6rpx 15rpx rgba(106, 192, 134, 0.4);
    border: none !important;
  }
  .open-button[disabled] {
     background: #ccc !important;
     box-shadow: none !important;
     color: #999 !important;
  }
}

.benefits-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);

  .section-title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 25rpx;

    .title-icon {
      width: 8rpx;
      height: 28rpx;
      background-color: #6AC086;
      border-radius: 4rpx;
      margin-right: 15rpx;
    }
  }

  .benefits-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.7;

    /* 替换ul/ol标签选择器为class选择器 */
    :deep(.list-style), :deep(.ordered-list-style) {
        padding-left: 20px;
    }
    :deep(.list-item-style) {
        margin-bottom: 8rpx;
     }
  }
}

.bottom-intro-button {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 690rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #888;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  z-index: 10;
}

.popup-content {
    padding: 30rpx;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    display: flex;
    flex-direction: column;
    max-height: 80vh;

    .popup-header {
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30rpx;
        color: #333;
    }

    .popup-body {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 30rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
    }

    .popup-close-btn {
        width: 100% !important;
        height: 80rpx !important;
        background-color: #6AC086 !important;
        color: #fff !important;
        border: none !important;
        border-radius: 40rpx !important;
        font-size: 30rpx !important;
    }
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 750rpx) {
  .page-content {
    width: 100%;
    padding: 180rpx 20rpx 0 20rpx;
  }

  :deep(.title) {
    height: 160rpx !important;
  }
}

/* 确保在微信小程序中的兼容性 */
@media screen and (max-width: 375px) {
  .page-content {
    padding-top: 160rpx;
  }
}

</style>