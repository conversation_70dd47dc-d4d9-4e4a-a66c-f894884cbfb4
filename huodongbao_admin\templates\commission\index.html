<fieldset class="table-search-fieldset">
    <legend>佣金管理中心</legend>
    <div style="margin: 20px;">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #1E9FFF; color: white;">
                        <i class="layui-icon layui-icon-rmb"></i> 本月运营佣金
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #1E9FFF;">
                            ¥<?php echo number_format($stats['month_operation_commission'], 2); ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            <?php echo date('Y年m月'); ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #5FB878; color: white;">
                        <i class="layui-icon layui-icon-chart"></i> 累计运营佣金
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #5FB878;">
                            ¥<?php echo number_format($stats['total_operation_commission'], 2); ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            历史总计
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #9C27B0; color: white;">
                        <i class="layui-icon layui-icon-diamond"></i> 本月销售佣金
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #9C27B0;">
                            ¥<?php echo number_format($stats['month_sales_commission'], 2); ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            <?php echo date('Y年m月'); ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #FF9800; color: white;">
                        <i class="layui-icon layui-icon-star-fill"></i> 累计销售佣金
                    </div>
                    <div class="layui-card-body" style="text-align: center; padding: 30px;">
                        <div style="font-size: 28px; font-weight: bold; color: #FF9800;">
                            ¥<?php echo number_format($stats['total_sales_commission'], 2); ?>
                        </div>
                        <div style="color: #999; margin-top: 10px;">
                            历史总计
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🆕 新增：活动收入统计 -->
        <div class="layui-row layui-col-space15" style="margin-top: 30px;">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header" style="background: #FF5722; color: white;">
                        <i class="layui-icon layui-icon-star-fill"></i> 活动收入统计
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #FF5722;">
                                        ¥<?php echo number_format($activity_income_stats['month']['total_income'] ?? 0, 2); ?>
                                    </div>
                                    <div style="color: #999; margin-top: 5px;">本月活动收入</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #FF9800;">
                                        ¥<?php echo number_format($activity_income_stats['total']['total_income'] ?? 0, 2); ?>
                                    </div>
                                    <div style="color: #999; margin-top: 5px;">累计活动收入</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">
                                        ¥<?php echo number_format($activity_income_stats['month']['total_platform_fee'] ?? 0, 2); ?>
                                    </div>
                                    <div style="color: #999; margin-top: 5px;">本月平台抽成</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div style="text-align: center; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #2196F3;">
                                        <?php echo $activity_income_stats['month']['activity_count'] ?? 0; ?>
                                    </div>
                                    <div style="color: #999; margin-top: 5px;">本月收费活动</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="layui-row layui-col-space15" style="margin-top: 20px;">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <i class="layui-icon layui-icon-set"></i> 佣金管理
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md4">
                                <a href="/commission/calculate" class="layui-btn layui-btn-fluid layui-btn-normal">
                                    <i class="layui-icon layui-icon-calculator"></i> 计算运营佣金
                                </a>
                            </div>
                            <div class="layui-col-md4">
                                <a href="/commission/records" class="layui-btn layui-btn-fluid">
                                    <i class="layui-icon layui-icon-table"></i> 佣金记录查询
                                </a>
                            </div>
                            <div class="layui-col-md4">
                                <a href="/commission/config_management" class="layui-btn layui-btn-fluid" style="background-color: #009688;">
                                    <i class="layui-icon layui-icon-list"></i> 佣金配置管理
                                </a>
                            </div>
                        </div>

                        <!-- 🆕 新增：活动收入管理按钮 -->
                        <div class="layui-row layui-col-space10" style="margin-top: 15px;">
                            <div class="layui-col-md6">
                                <a href="/commission/activity_income_records" class="layui-btn layui-btn-warm layui-btn-fluid">
                                    <i class="layui-icon layui-icon-star-fill"></i> 活动收入记录
                                </a>
                            </div>
                            <div class="layui-col-md6">
                                <a href="/commission/activity_income_withdrawals" class="layui-btn layui-btn-danger layui-btn-fluid">
                                    <i class="layui-icon layui-icon-rmb"></i> 活动收入提现审核
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近操作记录 -->
        <div class="layui-card" style="margin-top: 20px;">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-log"></i> 最近操作记录
            </div>
            <div class="layui-card-body">
                <div style="text-align: center; padding: 40px; color: #999;">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                    <div style="margin-top: 10px;">功能开发中...</div>
                </div>
            </div>
        </div>
    </div>
</fieldset>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 可以在这里添加一些初始化逻辑
        console.log('佣金管理中心加载完成');
    });
});
</script>

<style>
.layui-card {
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    border-radius: 6px;
}

.layui-card-header {
    border-radius: 6px 6px 0 0;
    font-weight: bold;
}

.layui-btn-fluid {
    width: 100%;
    text-align: center;
}

.layui-badge {
    position: relative;
    top: -2px;
    margin-left: 5px;
}

/* 统计卡片动画效果 */
.layui-card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,.1);
}

/* 按钮悬停效果 */
.layui-btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .layui-col-md3 {
        width: 50% !important;
        margin-bottom: 15px;
    }
}

@media screen and (max-width: 480px) {
    .layui-col-md3 {
        width: 100% !important;
        margin-bottom: 15px;
    }

    .layui-card-body {
        padding: 20px !important;
    }

    .layui-card-body div[style*="font-size: 28px"] {
        font-size: 24px !important;
    }
}
</style>
