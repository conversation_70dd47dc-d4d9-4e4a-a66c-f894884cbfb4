-- ==================== 为摘录表添加图片字段 ====================
-- 数据库：huodong
-- 执行前请备份quotes表

-- 1. 为quotes表添加images_json字段
ALTER TABLE `huodong`.`quotes` 
ADD COLUMN `images_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL 
COMMENT '图片URL列表 (JSON数组)' 
CHECK (json_valid(`images_json`)) 
AFTER `content`;

-- 2. 验证字段添加结果
DESCRIBE `huodong`.`quotes`;

-- 3. 查看表结构确认
SHOW CREATE TABLE `huodong`.`quotes`;

-- 4. 测试JSON字段功能
-- 插入测试数据验证JSON字段
INSERT INTO `huodong`.`quotes` 
(`user_id`, `content`, `images_json`, `author`, `source`, `tags`, `privacy`) 
VALUES 
(215, '测试摘录内容', '["https://example.com/image1.jpg", "https://example.com/image2.jpg"]', '测试作者', '测试来源', '测试', 'public');

-- 5. 验证JSON数据查询
SELECT id, content, images_json, JSON_LENGTH(images_json) as image_count 
FROM `huodong`.`quotes` 
WHERE images_json IS NOT NULL;

-- 6. 清理测试数据（可选）
-- DELETE FROM `huodong`.`quotes` WHERE content = '测试摘录内容';
