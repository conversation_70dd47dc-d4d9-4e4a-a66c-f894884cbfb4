<?php
namespace controller;
use Swoole\Http\Request;
use Swoole\Http\Response;
use Swoole\WebSocket\CloseFrame;
use Swoole\Coroutine\Http\Server;
use function Swoole\Coroutine\run;

class Websocket extends \core\Swoole{
	
	static public function start_rchat_huodongbao(){
		if(!IS_CLI){
			echo "只支持命令行访问\n";
			exit;
		}
		//rchat
		$rchat = [
			["column"=>"fd","type"=>"int","len"=>10],
			["column"=>"nickname","type"=>"string","len"=>32],
			["column"=>"channel","type"=>"string","len"=>32],//加入的频道，逗号隔开
			["column"=>"notify","type"=>"int","len"=>10],//是否开启浏览器通知
			["column"=>"rchat_is_show","type"=>"int","len"=>10],//是否打开了rchat
			["column"=>"user_agent","type"=>"string","len"=>100],//是否打开了rchat
			["column"=>"position","type"=>"string","len"=>50],//是否打开了rchat
		];
		self::createTable("rchat",$rchat);
		
		//fd对应的登录信息
		$login_fd = [
			["column"=>"fd","type"=>"int","len"=>10],
			["column"=>"uid","type"=>"int","len"=>10],
			["column"=>"channels","type"=>"string","len"=>500],//加入的频道，逗号隔开
		];
		self::createTable("login_fd",$login_fd);
		
		//uid换fd，一对多
		$login_uid = [
			["column"=>"fds","type"=>"string","len"=>100],
		];
		self::createTable("login_uid",$login_uid);
		
		//
		run(function () {
			$server = new Server("0.0.0.0", config("sys.swoole.websocket_port"), false);
			$server_config = [
				'daemonize' => config("sys.swoole.daemonize"),
				'heartbeat_check_interval'=>60,//表示每60秒遍历一次
				'heartbeat_idle_time'=>600,//表示一个连接如果600秒内未向服务器发送任何数据，此连接将被强制关闭
				'log_file'=>parent::$base_dir . "socket.err.log",		
			];
			$ssl_key_file = config("sys.swoole.ssl_key_file");
			$ssl_cert_file = config("sys.swoole.ssl_cert_file");
			if(!empty($ssl_key_file) && !empty($ssl_cert_file)){
				$server_config['ssl_key_file'] = $ssl_key_file;
				$server_config['ssl_cert_file'] = $ssl_cert_file;
				$server_config['ssl_protocols'] = 0;
			}
			//dump($server_config);
			$server->set($server_config);
			$server->handle('/', function (Request $request, Response $ws) {
				$ws->upgrade();
				$conn_id = spl_object_id($ws);
				(parent::$links)[$conn_id] = $ws;
				self::connectd($conn_id);
				while (true) {
					$frame = $ws->recv();
					if($frame === false){
						echo date("Y-m-d H:i:s") . ': errorCode: ' . swoole_last_error() . "," . swoole_strerror(swoole_last_error(), 9)."\n";
						$ws->close();
						break;
					}else if ($frame === '' || $frame->data == 'close' || get_class($frame) === \CloseFrame::class){
						echo date("Y-m-d H:i:s") . ': errorCode: ' . swoole_last_error() . "," . swoole_strerror(swoole_last_error(), 9)."\n";
						$ws->close();
						break;
					}else{
						//echo $frame->data . "\n";
						$data = json_decode($frame->data,true);
						if(empty($data) || !is_array($data) || !isset($data['url'])){
							parent::write($ws,["type"=>"error","msg"=>"param [ url ] error"]);
							continue;
						}
						$data['conn'] = $ws;
						$data['conn_id'] = $conn_id;
						parent::route($ws,$data);
					}
				}
				self::disconnectd($conn_id);
			});
			parent::$websocketServer = $server;
			$server->start();
		});
	}
}