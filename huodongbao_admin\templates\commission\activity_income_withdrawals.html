<fieldset class="table-search-fieldset">
    <legend>活动收入提现审核</legend>
    
    <!-- 搜索表单 -->
    <form class="layui-form layui-form-pane" method="get">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部状态</option>
                        <option value="0" <?php echo ($filters['status'] ?? '') === '0' ? 'selected' : ''; ?>>待审核</option>
                        <option value="1" <?php echo ($filters['status'] ?? '') === '1' ? 'selected' : ''; ?>>已批准</option>
                        <option value="2" <?php echo ($filters['status'] ?? '') === '2' ? 'selected' : ''; ?>>已驳回</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-inline">
                <button type="submit" class="layui-btn">
                    <i class="layui-icon layui-icon-search"></i> 搜索
                </button>
                <a href="/commission/activity_income_withdrawals" class="layui-btn layui-btn-primary">
                    <i class="layui-icon layui-icon-refresh"></i> 重置
                </a>
            </div>
        </div>
    </form>
</fieldset>

<!-- 数据表格 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-rmb"></i> 活动收入提现申请列表
        <div style="float: right;">
            <span class="layui-badge layui-bg-blue">共 <?php echo $total; ?> 条记录</span>
        </div>
    </div>
    <div class="layui-card-body">
        <table class="layui-table">
            <thead>
                <tr>
                    <th>申请ID</th>
                    <th>用户信息</th>
                    <th>提现金额</th>
                    <th>到账金额</th>
                    <th>银行信息</th>
                    <th>状态</th>
                    <th>申请时间</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($withdrawals)): ?>
                    <?php foreach ($withdrawals as $withdrawal): ?>
                        <tr>
                            <td>
                                <span style="font-weight: bold; color: #1E9FFF;">
                                    #<?php echo $withdrawal['id']; ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <div style="font-weight: bold;">ID: <?php echo $withdrawal['uid']; ?></div>
                                    <div style="color: #666; font-size: 12px;">
                                        <?php echo htmlspecialchars($withdrawal['nickname'] ?? '未知用户'); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="color: #FF5722; font-weight: bold; font-size: 16px;">
                                    ¥<?php echo number_format($withdrawal['money'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <span style="color: #4CAF50; font-weight: bold;">
                                    ¥<?php echo number_format($withdrawal['daozhang_money'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <div style="max-width: 200px;">
                                    <div style="font-weight: bold;">
                                        <?php echo htmlspecialchars($withdrawal['bank_name'] ?? '未知银行'); ?>
                                    </div>
                                    <div style="color: #666; font-size: 12px; margin-top: 2px;">
                                        <?php echo htmlspecialchars($withdrawal['account_name'] ?? ''); ?>
                                    </div>
                                    <div style="color: #999; font-size: 11px; margin-top: 1px;">
                                        <?php 
                                        $account = $withdrawal['bank_account'] ?? '';
                                        if (strlen($account) > 4) {
                                            echo '****' . substr($account, -4);
                                        } else {
                                            echo $account;
                                        }
                                        ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $status_colors = [
                                    0 => '#FF9800', // 待审核
                                    1 => '#4CAF50', // 已批准
                                    2 => '#F44336'  // 已驳回
                                ];
                                $color = $status_colors[$withdrawal['status']] ?? '#999';
                                ?>
                                <span class="layui-badge" style="background-color: <?php echo $color; ?>;">
                                    <?php echo $withdrawal['status_text']; ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-size: 12px;">
                                    <?php echo date('Y-m-d H:i', strtotime($withdrawal['time'])); ?>
                                </div>
                            </td>
                            <td>
                                <div style="max-width: 150px; font-size: 12px; color: #666;">
                                    <?php echo htmlspecialchars($withdrawal['beizhu'] ?? '-'); ?>
                                </div>
                            </td>
                            <td>
                                <div class="layui-btn-group">
                                    <button class="layui-btn layui-btn-xs" onclick="viewWithdrawalDetail(<?php echo $withdrawal['id']; ?>)">
                                        <i class="layui-icon layui-icon-search"></i> 详情
                                    </button>
                                    <?php if ($withdrawal['status'] == 0): ?>
                                        <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="approveWithdrawal(<?php echo $withdrawal['id']; ?>, 'approve')">
                                            <i class="layui-icon layui-icon-ok"></i> 批准
                                        </button>
                                        <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="approveWithdrawal(<?php echo $withdrawal['id']; ?>, 'reject')">
                                            <i class="layui-icon layui-icon-close"></i> 驳回
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 50px; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i>
                            <div style="margin-top: 10px;">暂无数据</div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <?php if ($total > $limit): ?>
            <div style="text-align: center; margin-top: 20px;">
                <?php
                $total_pages = ceil($total / $limit);
                $query_params = $_GET;
                ?>
                <div class="layui-box layui-laypage layui-laypage-default">
                    <?php if ($page > 1): ?>
                        <?php $query_params['page'] = $page - 1; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" class="layui-laypage-prev">上一页</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <?php $query_params['page'] = $i; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" 
                           class="<?php echo $i == $page ? 'layui-laypage-curr' : ''; ?>">
                            <em><?php echo $i; ?></em>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <?php $query_params['page'] = $page + 1; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" class="layui-laypage-next">下一页</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// 查看提现详情
function viewWithdrawalDetail(id) {
    layer.open({
        type: 2,
        title: '提现申请详情',
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '600px'],
        content: '/commission/withdrawal_detail?id=' + id
    });
}

// 审核提现申请
function approveWithdrawal(id, action) {
    var title = action === 'approve' ? '批准提现' : '驳回提现';
    var confirmText = action === 'approve' ? '确定要批准这笔提现申请吗？' : '确定要驳回这笔提现申请吗？';
    
    layer.prompt({
        title: title + ' - 请输入备注',
        formType: 2,
        value: '',
        area: ['400px', '200px']
    }, function(remark, index) {
        $.post('/commission/approve_activity_income_withdrawal', {
            withdrawal_id: id,
            action: action,
            remark: remark
        }, function(res) {
            if (res.status === 'ok') {
                layer.msg(res.msg || '操作成功', {icon: 1});
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || '操作失败', {icon: 2});
            }
            layer.close(index);
        }, 'json').fail(function() {
            layer.msg('网络错误，请重试', {icon: 2});
            layer.close(index);
        });
    });
}

// 批量操作
function batchApprove(action) {
    var checkboxes = $('input[name="withdrawal_ids"]:checked');
    if (checkboxes.length === 0) {
        layer.msg('请选择要操作的记录', {icon: 0});
        return;
    }
    
    var ids = [];
    checkboxes.each(function() {
        ids.push($(this).val());
    });
    
    var title = action === 'approve' ? '批量批准' : '批量驳回';
    var confirmText = action === 'approve' ? '确定要批准选中的提现申请吗？' : '确定要驳回选中的提现申请吗？';
    
    layer.confirm(confirmText, {
        btn: ['确定', '取消']
    }, function(index) {
        $.post('/commission/batch_approve_withdrawals', {
            withdrawal_ids: ids,
            action: action
        }, function(res) {
            if (res.status === 'ok') {
                layer.msg(res.msg || '操作成功', {icon: 1});
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || '操作失败', {icon: 2});
            }
            layer.close(index);
        }, 'json');
    });
}

layui.use(['form'], function(){
    var form = layui.form;
    form.render();
});
</script>
