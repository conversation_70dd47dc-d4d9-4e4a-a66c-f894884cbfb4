<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  userbank_add,
  usertixian,
  userget_daijiesuan_status,
  apply_commission_withdraw,
  //  新增活动收入相关API
  get_activity_income_status,
  apply_activity_income_withdraw
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto } from "@/utils";
import { getAuthParams, isLoggedIn, safeApiCall } from "@/utils/auth";
// 修复：使用统一的权限检查工具函数
import { hasActivityIncomePermission } from "@/utils/permissions";

const formRef = ref();
const form = ref({
  money: "",
  username: "",
  bank_num: "",
  bank_name: "",
});
const rules = ref({
  money: [
    {
      type: "number",
      required: true,
      message: `请填写不小于${store().$state.config.config.min_tixian_money.val}的金额`,
      trigger: ["blur", "change"],
    },
    {
      // 自定义验证函数，见上说明
      validator: (rule, value, callback) =>
        Number(value) >= +store().$state.config.config.min_tixian_money.val,
      message: `请填写不小于${store().$state.config.config.min_tixian_money.val}的金额`,
      // 触发器可以同时用blur和change
      trigger: ["change", "blur"],
    },
  ],
  username: {
    type: "string",
    required: true,
    message: "请填写正确的姓名",
    trigger: ["blur", "change"],
  },
  bank_num: {
    type: "number",
    max: 19,
    required: true,
    message: "请填写正确的银行卡号",
    trigger: ["blur", "change"],
  },
  bank_name: {
    type: "string",
    required: true,
    message: "请填写正确的银行名称",
    trigger: ["blur", "change"],
  },
});
const radiolist = ref([
  { name: "银行卡", icon: "coupon-fill" },
  // { name: "支付宝", icon: "zhifubao-circle-fill" },
]);
const radiovalue = ref("银行卡");
const checkboxValue = ref();
const show = ref(false);
const info = ref({ daijiesuan: 0 });

// 🆕 新增：提现类型选择
const withdrawType = ref("commission"); // commission: 佣金提现, activity: 活动收入提现

// 🆕 新增：活动收入状态
const activityIncomeInfo = ref({
  available_income: '0.00',
  pending_income: '0.00',
  withdrawing_income: '0.00',
  total_income: '0.00'
});

onLoad(async () => {
  if (isLoggedIn()) {
    try {
      // 现有的佣金状态获取
      const res = await safeApiCall(
        () => userget_daijiesuan_status(getAuthParams()),
        '获取待结算状态'
      );
      if (res && res.data) {
        info.value = { daijiesuan: 0, ...res.data };
        for (let i in res.data) {
          res.data[i] = (res.data[i] * 100) / 100;
          if (i !== "yue") info.value.daijiesuan += (res.data[i] * 100) / 100;
        }
      } else {
        info.value = { daijiesuan: 0 };
      }

      // 🆕 新增：获取活动收入状态
      const activityRes = await safeApiCall(
        () => get_activity_income_status(getAuthParams()),
        '获取活动收入状态'
      );
      if (activityRes && activityRes.data) {
        activityIncomeInfo.value = activityRes.data;
      }
    } catch (error) {
      console.error("获取状态失败:", error);
      info.value = { daijiesuan: 0 };
      activityIncomeInfo.value = {
        available_income: '0.00',
        pending_income: '0.00',
        withdrawing_income: '0.00',
        total_income: '0.00'
      };
    }
  } else {
    info.value = { daijiesuan: 0 };
    activityIncomeInfo.value = {
      available_income: '0.00',
      pending_income: '0.00',
      withdrawing_income: '0.00',
      total_income: '0.00'
    };
  }
});

// 🆕 新增：切换提现类型
const switchWithdrawType = (type) => {
  withdrawType.value = type;
  form.value.money = ""; // 清空金额
  show.value = false; // 关闭详情弹窗
};

// 🆕 新增：获取当前可提现金额
const getCurrentAvailableAmount = () => {
  if (withdrawType.value === "commission") {
    return info.value.yue || '0.00';
  } else {
    return activityIncomeInfo.value.available_income || '0.00';
  }
};

// 🆕 新增：获取当前提现类型文本
const getCurrentWithdrawTypeText = () => {
  return withdrawType.value === "commission" ? "佣金" : "活动收入";
};

// 修复：使用统一的权限检查工具函数
const checkActivityIncomePermission = () => {
  return hasActivityIncomePermission();
};

// 修复：检查是否显示活动收入功能
const shouldShowActivityIncome = () => {
  // 首先检查角色权限
  if (!hasActivityIncomePermission()) return false;

  // 检查是否有活动收入记录（总收入大于0）
  const totalIncome = parseFloat(activityIncomeInfo.value.total_income || 0);
  return totalIncome > 0;
};

const groupChange = (e) => {
  if (e === "银行卡") {
    form.value.bank_name = "";
    delete rules.value.zfbCard;
    rules.value = {
      ...rules.value,
      bank_num: {
        type: "number",
        max: 19,
        required: true,
        message: "请填写正确的银行卡号",
        trigger: ["blur", "change"],
      },
      bank_name: {
        type: "string",
        required: true,
        message: "请填写正确的银行名称",
        trigger: ["blur", "change"],
      },
    };
  } else {
    form.value.bank_name = "支付宝";
    rules.value.bank_num.max = 100;
    rules.value.bank_num.message = "请填写正确的支付宝账号";
  }
};
const submit = async () => {
  // 🆕 修改：根据提现类型选择不同的可提现金额
  const availableAmount = parseFloat(getCurrentAvailableAmount());
  if (form.value.money > availableAmount)
    return uni.$u.toast(`提现金额不能大于可提现${getCurrentWithdrawTypeText()}金额`);

  if (!uni.$u.test.isEmpty(checkboxValue.value))
    formRef.value
      .validate()
      .then(async (res) => {
        const ress = await userbank_add(form.value);
        if (ress.status === "ok") {
          // 🆕 修改：根据提现类型调用不同的API
          let withdrawRes;
          if (withdrawType.value === "commission") {
            // 佣金提现
            withdrawRes = await apply_commission_withdraw({
              bank_id: ress.data,
              money: form.value.money,
            });
          } else {
            // 活动收入提现
            withdrawRes = await apply_activity_income_withdraw({
              bank_id: ress.data,
              money: form.value.money,
            });
          }

          if (withdrawRes.status === "ok") {
            uni.$u.toast(`${getCurrentWithdrawTypeText()}提现申请成功`);
            // 刷新页面数据
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.$u.toast(withdrawRes.msg);
          }
        }
      })
      .catch((errors) => uni.$u.toast(errors[0].message));
  else uni.$u.toast("请先阅读并同意《用户隐私协议》");
};


</script>
<template>
  <view class="withdraw-page" @click="show = false">
    <!-- 🆕 新增：提现类型切换 - 根据权限显示 -->
    <view class="withdraw-type-tabs" v-if="shouldShowActivityIncome()">
      <view
        class="tab-item"
        :class="{ active: withdrawType === 'commission' }"
        @click="switchWithdrawType('commission')"
      >
        <u-text
          :color="withdrawType === 'commission' ? '#6AC086' : '#666'"
          size="32rpx"
          :bold="withdrawType === 'commission'"
          text="佣金提现"
        ></u-text>
      </view>
      <view
        class="tab-item"
        :class="{ active: withdrawType === 'activity' }"
        @click="switchWithdrawType('activity')"
      >
        <u-text
          :color="withdrawType === 'activity' ? '#6AC086' : '#666'"
          size="32rpx"
          :bold="withdrawType === 'activity'"
          text="活动收入提现"
        ></u-text>
      </view>
    </view>

    <!-- 🆕 新增：无权限时的提示 -->
    <view class="no-permission-tip" v-if="hasActivityIncomePermission() && !shouldShowActivityIncome()">
      <u-text
        size="28rpx"
        color="#999"
        text="暂无活动收入记录，发布收费活动后可查看活动收入"
      ></u-text>
    </view>

    <!-- 余额卡片 - 根据提现类型显示不同内容 -->
    <view class="balance-card">
      <view class="balance-section">
        <view @click.stop="show = !show" class="balance-item">
          <view class="balance-header">
            <u-text
              size="28rpx"
              color="#666"
              :text="`可提现${getCurrentWithdrawTypeText()}(元)`"
            ></u-text>
            <u-icon
              name="info-circle-fill"
              color="#6AC086"
              size="32rpx"
              style="margin-left: 16rpx;"
            ></u-icon>
          </view>
          <view class="balance-amount">
            <u-text
              mode="price"
              color="#6AC086"
              size="48rpx"
              bold
              :text="getCurrentAvailableAmount()"
            ></u-text>
          </view>

          <!-- 详细信息弹窗 -->
          <view class="detail-popup" v-if="show">
            <view class="popup-arrow"></view>
            <view class="popup-content">
              <template v-if="withdrawType === 'commission'">
                <!-- 佣金详情 -->
                <view
                  class="popup-item"
                  @click="navto('jiesuanList?id=1')"
                >
                  <view class="popup-label">邀请佣金(待结算)</view>
                  <view class="popup-value">
                    <u-text
                      mode="price"
                      size="32rpx"
                      bold
                      color="#fff"
                      :text="info.invite_yongjin_daijiesuan || '0.00'"
                    ></u-text>
                  </view>
                </view>

                <view
                  class="popup-item"
                  @click="navto('jiesuanList?id=5')"
                >
                  <view class="popup-label">本月运营佣金(待结算)</view>
                  <view class="popup-value">
                    <u-text
                      mode="price"
                      size="32rpx"
                      bold
                      color="#fff"
                      :text="info.operation_yongjin_daijiesuan || '0.00'"
                    ></u-text>
                  </view>
                </view>
              </template>

              <template v-else>
                <!-- 🆕 活动收入详情 -->
                <view class="popup-item">
                  <view class="popup-label">活动收入(待结算)</view>
                  <view class="popup-value">
                    <u-text
                      mode="price"
                      size="32rpx"
                      bold
                      color="#fff"
                      :text="activityIncomeInfo.pending_income || '0.00'"
                    ></u-text>
                  </view>
                </view>

                <view class="popup-item">
                  <view class="popup-label">活动收入(提现中)</view>
                  <view class="popup-value">
                    <u-text
                      mode="price"
                      size="32rpx"
                      bold
                      color="#fff"
                      :text="activityIncomeInfo.withdrawing_income || '0.00'"
                    ></u-text>
                  </view>
                </view>

                <view class="popup-item">
                  <view class="popup-label">活动收入(总计)</view>
                  <view class="popup-value">
                    <u-text
                      mode="price"
                      size="32rpx"
                      bold
                      color="#fff"
                      :text="activityIncomeInfo.total_income || '0.00'"
                    ></u-text>
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view>

        <view class="divider"></view>

        <view class="balance-item">
          <view class="balance-header">
            <u-text size="28rpx" color="#666" text="可提现(元)"></u-text>
          </view>
          <view class="balance-amount">
            <u-text
              mode="price"
              color="#6AC086"
              size="48rpx"
              bold
              :text="info.yue || '0.00'"
            ></u-text>
          </view>
        </view>
      </view>
    </view>
    <!-- 提现表单 -->
    <view class="form-card">
      <u-form
        labelPosition="left"
        label-width="200rpx"
        :label-style="{ fontSize: '32rpx', fontWeight: '500', color: '#333' }"
        :model="form"
        :rules="rules"
        ref="formRef"
        errorType="toast"
      >
        <u-form-item label="提现金额" prop="money" borderBottom>
          <u-input
            type="number"
            input-align="right"
            placeholder-style="font-size: 32rpx;color: #999;"
            v-model="form.money"
            :placeholder="`单次最少提现${
              store().$state.config?.config?.min_tixian_money?.val
            }元`"
            border="none"
            :customStyle="{ fontSize: '32rpx', color: '#333' }"
          ></u-input>
        </u-form-item>

        <u-form-item label="提现至" prop="type" borderBottom>
          <view class="payment-methods">
            <u-radio-group
              v-model="radiovalue"
              placement="row"
              @change="groupChange"
              activeColor="#6AC086"
            >
              <view class="payment-option" v-for="(val, i) in radiolist" :key="i">
                <view class="payment-info">
                  <u-icon
                    :name="val.icon"
                    color="#6AC086"
                    size="40rpx"
                  ></u-icon>
                  <u-text
                    color="#333"
                    size="32rpx"
                    :text="val.name"
                  ></u-text>
                  <u-radio :name="val.name"></u-radio>
                </view>
              </view>
            </u-radio-group>
          </view>
        </u-form-item>

        <u-form-item label="姓名" prop="username" borderBottom>
          <u-input
            input-align="right"
            placeholder-style="font-size: 32rpx;color: #999;"
            v-model="form.username"
            placeholder="请填写您的真实姓名"
            border="none"
            :customStyle="{ fontSize: '32rpx', color: '#333' }"
          ></u-input>
        </u-form-item>

        <u-form-item
          :label="radiovalue === '银行卡' ? '银行卡号' : '支付宝账号'"
          prop="bank_num"
          borderBottom
        >
          <u-input
            input-align="right"
            placeholder-style="font-size: 32rpx;color: #999;"
            v-model="form.bank_num"
            :placeholder="`请填写${radiovalue === '银行卡' ? '银行卡号' : '支付宝账号'}`"
            border="none"
            :customStyle="{ fontSize: '32rpx', color: '#333' }"
          ></u-input>
        </u-form-item>

        <template v-if="radiovalue === '银行卡'">
          <u-form-item label="银行名称" prop="bank_name" borderBottom>
            <u-input
              input-align="right"
              placeholder-style="font-size: 32rpx;color: #999;"
              v-model="form.bank_name"
              placeholder="请填写银行名称"
              border="none"
              :customStyle="{ fontSize: '32rpx', color: '#333' }"
            ></u-input>
          </u-form-item>
        </template>
      </u-form>
    </view>
    <!-- 提示信息 -->
    <view class="tips-section">
      <u-text
        color="#999"
        size="28rpx"
        text="请确认您提现的真实信息以及真实账号"
      ></u-text>
    </view>

    <!-- 协议确认 -->
    <view class="agreement-section">
      <u-checkbox-group
        v-model="checkboxValue"
        placement="column"
        shape="circle"
        activeColor="#6AC086"
      >
        <view class="agreement-item">
          <u-checkbox name="a"></u-checkbox>
          <view class="agreement-text">
            <u-text color="#666" size="28rpx" text="同意"></u-text>
            <view class="agreement-link" @click="navto('/pages/bundle/common/xieyi?type=4')">
              <u-text color="#6AC086" size="28rpx" text="《用户隐私协议》"></u-text>
            </view>
          </view>
        </view>
      </u-checkbox-group>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <u-button
        shape="circle"
        color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        text="申请提现"
        :customStyle="{
          width: '100%',
          height: '96rpx',
          color: '#fff',
          fontSize: '36rpx',
          fontWeight: 'bold',
          boxShadow: '0 8rpx 24rpx rgba(106, 192, 134, 0.3)'
        }"
        @click="submit"
      ></u-button>

      <view class="record-link" @click="navto(`/pages/bundle/user/withDrawList`)">
        <u-text
          color="#6AC086"
          size="32rpx"
          text="查看提现记录"
        ></u-text>
        <u-icon name="arrow-right" color="#6AC086" size="24rpx" style="margin-left: 8rpx;"></u-icon>
      </view>
    </view>
  </view>
</template>

<style scoped lang="less">
/* 页面容器 */
.withdraw-page {
  min-height: 100vh;
  background: #f8f7f5;
  padding: 30rpx;
}

/* 余额卡片 */
.balance-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1000;
}

.balance-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.balance-item {
  flex: 1;
}

.balance-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.balance-amount {
  margin-top: 8rpx;
}

.divider {
  width: 2rpx;
  height: 80rpx;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
  margin: 0 40rpx;
}

/* 详细信息弹窗 */
.detail-popup {
  position: absolute;
  top: 100%;
  left: 30rpx;
  right: 30rpx;
  z-index: 99999;
  margin-top: 20rpx;
}

.popup-arrow {
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 20rpx solid #6AC086;
  margin: 0 auto 0 80rpx;
}

.popup-content {
  background: #6AC086;
  border-radius: 20rpx;
  padding: 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.popup-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.popup-item:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.popup-label {
  font-size: 24rpx;
  color: #fff;
  margin-bottom: 8rpx;
  opacity: 0.9;
}

.popup-value {
  font-weight: bold;
}

/* 表单卡片 */
.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}

/* 支付方式 */
.payment-methods {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  gap: 20rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.payment-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 提示信息 */
.tips-section {
  text-align: center;
  margin-bottom: 40rpx;
}

/* 协议确认 */
.agreement-section {
  display: flex;
  justify-content: center;
  margin-bottom: 50rpx;
}

.agreement-item {
  display: flex;
  align-items: center;
}

.agreement-text {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}

.agreement-link {
  margin-left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.agreement-link:active {
  background: rgba(106, 192, 134, 0.1);
}

/* 操作按钮区域 */
.action-section {
  margin-bottom: 40rpx;
}

.record-link {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.record-link:active {
  background: rgba(106, 192, 134, 0.1);
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .balance-section {
    flex-direction: column;
    gap: 30rpx;
  }

  .divider {
    width: 80%;
    height: 2rpx;
    margin: 20rpx auto;
  }

  .popup-content {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.balance-card,
.form-card {
  transition: all 0.3s ease;
}

.balance-card:hover,
.form-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

/* 🆕 提现类型切换样式 */
.withdraw-type-tabs {
  display: flex;
  background: #fff;
  margin: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  padding: 32rpx 0;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(106, 192, 134, 0.1);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #6AC086;
  border-radius: 3rpx;
}

.tab-item:active {
  background: rgba(106, 192, 134, 0.05);
}

/* 🆕 无权限提示样式 */
.no-permission-tip {
  margin: 24rpx;
  padding: 32rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  text-align: center;
  border: 2rpx dashed #ddd;
}
</style>
