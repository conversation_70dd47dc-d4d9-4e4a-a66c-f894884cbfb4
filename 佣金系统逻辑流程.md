# 佣金体系流程图

## 1. 整体流程图

```mermaid
graph TD
    A[用户注册] --> B{是否通过邀请链接?}
    B -->|是| C[记录邀请关系 p_uid]
    B -->|否| D[普通注册]
    
    C --> E[被邀请用户购买会员]
    E --> F{是否首次购买?}
    F -->|是| G[生成邀请佣金记录]
    F -->|否| H[跳过邀请佣金]
    
    E --> I{邀请人是否为分会长?}
    I -->|是| J[生成运营佣金记录]
    I -->|否| K[跳过运营佣金]
    
    G --> L[佣金状态: 待结算 status=0]
    J --> L
    
    L --> M[定时任务检查]
    M --> N{是否满足结算条件?}
    N -->|邀请佣金7天后| O[更新为可提取 status=1]
    N -->|运营佣金1月后| O
    N -->|否| M
    
    O --> P[用户申请提现]
    P --> Q[更新为提现中 status=2]
    Q --> R[管理员审核]
    R --> S{审核结果}
    S -->|通过| T[更新为已提现 status=3]
    S -->|驳回| U[恢复为可提取 status=1]
```

## 2. 邀请佣金流程

```mermaid
sequenceDiagram
    participant U1 as 邀请人
    participant U2 as 被邀请人
    participant SYS as 系统
    participant DB as 数据库
    participant TASK as 定时任务
    
    U1->>SYS: 生成邀请链接
    U2->>SYS: 通过邀请链接注册
    SYS->>DB: 记录邀请关系 (p_uid)
    
    U2->>SYS: 首次购买会员
    SYS->>SYS: 检查是否首次购买
    SYS->>SYS: 获取邀请佣金配置
    SYS->>SYS: 计算佣金金额
    SYS->>DB: 插入邀请佣金记录 (status=0)
    
    TASK->>DB: 检查7天前的待结算邀请佣金
    TASK->>DB: 批量更新状态为可提取 (status=1)
    
    U1->>SYS: 申请佣金提现
    SYS->>DB: 更新佣金状态为提现中 (status=2)
    SYS->>DB: 创建提现申请记录
    
    Note over SYS: 管理员审核
    SYS->>DB: 更新佣金状态为已提现 (status=3)
```

## 3. 运营佣金流程

```mermaid
sequenceDiagram
    participant PRES as 分会长
    participant USER as 用户
    participant SYS as 系统
    participant DB as 数据库
    participant TASK as 定时任务
    
    USER->>SYS: 购买会员 (首次或续费)
    SYS->>SYS: 检查邀请人是否为分会长
    SYS->>SYS: 获取运营佣金配置
    SYS->>SYS: 计算佣金金额
    SYS->>DB: 插入运营佣金记录 (status=0)
    
    TASK->>DB: 检查1个月前的待结算运营佣金
    TASK->>DB: 批量更新状态为可提取 (status=1)
    
    PRES->>SYS: 申请佣金提现
    SYS->>DB: 更新佣金状态为提现中 (status=2)
    SYS->>DB: 创建提现申请记录
    
    Note over SYS: 管理员审核
    SYS->>DB: 更新佣金状态为已提现 (status=3)
```

## 4. 佣金状态流转图

```mermaid
stateDiagram-v2
    [*] --> 待结算: 佣金生成
    待结算 --> 可提取: 定时任务\n(邀请7天/运营1月)
    可提取 --> 提现中: 用户申请提现
    提现中 --> 已提现: 管理员审核通过
    提现中 --> 可提取: 管理员审核驳回
    可提取 --> 已冻结: 管理员冻结
    已冻结 --> 可提取: 管理员解冻
    已提现 --> [*]: 流程结束
    
    note right of 待结算
        status = 0
        刚生成的佣金记录
    end note
    
    note right of 可提取
        status = 1
        用户可以申请提现
    end note
    
    note right of 提现中
        status = 2
        等待管理员审核
    end note
    
    note right of 已提现
        status = 3
        提现完成
    end note
    
    note right of 已冻结
        status = 5
        暂停提现
    end note
```

## 5. 数据库表关系图

```mermaid
erDiagram
    user {
        int uid PK
        int p_uid "邀请人ID"
        int role_type "角色类型(0=普通,1=分会长)"
        int branch_id "分会ID"
        decimal money "余额"
        string nickname "昵称"
        string mobile "手机号"
    }
    
    user_huiyuan_order {
        int id PK
        int uid FK
        decimal money "订单金额"
        int status "订单状态"
        datetime pay_time "支付时间"
        string order_id "订单号"
    }
    
    user_yongjin_log {
        int id PK
        int uid FK "佣金归属用户"
        decimal money "佣金金额"
        int type "佣金类型(1=邀请,4=运营)"
        string commission_type "佣金分类(invite/operation)"
        int status "状态(0=待结算,1=可提取,2=提现中,3=已提现)"
        string order_id "关联订单号"
        string related_month "关联月份"
        datetime time "创建时间"
        datetime settlement_time "结算时间"
        datetime available_time "可提取时间"
        string remark "备注"
    }
    
    user_tixian {
        int id PK
        int uid FK
        decimal money "提现金额"
        int status "审核状态(0=待审核,1=已通过,2=已驳回)"
        string source_type "提现来源(balance=余额,commission=佣金)"
        text commission_ids "关联佣金记录ID(JSON)"
        datetime time "申请时间"
        string beizhu "备注"
    }
    
    commission_status_logs {
        int id PK
        int commission_id FK "佣金记录ID"
        int old_status "原状态"
        int new_status "新状态"
        int operator_id "操作人ID"
        string operator_type "操作类型(user/admin/system)"
        string remark "操作备注"
        datetime created_at "创建时间"
    }
    
    commission_configs {
        int id PK
        string config_name "配置名称"
        string target_type "目标类型(user/branch/role/global)"
        int target_id "目标ID"
        string commission_type "佣金类型(invite/operation)"
        string calculation_method "计算方式(fixed/percentage)"
        decimal base_amount "基础金额"
        decimal percentage_rate "百分比率"
        decimal min_amount "最小金额"
        decimal max_amount "最大金额"
        int priority "优先级"
        int status "状态(0=停用,1=启用)"
    }
    
    user ||--o{ user_huiyuan_order : "购买会员"
    user ||--o{ user_yongjin_log : "获得佣金"
    user ||--o{ user_tixian : "申请提现"
    user_yongjin_log ||--o{ commission_status_logs : "状态变更"
    user_tixian ||--o{ user_yongjin_log : "关联佣金"
    commission_configs ||--o{ user_yongjin_log : "配置规则"
```

## 6. 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A1[小程序用户端]
        A2[管理后台]
    end
    
    subgraph "API层"
        B1[User.php - 用户相关API]
        B2[Branchpresident.php - 分会长API]
        B3[Admin.php - 管理员API]
    end
    
    subgraph "业务逻辑层"
        C1[佣金生成逻辑]
        C2[佣金配置管理]
        C3[状态流转逻辑]
        C4[提现审核逻辑]
    end
    
    subgraph "定时任务层"
        D1[Daemon.php - 定时任务]
        D2[佣金状态更新]
        D3[数据一致性检查]
    end
    
    subgraph "数据层"
        E1[(user表)]
        E2[(user_yongjin_log表)]
        E3[(user_tixian表)]
        E4[(commission_configs表)]
        E5[(commission_status_logs表)]
    end
    
    A1 --> B1
    A1 --> B2
    A2 --> B3
    
    B1 --> C1
    B1 --> C3
    B2 --> C1
    B2 --> C2
    B3 --> C2
    B3 --> C4
    
    C1 --> E1
    C1 --> E2
    C2 --> E4
    C3 --> E2
    C3 --> E5
    C4 --> E3
    C4 --> E2
    
    D1 --> D2
    D1 --> D3
    D2 --> E2
    D3 --> E2
```

## 7. 关键时间节点

```mermaid
timeline
    title 佣金生命周期时间线
    
    section 佣金生成
        用户购买会员 : 立即生成佣金记录
                    : status = 0 (待结算)
    
    section 等待期
        邀请佣金 : 等待7天
        运营佣金 : 等待1个月
    
    section 可提取期
        定时任务更新 : status = 1 (可提取)
        用户可申请提现 : 显示在提现页面
    
    section 提现流程
        用户申请 : status = 2 (提现中)
        管理员审核 : 1-3个工作日
        审核完成 : status = 3 (已提现)
```

## 8. 错误处理流程

```mermaid
flowchart TD
    A[佣金操作开始] --> B{数据验证}
    B -->|失败| C[记录错误日志]
    B -->|成功| D[执行业务逻辑]
    
    D --> E{事务执行}
    E -->|失败| F[回滚事务]
    E -->|成功| G[提交事务]
    
    F --> C
    C --> H[返回错误信息]
    G --> I[记录成功日志]
    I --> J[返回成功结果]
    
    H --> K[结束]
    J --> K
    
    style C fill:#ffcccc
    style F fill:#ffcccc
    style H fill:#ffcccc
    style I fill:#ccffcc
    style J fill:#ccffcc
```
