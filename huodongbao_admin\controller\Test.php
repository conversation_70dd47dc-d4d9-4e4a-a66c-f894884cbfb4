<?php
namespace controller;
use core\Controller;
use core\Db;

class Test extends Controller{

	public function __construct(){
		parent::__construct();
	}

	public function t1(){
		$t1 = microtime(true);
		$count = $this->t2();
		$t2 = microtime(true);
		echo "{$t1} | {$t2} | {$count}<br>";
		echo '耗时'.round($t2-$t1,3).'秒<br>';
	}
	
	public function t2(){
		require BASE_PATH . "lib" . DS . "QrReader" . DS . "QrReader.php";
		$qrcode = new \Zxing\QrReader('./qr2.png');  //图片路径
		$text = $qrcode->text(); //返回识别后的文本
		echo $text;
	}
	public function _empty(){
		echo __CLASS__." -> _empty";
	}
	function __destruct(){

	}
}
