<?php
namespace model;
use core\Db;
class Qcloudsms{
	
	static private $appid = "";
	static private $appkey = "";
	
	static private $sms_url = "https://yun.tim.qq.com/v5/tlssmssvr/sendsms";//模板单发
	static private $sms_url2 = "https://yun.tim.qq.com/v5/tlssmssvr/sendmultisms2";//模板群发
	static private $sms_url3 = "https://cloud.tim.qq.com/v5/tlsvoicesvr/sendtvoice";//模板语音单发

	public function __construct(){
		
	}
	
    /**
     * 指定模板单发
     *
     * @param string $nationCode  国家码，如 86 为中国
     * @param string $phoneNumber 不带国家码的手机号
     * @param int    $templId     模板 id
     * @param array  $params      模板参数列表，如模板 {1}...{2}...{3}，那么需要带三个参数
     * @param string $sign        签名，如果填空串，系统会使用默认签名
     * @param string $extend      扩展码，可填空串
     * @param string $ext         服务端原样返回的参数，可填空串
     * @return string 应答json字符串，详细内容参见腾讯云协议文档
     */
    static public function send($phoneNumber,$params=[],$templId=1886062,$sign="桀骜网络",$nationCode="86",$extend="",$ext=""){
        $random = rand(100000, 999999);
        $curTime = time();
        $url = self::$sms_url . "?sdkappid=" . self::$appid . "&random=" . $random;
		$params = [
			"tel"=>[
					"nationcode"=>$nationCode,
					"mobile"=>$phoneNumber
				],
			"sig"=>self::getSign($random,$curTime,$phoneNumber),
			"tpl_id"=>$templId,
			"params"=>$params,
			"sign"=>$sign,			
			"time"=>$curTime,
			"extend"=>$extend,
			"ext"=>$ext,
		];
		$res = curl($url,$params,true);
		$result = json_decode($res['body'],true);
		if(isset($result['ActionStatus']) && strtoupper($result['ActionStatus']) == "FAIL"){
			$msg = isset($result['ErrorInfo']) ? $result['ErrorInfo'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];
		}
		if(isset($result['result']) && $result['result'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['errmsg']) ? $result['errmsg'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }
	
    /**
     * 指定模板群发
     * @param  string $nationCode   国家码，如 86 为中国
     * @param  array  $phoneNumbers 不带国家码的手机号列表
     * @param  int    $templId      模板id
     * @param  array  $params       模板参数列表，如模板 {1}...{2}...{3}，那么需要带三个参数
     * @param  string $sign         签名，如果填空串，系统会使用默认签名
     * @param  string $extend       扩展码，可填空串
     * @param  string $ext          服务端原样返回的参数，可填空串
     * @return string 应答json字符串，详细内容参见腾讯云协议文档
     */
    static public function sendMulti($phoneNumbers=[], $params=[], $templId=1886062,$sign = "桀骜网络",$nationCode="86",  $extend = "", $ext = ""){
        $random = rand(100000, 999999);
        $curTime = time();
        $url = self::$sms_url2 . "?sdkappid=" . self::$appid . "&random=" . $random;
		$params = [
			"tel"=>self::phoneNumbersToArray($nationCode, $phoneNumbers),
			"sign"=>$sign,
			"tpl_id"=>$templId,
			"params"=>$params,
			"sig"=>self::getSign($random,$curTime,$phoneNumbers),
			"time"=>$curTime,
			"extend"=>$extend,
			"ext"=>$ext,
		];
		$res = curl($url,$params,true);
		$result = json_decode($res['body'],true);
		if(isset($result['ActionStatus']) && strtoupper($result['ActionStatus']) == "FAIL"){
			$msg = isset($result['ErrorInfo']) ? $result['ErrorInfo'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];
		}
		if(isset($result['result']) && $result['result'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['errmsg']) ? $result['errmsg'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }
	
    /**
     *
     * 指定模板发送语音短信
     *
     * @param string $nationCode  国家码，如 86 为中国
     * @param string $phoneNumber 不带国家码的手机号
     * @param int    $templId     模板 id
     * @param array  $params      模板参数列表，如模板 {1}...{2}...{3}，需要带三个参数
     * @param string $playtimes   播放次数，可选，最多3次，默认2次
     * @param string $ext         用户的session内容，服务端原样返回，可选字段，不需要可填空串
     * @return string 应答json字符串，详细内容参见腾讯云协议文档
     */
    static public function sendVoice($phoneNumber, $params=[],$templId=1886062, $playtimes = 2, $nationCode="86", $ext = ""){
        $random = rand(100000, 999999);
        $curTime = time();
        $url = self::$sms_url3 . "?sdkappid=" . self::$appid . "&random=" . $random;
		$params = [
			"tel"=>["nationcode"=>$nationCode,"mobile"=>$phoneNumber],
			"tpl_id"=>$templId,
			"params"=>$params,
			"playtimes"=>$playtimes,
			"sig"=>hash("sha256", "appkey=".self::$appkey."&random=".$random."&time=".$curTime."&mobile=".$phoneNumber),
			"time"=>$curTime,
			"ext"=>$ext,
		];
		$res = curl($url,$params,true);
		$result = json_decode($res['body'],true);
		if(isset($result['ActionStatus']) && strtoupper($result['ActionStatus']) == "FAIL"){
			$msg = isset($result['ErrorInfo']) ? $result['ErrorInfo'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];
		}
		if(isset($result['result']) && $result['result'] == 0){
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			$msg = isset($result['errmsg']) ? $result['errmsg'] : "发送失败";
			return ["status"=>"error","msg"=>$msg];			
		}
    }
	
	//签名
	static private function getSign($random,$curTime, $phoneNumbers){
		if(is_string($phoneNumbers)){
			$phoneNumbers_arr = [$phoneNumbers];
		}else{
			$phoneNumbers_arr = $phoneNumbers;
		}
        $phoneNumbersString = $phoneNumbers_arr[0];
        for ($i = 1; $i < count($phoneNumbers_arr); $i++) {
            $phoneNumbersString .= ("," . $phoneNumbers_arr[$i]);
        }
        return hash("sha256", "appkey=".self::$appkey."&random=".$random."&time=".$curTime."&mobile=".$phoneNumbersString);
    }
	
    static public function phoneNumbersToArray($nationCode, $phoneNumbers=[]){
		$data = [];
		foreach($phoneNumbers as $phoneNumber){
			$data[] = ["nationcode"=>$nationCode,"mobile"=>$phoneNumber];
		}
        return $data;
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
