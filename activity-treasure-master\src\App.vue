<script setup>
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { configapp, configpop } from "@/api";
import { store } from "@/store";
import { restoreLoginState } from "@/utils/auth";

onLaunch(async () => {
  try {
    // 应用启动时首先恢复登录状态
    console.log('应用启动，尝试恢复登录状态...');
    const restored = restoreLoginState();
    if (restored) {
      console.log('登录状态恢复成功');
    } else {
      console.log('无有效的登录状态需要恢复');
    }

    const res = await configapp();

    // 检查配置获取是否成功
    if (res?.status === "ok" && res?.data) {
      // 保存配置数据到store
      store().changeConfig(res.data);
    }

    // 获取弹出公告
    const popRes = await configpop();
    if (popRes?.status == "ok") {
      store().setPopContent(popRes.data);
    }
  } catch (error) {
    console.error('获取App配置出错:', error);
  }
});

onShow(() => {
  // App显示时的处理
});

onHide(() => {
  // App隐藏时的处理
});


</script>

<template>
  <!-- 主应用内容 -->
  <view>
    <slot></slot>
  </view>
</template>

<style lang="scss">
@import "uview-plus/index.scss";
@import "./style/judu-theme.scss";

/* 修复小程序不支持标签选择器的问题 */
.ql-editor {
  /* 覆盖 ul 标签选择器样式 */
  .ul-style {
    margin: 0;
    padding: 0;
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  }

  /* 覆盖 ul > li 标签选择器样式 */
  .ul-li-style {
    list-style-type: none;
  }

  .ul-li-style:before {
    content: "•";
  }
}
</style>
<style lang="less">
@import url("style/base.less");
/*每个页面公共css */

/* 全局字体栈声明 - 微信小程序兼容版本 */
page {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

/* 具体组件字体声明 */
view, text, button, input, textarea, picker, navigator {
  font-family: inherit;
}

/* 确保所有文本元素继承字体 */
.uni-input-input, .uni-textarea-textarea {
  font-family: inherit;
}

/* 修复uView图标字体CDN失效问题 - 完整版本 */
@font-face {
  font-family: "uicon-iconfont";
  src: url("data:font/truetype;charset=utf-8;base64,") format("truetype");
  font-display: swap; /* 字体加载失败时使用降级字体 */
}

/* 阻止加载阿里云字体CDN - 防止ERR_CACHE_MISS错误 */
@font-face {
  font-family: "iconfont";
  src: url("data:font/truetype;charset=utf-8;base64,") format("truetype");
  font-display: swap;
}

/* uView图标字体降级处理 - 移除多余小点 */
.u-icon__icon {
  font-family: "uicon-iconfont", "iconfont", sans-serif !important;
}

/* 移除导致多余小点的降级样式 */
.u-icon__icon:before {
  content: "" !important; /* 清空内容，避免显示多余的圆点 */
}

/* 全局阻止阿里云字体CDN加载 */
@font-face {
  font-family: "font_2225171_8kdcwk4po24";
  src: url("data:font/truetype;charset=utf-8;base64,") format("truetype");
  font-display: swap;
}

/* 应用基础样式 */

/* 全局色彩系统 - 基于新配色方案 */
:root {
  /* 主色调系统 */
  --primary-color: #245D3C;        /* 深绿色 - 主色 */
  --primary-light: #3AEE55;        /* 亮绿色 - 强调色 */
  --primary-medium: #73C088;       /* 中绿色 - 辅助色 */
  --primary-soft: #A5E1B8;         /* 浅绿色 - 柔和色 */
  --primary-bg: #C8E6D1;           /* 极浅绿色 - 背景色 */

  /* 中性色系统 */
  --neutral-white: #FFFFFF;
  --neutral-light: #F8F9FA;
  --neutral-gray: #E5E5E5;
  --neutral-dark: #333333;
  --neutral-text: #666666;

  /* 功能色系统 */
  --success-color: #3AEE55;
  --warning-color: #FF9500;
  --error-color: #FF4757;
  --info-color: #245D3C;

  /* 阴影系统 */
  --shadow-light: 0 4px 10px rgba(36, 93, 60, 0.05);
  --shadow-medium: 0 8px 15px rgba(36, 93, 60, 0.1);
  --shadow-heavy: 0 12px 24px rgba(36, 93, 60, 0.15);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, #3AEE55 0%, #73C088 100%);
  --gradient-soft: linear-gradient(135deg, #A5E1B8 0%, #C8E6D1 100%);
  --gradient-bg: linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 50%);
}

/* 全局按钮呼吸感阴影效果 */
.btn-enhanced,
.u-button,
.publish-btn,
.submit-btn,
.action-btn,
.primary-btn {
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced:hover,
.u-button:hover,
.publish-btn:hover,
.submit-btn:hover,
.action-btn:hover,
.primary-btn:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

/* 玻璃拟态效果 */
.glass-effect,
.navbar-glass,
.card-glass,
.popup-glass {
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 关键卡片玻璃效果 */
.activity-card,
.user-card,
.world-card,
.feed-card {
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-card:hover,
.user-card:hover,
.world-card:hover,
.feed-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* 主题色彩应用 */
.primary-color { color: var(--primary-color) !important; }
.primary-bg { background-color: var(--primary-color) !important; }
.primary-light-color { color: var(--primary-light) !important; }
.primary-light-bg { background-color: var(--primary-light) !important; }
.primary-gradient { background: var(--gradient-primary) !important; }
.primary-soft-bg { background-color: var(--primary-soft) !important; }
</style>
