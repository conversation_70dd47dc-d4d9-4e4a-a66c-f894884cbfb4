<?php
namespace core;
class View{
	static protected $tpl = [];
	static $data = [];
	public function __construct(){

	}

	//加载模版地址
	static public function load($tplPath=""){
		if(is_array($tplPath)){
			foreach($tplPath as $v){
				self::$tpl[] = tplUrl($v);
			}
		}else{
			self::$tpl[] = tplUrl($tplPath);
		}
		return true;
	}

	//获取加载好的模版地址
	static public function getView(){
		return self::$tpl;
	}

	//加载模版返回输出
	static public function output($data=null){
		foreach($GLOBALS as $k=>$v){
			if(!in_array($k,["_GET","_POST","_COOKIE","_FILES","_SERVER","_REQUEST","GLOBALS","_SESSION"])){
				$$k = $v;
			}
		}
		if(empty(self::$tpl))self::$tpl = tplUrl();
		ob_start();
		foreach(self::$tpl as $v){
			if(file_exists($v)){
				include $v;
			}else{
				return "文件 [ {$v} ] 不存在";
			}
		}

		//if(\core\Page::$count>0 && \core\Page::$pageSize>0)echo \core\Page::render();

		$content = ob_get_contents();
		ob_end_clean();
		self::$tpl = [];
		return $content;
	}

	public function __destruct(){

	}
}
