<fieldset class="table-search-fieldset">
    <legend>佣金配置管理</legend>
    <div style="margin: 10px 10px 10px 10px">
        <form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="post" id="search">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">目标类型</label>
                    <div class="layui-input-inline">
                        <select name="target_type">
                            <option value="">全部</option>
                            <option value="role" <?php if(isset($target_type) && $target_type=='role') echo 'selected'; ?>>角色</option>
                            <option value="user" <?php if(isset($target_type) && $target_type=='user') echo 'selected'; ?>>用户</option>
                            <option value="branch" <?php if(isset($target_type) && $target_type=='branch') echo 'selected'; ?>>分会</option>
                            <option value="global" <?php if(isset($target_type) && $target_type=='global') echo 'selected'; ?>>全局</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">佣金类型</label>
                    <div class="layui-input-inline">
                        <select name="commission_type">
                            <option value="">全部</option>
                            <option value="invite" <?php if(isset($commission_type) && $commission_type=='invite') echo 'selected'; ?>>邀请佣金</option>
                            <option value="operation" <?php if(isset($commission_type) && $commission_type=='operation') echo 'selected'; ?>>运营佣金</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value="">全部</option>
                            <option value="1" <?php if(isset($status) && $status=='1') echo 'selected'; ?>>启用</option>
                            <option value="0" <?php if(isset($status) && $status=='0') echo 'selected'; ?>>停用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="location.href='<?php echo url('commission/config_management'); ?>'">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="addConfig()">
                            <i class="layui-icon layui-icon-add-1"></i> 新增配置
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</fieldset>

<style>
.table_height_ {
    max-height: 600px;
    overflow-y: auto;
}
.status-enabled {
    color: #5FB878;
}
.status-disabled {
    color: #FF5722;
}
.priority-high {
    color: #FF5722;
    font-weight: bold;
}
.priority-normal {
    color: #1E9FFF;
}
.priority-low {
    color: #999;
}

/* 修复弹窗层级问题 */
.layui-layer {
    z-index: 19891014 !important;
}
.layui-layer-shade {
    z-index: 19891013 !important;
}
.layui-layer-dialog {
    z-index: 19891014 !important;
}
.layui-layer-content {
    position: relative !important;
    z-index: 19891015 !important;
}

/* 优化弹窗内容样式 */
.layui-layer-content {
    overflow: visible !important;
    pointer-events: auto !important;
}

/* 确保弹窗内所有元素可交互 */
.layui-layer-content * {
    pointer-events: auto !important;
}

/* 确保表单元素可交互 */
.layui-layer-content input,
.layui-layer-content select,
.layui-layer-content button,
.layui-layer-content textarea {
    pointer-events: auto !important;
    position: relative !important;
    z-index: 1 !important;
}

/* 确保layui组件可交互 */
.layui-layer-content .layui-form-item,
.layui-layer-content .layui-input,
.layui-layer-content .layui-select,
.layui-layer-content .layui-btn {
    pointer-events: auto !important;
    position: relative !important;
}

#configForm {
    max-height: 500px;
    overflow-y: auto;
    pointer-events: auto !important;
}

/* 修复可能的遮罩问题 */
.layui-layer-shade {
    pointer-events: auto !important;
}

/* 确保弹窗标题栏可交互 */
.layui-layer-title {
    pointer-events: auto !important;
}

/* 确保关闭按钮可交互 */
.layui-layer-close {
    pointer-events: auto !important;
    z-index: 19891016 !important;
}
</style>

<div class="layui-form layui-border-box layui-table-view">
    <div class="layui-table-body layui-table-main table_height_">
        <table cellspacing="0" cellpadding="0" border="0" class="layui-table" lay-even>
            <thead>
                <tr>
                    <td><div class="layui-table-cell">ID</div></td>
                    <td><div class="layui-table-cell">配置名称</div></td>
                    <td><div class="layui-table-cell">目标类型</div></td>
                    <td><div class="layui-table-cell">目标ID</div></td>
                    <td><div class="layui-table-cell">佣金类型</div></td>
                    <td><div class="layui-table-cell">计算方式</div></td>
                    <td><div class="layui-table-cell">金额/比例</div></td>
                    <td><div class="layui-table-cell">结算周期</div></td>
                    <td><div class="layui-table-cell">延迟天数</div></td>
                    <td><div class="layui-table-cell">优先级</div></td>
                    <td><div class="layui-table-cell">状态</div></td>
                    <td><div class="layui-table-cell">生效时间</div></td>
                    <td><div class="layui-table-cell">失效时间</div></td>
                    <td><div class="layui-table-cell">操作</div></td>
                </tr>
            </thead>
            <tbody>
                <?php if(!empty($data)){ ?>
                    <?php foreach($data as $v){ ?>
                    <tr>
                        <td><div class="layui-table-cell"><?php echo $v['id']; ?></div></td>
                        <td><div class="layui-table-cell"><?php echo htmlspecialchars($v['config_name']); ?></div></td>
                        <td><div class="layui-table-cell">
                            <?php 
                            $target_types = ['role' => '角色', 'user' => '用户', 'branch' => '分会', 'global' => '全局'];
                            echo $target_types[$v['target_type']] ?? $v['target_type']; 
                            ?>
                        </div></td>
                        <td><div class="layui-table-cell"><?php echo $v['target_id']; ?></div></td>
                        <td><div class="layui-table-cell">
                            <?php echo $v['commission_type'] == 'invite' ? '邀请佣金' : '运营佣金'; ?>
                        </div></td>
                        <td><div class="layui-table-cell">
                            <?php echo $v['calculation_method'] == 'fixed' ? '固定金额' : '百分比'; ?>
                        </div></td>
                        <td><div class="layui-table-cell">
                            <?php 
                            if ($v['calculation_method'] == 'fixed') {
                                echo '￥' . number_format($v['base_amount'], 2);
                            } else {
                                echo $v['percentage_rate'] . '%';
                            }
                            ?>
                        </div></td>
                        <td><div class="layui-table-cell">
                            <?php 
                            $cycles = ['realtime' => '实时', 'daily' => '每日', 'weekly' => '每周', 'monthly' => '每月'];
                            echo $cycles[$v['settlement_cycle']] ?? $v['settlement_cycle']; 
                            ?>
                        </div></td>
                        <td><div class="layui-table-cell"><?php echo $v['available_delay_days']; ?>天</div></td>
                        <td><div class="layui-table-cell">
                            <span class="<?php echo $v['priority'] >= 100 ? 'priority-high' : ($v['priority'] >= 50 ? 'priority-normal' : 'priority-low'); ?>">
                                <?php echo $v['priority']; ?>
                            </span>
                        </div></td>
                        <td><div class="layui-table-cell">
                            <span class="<?php echo $v['status'] == 1 ? 'status-enabled' : 'status-disabled'; ?>">
                                <?php echo $v['status'] == 1 ? '启用' : '停用'; ?>
                            </span>
                        </div></td>
                        <td><div class="layui-table-cell"><?php echo $v['effective_date'] ?: '-'; ?></div></td>
                        <td><div class="layui-table-cell"><?php echo $v['expiry_date'] ?: '-'; ?></div></td>
                        <td><div class="layui-table-cell">
                            <button class="layui-btn layui-btn-xs" onclick="editConfig(<?php echo $v['id']; ?>)">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                            </button>
                            <button class="layui-btn layui-btn-xs <?php echo $v['status'] == 1 ? 'layui-btn-warm' : 'layui-btn-normal'; ?>" 
                                    onclick="toggleStatus(<?php echo $v['id']; ?>, <?php echo $v['status']; ?>)">
                                <i class="layui-icon <?php echo $v['status'] == 1 ? 'layui-icon-pause' : 'layui-icon-play'; ?>"></i>
                                <?php echo $v['status'] == 1 ? '停用' : '启用'; ?>
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteConfig(<?php echo $v['id']; ?>)">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                        </div></td>
                    </tr>
                    <?php } ?>
                <?php } else { ?>
                    <tr>
                        <td colspan="14"><div class="layui-table-cell" style="text-align: center; padding: 20px;">暂无数据</div></td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
    <?php echo pageRender(); ?>
</div>

<!-- 添加/编辑配置弹窗 -->
<div id="configForm" style="display: none; padding: 20px;">
    <form class="layui-form" id="configFormData">
        <input type="hidden" name="id" id="configId">
        <input type="hidden" name="action" id="formAction">

        <div class="layui-form-item">
            <label class="layui-form-label">配置名称</label>
            <div class="layui-input-block">
                <input type="text" name="config_name" id="config_name" required lay-verify="required" placeholder="请输入配置名称" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">目标类型</label>
                <div class="layui-input-inline">
                    <select name="target_type" id="target_type" lay-verify="required" lay-filter="targetType">
                        <option value="">请选择</option>
                        <option value="role">角色</option>
                        <option value="user">用户</option>
                        <option value="branch">分会</option>
                        <option value="global">全局</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">目标ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="target_id" id="target_id" required lay-verify="required" placeholder="目标ID" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">佣金类型</label>
                <div class="layui-input-inline">
                    <select name="commission_type" id="commission_type" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="invite">邀请佣金</option>
                        <option value="operation">运营佣金</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">计算方式</label>
                <div class="layui-input-inline">
                    <select name="calculation_method" id="calculation_method" lay-verify="required" lay-filter="calculationMethod">
                        <option value="">请选择</option>
                        <option value="fixed">固定金额</option>
                        <option value="percentage">百分比</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item" id="baseAmountDiv" style="display: none;">
            <label class="layui-form-label">固定金额</label>
            <div class="layui-input-block">
                <input type="number" name="base_amount" id="base_amount" step="0.01" min="0" placeholder="请输入固定金额" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item" id="percentageDiv" style="display: none;">
            <div class="layui-inline">
                <label class="layui-form-label">百分比率</label>
                <div class="layui-input-inline">
                    <input type="number" name="percentage_rate" id="percentage_rate" step="0.01" min="0" max="100" placeholder="百分比" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">最小金额</label>
                <div class="layui-input-inline">
                    <input type="number" name="min_amount" id="min_amount" step="0.01" min="0" placeholder="最小金额" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">最大金额</label>
            <div class="layui-input-block">
                <input type="number" name="max_amount" id="max_amount" step="0.01" min="0" placeholder="最大金额（可选）" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">结算周期</label>
                <div class="layui-input-inline">
                    <select name="settlement_cycle" id="settlement_cycle">
                        <option value="realtime">实时</option>
                        <option value="daily">每日</option>
                        <option value="weekly">每周</option>
                        <option value="monthly">每月</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">延迟天数</label>
                <div class="layui-input-inline">
                    <input type="number" name="available_delay_days" id="available_delay_days" min="0" value="0" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">优先级</label>
            <div class="layui-input-block">
                <input type="number" name="priority" id="priority" min="0" value="0" placeholder="数值越大优先级越高" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">生效时间</label>
                <div class="layui-input-inline">
                    <input type="date" name="effective_date" id="effective_date" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">失效时间</label>
                <div class="layui-input-inline">
                    <input type="date" name="expiry_date" id="expiry_date" class="layui-input">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="configSubmit">确定</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    // 搜索表单提交
    form.on('submit(search)', function(data){
        var searchForm = document.getElementById('search');
        searchForm.submit();
        return false;
    });

    // 监听目标类型变化
    form.on('select(targetType)', function(data){
        var targetType = data.value;
        var targetIdInput = $('#target_id');

        // 根据目标类型给出提示
        switch(targetType) {
            case 'role':
                targetIdInput.attr('placeholder', '角色ID (0=普通用户, 1=分会长, 2=管理员)');
                break;
            case 'user':
                targetIdInput.attr('placeholder', '用户ID');
                break;
            case 'branch':
                targetIdInput.attr('placeholder', '分会ID');
                break;
            case 'global':
                targetIdInput.attr('placeholder', '全局配置请填写0');
                targetIdInput.val('0');
                break;
            default:
                targetIdInput.attr('placeholder', '目标ID');
        }
    });

    // 监听计算方式变化
    form.on('select(calculationMethod)', function(data){
        var method = data.value;
        var baseAmountDiv = $('#baseAmountDiv');
        var percentageDiv = $('#percentageDiv');

        if (method === 'fixed') {
            baseAmountDiv.show();
            percentageDiv.hide();
            $('#base_amount').attr('required', true);
            $('#percentage_rate').attr('required', false);
        } else if (method === 'percentage') {
            baseAmountDiv.hide();
            percentageDiv.show();
            $('#base_amount').attr('required', false);
            $('#percentage_rate').attr('required', true);
        } else {
            baseAmountDiv.hide();
            percentageDiv.hide();
            $('#base_amount').attr('required', false);
            $('#percentage_rate').attr('required', false);
        }
    });

    // 配置表单提交
    form.on('submit(configSubmit)', function(data){
        var formData = data.field;

        console.log('提交表单数据:', formData);

        // 验证计算方式对应的字段
        if (formData.calculation_method === 'fixed' && !formData.base_amount) {
            layer.msg('请填写固定金额');
            return false;
        }
        if (formData.calculation_method === 'percentage' && !formData.percentage_rate) {
            layer.msg('请填写百分比率');
            return false;
        }

        // 显示提交中状态
        var submitBtn = $('button[lay-filter="configSubmit"]');
        var originalText = submitBtn.text();
        submitBtn.text('提交中...').prop('disabled', true);

        $.post('<?php echo url("commission/config_management"); ?>', formData, function(res){
            console.log('提交响应:', res);

            if (res.status === 'ok') {
                layer.msg(res.msg || '操作成功', {icon: 1});
                setTimeout(function(){
                    layer.closeAll();
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || '操作失败', {icon: 2});
                submitBtn.text(originalText).prop('disabled', false);
            }
        }, 'json').fail(function(xhr, status, error){
            console.error('提交失败:', error);
            layer.msg('网络请求失败，请重试', {icon: 2});
            submitBtn.text(originalText).prop('disabled', false);
        });

        return false;
    });

    // 绑定表单事件函数
    window.bindFormEvents = function() {
        // 重新绑定目标类型变化事件
        form.on('select(targetType)', function(data){
            var targetType = data.value;
            var targetIdInput = $('input[name="target_id"]');

            // 根据目标类型给出提示
            switch(targetType) {
                case 'role':
                    targetIdInput.attr('placeholder', '角色ID (0=普通用户, 1=分会长, 2=管理员)');
                    break;
                case 'user':
                    targetIdInput.attr('placeholder', '用户ID');
                    break;
                case 'branch':
                    targetIdInput.attr('placeholder', '分会ID');
                    break;
                case 'global':
                    targetIdInput.attr('placeholder', '全局配置请填写0');
                    targetIdInput.val('0');
                    break;
                default:
                    targetIdInput.attr('placeholder', '目标ID');
            }
        });

        // 重新绑定计算方式变化事件
        form.on('select(calculationMethod)', function(data){
            var method = data.value;
            var baseAmountDiv = $('#baseAmountDiv');
            var percentageDiv = $('#percentageDiv');

            if (method === 'fixed') {
                baseAmountDiv.show();
                percentageDiv.hide();
                $('input[name="base_amount"]').attr('required', true);
                $('input[name="percentage_rate"]').attr('required', false);
            } else if (method === 'percentage') {
                baseAmountDiv.hide();
                percentageDiv.show();
                $('input[name="base_amount"]').attr('required', false);
                $('input[name="percentage_rate"]').attr('required', true);
            } else {
                baseAmountDiv.hide();
                percentageDiv.hide();
                $('input[name="base_amount"]').attr('required', false);
                $('input[name="percentage_rate"]').attr('required', false);
            }
        });

        // 重新绑定表单提交事件
        form.on('submit(configSubmit)', function(data){
            var formData = data.field;

            console.log('提交表单数据:', formData);

            // 验证计算方式对应的字段
            if (formData.calculation_method === 'fixed' && !formData.base_amount) {
                layer.msg('请填写固定金额');
                return false;
            }
            if (formData.calculation_method === 'percentage' && !formData.percentage_rate) {
                layer.msg('请填写百分比率');
                return false;
            }

            // 显示提交中状态
            var submitBtn = $('button[lay-filter="configSubmit"]');
            var originalText = submitBtn.text();
            submitBtn.text('提交中...').prop('disabled', true);

            $.post('<?php echo url("commission/config_management"); ?>', formData, function(res){
                console.log('提交响应:', res);

                if (res.status === 'ok') {
                    layer.msg(res.msg || '操作成功', {icon: 1});
                    setTimeout(function(){
                        layer.closeAll();
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg || '操作失败', {icon: 2});
                    submitBtn.text(originalText).prop('disabled', false);
                }
            }, 'json').fail(function(xhr, status, error){
                console.error('提交失败:', error);
                layer.msg('网络请求失败，请重试', {icon: 2});
                submitBtn.text(originalText).prop('disabled', false);
            });

            return false;
        });
    };
});

// 调试函数
function debugLayerInteraction() {
    console.log('=== 弹窗交互调试信息 ===');
    console.log('当前弹窗数量:', $('.layui-layer').length);

    $('.layui-layer').each(function(index, element) {
        var $layer = $(element);
        console.log('弹窗 ' + index + ':', {
            'z-index': $layer.css('z-index'),
            'pointer-events': $layer.css('pointer-events'),
            'display': $layer.css('display'),
            'position': $layer.css('position')
        });

        var $content = $layer.find('.layui-layer-content');
        if ($content.length > 0) {
            console.log('弹窗内容 ' + index + ':', {
                'z-index': $content.css('z-index'),
                'pointer-events': $content.css('pointer-events'),
                'position': $content.css('position')
            });
        }
    });

    console.log('遮罩层信息:');
    $('.layui-layer-shade').each(function(index, element) {
        var $shade = $(element);
        console.log('遮罩 ' + index + ':', {
            'z-index': $shade.css('z-index'),
            'pointer-events': $shade.css('pointer-events'),
            'display': $shade.css('display')
        });
    });
    console.log('=== 调试信息结束 ===');
}

// 新增配置
function addConfig() {
    // 重置表单
    document.getElementById('configFormData').reset();
    document.getElementById('configId').value = '';
    document.getElementById('formAction').value = 'add';

    // 隐藏金额输入框
    document.getElementById('baseAmountDiv').style.display = 'none';
    document.getElementById('percentageDiv').style.display = 'none';

    // 重置必填属性
    $('#base_amount').attr('required', false);
    $('#percentage_rate').attr('required', false);

    layer.open({
        type: 1,
        title: '新增佣金配置',
        content: $('#configForm').html(),
        area: ['800px', '600px'],
        zIndex: 19891014,
        shade: [0.3, '#000'],
        shadeClose: true,
        maxmin: false,
        move: false,
        success: function(layero, index){
            console.log('新增配置弹窗已打开');

            // 确保弹窗可交互
            layero.css({
                'z-index': 19891014,
                'pointer-events': 'auto'
            });

            // 确保弹窗内容可交互
            layero.find('.layui-layer-content').css({
                'pointer-events': 'auto',
                'position': 'relative',
                'z-index': 1
            });

            // 重新渲染表单
            layui.form.render();

            // 重新绑定表单事件
            bindFormEvents();

            // 调试弹窗交互
            setTimeout(function() {
                debugLayerInteraction();
            }, 200);
        },
        end: function(){
            console.log('新增配置弹窗已关闭');
        }
    });
}

// 编辑配置
function editConfig(id) {
    console.log('开始编辑配置，ID:', id);

    // 显示加载提示
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    // 获取配置详情
    $.post('<?php echo url("commission/config_management"); ?>', {
        action: 'get_config',
        id: id
    }, function(res){
        layer.close(loadingIndex);

        console.log('获取配置详情响应:', res);

        if (res.status === 'ok') {
            var config = res.data;
            console.log('配置数据:', config);

            // 打开弹窗
            layer.open({
                type: 1,
                title: '编辑佣金配置',
                content: $('#configForm').html(),
                area: ['800px', '600px'],
                zIndex: 19891014,
                shade: [0.3, '#000'],
                shadeClose: true,
                maxmin: false,
                move: false,
                success: function(layero, index){
                    console.log('编辑弹窗已打开，开始填充数据');

                    // 确保弹窗可交互
                    layero.css({
                        'z-index': 19891014,
                        'pointer-events': 'auto'
                    });

                    // 确保弹窗内容可交互
                    layero.find('.layui-layer-content').css({
                        'pointer-events': 'auto',
                        'position': 'relative',
                        'z-index': 1
                    });

                    // 🔧 修复：使用setTimeout确保DOM已渲染，优化数据回填逻辑
                    setTimeout(function(){
                        console.log('开始填充表单数据，配置信息:', config);

                        // 填充基础表单数据
                        layero.find('#configId').val(config.id || '');
                        layero.find('#formAction').val('edit');
                        layero.find('#config_name').val(config.config_name || '');
                        layero.find('#target_id').val(config.target_id || '');
                        layero.find('#base_amount').val(config.base_amount || '');
                        layero.find('#percentage_rate').val(config.percentage_rate || '');
                        layero.find('#min_amount').val(config.min_amount || '');
                        layero.find('#max_amount').val(config.max_amount || '');
                        layero.find('#available_delay_days').val(config.available_delay_days || 0);
                        layero.find('#priority').val(config.priority || 0);
                        layero.find('#effective_date').val(config.effective_date || '');
                        layero.find('#expiry_date').val(config.expiry_date || '');

                        // 🔧 修复：直接设置下拉框的值，而不是使用layui.form.val
                        layero.find('select[name="target_type"]').val(config.target_type || '');
                        layero.find('select[name="commission_type"]').val(config.commission_type || '');
                        layero.find('select[name="calculation_method"]').val(config.calculation_method || '');
                        layero.find('select[name="settlement_cycle"]').val(config.settlement_cycle || 'realtime');

                        // 根据计算方式显示对应的输入框
                        var calculationMethod = config.calculation_method || '';
                        console.log('计算方式:', calculationMethod);

                        if (calculationMethod === 'fixed') {
                            layero.find('#baseAmountDiv').show();
                            layero.find('#percentageDiv').hide();
                            layero.find('#base_amount').attr('required', true);
                            layero.find('#percentage_rate').attr('required', false);
                        } else if (calculationMethod === 'percentage') {
                            layero.find('#baseAmountDiv').hide();
                            layero.find('#percentageDiv').show();
                            layero.find('#base_amount').attr('required', false);
                            layero.find('#percentage_rate').attr('required', true);
                        } else {
                            layero.find('#baseAmountDiv').show();
                            layero.find('#percentageDiv').show();
                            layero.find('#base_amount').attr('required', false);
                            layero.find('#percentage_rate').attr('required', false);
                        }

                        // 🔧 修复：延迟渲染表单，确保数据已填充
                        setTimeout(function() {
                            layui.form.render();
                            console.log('表单渲染完成');
                        }, 50);

                        // 重新绑定表单事件
                        bindFormEvents();

                        console.log('表单数据填充完成');

                        // 调试弹窗交互
                        setTimeout(function() {
                            debugLayerInteraction();
                        }, 200);
                    }, 150);
                },
                end: function(){
                    console.log('编辑配置弹窗已关闭');
                }
            });
        } else {
            layer.msg(res.msg || '获取配置详情失败', {icon: 2});
        }
    }, 'json').fail(function(xhr, status, error){
        layer.close(loadingIndex);
        console.error('获取配置详情失败:', error);
        layer.msg('网络请求失败，请重试', {icon: 2});
    });
}

// 切换状态
function toggleStatus(id, currentStatus) {
    var action = currentStatus == 1 ? '停用' : '启用';
    layer.confirm('确定要' + action + '这个配置吗？', {
        icon: 3,
        title: '确认操作'
    }, function(index){
        var loadingIndex = layer.load(2);

        $.post('<?php echo url("commission/config_management"); ?>', {
            action: 'toggle_status',
            id: id
        }, function(res){
            layer.close(loadingIndex);

            if (res.status === 'ok') {
                layer.msg(res.msg || action + '成功', {icon: 1});
                setTimeout(function(){
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || action + '失败', {icon: 2});
            }
        }, 'json').fail(function(){
            layer.close(loadingIndex);
            layer.msg('网络请求失败，请重试', {icon: 2});
        });

        layer.close(index);
    });
}

// 删除配置
function deleteConfig(id) {
    layer.confirm('确定要删除这个配置吗？删除后不可恢复！', {
        icon: 0,
        title: '危险操作'
    }, function(index){
        var loadingIndex = layer.load(2);

        $.post('<?php echo url("commission/config_management"); ?>', {
            action: 'delete',
            id: id
        }, function(res){
            layer.close(loadingIndex);

            if (res.status === 'ok') {
                layer.msg(res.msg || '删除成功', {icon: 1});
                setTimeout(function(){
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || '删除失败', {icon: 2});
            }
        }, 'json').fail(function(){
            layer.close(loadingIndex);
            layer.msg('网络请求失败，请重试', {icon: 2});
        });

        layer.close(index);
    });
}
</script>
