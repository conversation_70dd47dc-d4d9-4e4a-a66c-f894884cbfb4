<script setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad, onShow, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app";
import { userget_share_records } from "@/api";
import { store } from "@/store";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";
import myTitle from "@/components/myTitle.vue";

const shareRecords = ref([]);
const loading = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = 20;
const total = ref(0);

onLoad(() => {
  getShareRecords();
});

onShow(() => {
  // 页面显示时刷新数据
  refreshData();
});

onPullDownRefresh(() => {
  refreshData();
});

onReachBottom(() => {
  if (!finished.value && !loading.value) {
    loadMore();
  }
});

// 获取分享记录
const getShareRecords = async (isRefresh = false) => {
  if (loading.value) return;

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后查看分享记录')) {
    return;
  }

  loading.value = true;

  try {
    const res = await userget_share_records({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      page: isRefresh ? 1 : page.value,
      page_size: pageSize
    });

    if (res?.status === 'ok') {
      const newRecords = res.data?.list || [];

      if (isRefresh) {
        shareRecords.value = newRecords;
        page.value = 1;
        finished.value = false;
      } else {
        shareRecords.value = [...shareRecords.value, ...newRecords];
      }

      total.value = res.data?.total || 0;

      // 检查是否还有更多数据
      if (newRecords.length < pageSize) {
        finished.value = true;
      } else {
        page.value++;
      }
    } else {
      uni.$u.toast(res?.msg || '获取分享记录失败');
    }
  } catch (error) {
    console.error('获取分享记录失败:', error);
    uni.$u.toast('获取分享记录失败');
  } finally {
    loading.value = false;
    uni.stopPullDownRefresh();
  }
};

// 刷新数据
const refreshData = () => {
  page.value = 1;
  finished.value = false;
  getShareRecords(true);
};

// 加载更多
const loadMore = () => {
  getShareRecords(false);
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);

  if (isNaN(time.getTime())) {
    return '时间格式错误';
  }

  return time.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '未领取',
    1: '已领取',
    2: '已过期'
  };
  return statusMap[status] || '未知';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    0: '#FF9500',
    1: '#6AC086',
    2: '#999'
  };
  return colorMap[status] || '#999';
};

// 复制分享链接
const copyShareLink = (record) => {
  if (!record.share_code) {
    uni.$u.toast('分享码不存在');
    return;
  }

  // 获取当前域名或使用默认值
  const domain = window?.location?.host || 'your-domain.com';
  const protocol = window?.location?.protocol || 'https:';
  const shareUrl = `${protocol}//${domain}/pages/bundle/user/trialClaim?code=${record.share_code}`;

  uni.setClipboardData({
    data: shareUrl,
    success: () => {
      uni.$u.toast('分享链接已复制到剪贴板');
    },
    fail: () => {
      uni.$u.toast('复制失败');
    }
  });
};
</script>

<template>
  <view class="page">
    <myTitle
      title="分享记录"
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      color="#ffffff"
      :blod="true"
    ></myTitle>

    <view class="records-container">
      <!-- 统计信息 -->
      <view v-if="total > 0" class="stats-card">
        <text class="stats-text">共 {{ total }} 条分享记录</text>
      </view>

      <!-- 分享记录列表 -->
      <view v-if="shareRecords.length > 0" class="records-list">
        <view
          v-for="(item, index) in shareRecords"
          :key="item.id"
          class="record-item"
        >
          <!-- 记录头部 -->
          <view class="record-header">
            <view class="record-info">
              <text class="record-title">体验会员分享</text>
              <text class="record-time">{{ formatTime(item.share_time) }}</text>
            </view>
            <view class="record-status" :style="{ color: getStatusColor(item.status) }">
              {{ getStatusText(item.status) }}
            </view>
          </view>

          <!-- 记录内容 -->
          <view class="record-content">
            <view class="record-detail">
              <text class="detail-label">体验天数：</text>
              <text class="detail-value">{{ item.trial_days }}天</text>
            </view>
            <view class="record-detail">
              <text class="detail-label">分享码：</text>
              <text class="detail-value">{{ item.share_code }}</text>
            </view>
            <view v-if="item.expire_time" class="record-detail">
              <text class="detail-label">过期时间：</text>
              <text class="detail-value">{{ formatTime(item.expire_time) }}</text>
            </view>
            <view v-if="item.receive_time" class="record-detail">
              <text class="detail-label">领取时间：</text>
              <text class="detail-value">{{ formatTime(item.receive_time) }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view v-if="item.status === 0" class="record-actions">
            <button class="action-btn copy-btn" @click="copyShareLink(item)">
              复制分享链接
            </button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-state">
        <u-icon name="share-square" color="#ccc" size="120rpx"></u-icon>
        <text class="empty-text">暂无分享记录</text>
        <text class="empty-desc">您的分享记录将在这里显示</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading && shareRecords.length === 0" class="loading-state">
        <u-loading-icon mode="circle" color="#6AC086" size="60rpx"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading && shareRecords.length > 0" class="load-more">
        <u-loading-icon mode="circle" color="#6AC086" size="40rpx"></u-loading-icon>
        <text class="load-more-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="finished && shareRecords.length > 0" class="no-more">
        <text class="no-more-text">没有更多记录了</text>
      </view>
    </view>
  </view>
</template>

<style lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
  padding-bottom: 40rpx;
}

.records-container {
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
}

.stats-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-status {
  font-size: 24rpx;
  font-weight: 500;
  background: rgba(106, 192, 134, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.record-content {
  margin-bottom: 16rpx;
}

.record-detail {
  display: flex;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: none;

  &.copy-btn {
    background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
    color: #fff;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
  margin: 24rpx 0 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  line-height: 1.5;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
  margin-top: 16rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style>
