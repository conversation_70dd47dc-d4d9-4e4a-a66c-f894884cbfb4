/**
 * 基于Painter的分享图片生成工具
 * 替换原有的Canvas实现
 */

import { createCardShareConfig, createFeedShareConfig, createDefaultShareConfig } from './painterConfig';

/**
 * 使用Painter生成分享图片
 * @param {Object} options 配置选项
 * @param {String} options.template 模板类型：card, feed, default
 * @param {Object} options.data 数据对象
 * @returns {Promise<String>} 返回生成的图片临时路径
 */
export const generateShareImageWithPainter = (options) => {
  return new Promise((resolve, reject) => {
    const { template = 'default', data = {} } = options;

    console.log('开始使用Painter生成分享图片:', { template, data });

    try {
      // 根据模板类型生成配置
      let painterConfig;
      switch (template) {
        case 'card':
          painterConfig = createCardShareConfig(data);
          break;
        case 'feed':
          painterConfig = createFeedShareConfig(data);
          break;
        default:
          painterConfig = createDefaultShareConfig(data);
      }

      console.log('Painter配置生成完成:', painterConfig);

      // 创建临时的Painter实例
      const painterId = `painter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      // 动态创建Painter组件
      const painterComponent = createPainterComponent(painterId, painterConfig);
      
      // 监听生成结果
      painterComponent.onSuccess = (tempFilePath) => {
        console.log('Painter生成图片成功:', tempFilePath);
        // 清理临时组件
        cleanupPainterComponent(painterId);
        resolve(tempFilePath);
      };

      painterComponent.onFail = (error) => {
        console.error('Painter生成图片失败:', error);
        // 清理临时组件
        cleanupPainterComponent(painterId);
        reject(new Error(error.errMsg || '图片生成失败'));
      };

      // 开始生成
      painterComponent.generate();

    } catch (error) {
      console.error('Painter配置生成失败:', error);
      reject(error);
    }
  });
};

/**
 * 获取可用的Canvas ID
 * @returns {String} Canvas ID
 */
function getAvailableCanvasId() {
  // 优先级顺序：share-popup-canvas > share-canvas > 默认
  const candidateIds = ['share-popup-canvas', 'share-canvas', 'painter-canvas'];

  for (const canvasId of candidateIds) {
    try {
      const query = uni.createSelectorQuery();
      query.select(`#${canvasId}`)
        .boundingClientRect()
        .exec((res) => {
          if (res && res[0] && res[0].width > 0) {
            console.log(`找到可用Canvas: ${canvasId}`);
            return canvasId;
          }
        });
    } catch (error) {
      console.warn(`检查Canvas ${canvasId} 失败:`, error);
    }
  }

  // 默认返回第一个候选ID
  console.log('使用默认Canvas ID:', candidateIds[0]);
  return candidateIds[0];
}

/**
 * 创建临时Painter组件 - 微信小程序兼容版本
 * @param {String} painterId 组件ID
 * @param {Object} config Painter配置
 * @returns {Object} Painter组件实例
 */
function createPainterComponent(painterId, config) {
  // 动态选择可用的canvas ID
  const canvasId = getAvailableCanvasId();

  console.log('创建Painter组件，使用Canvas ID:', canvasId);

  const component = {
    painterId,
    canvasId,
    config,
    onSuccess: null,
    onFail: null,
    screenWidth: 375, // 默认屏幕宽度

    async init() {
      try {
        // 异步获取系统信息
        const systemInfo = await uni.getSystemInfo();
        this.screenWidth = systemInfo.windowWidth || 375;
        console.log('获取屏幕宽度成功:', this.screenWidth);
      } catch (error) {
        console.warn('获取系统信息失败，使用默认值:', error);
        this.screenWidth = 375;
      }
    },

    async generate() {
      try {
        console.log('开始生成图片，Canvas ID:', this.canvasId);

        // 初始化系统信息
        await this.init();

        // 使用uni-app的canvas API绘制
        await this.drawWithUniCanvas();
      } catch (error) {
        console.error('图片生成失败:', error);
        if (this.onFail) {
          this.onFail({ errMsg: error.message });
        }
      }
    },

    // 检查Canvas元素是否存在
    async checkCanvasExists() {
      return new Promise((resolve) => {
        // 添加超时处理
        const timeout = setTimeout(() => {
          console.warn('Canvas检查超时');
          resolve(false);
        }, 3000);

        const query = uni.createSelectorQuery();
        query.select(`#${this.canvasId}`)
          .boundingClientRect()
          .exec((res) => {
            clearTimeout(timeout);
            const exists = res && res[0] && res[0].width > 0 && res[0].height > 0;
            console.log('Canvas元素检查结果:', {
              canvasId: this.canvasId,
              exists,
              rect: res[0],
              timestamp: new Date().toISOString()
            });
            resolve(exists);
          });
      });
    },

    // 获取Canvas 2D节点
    async getCanvas2DNode() {
      return new Promise((resolve, reject) => {
        // 添加超时处理
        const timeout = setTimeout(() => {
          console.error('Canvas 2D节点获取超时');
          reject(new Error('Canvas 2D节点获取超时'));
        }, 5000);

        const query = uni.createSelectorQuery();
        query.select(`#${this.canvasId}`)
          .fields({ node: true, size: true })
          .exec((res) => {
            clearTimeout(timeout);
            console.log('Canvas 2D节点查询结果:', {
              canvasId: this.canvasId,
              result: res,
              hasNode: res && res[0] && res[0].node,
              timestamp: new Date().toISOString()
            });

            if (res && res[0] && res[0].node) {
              const node = res[0].node;
              console.log('Canvas 2D节点属性:', {
                width: node.width,
                height: node.height,
                type: typeof node,
                constructor: node.constructor.name
              });
              resolve(node);
            } else {
              const errorMsg = `Canvas 2D节点获取失败: ${JSON.stringify(res)}`;
              console.error(errorMsg);
              reject(new Error(errorMsg));
            }
          });
      });
    },

    // Canvas 2D绘制方法
    async drawWithCanvas2D(ctx) {
      try {
        console.log('使用Canvas 2D API绘制');

        // 清空画布
        const { width, height } = this.config;
        const canvasWidth = parseInt(width) || 750;
        const canvasHeight = parseInt(height) || 1334;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 绘制背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制内容
        await this.drawViews(ctx, this.config.views || [], true);

        console.log('Canvas 2D绘制完成，准备转换为图片...');

        // 直接转换为图片（Canvas 2D不需要draw调用）
        await this.convertToImage(ctx, true);

      } catch (error) {
        console.error('Canvas 2D绘制失败:', error);
        throw error;
      }
    },

    // 传统Canvas绘制方法
    async drawWithTraditionalCanvas(ctx) {
      try {
        console.log('使用传统Canvas API绘制');

        // 设置Canvas尺寸
        const { width, height } = this.config;
        const canvasWidth = parseInt(width) || 750;
        const canvasHeight = parseInt(height) || 1334;

        // 绘制背景
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制内容
        await this.drawViews(ctx, this.config.views || [], false);

        console.log('传统Canvas绘制完成，准备draw...');

        // 调用draw并转换为图片
        ctx.draw(false, async () => {
          console.log('Canvas draw完成，开始转换为临时文件...');
          try {
            await this.convertToImage(ctx, false);
          } catch (error) {
            console.error('图片转换失败:', error);
            if (this.onFail) {
              this.onFail({ errMsg: error.message });
            }
          }
        });

      } catch (error) {
        console.error('传统Canvas绘制失败:', error);
        throw error;
      }
    },

    async drawWithUniCanvas() {
      try {
        console.log('开始Canvas绘制，配置:', this.config);

        // 首先检查Canvas元素是否存在
        const canvasExists = await this.checkCanvasExists();
        if (!canvasExists) {
          throw new Error(`Canvas元素不存在，ID: ${this.canvasId}`);
        }

        // 尝试使用Canvas 2D API
        let ctx = null;
        let isCanvas2D = false;

        try {
          const canvasNode = await this.getCanvas2DNode();
          if (canvasNode) {
            ctx = canvasNode.getContext('2d');
            isCanvas2D = true;
            console.log('使用Canvas 2D API');

            // 设置Canvas 2D尺寸
            const { width, height } = this.config;
            const canvasWidth = parseInt(width) || 750;
            const canvasHeight = parseInt(height) || 1334;

            // 获取设备像素比
            const systemInfo = await uni.getSystemInfo();
            const pixelRatio = systemInfo.pixelRatio || 2;

            canvasNode.width = canvasWidth * pixelRatio;
            canvasNode.height = canvasHeight * pixelRatio;
            ctx.scale(pixelRatio, pixelRatio);

            console.log('Canvas 2D设置完成:', { canvasWidth, canvasHeight, pixelRatio });
          }
        } catch (canvas2DError) {
          console.warn('Canvas 2D API不可用，降级到传统API:', canvas2DError);
        }

        // 降级到传统Canvas API
        if (!ctx) {
          ctx = uni.createCanvasContext(this.canvasId);
          isCanvas2D = false;
          console.log('使用传统Canvas API');
        }

        if (!ctx) {
          throw new Error(`无法创建Canvas上下文，Canvas ID: ${this.canvasId}`);
        }

        // 根据API类型选择绘制方法
        if (isCanvas2D) {
          await this.drawWithCanvas2D(ctx);
        } else {
          await this.drawWithTraditionalCanvas(ctx);
        }

        // 设置canvas尺寸
        const { width, height } = this.config;
        const canvasWidth = parseInt(width) || 750;
        const canvasHeight = parseInt(height) || 1334;

        console.log('Canvas尺寸:', { canvasWidth, canvasHeight });

        // 绘制背景
        if (this.config.background) {
          console.log('绘制背景:', this.config.background);
          ctx.setFillStyle(this.config.background);
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        }

        // 绘制所有视图元素
        if (this.config.views && this.config.views.length > 0) {
          console.log('开始绘制视图元素，数量:', this.config.views.length);
          for (let i = 0; i < this.config.views.length; i++) {
            const view = this.config.views[i];
            console.log(`绘制第${i + 1}个视图:`, view);
            await this.drawView(ctx, view);
          }
        } else {
          console.warn('没有视图元素需要绘制');
        }
      } catch (error) {
        console.error('Canvas绘制过程出错:', error);
        if (this.onFail) {
          this.onFail({ errMsg: error.message });
        }
      }
    },

    // 统一的图片转换方法
    async convertToImage(ctx, isCanvas2D) {
      return new Promise((resolve, reject) => {
        console.log(`开始转换图片，使用${isCanvas2D ? 'Canvas 2D' : '传统Canvas'} API`);

        // 添加超时处理
        const timeout = setTimeout(() => {
          console.error('Canvas转换图片超时（15秒）');
          reject(new Error('Canvas转换图片超时'));
        }, 15000); // 增加到15秒超时

        // 验证Canvas状态
        if (!ctx) {
          clearTimeout(timeout);
          reject(new Error('Canvas上下文无效'));
          return;
        }

        // 准备转换参数
        const { width, height } = this.config;
        const canvasWidth = parseInt(width) || 750;
        const canvasHeight = parseInt(height) || 1334;

        const convertOptions = {
          x: 0,
          y: 0,
          width: canvasWidth,
          height: canvasHeight,
          destWidth: canvasWidth,
          destHeight: canvasHeight,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            clearTimeout(timeout);
            console.log('Canvas转换图片成功:', {
              tempFilePath: res.tempFilePath,
              size: `${canvasWidth}x${canvasHeight}`,
              api: isCanvas2D ? 'Canvas 2D' : '传统Canvas'
            });

            if (this.onSuccess) {
              this.onSuccess(res.tempFilePath);
            }
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            clearTimeout(timeout);
            console.error('Canvas转换图片失败:', err);

            // 详细错误信息
            const errorMsg = err.errMsg || err.message || '未知错误';
            console.error('错误详情:', {
              error: errorMsg,
              canvasId: this.canvasId,
              api: isCanvas2D ? 'Canvas 2D' : '传统Canvas',
              canvasSize: `${canvasWidth}x${canvasHeight}`
            });

            if (this.onFail) {
              this.onFail(err);
            }
            reject(new Error(errorMsg));
          }
        };

        // 根据API类型设置不同参数
        if (isCanvas2D) {
          // Canvas 2D模式：使用canvas对象
          if (ctx.canvas) {
            convertOptions.canvas = ctx.canvas;
            console.log('使用Canvas 2D转换，canvas对象:', ctx.canvas);
          } else {
            console.error('Canvas 2D模式下canvas对象不存在，降级到传统模式');
            convertOptions.canvasId = this.canvasId;
          }
        } else {
          // 传统模式：使用canvasId
          convertOptions.canvasId = this.canvasId;
          console.log('使用传统Canvas转换，canvasId:', this.canvasId);
        }

        // 执行转换
        console.log('调用uni.canvasToTempFilePath，参数:', convertOptions);

        // 添加延迟确保绘制完成
        setTimeout(() => {
          try {
            uni.canvasToTempFilePath(convertOptions);
          } catch (apiError) {
            clearTimeout(timeout);
            console.error('canvasToTempFilePath API调用异常:', apiError);
            reject(new Error(`API调用失败: ${apiError.message}`));
          }
        }, isCanvas2D ? 100 : 500); // Canvas 2D延迟更短
      });
    },

    // 绘制视图数组
    async drawViews(ctx, views, isCanvas2D) {
      for (const view of views) {
        await this.drawView(ctx, view, isCanvas2D);
      }
    },

    async drawView(ctx, view, isCanvas2D) {
      const { type, css = {} } = view;

      // 解析位置和尺寸
      const x = this.parseSize(css.left || css.x || 0);
      const y = this.parseSize(css.top || css.y || 0);
      const width = this.parseSize(css.width || 0);
      const height = this.parseSize(css.height || 0);

      switch (type) {
        case 'rect':
          this.drawRect(ctx, { x, y, width, height, ...css }, isCanvas2D);
          break;
        case 'text':
          this.drawText(ctx, { x, y, width, height, text: view.text, ...css }, isCanvas2D);
          break;
        case 'image':
          await this.drawImage(ctx, { x, y, width, height, url: view.url, ...css }, isCanvas2D);
          break;
      }
    },

    drawRect(ctx, options, isCanvas2D) {
      const { x, y, width, height, background, borderRadius } = options;

      if (background) {
        if (isCanvas2D) {
          ctx.fillStyle = background;
        } else {
          ctx.setFillStyle(background);
        }

        if (borderRadius) {
          // 绘制圆角矩形
          const radius = this.parseSize(borderRadius);
          this.drawRoundRect(ctx, x, y, width, height, radius);
          ctx.fill();
        } else {
          ctx.fillRect(x, y, width, height);
        }
      }
    },

    drawText(ctx, options, isCanvas2D) {
      const { x, y, width, text, fontSize, color, textAlign, maxLines } = options;

      if (!text) return;

      const fontSizePx = this.parseSize(fontSize || 28);

      if (isCanvas2D) {
        ctx.fillStyle = color || '#000000';
        ctx.font = `${fontSizePx}px sans-serif`;
        ctx.textAlign = textAlign || 'left';
        ctx.textBaseline = 'top';
      } else {
        ctx.setFillStyle(color || '#000000');
        ctx.setFontSize(fontSizePx);
        ctx.setTextAlign(textAlign || 'left');
      }

      // 简单的文本绘制（可以扩展支持多行）
      if (maxLines && maxLines > 1) {
        this.drawMultiLineText(ctx, text, x, y, width, maxLines, options, isCanvas2D);
      } else {
        const textY = isCanvas2D ? y : y + fontSizePx;
        ctx.fillText(text, x, textY);
      }
    },

    async drawImage(ctx, options, isCanvas2D) {
      const { x, y, width, height, url, borderRadius } = options;

      if (!url) return;

      try {
        // 下载图片到本地
        const res = await uni.downloadFile({ url });

        if (borderRadius) {
          // 绘制圆角图片
          ctx.save();
          const radius = this.parseSize(borderRadius);
          this.drawRoundRect(ctx, x, y, width, height, radius);
          ctx.clip();
          ctx.drawImage(res.tempFilePath, x, y, width, height);
          ctx.restore();
        } else {
          ctx.drawImage(res.tempFilePath, x, y, width, height);
        }
      } catch (error) {
        console.warn('图片加载失败:', url, error);
      }
    },

    drawRoundRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
    },

    drawMultiLineText(ctx, text, x, y, maxWidth, maxLines, options, isCanvas2D) {
      const { fontSize = 28, lineHeight } = options;
      const lineHeightPx = lineHeight ? this.parseSize(lineHeight) : this.parseSize(fontSize) * 1.2;
      
      const words = text.split('');
      let line = '';
      let lineCount = 0;
      
      for (let i = 0; i < words.length && lineCount < maxLines; i++) {
        const testLine = line + words[i];
        const metrics = ctx.measureText(testLine);
        
        if (metrics.width > maxWidth && line !== '') {
          ctx.fillText(line, x, y + lineCount * lineHeightPx);
          line = words[i];
          lineCount++;
        } else {
          line = testLine;
        }
      }
      
      if (lineCount < maxLines && line) {
        ctx.fillText(line, x, y + lineCount * lineHeightPx);
      }
    },

    parseSize(size) {
      if (typeof size === 'number') return size;
      if (typeof size === 'string') {
        if (size.endsWith('rpx')) {
          // 使用缓存的屏幕宽度，避免重复调用同步API
          const screenWidth = this.screenWidth || 375; // 默认值
          return parseInt(size) * (screenWidth / 750);
        }
        if (size.endsWith('px')) {
          return parseInt(size);
        }
        return parseInt(size) || 0;
      }
      return 0;
    }
  };

  return component;
}

/**
 * 清理临时Painter组件
 * @param {String} painterId 组件ID
 */
function cleanupPainterComponent(painterId) {
  try {
    const container = document.querySelector(`[data-painter-id="${painterId}"]`);
    if (container && container.parentNode) {
      container.parentNode.removeChild(container);
    }
  } catch (error) {
    console.warn('清理Painter组件失败:', error);
  }
}

/**
 * 兼容原有接口的分享图片生成函数
 * @param {Object} options 原有的配置选项
 * @returns {Promise<String>} 返回生成的图片临时路径
 */
export const generateShareImage = async (options) => {
  const {
    backgroundImage,
    content,
    author,
    authorAvatar,
    date,
    watermark,
    template = 'default',
    images = []
  } = options;

  // 转换为新的数据格式
  const data = {
    backgroundImage,
    content,
    author,
    authorAvatar,
    date,
    watermark,
    images
  };

  // 使用Painter生成图片
  return generateShareImageWithPainter({
    template,
    data
  });
};

/**
 * 保存图片到相册
 * @param {String} tempFilePath 临时文件路径
 * @returns {Promise}
 */
export const saveImageToAlbum = (tempFilePath) => {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: resolve,
      fail: reject
    });
  });
};
