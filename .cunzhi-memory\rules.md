# 开发规范和规则

- 不要使用leftJoin的ORM写法用原生sql，不要添加重试机制，错误补偿机制。修改数据库请提供sql给我，建立的新表和修改数据库需要将sql提供给我我来执行而不是改到huodong.sql。数据库查询代码写法一定要在已有代码中实现过！一定要参考已有的数据库表结构，前端页面一定要保持样式一致性
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 项目开发约束：不使用leftJoin的ORM写法用原生SQL，不添加重试机制和错误补偿机制，开发环境服务未启动生产环境为linux，数据库修改提供SQL语句不直接修改huodong.sql，新表和数据库修改需提供独立SQL，数据库查询必须参考已有代码实现，前端页面保持样式一致性，不添加降级处理机制，不写总结和报告文档，生成新功能前检查已有方法，使用中文回答
- 数据库操作规范：所有数据库修改必须指定数据库名为huodong，格式为`huodong`.`table_name`，提供独立SQL语句供手动执行，不直接修改huodong.sql文件，使用原生SQL不用ORM的leftJoin写法，必须参考已有代码实现模式，所有查询使用参数化查询防止SQL注入
- 项目开发严格规范：1.不使用leftJoin的ORM写法，必须用原生SQL；2.不添加重试机制和错误补偿机制；3.开发环境服务未启动，生产环境为linux；4.数据库修改提供SQL语句给用户执行，不直接修改huodong.sql；5.数据库查询必须参考已有代码实现；6.前端页面保持样式一致性；7.项目结构：后端API(huodongbao_api-PHP)、前端小程序(activity-treasure-master-uni-app)、PC管理端(huodongbao_admin)；8.不添加降级处理机制；9.不写总结文档和报告文档；10.生成新功能前检查是否存在可用老方法；11.用中文回答问题
- 发布页面设计规范：发布按钮应位于所有表单组件的右下方（页面底部右对齐或右下角固定定位），而不是导航栏中，确保用户完成所有输入后再进行发布操作
