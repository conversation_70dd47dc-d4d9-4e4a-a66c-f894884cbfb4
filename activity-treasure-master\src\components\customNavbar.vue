<template>
  <view class="custom-navbar" :style="{ background: bgColor, height: height }">
    <u-status-bar :bgColor="bgColor"></u-status-bar>
    <view class="navbar-content">
      <!-- 左侧返回按钮 -->
      <view class="navbar-left" @click="handleBack" v-if="showBack">
        <u-icon 
          :name="backIcon" 
          size="44rpx" 
          :color="iconColor"
        ></u-icon>
      </view>
      
      <!-- 中间标题 -->
      <view class="navbar-center">
        <text class="navbar-title" :style="{ color: titleColor, fontWeight: bold ? '700' : '400' }">
          {{ title }}
        </text>
      </view>
      
      <!-- 右侧操作区域 -->
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  // 标题文字
  title: {
    type: String,
    default: ''
  },
  // 背景色
  bgColor: {
    type: String,
    default: 'linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)'
  },
  // 导航栏高度
  height: {
    type: String,
    default: '200rpx'
  },
  // 标题颜色
  titleColor: {
    type: String,
    default: '#ffffff'
  },
  // 图标颜色
  iconColor: {
    type: String,
    default: '#ffffff'
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: true
  },
  // 返回按钮图标
  backIcon: {
    type: String,
    default: 'arrow-left'
  },
  // 返回类型：'back'返回上一页，'home'返回首页
  backType: {
    type: String,
    default: 'back'
  },
  // 标题是否加粗
  bold: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['back']);

const handleBack = () => {
  if (props.backType === 'home') {
    uni.reLaunch({ url: '/pages/index' });
  } else {
    uni.navigateBack({
      delta: 1,
      fail: () => uni.reLaunch({ url: '/pages/index' })
    });
  }
  emit('back');
};
</script>

<style scoped lang="less">
.custom-navbar {
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 var(--spacing-lg, 32rpx);
  position: relative;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navbar-left:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.navbar-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  max-width: 400rpx;
}

.navbar-title {
  font-size: 36rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
  height: 80rpx;
}
</style>
