-- 修复分会管理功能中的"未知分会"错误问题
-- 问题：用户215是分会长但branch_id字段为NULL，导致前端显示"未知分会"

-- 1. 更新用户215的branch_id字段，设置为0（对应总会）
UPDATE `huodong`.`user` 
SET `branch_id` = 0 
WHERE `uid` = 215 AND `role_type` = '1';

-- 2. 验证修复结果的查询语句
-- 查询用户215的信息
SELECT u.uid, u.nickname, u.role_type, u.branch_id, 
       b.branch_name, b.branch_location, b.branch_leader
FROM `huodong`.`user` u
LEFT JOIN `huodong`.`user_branch` b ON u.branch_id = b.branch_id
WHERE u.uid = 215;

-- 3. 查询所有分会长的分会关联情况
SELECT u.uid, u.nickname, u.role_type, u.branch_id, 
       b.branch_name, b.branch_location, b.branch_leader
FROM `huodong`.`user` u
LEFT JOIN `huodong`.`user_branch` b ON u.branch_id = b.branch_id
WHERE u.role_type = '1'
ORDER BY u.uid;

-- 4. 检查是否有其他分会长也存在类似问题
SELECT u.uid, u.nickname, u.role_type, u.branch_id
FROM `huodong`.`user` u
WHERE u.role_type = '1' AND u.branch_id IS NULL;
