<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { huodongget_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
import { getItem } from "@/utils";

const title = ref("");
const form = ref({});
const goods = ref([
  { num: 0, money: 20, zongMoney: 0 },
  { num: 0, money: 30, zongMoney: 0 }
]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
    onPageScroll,
    onReachBottom
);
const query = ref({ num: 0, money: "0.00"});
const height = ref("");

onLoad((e) => {
  if (e.title) title.value = e.title;
  form.value = store().$state.activeForm;
  form.value.is_tuijian = "ALL";
  form.value.status = 1;
  if (e.id) form.value.type_id = e.id;
  form.value.list_type = e.list_type || 1;
});
onReady(async () => {
  height.value = (await setListHeight()) - 10 + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  huodongget_list({
    page: mescroll.num,
    page_size: mescroll.size,
    ...form.value
  })
      .then((res) => {
        const curPageData = res.data || [];
        if (mescroll.num == 1) goods.value = [];
        goods.value = goods.value.concat(curPageData);
        console.log(goods);
        console.info(goods);
        mescroll.endBySize(curPageData.length, res.count);
      })
      .catch(() => {
        mescroll.endErr();
      });
};
</script>
<template>
  <view class="page">
    <myTitle img="jianbianTopBg.png" height="320rpx" :title="title"></myTitle>
    <view class="pa px30 w" style="top: 178rpx">
      <mescroll-uni
          class="list"
          :height="height"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @topclick="$event.scrollTo(0)"
      >
        <view
            class="pr ma py30 df w690 borderBottom"
            v-for="(val, i) in goods"
            :key="i"
            @click="navto(`/pages/bundle/index/activeInfo?id=${val.id}`)"
        >
          <view
              class="pa z20 ball r20"
              style="padding: 10rpx 14rpx; top: 46rpx; left: 20rpx"
          >
            <u-text
                size="18rpx"
                :text="
                getItem(
                  ['未开始', '报名中', '已结束'],
                  Date.now() * 1 < new Date(val.baoming_start_time) * 1
                    ? 0
                    : Date.now() * 1 > new Date(val.baoming_end_time) * 1
                    ? 2
                    : 1
                )
              "
            ></u-text>
          </view>
          <u-image
              width="414rpx"
              height="240rpx"
              :src="val.img_url"
              radius="30rpx"
          ></u-image>
          <view class="ml20 df fdc jcsb">
            <view class="df aic">
              <u-avatar
                  :src="val.user?.avatar"
                  mode="aspectFill"
                  size="50rpx"
              ></u-avatar>
              <u-text
                  margin="0 0 0 10rpx"
                  color="#333333"
                  size="22rpx"
                  lines="1"
                  :text="val.user?.nickname"
              ></u-text>
            </view>
            <u-text
                size="30rpx"
                :bold="true"
                lines="2"
                :text="val.name"
            ></u-text>
            <u-icon
                :name="`${store().$state.url}man.png`"
                size="20rpx"
                :label="`${val.baoming_num}人报名`"
                label-color="#AAAAAA"
                space="6rpx"
                label-size="22rpx"
            ></u-icon>
            <u-text
                :prefix-icon="`${store().$state.url}place.png`"
                icon-style="margin-right:6rpx;width:22rpx;height:22rpx;"
                :text="val.sheng + val.shi + val.qu + val.addr"
                lines="1"
                color="#AAAAAA"
                size="22rpx"
            ></u-text>
            <u-icon
                :name="`${store().$state.url}time.png`"
                size="20rpx"
                :label="val.start_time"
                space="6rpx"
                label-color="#AAAAAA"
                label-size="22rpx"
            ></u-icon>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
