<template>
  <view class="title">
    <view
      class="pr"
      :style="{
        background: bgColor,
        width: width,
        height: height,
      }"
    >
      <!-- 背景图 -->
      <image
        v-if="img"
        :style="{
          width: width,
          height: height,
          borderRadius: radius
        }"
        :src="img.indexOf('http') === -1 ? store().$state.url + img : img"
        :mode="mode"
        lazy-load
      ></image>
      <u-status-bar v-if="topBar && !img" :bgColor="bgColor"></u-status-bar>
      <!-- 标题 -->
      <view class="pa w" style="top: 0">
        <u-status-bar :bgColor="bgColor"></u-status-bar>
        <view
          class="df aic w pr"
          :class="[title ? 'jcc' : '']"
          :style="{ height: 88 + 'rpx', minHeight: 88 + 'rpx' }"
        >
          <!-- 返回按钮 -->
          <view
            class="pa z20 df aic jcc"
            style="top: 22rpx; left: 30rpx; width: 88rpx; height: 88rpx"
            v-if="backShow"
          >
            <u-icon :color="backColor" name="arrow-left" :size="size" @click="back" />
          </view>
          <!-- 标题 -->
          <view
            class="x32"
            :style="{ color, fontWeight: blod ? '700' : '400' }"
            v-if="title"
          >
            {{ title }}
          </view>
          <slot name="right"></slot>
          <slot></slot>
        </view>
        <!-- 搜索框 -->
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineComponent, watch, ref, reactive, nextTick } from "vue";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  img: {
    type: String,
  },
  width: {
    type: String,
    default: "750rpx",
  },
  height: {
    type: String,
    default: "128rpx",
  },
  bgColor: {
    type: String,
    default: "transparent",
  },
  color: {
    type: String,
    default: "#000",
  },
  blod: {
    type: Boolean,
    default: true,
  },
  backShow: {
    type: Boolean,
    default: true,
  },
  mode: {
    type: String,
    default: "aspectFill",
  },
  backColor: {
    type: String,
    default: "#000",
  },
  radius: {
    type: String,
  },
  back: {
    type: String,
    default: "",
  },
  topBar: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String,
    default: "44rpx",
  },
});

const rightHeight = ref();
nextTick(() => {
  rightHeight.value = pxToRpx(uni.getMenuButtonBoundingClientRect().height);
});
const back = () => {
  if (props.back == "switch") uni.reLaunch({ url: "/pages/index" });
  else
    uni.navigateBack({
      delta: 1,
      fail: (err) => uni.reLaunch({ url: "/pages/index" }),
    });
};
const pxToRpx = (px) => {
  // 使用新的 API 替代已废弃的 getSystemInfoSync
  const screenWidth = uni.getWindowInfo().windowWidth;
  return (750 * Number.parseInt(px)) / screenWidth;
};
</script>
<style scoped lang="less">
.title {
  position: relative;
  width: 100%;
  display: block;
  overflow: hidden;
  /* 确保导航栏有足够的最小高度 */
  min-height: 200rpx;
}

.title .pr {
  position: relative;
  display: block;
  /* 确保内容区域有足够的最小高度 */
  min-height: 200rpx;
}

.title .pa {
  position: absolute;
}

.title .w {
  width: 100%;
}

.title .df {
  display: flex;
}

.title .aic {
  align-items: center;
}

.title .jcc {
  justify-content: center;
}

.title .x32 {
  font-size: 32rpx;
}

.title .z20 {
  z-index: 20;
}

.title .jcc {
  justify-content: center;
}
</style>
