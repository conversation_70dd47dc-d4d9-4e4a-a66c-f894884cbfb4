<fieldset class="table-search-fieldset">
    <legend>活动收入记录管理</legend>
    
    <!-- 搜索表单 -->
    <form class="layui-form layui-form-pane" method="get">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部状态</option>
                        <option value="0" <?php echo ($filters['status'] ?? '') === '0' ? 'selected' : ''; ?>>待结算</option>
                        <option value="1" <?php echo ($filters['status'] ?? '') === '1' ? 'selected' : ''; ?>>可提取</option>
                        <option value="2" <?php echo ($filters['status'] ?? '') === '2' ? 'selected' : ''; ?>>提现中</option>
                        <option value="3" <?php echo ($filters['status'] ?? '') === '3' ? 'selected' : ''; ?>>已提现</option>
                        <option value="4" <?php echo ($filters['status'] ?? '') === '4' ? 'selected' : ''; ?>>已驳回</option>
                        <option value="5" <?php echo ($filters['status'] ?? '') === '5' ? 'selected' : ''; ?>>已冻结</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-inline">
                <label class="layui-form-label">活动ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_id" value="<?php echo htmlspecialchars($filters['activity_id'] ?? ''); ?>" placeholder="请输入活动ID" class="layui-input">
                </div>
            </div>
            
            <div class="layui-inline">
                <label class="layui-form-label">发布者ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="publisher_uid" value="<?php echo htmlspecialchars($filters['publisher_uid'] ?? ''); ?>" placeholder="请输入发布者ID" class="layui-input">
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">开始日期</label>
                <div class="layui-input-inline">
                    <input type="date" name="start_date" value="<?php echo htmlspecialchars($filters['start_date'] ?? ''); ?>" class="layui-input">
                </div>
            </div>
            
            <div class="layui-inline">
                <label class="layui-form-label">结束日期</label>
                <div class="layui-input-inline">
                    <input type="date" name="end_date" value="<?php echo htmlspecialchars($filters['end_date'] ?? ''); ?>" class="layui-input">
                </div>
            </div>
            
            <div class="layui-inline">
                <button type="submit" class="layui-btn">
                    <i class="layui-icon layui-icon-search"></i> 搜索
                </button>
                <a href="/commission/activity_income_records" class="layui-btn layui-btn-primary">
                    <i class="layui-icon layui-icon-refresh"></i> 重置
                </a>
            </div>
        </div>
    </form>
</fieldset>

<!-- 数据表格 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-table"></i> 活动收入记录列表
        <div style="float: right;">
            <span class="layui-badge layui-bg-blue">共 <?php echo $total; ?> 条记录</span>
        </div>
    </div>
    <div class="layui-card-body">
        <table class="layui-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>活动信息</th>
                    <th>发布者</th>
                    <th>订单号</th>
                    <th>订单金额</th>
                    <th>平台抽成</th>
                    <th>发布方收入</th>
                    <th>状态</th>
                    <th>创建时间</th>
                    <th>结算时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($records)): ?>
                    <?php foreach ($records as $record): ?>
                        <tr>
                            <td><?php echo $record['id']; ?></td>
                            <td>
                                <div style="max-width: 200px;">
                                    <div style="font-weight: bold; color: #1E9FFF;">
                                        ID: <?php echo $record['activity_id']; ?>
                                    </div>
                                    <div style="color: #666; font-size: 12px; margin-top: 2px;">
                                        <?php echo htmlspecialchars($record['activity_name'] ?? '未知活动'); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div style="font-weight: bold;">ID: <?php echo $record['publisher_uid']; ?></div>
                                    <div style="color: #666; font-size: 12px;">
                                        <?php echo htmlspecialchars($record['publisher_name'] ?? '未知用户'); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <code><?php echo $record['order_id']; ?></code>
                            </td>
                            <td>
                                <span style="color: #FF5722; font-weight: bold;">
                                    ¥<?php echo number_format($record['total_amount'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <span style="color: #FF9800; font-weight: bold;">
                                        ¥<?php echo number_format($record['platform_fee'], 2); ?>
                                    </span>
                                    <div style="color: #999; font-size: 12px;">
                                        (<?php echo $record['platform_fee_rate']; ?>%)
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="color: #4CAF50; font-weight: bold;">
                                    ¥<?php echo number_format($record['publisher_income'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $status_colors = [
                                    0 => '#FF9800', // 待结算
                                    1 => '#4CAF50', // 可提取
                                    2 => '#2196F3', // 提现中
                                    3 => '#9E9E9E', // 已提现
                                    4 => '#F44336', // 已驳回
                                    5 => '#795548'  // 已冻结
                                ];
                                $color = $status_colors[$record['status']] ?? '#999';
                                ?>
                                <span class="layui-badge" style="background-color: <?php echo $color; ?>;">
                                    <?php echo $record['status_text']; ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-size: 12px;">
                                    <?php echo date('Y-m-d H:i', strtotime($record['time'])); ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 12px;">
                                    <?php if ($record['settlement_time']): ?>
                                        <?php echo date('Y-m-d H:i', strtotime($record['settlement_time'])); ?>
                                    <?php else: ?>
                                        <span style="color: #999;">-</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="layui-btn-group">
                                    <button class="layui-btn layui-btn-xs" onclick="viewDetail(<?php echo $record['id']; ?>)">
                                        <i class="layui-icon layui-icon-search"></i> 详情
                                    </button>
                                    <?php if ($record['status'] == 1): ?>
                                        <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="freezeIncome(<?php echo $record['id']; ?>)">
                                            <i class="layui-icon layui-icon-pause"></i> 冻结
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="11" style="text-align: center; padding: 50px; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i>
                            <div style="margin-top: 10px;">暂无数据</div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <?php if ($total > $limit): ?>
            <div style="text-align: center; margin-top: 20px;">
                <?php
                $total_pages = ceil($total / $limit);
                $query_params = $_GET;
                ?>
                <div class="layui-box layui-laypage layui-laypage-default">
                    <?php if ($page > 1): ?>
                        <?php $query_params['page'] = $page - 1; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" class="layui-laypage-prev">上一页</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <?php $query_params['page'] = $i; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" 
                           class="<?php echo $i == $page ? 'layui-laypage-curr' : ''; ?>">
                            <em><?php echo $i; ?></em>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <?php $query_params['page'] = $page + 1; ?>
                        <a href="?<?php echo http_build_query($query_params); ?>" class="layui-laypage-next">下一页</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// 查看详情
function viewDetail(id) {
    layer.open({
        type: 2,
        title: '活动收入详情',
        shadeClose: true,
        shade: 0.8,
        area: ['800px', '600px'],
        content: '/commission/activity_income_detail?id=' + id
    });
}

// 冻结收入
function freezeIncome(id) {
    layer.confirm('确定要冻结这笔收入吗？', {
        btn: ['确定', '取消']
    }, function(index) {
        $.post('/commission/freeze_activity_income', {
            id: id
        }, function(res) {
            if (res.status === 'ok') {
                layer.msg('冻结成功', {icon: 1});
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                layer.msg(res.msg || '冻结失败', {icon: 2});
            }
            layer.close(index);
        }, 'json');
    });
}

layui.use(['form'], function(){
    var form = layui.form;
    form.render();
});
</script>
