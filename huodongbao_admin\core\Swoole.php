<?php
namespace core;
use Swoole\Database\PDOConfig;
use Swoole\Database\PDOPool;
use Swoole\Database\RedisConfig;
use Swoole\Database\RedisPool;
class Swoole{
	static public $base_dir = BASE_PATH . "socket" . DS;
	static public $tcpServer = null;
	static public $websocketServer = null;
	static public $tables = [];
	static public $links = [];
	static public $dbPools = [];
	static public $redisPools = null;
	
	//获取数据库连接池
	static public function getDbPool($useConfig=""){
		$config = Config::getDatabaseConfig();
		if(empty($config) || !is_array($config) || count($config) == 0)throw new \Exception("数据库配置为空");
		if(empty($useConfig)){
			if(count($config) > 1)throw new \Exception("未选择数据库配置");
			$config_keys = array_keys($config);
			$useConfig = $config_keys[0];
		}
		if(!isset($config[$useConfig]))throw new \Exception("数据库配置不存在");
		if(isset(self::$dbPools[$useConfig])){
			return self::$dbPools[$useConfig];
		}
		$configInfo = $config[$useConfig];
		$pool = new PDOPool((new PDOConfig)->withHost($configInfo['host'])->withPort($configInfo['port'])->withDbName($configInfo['database'])->withCharset($configInfo['charset'])->withUsername($configInfo['user'])->withPassword($configInfo['password']));
		self::$dbPools[$useConfig] = $pool;
		return self::$dbPools[$useConfig];
		//$pdo = $pool->get();
		//$pool->put($pdo);
		//$pool->put(null);
	}
	
	//获取Redis连接池
	static public function getRedisPool($db_index=0){
		if(isset(self::$redisPools[$db_index])){
			return self::$redisPools[$db_index];
		}
		$pool = new RedisPool((new RedisConfig)->withHost(config("sys.redis.host"))->withPort(config("sys.redis.port"))->withAuth(config("sys.redis.password"))->withDbIndex($db_index));
		self::$redisPools[$db_index] = $pool;
		return self::$redisPools[$db_index];
		//$redis = $pool->get();
		//$pool->put($redis);
	}
	
	//创建内存表,格式$columns = [["column"=>"uid","type"=>"int","len"=>10],["column"=>"username","type"=>"string","len"=>100]]
	static public function createTable($tableName,$columns,$num = 65536){
		if(!is_string($tableName) || empty($columns) || !is_int($num) || $num < 1024)return false;
		$table = new \Swoole\Table($num);
		foreach($columns as $v){
			if(empty($v['column']) || empty($v['type']) || empty($v['len']) || !in_array($v['type'],['int','string']) || !is_int($v['len']) || $v['len'] < 1)continue;
			$type = \Swoole\Table::TYPE_STRING;
			if($v['type'] == "int")$type = \Swoole\Table::TYPE_INT;
			$table->column($v['column'], $type, $v['len']); 
		}
		$res = $table->create();
		self::$tables[$tableName] = $table;
		return self::$tables[$tableName];
	}
	
	//路由 action@controller
	static public function route($conn,$data){
		$url = $data['url'];
		$url_path = explode("@",$url);
		if(count($url_path) !== 2){
			self::write($conn,["type"=>"error","msg"=>"params error"]);
			return false;
		}
		$controller = $url_path[1];
		$controller[0] = strtoupper($controller[0]);
		$action = $url_path[0];
		$controllerClassName = "tcp\\" . $controller;
		if(method_exists($controllerClassName,$action)){
			$method = new \ReflectionMethod($controllerClassName,$action);
			$methodParams = $method->getParameters();
			$inParams = [];
			if(!empty($methodParams)){
				foreach($methodParams as $methodParam){
					if(!isset($data[$methodParam->name]) && !$methodParam->isDefaultValueAvailable()){
						self::write($conn,["type"=>"error","msg"=>"param [ ".$methodParam->name." ] error"]);
						return false;
					}else{
						$paramValue = isset($data[$methodParam->name]) ? $data[$methodParam->name] : ($methodParam->isDefaultValueAvailable() ? $methodParam->getDefaultValue() : "");
						$inParams[$methodParam->name] = $paramValue;
					}
				}
			}
			$method->invokeArgs(new $controllerClassName,$inParams);				
		}else{
			self::write($conn,["type"=>"error","msg"=>"url error"]);
		}
	}

	//新连接事件
	static public function connectd($conn_id){
		//
	}
	
	//断开连接事件
	static public function disconnectd($conn_id){
		//
		$uid = self::$tables['login_fd']->get("fd_{$conn_id}","uid");
		if($uid){
			self::$tables['login_fd']->del("fd_{$conn_id}");
			$fds = self::$tables['login_uid']->get("uid_{$uid}","fds");
			if($fds){
				if(strpos($fds,",") === false){
					self::$tables['login_uid']->del("uid_{$uid}");
				}else{
					$fds_arr = explode(",",$fds);
					foreach($fds_arr as $k=>$fd){
						if($fd == $conn_id){
							unset($fds_arr[$k]);
						}
					}
					self::$tables['login_uid']->set("uid_{$uid}",["fds"=>implode(",",$fds_arr)]);
				}
			}
		}
		//
		unset((self::$links)[$conn_id]);
		\tcp\Rchat::notify_offline($conn_id);
		//
	}
	
	//获取连接数量
	static public function countConns(){
		return count(self::$links);
	}

	//获取登录数量
	static public function countLogins(){
		return self::$tables['login_fd']->count();
	}	
	
	//输出数据
	static public function write($conn,$data){
		$data = json_encode($data);
		if(empty($data)){
			echo "write data error\r\n";
			return false;
		}
		if($conn instanceof \Swoole\Coroutine\Server\Connection){
			$conn->send(json_encode($data)."\r\n");
		}else if($conn instanceof \Swoole\Http\Response){
			$conn->push(json_encode($data));
		}else{
			//var_dump(get_class($conn));
			echo "write type error\r\n";
			return false;
		}
		return true;
	}
	
	//关闭链接
	static public function connClose($conn){
		$conn->close();
	}
	//
}