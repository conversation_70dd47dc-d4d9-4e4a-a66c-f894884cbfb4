<script setup>
import { watch, ref, reactive } from "vue";
import {
  userupdate,
  useradd_label,
  userdel_label,
  upload_img,
  useradd_img,
  userdel_img,
  setPhone
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom
} from "@dcloudio/uni-app";
import h from "@/utils/request";
import { store } from "@/store";
import { getListHeight, back, getUserInfo } from "@/utils";

const form = ref({
  avatar: "",
  nickname: "",
  birthday: "",
  mobile: "",
  sex: "",
  gexingqianming: "",
  city: "", // 🆕 新增：城市信息
  labels: [],
  imgs: []
});
const fileList = ref([]);
const columns = ref([["男", "女"]]);
const show = ref(false);
const showDatePicker = ref(false);
const date = ref(Date.now());
const popupShow = ref(false);
const text = ref(""); // 输入框的值
const gapHeight = ref(0);
const scroll = ref(0);

onPageScroll((e) => {
  scroll.value = e.scrollTop;
});
onLoad(() => {
  form.value.avatar = store().$state.userInfo.avatar;
  form.value.nickname = store().$state.userInfo.nickname;
  form.value.birthday = store().$state.userInfo.birthday;
  form.value.mobile = store().$state.userInfo.mobile;
  form.value.sex = store().$state.userInfo.sex;
  form.value.gexingqianming = store().$state.userInfo.gexingqianming;
  form.value.city = store().$state.userInfo.city || ''; // 🆕 新增：城市信息
  form.value.labels = store().$state.userInfo.labels;
  form.value.imgs = store().$state.userInfo.imgs;
  form.value.imgs.map((val) =>
    fileList.value.push({
      url: val.img_url
    })
  );
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});

// 获取手机号
const getphonenumber = async (e) => {
  const res = await setPhone({
    code: e.detail.code,
    token: store().$state.userInfo.token,
    uid: store().$state.userInfo.uid
  });
  if (res.status === "ok") {
    uni.$u.toast(res.msg);
    getUserInfo();
  } else uni.$u.toast(res.msg);
};
// 删除图片
const deletePic = async (e) => {
  fileList.value.splice(e.index, 1);
  const res = await userdel_img({ ids: form.value.imgs[e.index].id });
  if (res.status === "ok") {
    store().$state.userInfo.imgs.forEach((val) => {
      if (val.id === form.value.imgs[e.index].id)
        store().$state.userInfo.imgs.splice(
          store().$state.userInfo.imgs.indexOf(val),
          1
        );
    });
  }
};
// 新增图片
const afterRead = async (e) => {
  // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(e.file);
  let fileListLen = fileList.value.length;
  lists.map((item) => {
    fileList.value.push({
      ...item,
      status: "uploading",
      message: "上传中"
    });
  });
  for (let i = 0; i < lists.length; i++) {
    const res = await upload_img(lists[i].url);
    let item = fileList.value[fileListLen];
    fileList.value.splice(fileListLen, 1, {
      ...item,
      status: "success",
      message: "",
      url: res.data
    });
    fileListLen++;
    if (res.status === "ok") {
      const re = await useradd_img({ img_url: res.data });
      if (re.status === "ok")
        store().$state.userInfo.imgs.push({ id: re.data, img_url: res.data });
    }
  }
};
// 选择性别
const confirm = async (e) => {
  form.value.sex = e.indexs[0] + 1;
  show.value = false;
};
// 时间选择器确定
const dateConfirm = (e) => {
  form.value.birthday = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
  showDatePicker.value = false;
};
// 添加标签
const addLabel = async () => {
  const res = await useradd_label({ label: text.value });
  if (res.status == "ok")
    store().$state.userInfo.labels.unshift({ label: text.value, id: res.data });
  else uni.$u.toast(res.msg);
};
// 删除标签
const delLabel = async (id) => {
  const res = await userdel_label({ id });
  if (res.status == "ok")
    store().$state.userInfo.labels.forEach((val) => {
      if (val.id === id)
        store().$state.userInfo.labels.splice(
          store().$state.userInfo.labels.indexOf(val),
          1
        );
    });
  else uni.$u.toast(res.msg);
};
// 选择头像
const onChooseAvatar = async (e) => {
  const res = await upload_img(e.detail.avatarUrl);
  if (res.status === "ok") form.value.avatar = res?.data;
  else uni.$u.toast(res.msg);
};
// 选择昵称
const onNickName = (e) => (form.value.nickname = e);
const bindinput = (e) => (form.value.nickname = e);
// 提交
const submit = async () => {
  // for (let i in form.value) {
  //   if (uni.$u.test.isEmpty(form.value[i])) {
  //     console.log(i);
  //     uni.$u.toast("请填写完整信息");
  //     return;
  //   }
  // }
  const res = await h.p("user/update", {
    avatar: form.value.avatar,
    nickname: form.value.nickname,
    sex: form.value.sex,
    birthday: form.value.birthday,
    gexingqianming: form.value.gexingqianming,
    city: form.value.city, // 🆕 新增：城市信息
    uid: store().$state.userInfo.uid,
    token: store().$state.userInfo.token
  });
  // const res = await userupdate({
  //   avatar: form.value.avatar,
  //   nickname: form.value.nickname,
  //   sex: form.value.sex,
  //   birthday: form.value.birthday,
  //   gexingqianming: form.value.gexingqianming,
  // });
  if (res.status === "ok")
    back({ tip: "修改成功，即将返回上级页面", time: 2000 });
};
</script>
<template>
  <view class="page b6f">
    <u-sticky z-index="2">
      <myTitle
        bgColor="#f8f8f8"
        title="修改个人信息"
        height="200rpx"
      />
    </u-sticky>
    <view
      class="pa left50 tl50"
      style="width: 180rpx; height: 180rpx; top: 150rpx"
      :style="{ zIndex: scroll > 17 ? '1' : '20' }"
    >
      <view
        class="pa w df aic jcc"
        :style="{ zIndex: scroll > 17 ? '1' : '20' }"
      >
        <!-- <u-image width="180rpx" height="180rpx" radius="50%" :src="form.avatar"></u-image> -->
        <button
          class="u-reset-button df aic jcc"
          open-type="chooseAvatar"
          type="balanced"
          @chooseavatar="onChooseAvatar"
        >
          <image
            style="width: 180rpx; height: 180rpx; border-radius: 50%;"
            :src="form.avatar"
            mode="aspectFill"
          ></image>
          <image
            class="pa"
            :style="{ zIndex: scroll > 17 ? '1' : '20' }"
            style="
              bottom: 0;
              right: 0;
              width: 50rpx;
              height: 50rpx;
              z-index: 9999999999999999;
            "
            :src="`${store().$state.url}editMyInfo.png`"
            mode="scaleToFill"
            @chooseavatar="onChooseAvatar"
          />
        </button>
      </view>
    </view>
    <view class="px30 mt50">
      <view class="py30 df aic jcsb borderBottom">
        <view class="x26" style="color: #7e7d7d">昵称：</view>
        <u-input
          type="nickname"
          v-model="form.nickname"
          input-align="right"
          placeholder="请输入昵称"
          border="none"
          placeholder-style="font-size:26rpx;color:#aaa"
          :customStyle="{ fontSize: '26rpx' }"
          @blur="onNickName"
          @input="bindinput"
        ></u-input>
        <!-- <input
          type="nickname"
          class="mb50 tac"
          :value="nickname"
          placeholder="请输入昵称"
          @blur="onNickName"
          @input="bindinput"
        /> -->
      </view>
      <view
        class="py30 df aic jcsb borderBottom"
        @click="showDatePicker = true"
      >
        <view class="x26" style="color: #7e7d7d">出生日期：</view>
        <view class="x26" :style="{ color: form.birthday ? '' : '#aaa' }">
          {{ form.birthday || "请选择出生日期" }}
        </view>
        <!-- <u-input
          v-model="form.sex"
          input-align="right"
          placeholder="请输入年龄"
          border="none"
          placeholder-style="font-size:26rpx;color:#aaa"
          :customStyle="{ fontSize: '26rpx' }"
        ></u-input> -->
      </view>
      <view class="py30 df aic jcsb borderBottom">
        <view class="x26" style="color: #7e7d7d">ID：</view>
        <u-input
          v-model="store().$state.userInfo.uid"
          :disabled="true"
          disabled-color="#fff"
          input-align="right"
          placeholder="请输入ID"
          border="none"
          placeholder-style="font-size:26rpx;color:#aaa"
          :customStyle="{ fontSize: '26rpx' }"
        ></u-input>
      </view>
      <view class="py30 df aic jcsb borderBottom">
        <view class="x26" style="color: #7e7d7d">手机号：</view>
        <button
          class="u-reset-button btnm"
          open-type="getPhoneNumber"
          type="balanced"
          @getphonenumber="getphonenumber"
        >
          <u-input
            v-model="form.mobile"
            input-align="right"
            maxlength="11"
            placeholder="绑定手机号"
            :disabled="true"
            border="none"
            placeholder-style="font-size:26rpx;color:#aaa"
            :customStyle="{ fontSize: '26rpx', background: 'transparent' }"
          ></u-input>
        </button>
      </view>
      <view class="py30 df aic jcsb borderBottom" @click="show = true">
        <view class="x26" style="color: #7e7d7d">性别：</view>
        <view class="x26" :style="{ color: form.sex ? '' : '#aaa' }">
          {{ form.sex == 1 ? "男" : form.sex == 2 ? "女" : "请选择性别" }}
        </view>
      </view>
      <!-- 🆕 新增：城市信息输入框 -->
      <view class="py30 df aic jcsb borderBottom">
        <view class="x26" style="color: #7e7d7d">常住城市：</view>
        <u-input
          v-model="form.city"
          input-align="right"
          placeholder="请输入常住城市"
          border="none"
          placeholder-style="font-size:26rpx;color:#aaa"
          :customStyle="{ fontSize: '26rpx' }"
          maxlength="50"
        ></u-input>
      </view>
      <view class="py30 df aic jcsb borderBottom" @click="popupShow = true">
        <view class="x26" style="color: #7e7d7d">添加标签：</view>
        <u-icon name="arrow-right"></u-icon>
      </view>
      <view class="py30 borderBottom">
        <view class="x26" style="color: #7e7d7d">简介：</view>
        <view
          class="mt20"
          style="background-color: #f7f7f7; border-radius: 20rpx"
        >
          <u-textarea
            v-model="form.gexingqianming"
            radius="20"
            height="143rpx"
            placeholder="说点什么，让大家更了解你~"
            placeholder-style="font-size:26rpx;color:#aaa"
          ></u-textarea>
        </view>
      </view>
      <view class="py30 borderBottom">
        <view class="x26" style="color: #7e7d7d">
          上传社交图：（可上传九张图片）
        </view>
        <view class="mt20" style="">
          <u-upload
            :fileList="fileList"
            @afterRead="afterRead"
            @delete="deletePic"
            name="1"
            multiple
            :maxCount="9"
            width="215rpx"
            height="215rpx"
          >
            <view
              class="df aic jcc"
              style="
                width: 215rpx;
                height: 215rpx;
                background: #eeeeee;
                border-radius: 10rpx;
              "
            >
              <u-icon color="#BFBFBF" size="80rpx" name="plus"></u-icon>
            </view>
          </u-upload>
        </view>
      </view>
      <u-gap :height="gapHeight"></u-gap>
      <view class="pfx bottom0 w690 b6f bottomBox">
        <u-button
          text="保存"
          shape="circle"
          color="linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)"
          :customStyle="{
            marginTop: '20rpx',
            height: '90rpx',
            color: '#ffffff',
            fontSize: '30rpx'
          }"
          @click="submit"
        >
        </u-button>
        <u-safe-bottom></u-safe-bottom>
      </view>
    </view>
    <u-picker
      :show="show"
      :columns="columns"
      :close-on-click-overlay="true"
      @close="show = false"
      @cancel="show = false"
      @confirm="confirm"
    ></u-picker>
    <u-datetime-picker
      :show="showDatePicker"
      v-model="date"
      mode="date"
      :minDate="new Date(1900, 0, 1) * 1"
      :close-on-click-overlay="true"
      @cancel="showDatePicker = false"
      @close="showDatePicker = false"
      @confirm="dateConfirm"
    ></u-datetime-picker>
    <u-popup
      :show="popupShow"
      :close-on-click-overlay="true"
      round="20"
      @close="popupShow = false"
    >
      <view class="p30">
        <view
          class="df aic jcsb px20 pt10 pb10 r20"
          style="border: 2rpx #aaa solid"
        >
          <u-input
            v-model="text"
            placeholder="请输入标签（最多10字）"
            border="none"
            maxlength="10"
            :customStyle="{
              width: '500rpx'
            }"
          ></u-input>
          <u-button
            shape="circle"
            text="添加"
            color="linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)"
            :customStyle="{
              margin: '0',
              width: '88rpx',
              border: 'none',
              color: '#ffffff',
              fontSize: '28rpx'
            }"
            @click="addLabel"
          ></u-button>
        </view>
        <view class="my20">
          <scroll-view scroll-y style="max-height: 500rpx">
            <view class="df aic fw">
              <view
                class="pr m20 p20 x24"
                style="background-color: #eeeeee; border-radius: 26rpx"
                v-for="(val, i) in form.labels"
                :key="i"
              >
                {{ val.label }}
                <view class="pa" style="top: -5rpx; right: -5rpx">
                  <u-icon
                    size="30rpx"
                    name="close-circle-fill"
                    @click="delLabel(val.id)"
                  ></u-icon>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <u-button
          color="linear-gradient(90deg, #88D7A0 0%, #6AC086 100%)"
          text="完成"
          :customStyle="{
            height: '90rpx',
            color: '#ffffff',
            fontWeight: '500'
          }"
          @click="popupShow = false"
        ></u-button>
      </view>
    </u-popup>
  </view>
</template>

<style scoped lang="less"></style>
