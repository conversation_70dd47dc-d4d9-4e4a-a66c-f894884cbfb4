<script setup>
import {watch, ref, reactive,inject} from "vue";
import {userlogin, userupdate, setPhone, upload_img, clearInfo} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app";
import {store} from "@/store";
import {navto, back} from "@/utils";
import {BaseUrl} from "@/utils/BaseUrl";

const show = ref(false);
const checkboxValue = ref([]);
const checkboxChange = (e) => {
};
const avatar = ref("");
const avatarUrl = ref("/static/myd.png");
const nickname = ref("");
const msg = ref("绑定手机号");
// const userInfo = ref({uid: "12",token: "f61f4c5067f1142996b1e2f8618db75b"});//测试写死
const userInfo = ref({});
const mobile = ref("");
const mobileInput = ref(""); // 手动输入的手机号
const sex = ref();
const pickerShow = ref(false);
const showPrivacy = ref(false);
const showPhoneInput = ref(false); // 是否显示手机号输入框
// 移除：不再需要手机号授权模态框
const phoneBindingStatus = ref(""); // 手机号绑定状态

// 存储返回URL
const returnUrl = ref('');

// 主要页面列表（项目使用自定义tabBar，不是原生tabBar）
const mainPages = [
  '/pages/index',
  '/pages/addActive',
  '/pages/world',
  '/pages/my'
];

// 智能跳转函数
const smartNavigate = (url) => {
  return new Promise((resolve, reject) => {
    console.log('=== 智能跳转开始 ===');
    console.log('目标URL:', url);
    console.log('URL类型检查:', url);

    // 验证URL格式
    if (!url || typeof url !== 'string') {
      const error = new Error('无效的URL参数');
      console.error('URL验证失败:', error);
      reject(error);
      return;
    }

    // 检查是否为主要页面（去除参数部分进行比较）
    const urlWithoutParams = url.split('?')[0];
    const isMainPage = mainPages.includes(urlWithoutParams);
    console.log('URL去除参数后:', urlWithoutParams);
    console.log('是否为主要页面:', isMainPage);
    console.log('主要页面列表:', mainPages);

    if (isMainPage) {
      console.log('使用 uni.reLaunch 跳转到主要页面（项目使用自定义tabBar）');

      // 主要页面使用reLaunch，支持参数传递
      uni.reLaunch({
        url: url,
        success: (res) => {
          console.log('✅ 主要页面跳转成功:', url);
          console.log('跳转结果:', res);
          resolve(res);
        },
        fail: (error) => {
          console.error('❌ 主要页面跳转失败:', error);
          console.error('错误详情:', JSON.stringify(error));
          reject(error);
        }
      });
    } else {
      console.log('使用 uni.navigateTo 跳转到普通页面');
      uni.navigateTo({
        url: url,
        success: (res) => {
          console.log('✅ navigateTo 跳转成功:', url);
          console.log('跳转结果:', res);
          resolve(res);
        },
        fail: (error) => {
          console.warn('⚠️ navigateTo 失败，尝试 redirectTo:', error);
          console.warn('navigateTo 错误详情:', JSON.stringify(error));

          // 如果 navigateTo 失败，尝试 redirectTo
          uni.redirectTo({
            url: url,
            success: (res) => {
              console.log('✅ redirectTo 跳转成功:', url);
              console.log('跳转结果:', res);
              resolve(res);
            },
            fail: (redirectError) => {
              console.error('❌ 所有跳转方式都失败');
              console.error('redirectTo 错误详情:', JSON.stringify(redirectError));
              reject(redirectError);
            }
          });
        }
      });
    }

    console.log('=== 智能跳转请求已发送 ===');
  });
};

onLoad((e) => {
  getPopup();
  if (e?.id) store().changePid(e.pid);

  // 保存返回URL参数
  if (e?.returnUrl) {
    returnUrl.value = decodeURIComponent(e.returnUrl);
    console.log('登录页面接收到返回URL:', returnUrl.value);
  }
});

const clear = async () => {
  const res = await clearInfo({column: "avatar"});
};
// 🔧 修复：登录模式移除协议验证逻辑
const logind = () => {
  uni.login({
      provider: "weixin",
      success: async (loginRes) => {
        const res = await userlogin({
          code: loginRes.code,
          pid: store().$state.pid,
        });
        if (res.status === "ok") {
          avatar.value = res.data.avatar;
          nickname.value = res.data.nickname;
          // mobile.value = res.data.mobile;
          sex.value = res.data.sex;
          if (res.data?.mobile) msg.value = "已绑定手机号";
          if (
              res.data.avatar &&
              res.data.nickname &&
              // uni.$u.test.mobile(res.data.mobile) &&
              res.data?.sex &&
              res.data?.sex != 0
          ) {
            store().changeUserInfo(res.data);
            console.log('登录成功，用户信息完整，保存用户信息:', res.data);

            // 登录成功后的跳转处理
            uni.$u.toast("登录成功");
            setTimeout(async () => {
              if (returnUrl.value) {
                try {
                  await smartNavigate(returnUrl.value);
                  console.log('成功跳转到指定页面:', returnUrl.value);
                } catch (error) {
                  console.error('智能跳转失败:', error);
                  // 尝试直接返回上一页
                  try {
                    uni.navigateBack();
                    console.log('已返回上一页');
                  } catch (backError) {
                    console.error('返回上一页也失败，跳转到首页');
                    uni.switchTab({ url: '/pages/index' });
                  }
                }
              } else {
                console.log('返回上一页');
                uni.navigateBack();
              }
            }, 1500);
            return;
          } else {
            show.value = true;
            userInfo.value = res.data;
            uni.$u.toast("请上传头像、昵称以及绑定手机号码");
          }
        } else uni.$u.toast(res.msg);
      },
    });
};
const getphonenumber = async (e) => {
  try {
    console.log('微信手机号授权结果:', e.detail);

    if (e.detail.code) {
      mobile.value = e.detail.code;
      const {uid, token} = userInfo.value;
      const res = await request("user/update_mobile", "POST", {
        code: e.detail.code,
        uid,
        token,
      });

      if (res.status == "ok") {
        phoneBindingStatus.value = "已绑定手机号";
        showPhoneInput.value = false;
        uni.showToast({
          title: '手机号绑定成功',
          icon: 'success'
        });
      } else {
        phoneBindingStatus.value = res.msg || "绑定失败";
        showPhoneInput.value = true;
        uni.showToast({
          title: '绑定失败，请手动输入手机号',
          icon: 'none'
        });
      }
    } else {
      console.log('用户拒绝微信手机号授权，强制显示手动输入框');
      showPhoneInput.value = true;
      phoneBindingStatus.value = "请输入手机号";
      uni.showToast({
        title: '请手动输入手机号完成注册',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('手机号绑定异常:', error);
    showPhoneInput.value = true;
    phoneBindingStatus.value = "请输入手机号";
    uni.showToast({
      title: '请手动输入手机号完成注册',
      icon: 'none'
    });
  }
};

// 🆕 新增：手动绑定手机号
const bindPhoneManually = async () => {
  // 验证手机号格式
  if (!mobileInput.value || !uni.$u.test.mobile(mobileInput.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    });
    return;
  }

  try {
    const {uid, token} = userInfo.value;
    const res = await request("user/update_mobile_manual", "POST", {
      mobile: mobileInput.value,
      uid,
      token,
    });

    if (res.status == "ok") {
      mobile.value = mobileInput.value;
      phoneBindingStatus.value = "已绑定手机号";
      showPhoneInput.value = false;
      uni.showToast({
        title: '手机号绑定成功',
        icon: 'success'
      });

      // 🔧 优化：手动绑定成功后自动完成注册流程
      setTimeout(async () => {
        await submitForm();
      }, 1000);
    } else {
      uni.showToast({
        title: res.msg || '绑定失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('手动绑定手机号异常:', error);
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    });
  }
};

// 移除：不再需要手动输入手机号函数（已集成到getphonenumber中）
const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BaseUrl + url,
      method,
      data,
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
      success: (res) => resolve(res.data),
    });
  });
};
const getPopup = () => {
  wx.getPrivacySetting({
    success: (res) => {
      if (res.needAuthorization) {
        showPrivacy.value = true;
        wx.requirePrivacyAuthorize({
          success: () => {
            // 用户同意授权
            // 继续小程序逻辑
          },
          fail: () => uni.$u.toast("您已拒绝授权"), // 用户拒绝授权
          complete: () => {
          },
        });
        // 需要弹出隐私协议
      } else {
      }
    },
    fail: () => {
    },
    complete: () => {
    },
  });
};


const onNickName = (e) => (nickname.value = e.detail.value);

const bindinput = (e) => (nickname.value = e.detail.value);

const confirm = async (e) => {
  sex.value = e.indexs[0] + 1;
  pickerShow.value = false;
};
// 🔧 优化：微信手机号授权处理方法
const handleWechatPhoneAuth = async (e) => {
  console.log('微信手机号授权结果:', e.detail);

  // 先进行基础信息验证
  if (!validateBasicInfo()) {
    return;
  }

  try {
    if (e.detail.code) {
      // 微信授权成功，获取到手机号code
      console.log('微信授权成功，获取手机号code:', e.detail.code);

      // 调用后端API绑定手机号
      const {uid, token} = userInfo.value;
      const res = await request("user/update_mobile", "POST", {
        code: e.detail.code,
        uid,
        token,
      });

      if (res.status === "ok") {
        // 手机号绑定成功，直接完成注册
        console.log('手机号绑定成功，完成注册流程');
        mobile.value = res.data?.mobile || '已绑定';
        phoneBindingStatus.value = "已绑定手机号";
        await submitForm();
      } else {
        // 绑定失败，降级到手动输入
        console.log('手机号绑定失败，降级到手动输入:', res.msg);
        fallbackToManualInput();
      }
    } else {
      // 用户拒绝授权，降级到手动输入
      console.log('用户拒绝微信授权，降级到手动输入');
      fallbackToManualInput();
    }
  } catch (error) {
    console.error('微信授权处理异常:', error);
    fallbackToManualInput();
  }
};

// 🆕 基础信息验证方法
const validateBasicInfo = () => {
  // 协议验证
  if (checkboxValue.value.length === 0) {
    uni.$u.toast("请先同意用户注册协议");
    return false;
  }

  // 基础信息验证
  if (!avatar.value || !nickname.value) {
    uni.$u.toast("请填写完整信息");
    return false;
  }

  if (!sex.value) {
    uni.$u.toast("请选择性别");
    return false;
  }

  return true;
};

// 🆕 降级到手动输入方法
const fallbackToManualInput = () => {
  showPhoneInput.value = true;
  phoneBindingStatus.value = "请输入手机号完成注册";
  uni.showToast({
    title: '请输入手机号完成注册',
    icon: 'none'
  });
};

// 🔧 修复：保留原有的提交方法，用于已绑定手机号的情况
const submitForm = async () => {

  // 提交用户信息
  const {uid, token} = userInfo.value;
  const res = await request("user/update", "POST", {
    avatar: avatar.value,
    nickname: nickname.value,
    sex: sex.value,
    uid,
    token,
  });

  if (res.status === "ok") {
    const updatedUserInfo = {
      ...userInfo.value,
      avatar: avatar.value,
      nickname: nickname.value,
      sex: sex.value
    };
    store().changeUserInfo(updatedUserInfo);
    console.log('登录成功，保存用户信息:', updatedUserInfo);

    uni.$u.toast("登录成功");
    setTimeout(async () => {
      if (returnUrl.value) {
        try {
          await smartNavigate(returnUrl.value);
          console.log('成功跳转到指定页面:', returnUrl.value);
        } catch (error) {
          console.error('智能跳转失败:', error);
          try {
            uni.navigateBack();
            console.log('已返回上一页');
          } catch (backError) {
            console.error('返回上一页也失败，跳转到首页');
            uni.switchTab({ url: '/pages/index' });
          }
        }
      } else {
        console.log('返回上一页');
        uni.navigateBack();
      }
    }, 1500);
    return;
  } else {
    uni.$u.toast(res.msg);
  }
};


// 手动选择头像（相册或拍照）
const chooseAvatar = async () => {
  uni.showActionSheet({
    itemList: ['从相册选择', '拍照'],
    success: (res) => {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: res.tapIndex === 0 ? ['album'] : ['camera'],
        success: async (res) => {
          try {
            uni.showLoading({
              title: '上传头像中...'
            });

            const tempFilePaths = res.tempFilePaths;
            avatarUrl.value = tempFilePaths[0];

            // 上传头像到服务器
            const uploadRes = await upload_img(tempFilePaths[0]);
            if (uploadRes.status === "ok") {
              avatar.value = uploadRes?.data;
              uni.showToast({
                title: '头像设置成功',
                icon: 'success'
              });
            } else {
              uni.$u.toast(uploadRes.msg || '头像上传失败');
            }
          } catch (error) {
            console.error('头像上传失败:', error);
            uni.showToast({
              title: '头像上传失败',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        },
        fail: (err) => {
          console.error('选择图片失败：', err);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    }
  });
}

// 微信头像选择处理 - 使用官方推荐的chooseAvatar方式
const onChooseAvatar = async (e) => {
  try {
    console.log('微信头像选择结果:', e.detail);

    if (e.detail.avatarUrl) {
      uni.showLoading({
        title: '上传头像中...'
      });

      // 更新本地显示
      avatarUrl.value = e.detail.avatarUrl;

      // 上传头像到服务器
      const uploadRes = await upload_img(e.detail.avatarUrl);
      if (uploadRes.status === "ok") {
        avatar.value = uploadRes?.data;
        console.log('微信头像上传成功:', uploadRes.data);
        uni.showToast({
          title: '头像设置成功',
          icon: 'success'
        });
      } else {
        console.error('微信头像上传失败:', uploadRes.msg);
        uni.$u.toast(uploadRes.msg || '头像上传失败');
      }
    } else {
      uni.showToast({
        title: '未获取到头像',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('头像处理失败:', error);
    uni.showToast({
      title: '头像处理失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
}




</script>
<!--<script>
 export default {
  data() {
  return {
  avatarUrl: '/static/myd.png', // 默认头像
  tempFilePath: '' // 临时文件路径
};
},
  methods: {
  // 选择头像
  chooseAvatar() {
  uni.chooseImage({
  count: 1,
  sizeType: ['compressed'], // 可以指定是原图还是压缩图
  sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机
  success: (res) => {
  const tempFilePaths = res.tempFilePaths;
  this.tempFilePath = tempFilePaths[0];
  this.avatarUrl = tempFilePaths[0];

  // 自动触发上传
  this.onChooseAvatar();
},
  fail: (err) => {
  console.error('选择图片失败：', err);
  uni.showToast({
  title: '选择图片失败',
  icon: 'none'
});
}
});
},
  onChooseAvatar() {
    const res = upload_img(this.avatarUrl);
    this.avatar.value =this.avatarUrl;
    uni.$u.toast(res.status);
   /* if (res.status === "ok") avatar.value = res?.data;
    else uni.$u.toast(res.msg);*/
  }
  // 上传图片到服务器
 /* uploadImage() {
  if (!this.tempFilePath) {
  uni.showToast({
  title: '请先选择图片',
  icon: 'none'
});
  return;
}

  uni.showLoading({
  title: '上传中...',
  mask: true
});

  // 换成你的上传接口地址
  const uploadUrl = 'https://your-api-domain.com/upload';

  uni.uploadFile({
  url: uploadUrl,
  filePath: this.tempFilePath,
  name: 'file', // 根据后端接口定义修改
  formData: {
  // 其他需要携带的参数
  'userId': '123'
},
  header: {
  'Authorization': 'Bearer ' + uni.getStorageSync('token') // 添加认证头
},
  success: (res) => {
  if (res.statusCode === 200) {
  const data = JSON.parse(res.data);
  this.avatarUrl = data.url; // 更新为服务器返回的地址
  uni.showToast({
  title: '上传成功',
  icon: 'success'
});
} else {
  throw new Error(`上传失败，状态码：${res.statusCode}`);
}
},
  fail: (err) => {
  console.error('上传失败：', err);
  uni.showToast({
  title: '上传失败',
  icon: 'none'
});
},
  complete: () => {
  uni.hideLoading();
}
});
}*/
}
};
</script>-->
<template>
  <view class="page df fdc aic b6f">
    <u-gap height="150rpx"></u-gap>
    <template>
      <view class="container">
        <!-- 图片展示区域 -->
        <view class="image-container">
          <image
            class="fixed-image"
            :src="store().$state.config?.img_config?.app_logo?.img_url"
            mode="widthFix"
            lazy-load
          ></image>
        </view>
      </view>
    </template>
    <u-gap height="50rpx"></u-gap>
    <view>
      <u-text
          margin="20rpx 0"
          align="center"
          size="48rpx"
          bold
          color="#ff5700"
          :text="`${store().$state.config?.config?.app_name?.val}`"
      ></u-text>
    </view>

    <u-gap height="10rpx"></u-gap>
    <template v-if="show">
      <view class="form-container">
        <view class="avatar-container">
        <button class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <view class="avatar-wrapper">
            <image class="avatar" :src="avatarUrl" mode="aspectFill"></image>
            <view class="upload-text">点击更换头像</view>
          </view>
        </button>
        <view class="avatar-tip">微信头像或本地照片</view>
      </view>
      <!--    <template v-if="show">-->
<!--      <u-button
          class="u-reset-button df aic jcc mb50"
          open-type="chooseAvatar"
          type="balanced"
          text="上传头像"
          @click="onChooseAvatar"
          @chooseavatar="onChooseAvatar"

          :customStyle="{
          width: '150rpx',
          height: '150rpx',
          color: '#ff5700',
          fontSize: '22rpx',
        }"
      >
        <u-image width="150rpx" height="150rpx" :src="avatar?.value" mode="widthFix"></u-image>
      </u-button>-->
      <u-gap height="30rpx"></u-gap>
      <input
          type="nickname"
          class="mb50 tac"
          :value="nickname"
          placeholder="建议使用微信昵称"
          @focus="getPopup"
          @blur="onNickName"
          @input="bindinput"
      />

      <!-- 手机号状态显示 -->
      <view v-if="phoneBindingStatus" class="phone-status mb50">
        <u-icon name="checkmark-circle" size="32rpx" color="#6AC086" style="margin-right: 16rpx;"></u-icon>
        {{ phoneBindingStatus }}
      </view>

      <!-- 手动输入手机号 -->
      <view v-if="showPhoneInput" class="phone-input-section mb50">
        <view class="phone-input-wrapper">
          <input
              type="number"
              class="phone-input"
              v-model="mobileInput"
              placeholder="请输入手机号"
              maxlength="11"
          />
          <u-button
              size="small"
              type="primary"
              text="绑定"
              :customStyle="{
                width: '120rpx',
                height: '60rpx',
                fontSize: '24rpx',
                backgroundColor: '#6AC086',
                borderColor: '#6AC086'
              }"
              @click="bindPhoneManually"
          ></u-button>
        </view>
        <view class="phone-tip">
          <text class="tip-text">请输入手机号完成注册</text>
        </view>
      </view>

      <view class="mb50 tac gender-select" :class="[sex ? '' : 'c6a']" @click="pickerShow = true">
        {{ sex ? (sex == 1 ? "男" : "女") : "请选择性别" }}
      </view>
      <u-gap height="170rpx"></u-gap>

      <!-- 🔧 修复：移除重复的用户协议组件和提交按钮，已移到全局位置 -->
      </view>
    </template>

    <!-- 🔧 修复：用户协议组件只在注册模式显示 -->
    <view v-if="show" class="agreement-section-global">
      <u-checkbox-group
          v-model="checkboxValue"
          placement="column"
          @change="checkboxChange"
          shape="circle"
          active-color="#ff8110"
      >
        <view class="df aic">
          <u-checkbox name="a"></u-checkbox>
          同意
          <view class="text call"  @click="navto('/pages/bundle/common/xieyi?type=1')">
            &nbsp; 《用户注册协议》 &nbsp;
          </view>
        </view>
      </u-checkbox-group>
    </view>

    <!-- 🔧 修复：统一的按钮区域，根据状态显示不同按钮 -->
    <view class="button-container-global">
      <!-- 🔧 优化：注册模式使用微信授权获取手机号按钮 -->
      <button
          v-if="show"
          class="wechat-submit-btn"
          open-type="getPhoneNumber"
          @getphonenumber="handleWechatPhoneAuth"
      >
        提交
      </button>
      <u-button
          v-else
          color="linear-gradient(103deg, #836FFF 0%, #836FFF 100%)"
          shape="circle"
          :customStyle="{
          width: '580rpx',
          height: '66rpx',
          color: '#f5f5f5',
          fontSize: '22rpx',
        }"
        text="登录"
        @click="logind"
      ></u-button>
    </view>
    <!-- <u-icon
      v-else
      size="50"
      name="weixin-circle-fill"
      color="#39e274"
      label="一键登录"
      label-pos="bottom"
      label-size="30rpx"
      @click="logind"
    ></u-icon> -->
    <!-- 🔧 修复：移除重复的用户协议组件，已移动到提交按钮上方 -->
    <u-picker
        :show="pickerShow"
        :columns="[['男', '女']]"
        :close-on-click-overlay="true"
        @close="pickerShow = false"
        @cancel="pickerShow = false"
        @confirm="confirm"
    ></u-picker>

    <!-- 移除：不再需要手机号授权模态框 -->
  </view>
</template>

<style scoped lang="less">

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  width: 300rpx; /* 确保容器宽度足够 */
  height: 300rpx; /* 或其他合理高度 */
  position: relative; /* 确保不会脱离文档流 */
  z-index: 1; /* 设置较低的 z-index */
}

.image-container {
  width: 300rpx;
  height: 300rpx;
  overflow: hidden;
  border-radius: 50rpx;
}

.fixed-image {
  width: 100%;
  height: 100%;
  border-radius: 50rpx;
}
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.avatar-button {
  background: none;
  border: none;
  padding: 0;
}

.avatar-wrapper {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  border: 4rpx solid #fff;
  background-color: #f5f5f5;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-text {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
}

.avatar-tip {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 🆕 手机号绑定功能样式 */
.phone-binding-section {
  width: 100%;
  padding: 0 var(--spacing-lg, 32rpx);
}

.phone-auth-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #6AC086 0%, #88D7A0 100%);
  border: none;
  border-radius: 50rpx;
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.3);
  transition: all 0.3s ease;
}

.phone-auth-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.3);
}

.phone-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: rgba(106, 192, 134, 0.1);
  border-radius: 20rpx;
  color: #6AC086;
  font-size: 28rpx;
}

.phone-input-section {
  width: 100%;
}

.phone-input-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
}

.phone-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.phone-input::placeholder {
  color: #999;
}

.phone-tip {
  margin-top: 16rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 移除：不再需要的手机号授权模态框样式 */

/* 组件位置调整样式 */
.form-container {
  margin-top: -30rpx;
}

.agreement-section {
  margin-top: -30rpx;
}

.submit-button-container {
  margin-top: -20rpx;
}

/* 🔧 修复：全局用户协议组件样式 - 注册页面上移20rpx */
.agreement-section-global {
  margin: 0rpx 0 15rpx 0;
  padding: 0 var(--spacing-lg, 32rpx);
  display: flex;
  justify-content: center;
  width: 100%;
}

/* 🔧 修复：全局按钮容器样式 - 注册页面上移，保持与协议组件间距15rpx */
.button-container-global {
  margin: 0 auto;
  padding: 0 var(--spacing-lg, 32rpx);
  display: flex;
  justify-content: center;
  width: 100%;
}

/* 🔧 修复：性别选择对齐样式 */
.gender-select {
  text-align: center;
  padding: 0 var(--spacing-lg, 32rpx);
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}

.gender-select.c6a {
  color: #999;
}

/* 🔧 优化：微信授权提交按钮样式 */
.wechat-submit-btn {
  width: 580rpx;
  height: 88rpx;
  background: linear-gradient(103deg, #836FFF 0%, #836FFF 100%);
  border: none;
  border-radius: 50rpx;
  color: #f5f5f5;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
</style>

