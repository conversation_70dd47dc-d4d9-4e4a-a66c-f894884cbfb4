<template>
  <view class="page">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="申请成为分会长"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <view class="form-container">
        <u-form
          :model="formData"
          ref="formRef"
          :rules="rules"
          labelPosition="top"
          labelWidth="auto"
        >
          <u-form-item label="分会名称" prop="branch_name" required>
            <u-input
              v-model="formData.branch_name"
              placeholder="请输入分会名称（2-100字符）"
              maxlength="100"
              :clearable="true"
            />
          </u-form-item>
          
          <u-form-item label="分会地区" prop="branch_location" required>
            <u-input
              v-model="formData.branch_location"
              placeholder="请输入分会所在地区"
              maxlength="200"
              :clearable="true"
            />
          </u-form-item>

          <u-form-item label="申请人昵称" prop="nickname" required>
            <u-input
              v-model="formData.nickname"
              placeholder="请输入您的昵称（2-20个字符）"
              maxlength="20"
              :clearable="true"
            />
          </u-form-item>

          <u-form-item label="微信二维码" prop="wechat_qr_image" required>
            <view class="upload-container">
              <view v-if="!formData.wechat_qr_image" class="upload-placeholder" @click="chooseWechatQrImage">
                <u-icon name="camera" size="60rpx" color="#6AC086"></u-icon>
                <view class="upload-hint">点击上传</view>
              </view>
              <view v-else class="upload-preview" @click="previewWechatQrImage">
                <image :src="formData.wechat_qr_image" mode="aspectFit" class="preview-image"></image>
                <view class="upload-actions">
                  <u-icon name="eye" size="40rpx" color="#fff" @click.stop="previewWechatQrImage"></u-icon>
                  <u-icon name="trash" size="40rpx" color="#fff" @click.stop="removeWechatQrImage"></u-icon>
                </view>
              </view>
            </view>
          </u-form-item>

          <u-form-item label="分会描述" prop="branch_description">
            <u-textarea
              v-model="formData.branch_description"
              placeholder="请简要描述分会的特色和定位"
              maxlength="500"
              count
              height="120rpx"
            />
          </u-form-item>
          
          <u-form-item label="申请理由" prop="application_reason" required>
            <u-textarea
              v-model="formData.application_reason"
              placeholder="请详细说明您申请成为分会长的理由（至少50字）"
              maxlength="500"
              count
              height="200rpx"
            />
          </u-form-item>
        </u-form>
        
        <view class="submit-container">
          <u-button
            type="primary"
            :loading="submitting"
            :disabled="submitting"
            @click="submitApplication"
            customStyle="background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%); border: none; border-radius: 50rpx; height: 88rpx; font-size: 32rpx;"
          >
            {{ submitting ? '提交中...' : '提交申请' }}
          </u-button>
        </view>
        
        <view class="tips-container">
          <view class="tips-title">申请须知：</view>
          <view class="tips-item">• 申请提交后，管理员将在3个工作日内审核</view>
          <view class="tips-item">• 审核通过后，您将成为该分会的分会长</view>
          <view class="tips-item">• 分会长可以审核本分会的活动并获得运营佣金</view>
          <view class="tips-item">• 请确保分会名称的唯一性和合规性</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";
import { branch_presidentapply } from "@/api";
import { store } from "@/store";

// 表单数据
const formData = reactive({
  branch_name: '',
  branch_location: '',
  nickname: '',
  wechat_qr_image: '',
  branch_description: '',
  application_reason: ''
});

// 表单验证规则
const rules = reactive({
  branch_name: [
    {
      required: true,
      message: '请输入分会名称',
      trigger: ['blur', 'change']
    },
    {
      min: 2,
      max: 100,
      message: '分会名称长度应在2-100字符之间',
      trigger: ['blur', 'change']
    }
  ],
  branch_location: [
    {
      required: true,
      message: '请输入分会地区',
      trigger: ['blur', 'change']
    },
    {
      max: 200,
      message: '地区名称不能超过200字符',
      trigger: ['blur', 'change']
    }
  ],
  nickname: [
    {
      required: true,
      message: '请输入申请人昵称',
      trigger: ['blur', 'change']
    },
    {
      min: 2,
      max: 20,
      message: '昵称长度应在2-20个字符之间',
      trigger: ['blur', 'change']
    }
  ],
  wechat_qr_image: [
    {
      required: true,
      message: '请上传微信二维码',
      trigger: ['blur', 'change']
    }
  ],
  application_reason: [
    {
      required: true,
      message: '请输入申请理由',
      trigger: ['blur', 'change']
    },
    {
      min: 50,
      max: 500,
      message: '申请理由应在50-500字符之间',
      trigger: ['blur', 'change']
    }
  ]
});

// 表单引用
const formRef = ref();
const submitting = ref(false);

// 页面加载
onLoad(() => {
  // 检查登录状态
  if (!requireLogin()) {
    return;
  }
  
  // 检查用户是否已经是分会长
  const userInfo = store().$state.userInfo;
  if (userInfo?.role_type === '1') {
    uni.showModal({
      title: '提示',
      content: '您已经是分会长，无需重复申请',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
    return;
  }
  
  // 🔧 修复：移除会员身份验证，根据业务需求不需要会员身份即可申请分会长
  // 原验证逻辑已移除
});

// 提交申请
const submitApplication = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      return;
    }
    
    submitting.value = true;
    
    const userInfo = store().$state.userInfo;
    const params = {
      uid: userInfo.uid,
      token: userInfo.token,
      ...formData
    };
    
    const res = await branch_presidentapply(params);
    
    if (res.status === 'ok') {
      uni.showToast({
        title: '申请提交成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
      // 跳转到登录页面
      navto('/pages/bundle/common/login');
    } else {
      uni.showToast({
        title: res.msg || '申请失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('申请失败:', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    });
  } finally {
    submitting.value = false;
  }
};

// 选择微信二维码图片
const chooseWechatQrImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 验证图片格式
      const fileExtension = tempFilePath.split('.').pop().toLowerCase();
      if (!['jpg', 'jpeg', 'png'].includes(fileExtension)) {
        uni.showToast({
          title: '请选择jpg或png格式的图片',
          icon: 'none'
        });
        return;
      }

      // 上传图片
      uploadWechatQrImage(tempFilePath);
    },
    fail: (error) => {
      console.error('选择图片失败:', error);
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      });
    }
  });
};

// 上传微信二维码图片
const uploadWechatQrImage = (filePath) => {
  uni.showLoading({
    title: '上传中...'
  });

  const userInfo = store().$state.userInfo;

  uni.uploadFile({
    url: `${store().$state.url}config/upload_img`,
    filePath: filePath,
    name: 'img',
    formData: {
      uid: userInfo.uid,
      token: userInfo.token,
      type: 'wechat_qr'
    },
    success: (uploadRes) => {
      try {
        const result = JSON.parse(uploadRes.data);
        if (result.status === 'ok') {
          // 🔧 修复：根据实际API返回结构获取图片URL
          formData.wechat_qr_image = result.data || result.url;
          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: result.msg || '上传失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('解析上传结果失败:', error);
        console.error('上传响应数据:', uploadRes.data);
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    },
    fail: (error) => {
      console.error('上传失败:', error);
      uni.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      });
    },
    complete: () => {
      uni.hideLoading();
    }
  });
};

// 预览微信二维码图片
const previewWechatQrImage = () => {
  uni.previewImage({
    urls: [formData.wechat_qr_image],
    current: formData.wechat_qr_image
  });
};

// 移除微信二维码图片
const removeWechatQrImage = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张微信二维码图片吗？',
    success: (res) => {
      if (res.confirm) {
        formData.wechat_qr_image = '';
        uni.showToast({
          title: '已删除',
          icon: 'success'
        });
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.submit-container {
  margin-top: 60rpx;
  margin-bottom: 40rpx;
}

.tips-container {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  border-left: 6rpx solid #6AC086;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.tips-item {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.tips-item:last-child {
  margin-bottom: 0;
}

// uView组件样式覆盖
:deep(.u-form-item__label) {
  font-size: 28rpx !important;
  font-weight: bold !important;
  color: #333333 !important;
}

:deep(.u-input__content__field-wrapper__field) {
  font-size: 28rpx !important;
}

:deep(.u-textarea__content__field) {
  font-size: 28rpx !important;
}

// 🆕 微信二维码上传组件样式
.upload-container {
  width: 100%;
}

.upload-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #6AC086;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(106, 192, 134, 0.05);
  transition: all 0.3s ease;

  &:active {
    background: rgba(106, 192, 134, 0.1);
    border-color: #88D7A0;
    transform: scale(0.98);
  }
}

.upload-hint {
  font-size: 24rpx;
  color: #6AC086;
  margin-top: 10rpx;
  font-weight: 500;
}

.upload-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.upload-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 20rpx;
}

.upload-preview:active .upload-actions {
  opacity: 1;
}
</style>
