<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="活动宝 - 世界动态">
    <title>活动宝 - 世界</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 页面特定样式 */
        .world-header {
            background: var(--color-surface);
            border-bottom: 0.05rem solid var(--color-border-light);
            position: sticky;
            top: 0;
            z-index: var(--z-index-sticky);
        }
        
        .world-tabs {
            display: flex;
            background: var(--color-surface);
        }
        
        .world-tab {
            flex: 1;
            padding: var(--spacing-md);
            text-align: center;
            font-size: var(--font-size-md);
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            border: none;
            background: none;
        }
        
        .world-tab:hover {
            color: var(--color-primary);
        }
        
        .world-tab.active {
            color: var(--color-text-primary);
            font-weight: var(--font-weight-semibold);
        }
        
        .world-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 1.07rem; /* 20rpx */
            height: 0.11rem; /* 2rpx */
            background: linear-gradient(90deg, #FFD700, #FFA500);
            border-radius: var(--radius-xs);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                width: 0;
                opacity: 0;
            }
            to {
                width: 1.07rem;
                opacity: 1;
            }
        }
        
        .world-content {
            padding: var(--spacing-md) 0;
            min-height: calc(100vh - 8rem);
        }
        
        .feed-item {
            background: var(--color-surface);
            margin: 0 var(--spacing-lg) var(--spacing-md);
            border-radius: var(--radius-md);
            box-shadow: 0 0.11rem 0.32rem rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: all var(--transition-normal);
            position: relative;
        }

        .feed-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 0.11rem;
            background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
            transform: scaleX(0);
            transition: transform var(--transition-normal);
        }

        .feed-item:hover {
            transform: translateY(-0.05rem);
            box-shadow: 0 0.21rem 0.64rem rgba(0, 0, 0, 0.08);
        }

        .feed-item:hover::before {
            transform: scaleX(1);
        }
        
        .feed-header {
            padding: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .feed-avatar {
            width: 2.13rem; /* 40rpx */
            height: 2.13rem;
            border-radius: var(--radius-round);
            object-fit: cover;
            background: var(--color-gray-200);
        }
        
        .feed-user-info {
            flex: 1;
        }
        
        .feed-username {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
            margin-bottom: 0.13rem; /* 2rpx */
        }
        
        .feed-time {
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
        }
        
        .feed-content {
            padding: 0 var(--spacing-md) var(--spacing-md);
        }
        
        .feed-text {
            font-size: var(--font-size-md);
            line-height: var(--line-height-relaxed);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .feed-images {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
        }
        
        .feed-image {
            width: 100%;
            height: 4rem; /* 75rpx */
            object-fit: cover;
            border-radius: var(--radius-xs);
            background: var(--color-gray-100);
        }
        
        .feed-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            border-top: 0.05rem solid var(--color-border-light);
        }
        
        .feed-action {
            display: flex;
            align-items: center;
            gap: 0.27rem; /* 5rpx */
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
            padding: var(--spacing-xs);
            border-radius: var(--radius-xs);
        }
        
        .feed-action:hover {
            color: var(--color-primary);
            background: var(--color-primary-lighter);
        }
        
        .feed-action.liked {
            color: #ff4757;
        }
        
        .feed-action.liked:hover {
            color: #ff3742;
        }
        
        .fab-container {
            position: fixed;
            bottom: 5.33rem; /* 100rpx */
            right: var(--spacing-lg);
            z-index: var(--z-index-fixed);
        }
        
        .fab-menu {
            position: absolute;
            bottom: 4rem; /* 75rpx */
            right: 0;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            opacity: 0;
            visibility: hidden;
            transform: translateY(0.53rem); /* 10rpx */
            transition: all var(--transition-normal);
        }
        
        .fab-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .fab-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: var(--color-surface);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-round);
            box-shadow: var(--shadow-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            white-space: nowrap;
        }
        
        .fab-option:hover {
            transform: translateX(-0.27rem); /* 5rpx */
            box-shadow: var(--shadow-lg);
        }
        
        .fab-option-icon {
            width: 1.6rem; /* 30rpx */
            height: 1.6rem;
            border-radius: var(--radius-round);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            color: white;
        }
        
        .fab-option-text {
            font-size: var(--font-size-sm);
            color: var(--color-text-primary);
        }
        
        .fab-main {
            width: 3.2rem; /* 60rpx */
            height: 3.2rem;
            border-radius: var(--radius-round);
            background: var(--color-primary);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
            transition: all var(--transition-normal);
        }
        
        .fab-main:hover {
            transform: scale(1.1);
            box-shadow: 0 0.32rem 0.85rem rgba(106, 192, 134, 0.4);
        }
        
        .fab-main.rotated {
            transform: rotate(45deg);
        }
        
        .main-content {
            padding-bottom: 4rem; /* 为底部导航栏留空间 */
        }
        
        /* 日卡样式 */
        .card-item {
            background: var(--color-surface);
            margin: 0 var(--spacing-lg) var(--spacing-md);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            position: relative;
        }
        
        .card-image {
            width: 100%;
            height: 10.67rem; /* 200rpx */
            object-fit: cover;
            background: linear-gradient(45deg, var(--color-primary), var(--color-primary-light));
        }
        
        .card-content {
            padding: var(--spacing-md);
        }
        
        .card-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .card-description {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            line-height: var(--line-height-relaxed);
        }
        
        .card-date {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.13rem 0.27rem; /* 2rpx 5rpx */
            border-radius: var(--radius-xs);
            font-size: var(--font-size-xs);
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- 头部标签栏 -->
        <header class="world-header">
            <div class="world-tabs">
                <button class="world-tab active" data-tab="card">日卡</button>
                <button class="world-tab" data-tab="feed">动态</button>
                <button class="world-tab" data-tab="diary">日记</button>
                <button class="world-tab" data-tab="quote">摘录</button>
            </div>
        </header>
        
        <main class="main-content">
            <div class="world-content">
                <!-- 日卡内容 -->
                <div id="card-content" class="tab-content active">
                    <div class="card-item">
                        <img src="https://via.placeholder.com/345x200/6AC086/ffffff?text=日卡" alt="日卡" class="card-image">
                        <div class="card-date">04-09</div>
                        <div class="card-content">
                            <h3 class="card-title">今日心情</h3>
                            <p class="card-description">重要的不是治愈，而是带着病痛活下去。</p>
                        </div>
                    </div>
                </div>
                
                <!-- 动态内容 -->
                <div id="feed-content" class="tab-content">
                    <article class="feed-item hover-lift fade-in">
                        <div class="feed-header">
                            <img src="https://via.placeholder.com/40x40/88D7A0/ffffff?text=青" alt="用户头像" class="feed-avatar">
                            <div class="feed-user-info">
                                <div class="feed-username text-hierarchy-h2">青</div>
                                <div class="feed-time text-hierarchy-caption">04-09 23:59</div>
                            </div>
                        </div>
                        <div class="feed-content">
                            <div class="feed-text text-hierarchy-body">今天天气真好，出门散散步，心情舒畅！#日常 #好天气</div>
                            <div class="feed-images">
                                <img src="https://via.placeholder.com/150x75/5CB85C/ffffff?text=🏠" alt="图片1" class="feed-image">
                                <img src="https://via.placeholder.com/150x75/7BC97B/ffffff?text=🏡" alt="图片2" class="feed-image">
                            </div>
                        </div>
                        <div class="feed-actions">
                            <div class="feed-action" data-action="like">
                                <i class="far fa-heart"></i>
                                <span>1</span>
                            </div>
                            <div class="feed-action" data-action="comment">
                                <i class="far fa-comment"></i>
                                <span>6</span>
                            </div>
                            <div class="feed-action" data-action="favorite">
                                <i class="far fa-bookmark"></i>
                                <span>收藏</span>
                            </div>
                            <div class="feed-action" data-action="share">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </div>
                        </div>
                    </article>
                    
                    <article class="feed-item">
                        <div class="feed-header">
                            <img src="https://via.placeholder.com/40x40/5CB85C/ffffff?text=青" alt="用户头像" class="feed-avatar">
                            <div class="feed-user-info">
                                <div class="feed-username">青</div>
                                <div class="feed-time">04-09 23:59</div>
                            </div>
                        </div>
                        <div class="feed-content">
                            <div class="feed-text">昨晚和朋友吃了顿大餐，太满足了！#美食 #朋友聚餐</div>
                            <div class="feed-images">
                                <img src="https://via.placeholder.com/345x75/FF6B6B/ffffff?text=🍕" alt="美食图片" class="feed-image" style="grid-column: 1 / -1;">
                            </div>
                        </div>
                        <div class="feed-actions">
                            <div class="feed-action" data-action="like">
                                <i class="far fa-heart"></i>
                                <span>1</span>
                            </div>
                            <div class="feed-action" data-action="comment">
                                <i class="far fa-comment"></i>
                                <span>6</span>
                            </div>
                            <div class="feed-action" data-action="favorite">
                                <i class="far fa-bookmark"></i>
                                <span>收藏</span>
                            </div>
                            <div class="feed-action" data-action="share">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </div>
                        </div>
                    </article>
                </div>
                
                <!-- 日记内容 -->
                <div id="diary-content" class="tab-content">
                    <div class="empty-state">
                        <div class="empty-state-icon">📝</div>
                        <div class="empty-state-title">暂无日记</div>
                        <div class="empty-state-description">开始记录你的生活点滴吧</div>
                    </div>
                </div>
                
                <!-- 摘录内容 -->
                <div id="quote-content" class="tab-content">
                    <div class="empty-state">
                        <div class="empty-state-icon">📖</div>
                        <div class="empty-state-title">暂无摘录</div>
                        <div class="empty-state-description">收藏你喜欢的文字片段</div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- 浮动操作按钮 -->
        <div class="fab-container">
            <div class="fab-menu" id="fab-menu">
                <div class="fab-option" data-type="card">
                    <div class="fab-option-icon" style="background: #FFD700;">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="fab-option-text">日卡</div>
                </div>
                <div class="fab-option" data-type="feed">
                    <div class="fab-option-icon" style="background: #6AC086;">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="fab-option-text">动态</div>
                </div>
                <div class="fab-option" data-type="diary">
                    <div class="fab-option-icon" style="background: #FF6B6B;">
                        <i class="fas fa-pen"></i>
                    </div>
                    <div class="fab-option-text">日记</div>
                </div>
                <div class="fab-option" data-type="quote">
                    <div class="fab-option-icon" style="background: #4ECDC4;">
                        <i class="fas fa-quote-right"></i>
                    </div>
                    <div class="fab-option-text">摘录</div>
                </div>
            </div>
            <button class="fab-main" id="fab-main">
                <i class="fas fa-pen"></i>
            </button>
        </div>
        
        <!-- 底部导航栏 -->
        <nav class="bottom-nav">
            <a href="index.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="bottom-nav-text">活动</div>
            </a>
            <a href="world.html" class="bottom-nav-item active">
                <div class="bottom-nav-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="bottom-nav-text">世界</div>
            </a>
            <a href="add-activity.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="bottom-nav-text">发布</div>
            </a>
            <a href="my.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="bottom-nav-text">我的</div>
            </a>
        </nav>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签切换功能
            const tabs = document.querySelectorAll('.world-tab');
            const contents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // 切换标签状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容显示
                    contents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === targetTab + '-content') {
                            content.classList.add('active');
                        }
                    });
                });
            });
            
            // 浮动按钮功能
            const fabMain = document.getElementById('fab-main');
            const fabMenu = document.getElementById('fab-menu');
            let isMenuOpen = false;
            
            fabMain.addEventListener('click', function() {
                isMenuOpen = !isMenuOpen;
                fabMenu.classList.toggle('show', isMenuOpen);
                this.classList.toggle('rotated', isMenuOpen);
            });
            
            // 点击其他地方关闭菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.fab-container') && isMenuOpen) {
                    isMenuOpen = false;
                    fabMenu.classList.remove('show');
                    fabMain.classList.remove('rotated');
                }
            });
            
            // 动态操作功能
            const feedActions = document.querySelectorAll('.feed-action');
            feedActions.forEach(action => {
                action.addEventListener('click', function() {
                    const actionType = this.dataset.action;
                    
                    if (actionType === 'like') {
                        this.classList.toggle('liked');
                        const icon = this.querySelector('i');
                        const count = this.querySelector('span');
                        
                        if (this.classList.contains('liked')) {
                            icon.className = 'fas fa-heart';
                            count.textContent = parseInt(count.textContent) + 1;
                        } else {
                            icon.className = 'far fa-heart';
                            count.textContent = parseInt(count.textContent) - 1;
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>
