/**
 * 默认数据模块 - 提供API请求失败时的兜底数据
 */

// 默认日卡数据
export const defaultCards = [
  {
    id: 'default-1',
    title: '生活的馈赠',
    description: '我们活在世上，不是为了寻找什么，而是为了创造一些东西，去发现生活的馈赠。',
    author: '路遥',
    source: '平凡的世界',
    image: '/static/images/cards/default-1.jpg',
    tags: ['励志', '成长'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-2',
    title: '星辰大海',
    description: '我们终将浮出水面，仰望星空，找到各自的星辰大海。',
    author: '刘慈欣',
    source: '三体',
    image: '/static/images/cards/default-2.jpg',
    tags: ['唯美', '宇宙'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-3',
    title: '温暖的力量',
    description: '世界上最温暖的两个字是理解，最浪漫的四个字是陪你到老。',
    author: '张嘉佳',
    source: '从你的全世界路过',
    image: '/static/images/cards/default-3.jpg',
    tags: ['爱情', '治愈'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-4',
    title: '前路漫漫',
    description: '前路漫漫，万事皆为艰难，我心向往之，必将抵达。',
    author: '余光中',
    source: '乡愁',
    image: '/static/images/cards/default-4.jpg',
    tags: ['励志', '成长'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-5',
    title: '梦想的距离',
    description: '梦想，我想说你是个什么东西，让我们如此眷恋。',
    author: '韩寒',
    source: '1988：我想和这个世界谈谈',
    image: '/static/images/cards/default-5.jpg',
    tags: ['思考', '青春'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-6',
    title: '静水流深',
    description: '真正的关系是不声不响的，它无需解释，无需证明，静水流深。',
    author: '几米',
    source: '向左走向右走',
    image: '/static/images/cards/default-6.jpg',
    tags: ['爱情', '治愈'],
    created_at: new Date().toISOString(),
    liked: false
  }
];

// 情感标签
export const emotionTags = [
  '励志', '治愈', '伤感', '喜悦', '思考', '平静'
];

// 主题标签
export const themeTags = [
  '爱情', '成长', '友情', '亲情', '旅行', '生活', '工作', '阅读', '音乐', '电影'
];

// 默认背景颜色（与情感标签对应）
export const tagColors = {
  '励志': '#FFF3E0',
  '治愈': '#E8F5E9',
  '伤感': '#F5F5F5',
  '喜悦': '#FFF8E1',
  '思考': '#E0F7FA',
  '平静': '#F3E5F5',
  '爱情': '#FFE0E6',
  '成长': '#E8F5E9',
  '友情': '#E3F2FD',
  '亲情': '#FFEBEE',
  '旅行': '#E0F2F1',
  '生活': '#F9FBE7',
  '工作': '#E8EAF6',
  '阅读': '#FFF8E1',
  '音乐': '#F3E5F5',
  '电影': '#E0F7FA'
};

// 提供一个获取随机卡片的方法
export function getRandomCards(count = 3) {
  const shuffled = [...defaultCards].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 提供一个根据标签筛选卡片的方法
export function filterCardsByTag(tag) {
  return defaultCards.filter(card => card.tags.includes(tag));
} 