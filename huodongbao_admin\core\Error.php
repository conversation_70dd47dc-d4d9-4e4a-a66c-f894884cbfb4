<?php
namespace core;
class Error{

	public function __construct(){

	}
	//类不存在
	static public function emptyClass($className){
		header("Content-type: application/json; charset=utf-8;");
		echo json_encode(["status"=>"error","msg"=>"c error:".base64_encode($className)]);
		exit;
	}
	//文件不存在
	static public function emptyFile($filePath){
		header("Content-type: application/json; charset=utf-8;");
		echo json_encode(["status"=>"error","msg"=>"f error:".base64_encode($filePath)]);
		exit;
		
	}
	//404
	static public function urlErr($filePath=""){
		header("Content-type: application/json; charset=utf-8;");
		echo json_encode(["status"=>"error","msg"=>"url:".base64_encode($filePath)]);
		exit;
	}
	//未通过安全检查
	static public function securityCheck(){
		header("Content-type: application/json; charset=utf-8;");
		echo json_encode(["status"=>"error","msg"=>"Forbidden"]);
		exit;
	}
	public function __destruct(){

	}
}
