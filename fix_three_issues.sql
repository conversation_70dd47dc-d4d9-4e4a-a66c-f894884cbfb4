-- 修复活动宝后台管理系统三个问题的数据库修复SQL
-- 执行前请备份相关数据表

-- ==================== 问题1：用户角色转换功能缺陷修复 ====================
-- 检查分会长用户的分会关联情况
SELECT u.uid, u.nickname, u.role_type, u.branch_id, 
       b.branch_name, b.branch_location, b.branch_leader
FROM user u
LEFT JOIN user_branch b ON u.branch_id = b.branch_id
WHERE u.role_type = '1'
ORDER BY u.uid;

-- 修复分会长没有正确分会关联的问题（如果发现有role_type=1但branch_id为NULL的用户）
-- 注意：这个SQL需要根据实际情况调整，以下是示例
-- UPDATE user SET branch_id = 0 WHERE role_type = '1' AND branch_id IS NULL;

-- 检查user_branch表中是否有孤立的分会记录（有分会但没有对应的分会长）
SELECT b.branch_id, b.branch_name, b.branch_leader, u.uid, u.nickname, u.role_type
FROM user_branch b
LEFT JOIN user u ON b.branch_leader = u.uid
WHERE b.branch_leader > 0 AND (u.uid IS NULL OR u.role_type != '1');

-- ==================== 问题2：通知已读状态异常修复 ====================
-- 检查通知表结构和数据
SELECT COUNT(*) as total_notifications, 
       SUM(CASE WHEN is_global = 1 THEN 1 ELSE 0 END) as global_notifications,
       SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_notifications
FROM user_notifications;

-- 检查是否有重复的全局通知已读记录
SELECT uid, related_id, COUNT(*) as count
FROM user_notifications 
WHERE type = 'global_read' 
GROUP BY uid, related_id 
HAVING COUNT(*) > 1;

-- 清理重复的全局通知已读记录（如果存在）
-- DELETE n1 FROM user_notifications n1
-- INNER JOIN user_notifications n2 
-- WHERE n1.id > n2.id 
--   AND n1.uid = n2.uid 
--   AND n1.type = 'global_read' 
--   AND n1.related_id = n2.related_id;

-- ==================== 问题3：申请分会长会员验证错误修复 ====================
-- 这个问题主要是前端代码修复，无需数据库操作
-- 但可以检查当前申请分会长的用户会员状态分布
SELECT 
    COUNT(*) as total_applications,
    SUM(CASE WHEN u.is_huiyuan = 1 THEN 1 ELSE 0 END) as member_applications,
    SUM(CASE WHEN u.is_huiyuan = 0 THEN 1 ELSE 0 END) as non_member_applications
FROM branch_president_applications bpa
LEFT JOIN user u ON bpa.user_id = u.uid
WHERE bpa.status = 0; -- 待审核状态

-- 检查分会长申请表的状态分布
SELECT status, COUNT(*) as count,
       CASE 
           WHEN status = 0 THEN '待审核'
           WHEN status = 1 THEN '已通过'
           WHEN status = 2 THEN '已拒绝'
           ELSE '未知状态'
       END as status_name
FROM branch_president_applications
GROUP BY status;

-- ==================== 数据一致性检查 ====================
-- 检查分会成员数量是否与实际一致
SELECT 
    b.branch_id,
    b.branch_name,
    b.branch_members as recorded_count,
    COUNT(u.uid) as actual_count,
    (b.branch_members - COUNT(u.uid)) as difference
FROM user_branch b
LEFT JOIN user u ON b.branch_id = u.branch_id
GROUP BY b.branch_id, b.branch_name, b.branch_members
HAVING difference != 0;

-- 更新分会成员数量（如果发现不一致）
-- UPDATE user_branch b 
-- SET branch_members = (
--     SELECT COUNT(*) 
--     FROM user u 
--     WHERE u.branch_id = b.branch_id
-- );

-- ==================== 验证修复结果 ====================
-- 验证分会长角色转换修复结果
SELECT 
    '分会长用户检查' as check_type,
    COUNT(*) as count
FROM user u
INNER JOIN user_branch b ON u.branch_id = b.branch_id
WHERE u.role_type = '1' AND b.branch_leader = u.uid;

-- 验证通知系统修复结果
SELECT 
    '通知系统检查' as check_type,
    COUNT(DISTINCT uid) as users_with_notifications
FROM user_notifications
WHERE uid > 0 OR is_global = 1;

-- 验证申请分会长功能修复结果
SELECT 
    '分会长申请检查' as check_type,
    COUNT(*) as pending_applications
FROM branch_president_applications
WHERE status = 0;
