<script setup>
import { ref, onMounted, reactive, nextTick } from 'vue';
import { getFeeds, likeFeed, favoriteFeed } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth';
import { onReachBottom, onLoad, onShareAppMessage } from "@dcloudio/uni-app"; // Import for infinite scroll & onLoad for waterfall
import SharePopup from '@/components/share-popup/share-popup.vue';

// --- Refs & Reactive State ---
const feeds = ref([]); // 直接使用这个数组渲染列表
const isLoading = ref(false);
const isError = ref(false);
const errorMessage = ref('');
const hasMore = ref(true);

// 数据缓存机制 - 解决Tab切换数据丢失问题
const feedsCache = ref(new Map()); // 缓存不同分类的数据
const pageCache = ref(new Map()); // 缓存分页信息
const hasMoreCache = ref(new Map()); // 缓存是否还有更多数据
const form = reactive({
  page: 1,
  page_size: 10,
  uid: 0, // Initialize, will be set from ref
  token: '', // Initialize, will be set from ref
  user_id: 0,
  category: 'latest', // 初始化分类参数
  type: 'feed' // 初始化类型参数
});
const categories = ref([
    { name: '最新', id: 'latest' },
    { name: '热门', id: 'hot' },
]);
const currentCategory = ref('latest');

// --- Functions ---

/**
 * 格式化时间戳为易读格式
 * @param {string} timestamp - 时间戳字符串 (e.g., "YYYY-MM-DD HH:MM:SS")
 * @returns {string} 格式化后的时间字符串
 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';

  // 修复iOS日期格式问题
  const formattedTimeStr = timestamp.replace(/-/g, '/');
  const past = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - past;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = past.getFullYear();
  const month = String(past.getMonth() + 1).padStart(2, '0');
  const day = String(past.getDate()).padStart(2, '0');
  const hours = String(past.getHours()).padStart(2, '0');
  const minutes = String(past.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// 修复：生成包含用户ID的缓存键，确保不同用户数据隔离
const generateFeedCacheKey = (category, userId = null) => {
    // 优先使用传入的userId，然后是form.uid，最后是store中的uid
    const userInfo = store().$state.userInfo;
    const uid = userId || form.uid || userInfo?.uid || 'anonymous';
    return `feed_${category}_user_${uid}`;
};

const handleCategoryChange = (id) => {
    if (currentCategory.value === id) {
        console.log(`分类 ${id} 已经是当前选中状态，跳过切换`);
        return;
    }

    console.log(`分类切换: ${currentCategory.value} -> ${id}`);

    // 保存当前分类的数据到缓存 - 修复：使用包含用户ID的缓存键
    if (feeds.value.length > 0) {
        const currentCacheKey = generateFeedCacheKey(currentCategory.value, form.uid);
        feedsCache.value.set(currentCacheKey, [...feeds.value]);
        pageCache.value.set(currentCacheKey, form.page);
        hasMoreCache.value.set(currentCacheKey, hasMore.value);
        console.log(`缓存分类 ${currentCategory.value} 的数据，用户 ${form.uid}，共 ${feeds.value.length} 条`);
    }

    // 更新分类
    currentCategory.value = id;

    // 检查是否有缓存数据 - 修复：使用包含用户ID的缓存键
    const newCacheKey = generateFeedCacheKey(id, form.uid);
    if (feedsCache.value.has(newCacheKey)) {
        // 从缓存恢复数据
        feeds.value = [...feedsCache.value.get(newCacheKey)];
        form.page = pageCache.value.get(newCacheKey) || 1;
        hasMore.value = hasMoreCache.value.get(newCacheKey) || true;
        isLoading.value = false;
        isError.value = false;
        errorMessage.value = '';
        console.log(`从缓存恢复分类 ${id} 的数据，用户 ${form.uid}，共 ${feeds.value.length} 条`);
    } else {
        // 没有缓存，重置状态并获取新数据
        resetFeedList();
        nextTick(() => {
            console.log(`开始获取分类 ${id} 的新数据，用户 ${form.uid}`);
            fetchFeeds();
        });
    }
};

/**
 * 获取动态列表数据
 * @param {boolean} [loadMore=false] - 是否为加载更多操作
 */
const fetchFeeds = async (loadMore = false) => {
  // 直接从store获取用户信息，避免使用可能未定义的ref变量
  const userInfo = store().$state.userInfo;
  const currentUid = userInfo?.uid || 0;
  const currentToken = userInfo?.token || '';

  console.log(`fetchFeeds called - loadMore: ${loadMore}, uid: ${currentUid}, token present: ${!!currentToken}, category: ${currentCategory.value}`);

  // 防止重复请求
  if (isLoading.value) {
    console.log('正在加载中，跳过重复请求');
    return;
  }

  isLoading.value = true;
  isError.value = false;
  errorMessage.value = '';

  if (!loadMore) {
    form.page = 1;
    hasMore.value = true;
    console.log('首次加载，重置分页状态');
  } else {
    console.log(`加载更多，当前页码: ${form.page}`);
  }

  try {
     // 更新表单中的用户信息
     form.uid = currentUid;
     form.token = currentToken;

     // 添加分类参数 - 后端排序逻辑使用$_REQUEST['category']
     form.category = currentCategory.value;

     // 确保只获取动态类型的内容
     form.type = 'feed';

    console.log('Executing API call: getFeeds with params:', JSON.parse(JSON.stringify(form)));
    const res = await getFeeds(form);
    console.log('API getFeeds response:', res);

    if (res.status === 'ok' && res.data?.list) {
      const newFeeds = res.data.list.map(feed => {
        // 从全局状态获取点赞和收藏状态
        const globalLikeState = store().getFeedLikeState(feed.id);
        const globalFavoriteState = store().getFeedFavoriteState(feed.id);

        return {
          ...feed,
          // 优先使用全局状态，如果没有则使用API返回的状态
          isLiked: globalLikeState ? globalLikeState.isLiked : feed.isLiked,
          likeCount: globalLikeState ? globalLikeState.likeCount : feed.likeCount,
          isFavorited: globalFavoriteState !== null ? globalFavoriteState : feed.isFavorited,
          isExpanded: false // 保留以防意外访问
        };
      });
      if (loadMore) {
        feeds.value.push(...newFeeds); // 直接追加到数组
      } else {
        feeds.value = newFeeds; // 直接替换数组
      }
      hasMore.value = feeds.value.length < (res.data.total || 0); // 基于 feeds.value 判断
      form.page++;

      // 更新缓存 - 修复：使用包含用户ID的缓存键
      const cacheKey = generateFeedCacheKey(currentCategory.value, form.uid);
      feedsCache.value.set(cacheKey, [...feeds.value]);
      pageCache.value.set(cacheKey, form.page);
      hasMoreCache.value.set(cacheKey, hasMore.value);
      console.log(`更新分类 ${currentCategory.value} 的缓存，用户 ${form.uid}，共 ${feeds.value.length} 条数据`);
    } else if (res.status === 'empty') {
       if (!loadMore) {
         feeds.value = []; // 清空数组
      }
      hasMore.value = false;
    } else {
      isError.value = true;
      errorMessage.value = res.msg || '获取动态失败';
       if (!loadMore) {
          feeds.value = []; // 清空数组
       }
       hasMore.value = false;
    }
  } catch (error) {
    console.error('Error caught in fetchFeeds:', error);
    console.error('Error fetching feeds:', error);
    isError.value = true;
    errorMessage.value = '加载失败，请稍后重试';
     if (!loadMore) {
         feeds.value = []; // 清空数组
     }
     hasMore.value = false;
  } finally {
    isLoading.value = false;
  }
};

const resetFeedList = () => {
    console.log('重置动态列表状态');

    // 清空数据
    feeds.value = [];

    // 重置分页状态
    form.page = 1;
    hasMore.value = true;

    // 重置加载状态
    isLoading.value = false;
    isError.value = false;
    errorMessage.value = '';

    // 强制触发响应式更新
    nextTick(() => {
        console.log('动态列表状态重置完成，数据长度:', feeds.value.length);
    });
}

// --- Event Handlers ---
/**
 * 处理动态卡片点击事件，导航到详情页
 * @param {number} feedId - 动态 ID
 */
const handleFeedClick = (feedId) => {
  try {
    console.log(`handleFeedClick triggered for feedId: ${feedId}`);

    // 即使未登录也允许查看详情，但会在详情页面限制某些操作
    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}&showComments=true`);
  } catch (error) {
    console.error('导航到详情页失败:', error);
    uni.showToast({
      title: '页面加载失败，请重试',
      icon: 'none',
      duration: 2000
    });
  }
};

/**
 * 处理点赞/取消点赞
 * @param {object} feed - 动态对象
 */
const handleLike = async (feed) => {
    // 直接从store获取用户信息，避免使用可能未定义的ref变量
    const userInfo = store().$state.userInfo;
    const currentToken = userInfo?.token;
    const currentUserId = userInfo?.uid;

    console.log(`handleLike triggered - Token exists: ${!!currentToken}, UserID: ${currentUserId}`);

    // 使用统一的登录校验
    if (!requireLogin('', '请先登录后再点赞')) {
        return;
    }

    const originalLikedState = feed.isLiked;
    const originalLikeCount = feed.likeCount;

    // 乐观更新UI
    feed.isLiked = !feed.isLiked;
    feed.likeCount += feed.isLiked ? 1 : -1;

    try {
        const res = await likeFeed({
            id: feed.id,
            uid: currentUserId,
            token: currentToken
        });

        if (res.status !== 'ok') {
            // 操作失败，恢复原状态
            feed.isLiked = originalLikedState;
            feed.likeCount = originalLikeCount;
            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
        } else {
            // 操作成功，更新全局状态
            store().updateFeedLike(feed.id, feed.isLiked, feed.likeCount);
            console.log('Like/Unlike success', res);
        }
    } catch (error) {
        // 发生错误，恢复原状态
        feed.isLiked = originalLikedState;
        feed.likeCount = originalLikeCount;
        uni.showToast({ title: '操作失败，请重试', icon: 'none' });
        console.error('Like feed error:', error);
    }
}

/**
 * 处理收藏/取消收藏
 * @param {object} feed - 动态对象
 */
const handleFavorite = async (feed) => {
    const userInfo = store().$state.userInfo;
    const currentToken = userInfo?.token;
    const currentUserId = userInfo?.uid;

    if (!currentToken) {
        uni.showToast({ title: '请先登录', icon: 'none' });
        return;
    }

    const originalFavoritedState = feed.isFavorited || false;

    // 乐观更新UI
    feed.isFavorited = !feed.isFavorited;

    try {
        const res = await favoriteFeed({
            id: feed.id,
            uid: currentUserId,
            token: currentToken
        });

        if (res.status !== 'ok') {
            // 操作失败，恢复原状态
            feed.isFavorited = originalFavoritedState;
            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
        } else {
            // 操作成功，更新全局状态
            store().updateFeedFavorite(feed.id, feed.isFavorited);
            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
        }
    } catch (error) {
        // 发生错误，恢复原状态
        feed.isFavorited = originalFavoritedState;
        uni.showToast({ title: '操作失败，请重试', icon: 'none' });
        console.error('Favorite feed error:', error);
    }
}

/**
 * 处理用户头像点击，跳转到用户个人信息页面
 * @param {number} userId - 用户ID
 */
const handleUserAvatarClick = (userId) => {
    if (!userId) {
        console.warn('用户ID为空，无法跳转');
        return;
    }

    // 跳转到用户个人信息页面
    navto(`/pages/bundle/user/userInfo?uid=${userId}`);
};

/**
 * 图片加载成功处理
 */
const onImageLoad = () => {
    // 图片加载成功，可以进行一些优化操作
};

/**
 * 图片加载失败处理
 */
const onImageError = () => {
    // 可以设置默认图片或进行其他处理
};

/**
 * 处理评论点击事件
 * @param {number} feedId - 动态 ID
 */
const handleCommentClick = (feedId) => {
    console.log('Comment clicked for feed:', feedId);
    // 导航到评论页面，使用新的日记详情页面
    navto(`/pages/bundle/world/feed/detail?feedId=${feedId}&showComments=true`);
};

// 分享相关状态
const showSharePopup = ref(false);
const currentShareFeed = ref(null);

// 分享配置
onShareAppMessage(() => {
  try {
    if (!currentShareFeed.value) {
      console.warn('动态信息未设置，使用默认分享信息');
      return {
        title: '分享一条精彩动态',
        path: '/pages/bundle/world/feed/index',
        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
      };
    }

    return {
      title: currentShareFeed.value.content ?
        (currentShareFeed.value.content.length > 30 ?
          currentShareFeed.value.content.substring(0, 30) + '...' :
          currentShareFeed.value.content) :
        '分享一条精彩动态',
      path: `/pages/bundle/world/feed/detail?feedId=${currentShareFeed.value.id}`,
      imageUrl: currentShareFeed.value.images?.[0] || store().$state.config?.img_config?.app_logo?.val || ''
    };
  } catch (error) {
    console.error('动态分享配置失败:', error);
    return {
      title: '分享一条精彩动态',
      path: '/pages/bundle/world/feed/index',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  }
});

/**
 * 处理分享点击事件
 * @param {number} feedId - 动态 ID
 * @param {Event} event - 事件对象
 */
const handleShareClick = (feedId, event) => {
    if (event) {
        event.stopPropagation();
        event.preventDefault();
    }

    // 获取当前动态数据
    const currentFeed = feeds.value.find(feed => feed.id === feedId);
    if (!currentFeed) {
        uni.showToast({
            title: '分享内容不存在',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 设置当前分享的动态数据
    currentShareFeed.value = currentFeed;

    // 显示分享弹窗
    showSharePopup.value = true;
};

// 处理分享成功
const handleShareSuccess = (result) => {
    console.log('分享成功:', result);
    uni.showToast({
        title: '分享成功',
        icon: 'success'
    });
};

// 处理分享错误
const handleShareError = (error) => {
    console.error('分享失败:', error);
    uni.showToast({
        title: '分享失败',
        icon: 'none'
    });
};

/**
 * 处理图片点击事件，预览图片
 * @param {string[]} images - 图片 URL 数组
 * @param {number} index - 被点击图片的索引
 */
const handleImageClick = (images, index) => {
    uni.previewImage({
        urls: images,
        current: index
    });
}

// --- Lifecycle Hooks ---
/**
 * 页面加载时执行 (早于 onMounted)
 */
onLoad(() => {
    console.log('FeedIndex onLoad - 开始初始化');

    // 确保用户信息已加载
    const userInfo = store().$state.userInfo;
    if (userInfo?.uid) {
        form.uid = userInfo.uid;
        form.token = userInfo.token || '';
        console.log('用户信息已设置:', { uid: form.uid, hasToken: !!form.token });
    }

    // 立即加载初始数据
    nextTick(() => {
        console.log('开始加载初始动态数据');
        fetchFeeds();
    });

    // 加载本地存储的世界状态
    store().loadWorldStateFromLocal();

    // 立即设置默认分类
    currentCategory.value = 'latest';
    console.log('设置默认分类为:', currentCategory.value);

    // 立即重置列表状态
    resetFeedList();

    // 立即获取数据，不使用nextTick延迟
    console.log('立即开始获取动态数据');
    fetchFeeds();
});

/**
 * 组件挂载后执行
 */
onMounted(() => {
  console.log('FeedIndex onMounted');
  // fetchFeeds(); // Initial fetch (已移动到 onLoad)
});

// 防抖定时器
let reachBottomTimer = null;

/**
 * 页面滚动到底部时触发 (用于无限滚动)
 */
onReachBottom(() => {
  if (reachBottomTimer) {
    clearTimeout(reachBottomTimer);
  }

  reachBottomTimer = setTimeout(() => {
    if (hasMore.value && !isLoading.value) {
      console.log('Reached bottom, loading more feeds...');
      fetchFeeds(true); // Pass true for loadMore
    } else if (!hasMore.value) {
        console.log('No more feeds to load.');
        // Optionally show a "no more data" indicator
    }
  }, 300); // 300ms防抖
});

</script>

<template>
  <view class="feed-page-container">
     <!-- Category Tabs (Scrollable) -->
     <u-sticky offset-top="0" customNavHeight="0"> <!-- Adjust customNavHeight if needed -->
        <scroll-view scroll-x class="category-tabs-scroll" enable-flex>
            <view class="category-tabs-inner">
                <view
                    v-for="category in categories"
                    :key="category.id"
                    class="category-tab-item"
                    :class="{ 'active': currentCategory === category.id }"
                    @click="handleCategoryChange(category.id)"
                >
                    <text class="tab-text">{{ category.name }}</text>
                    <view v-if="currentCategory === category.id" class="tab-indicator"></view>
                </view>
            </view>
        </scroll-view>
     </u-sticky>

    <!-- Feed List (Simple v-for) -->
    <view class="feed-list">

      <!-- Simple List Rendering -->
      <view v-for="item in feeds" :key="item.id">
         <view class="feed-card">
           <!-- Card Content Start -->
            <view class="user-info">
                <image
                    class="avatar"
                    :src="item.user.avatar_url || '/static/default-avatar.png'"
                    mode="aspectFill"
                    @click.stop="handleUserAvatarClick(item.user.uid)"
                ></image>
                <view class="user-meta" @click.stop="handleUserAvatarClick(item.user.uid)">
                    <text class="nickname">{{ item.user.nickname }}</text>
                    <text class="timestamp">{{ formatTimestamp(item.created_at) }}</text>
                </view>
            </view>

            <view class="feed-content" @click="handleFeedClick(item.id)">
                <text>{{ item.content }}</text>
            </view>

           <!-- Image Grid / Single Image -->
            <view v-if="item.images && item.images.length > 0" class="image-display-area">
                <!-- Single Image -->
                <image
                    v-if="item.images.length === 1"
                    class="single-image"
                    :src="item.images[0]"
                    mode="aspectFill"
                    lazy-load
                    :fade-show="true"
                    :webp="true"
                    @click.stop="handleImageClick(item.images, 0)"
                    @load="onImageLoad"
                    @error="onImageError"
                ></image>
                <!-- Image Grid for multiple images -->
                <view v-else class="image-grid">
                     <view
                        v-for="(imgUrl, imgIndex) in item.images.slice(0, 9)"
                        :key="imgIndex"
                        class="image-grid-item"
                        @click.stop="handleImageClick(item.images, imgIndex)"
                     >
                        <image
                            :src="imgUrl"
                            mode="aspectFill"
                            lazy-load
                            :fade-show="true"
                            :webp="true"
                            @load="onImageLoad"
                            @error="onImageError"
                        ></image>
                     </view>
                </view>
            </view>

            <!-- Action Bar -->
            <view class="action-bar">
                 <!-- Left Actions: Like, Comment, Favorite, Share -->
                 <view class="left-actions">
                    <view class="action-item" @click.stop="handleLike(item)">
                        <image :src="item.isLiked ? '/static/dianzanqianhou.svg' : '/static/dianzanqian.svg'" 
                               style="width: 44rpx; height: 44rpx"
                               :style="{ filter: item.isLiked ? 'none' : 'opacity(0.7)' }"></image>
                        <text class="action-text" :style="{ color: item.isLiked ? '#FF6B81' : '#999' }">{{ item.likeCount || 0 }}</text>
                    </view>
                    <view class="action-item" @click.stop="handleCommentClick(item.id)">
                        <image src="/static/pinglun.svg" 
                               style="width: 44rpx; height: 44rpx; opacity: 0.7"></image>
                        <text class="action-text">{{ item.commentCount || 0 }}</text>
                    </view>
                    <view class="action-item" @click.stop="handleFavorite(item)">
                        <image :src="item.isFavorited ? '/static/shoucanghou.svg' : '/static/shoucangqian.svg'" 
                               style="width: 44rpx; height: 44rpx"
                               :style="{ filter: item.isFavorited ? 'none' : 'opacity(0.7)' }"></image>
                        <text class="action-text">收藏</text>
                    </view>
                    <!-- 分享按钮已注释，使用微信右上角分享 -->
                    <!-- <view class="action-item" @click.stop="handleShareClick(item.id, $event)">
                        <image src="/static/fenxiang.svg"
                               style="width: 44rpx; height: 44rpx; opacity: 0.7"></image>
                        <text class="action-text">分享</text>
                    </view> -->
                 </view>
                 <!-- Right Action: Resonance Count - 移除以符合需求 -->
                 <!-- <view class="action-item resonance-count" v-if="item.likeCount > 0">
                    <text>{{ item.likeCount }}人共鸣</text>
                 </view> -->
            </view>
           <!-- Card Content End -->
         </view>
      </view>

       <!-- Loading More / No More Data Indicators -->
      <view v-if="!isLoading && feeds.length > 0"> <!-- Based on feeds.value -->
        <u-loadmore :status="hasMore ? (isLoading ? 'loading' : 'loadmore') : 'nomore'"
                    loadingText="努力加载中"
                    loadmoreText="轻轻上拉"
                    nomoreText="到底啦" />
      </view>
       <!-- Initial Empty State -->
        <view v-if="!isLoading && !isError && feeds.length === 0" class="empty-initial">
            <u-empty mode="list" text="还没有人发布动态哦"></u-empty>
        </view>
         <view v-if="isError && feeds.length === 0" class="error-initial">
           <u-empty mode="network" :text="errorMessage"></u-empty>
      </view>

    </view>

    <!-- 隐藏的Canvas元素用于生成分享图片 -->
    <canvas
      canvas-id="share-canvas"
      id="share-canvas"
      style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"
    ></canvas>

    <!-- 分享弹窗 -->
    <share-popup
      :show="showSharePopup"
      title="分享动态"
      :share-data="{
        image: currentShareFeed?.images?.[0],
        content: currentShareFeed?.content,
        author: currentShareFeed?.user?.nickname,
        date: currentShareFeed?.created_at,
        template: 'dynamic'
      }"
      :show-member-invite="store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1"
      @close="showSharePopup = false"
      @share-success="handleShareSuccess"
      @share-error="handleShareError"
    />

  </view>
</template>

<style lang="scss" scoped>
.action-list {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 20rpx 0;
  background-color: #fff;
}

.share-popup {
  .share-options {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 30rpx 20rpx;

    .share-option {
      display: flex;
      flex-direction: column;
      align-items: center;

      .share-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10rpx;
        background-color: #f5f5f5;
      }

      .share-text {
        font-size: 24rpx;
        color: #333;
      }
    }
  }
}

.feed-page-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--window-top) - var(--window-bottom)); // Full height minus system bars
  background-color: #f8f9fa; // 统一背景色
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

// --- Category Tabs --- (Height 48dp ~ 96rpx)
.category-tabs-scroll {
  width: 100%;
  height: 100rpx; // 稍微增加高度
  background-color: #FFFFFF;
  white-space: nowrap; // Keep tabs in one line
  border-bottom: 1px solid #f0f0f0;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05); // 添加轻微阴影
  position: relative;
  z-index: 10;

  .category-tabs-inner {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 20rpx; // 增加两侧内边距
  }
}

.category-tab-item {
  display: inline-flex; // Use inline-flex for better alignment control
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 30rpx; // 增加内边距
  margin: 0 10rpx;
  font-size: 30rpx; // 增大字体
  color: #666666;
  position: relative;
  transition: all 0.3s ease;

  .tab-text {
      line-height: 100rpx; // 垂直居中文本
  }

  &.active {
    color: #576b95; // 微信蓝色
    font-weight: 600;

    .tab-indicator {
        position: absolute;
        bottom: 12rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 48rpx; // 增加指示器宽度
        height: 6rpx; // 保持厚度
        background-color: #576b95; // 微信蓝色
        border-radius: 3rpx;
    }
  }
}

// --- Feed List & Cards (Adjusted for Waterfall) ---
.feed-list {
  flex: 1;
  padding: 8rpx; // Reduce padding slightly for waterfall columns
  overflow-y: auto;
  padding-top: 20rpx; // 减少顶部间距，因为已有sticky定位的tab
}

.feed-card {
  background-color: #ffffff;
  margin: 0 24rpx 30rpx 24rpx; // 左右24rpx边距，底部30rpx
  padding: 28rpx;             // 增加内边距
  border-radius: 16rpx;        // 圆角
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); // 更明显的阴影
  overflow: hidden; // 隐藏溢出内容
  transition: all 0.2s ease; // 平滑过渡效果
  border: 1px solid #f5f5f5; // 添加细边框

  &:active {
      transform: scale(0.98); // 点击反馈
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); // 点击时减小阴影
  }

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx; // 增加间距
    image {
      width: 80rpx; // 更大的头像
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      flex-shrink: 0;
      border: 1px solid #f0f0f0; // 添加边框
    }
    .user-meta {
      flex: 1; // 允许元数据占据剩余空间
      display: flex;
      flex-direction: column; // 改为纵向排列
      justify-content: center; // 垂直居中

      .nickname {
        font-size: 28rpx;
        font-weight: 600;
        color: #333; // 更深的昵称颜色
        margin-bottom: 6rpx; // 昵称和时间戳之间的间距
      }

      .timestamp {
        font-size: 22rpx;
        color: #999; // 时间戳颜色
      }
    }
  }

  .feed-content {
    font-size: 30rpx;
    color: #333; // 主要内容颜色
    line-height: 1.8; // 增加行高
    margin-bottom: 24rpx;
    letter-spacing: 1rpx; // 增加字间距
    word-break: break-all; // 允许在任意字符间断行
  }

  .image-display-area {
    margin-top: 20rpx;   // 增加上边距
    margin-bottom: 20rpx; // 增加下边距
    border-radius: 12rpx; // 增加圆角
    overflow: hidden;    // 裁剪图像到圆角
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); // 轻微阴影

    .single-image {
      display: block; // 确保图像表现为块元素
      width: 100%;
      aspect-ratio: 3 / 2; // 3:2宽高比
      object-fit: cover;   // 覆盖区域，必要时裁剪
      border-radius: 12rpx; // 匹配区域圆角
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr); // 3等分列
      gap: 8rpx; // 增加网格间隙

      .image-grid-item {
        position: relative;
        width: 100%; // 占满列宽
        aspect-ratio: 1 / 1; // 改为正方形
        overflow: hidden;    // 隐藏内部图像溢出
        border-radius: 8rpx; // 每个项目的圆角
        background-color: #f5f5f5; // 占位背景色

        image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover; // 覆盖网格项
        }
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: space-between; // 左右动作间隔
    align-items: center;
    margin-top: 24rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f5f5f5; // 更浅的分隔线
  }

  .left-actions {
    display: flex;
    align-items: center;
  }

  .action-item {
    display: flex;
    align-items: center;
    margin-right: 36rpx; // 增加动作项之间的间距
    padding: 8rpx 12rpx; // 增加可点击区域
    border-radius: 30rpx; // 圆角
    transition: background-color 0.3s;

    &:active {
      background-color: #f5f5f5; // 点击时背景色变化
    }

    &:last-child {
        margin-right: 0;
    }

    .action-text {
      margin-left: 8rpx;
      font-size: 26rpx; // 增大字体
      color: #666; // 更深的文本颜色
    }
  }
}

.empty-initial, .error-initial {
    margin-top: 100rpx; /* Add some margin for empty/error states */
}

/* 移除空规则集 */

</style>