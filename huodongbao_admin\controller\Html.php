<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 协议管理 
*/
class Html extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/		
	public function index(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['name'])){
			$where .= " AND name LIKE :name";
			$prepareParam[":name"]="%".$_REQUEST['name']."%";
			$pageParams['name'] = $_REQUEST['name'];
		}
		$data = Db()->table("html")->where($where)->prepareParam($prepareParam)->order("id")->page($page,$page_size,$pageParams);
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		return view(['header','html/index','footer']);
	}
	/*
	* @name 编辑
	* @method edit
	*/	
	public function edit(){
		if(empty($_REQUEST['id']) || !check($_REQUEST['id'],"intgt0")){
			header("location:".url("html/index"));
		}
		$id = (int)$_REQUEST['id'];
		dbConn();
		if(
			!empty($_POST['name']) && 
			!empty($_POST['contents'])
		){
			$data = [
				"contents"=>":contents",
				"name"=>":name",
				"time"=>DATETIME,
			];
			$prepareParam = [
				":contents"=>htmlspecialchars($_POST['contents']),
				":name"=>htmlspecialchars($_POST['name']),
			];
			try{
				$res = Db()->table("html")->prepareParam($prepareParam)->where("id={$id}")->update($data);
				if($res){
					$this->root_log("编辑单页,id:{$id}");
					assign("alertTpl",true);
					header("location:".url("html/index/alert/{$id}"));
					exit;
				}else{
					assign("alertTpl",false);
				}
			}catch(\Exception $e){
				$this->exception_log($e->getMessage());
				assign("alertTpl",false);
				assign("alertMsg",$e->getMessage());
			}
		}
		$data = Db()->table("html")->where("id={$id}")->fetch();
		if(empty($data))header("location:".url("html/index"));
		if(isset($data['contents']))$data['contents'] = Db()->quote(htmlspecialchars_decode($data['contents']));
		assign("data",$data);
		return view(['header','html/edit','footer']);
	}	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
