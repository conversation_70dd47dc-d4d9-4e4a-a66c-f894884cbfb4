<script setup>
import { ref, computed, defineProps, watch, nextTick } from 'vue'; // 引入 nextTick
import { onShareAppMessage } from '@dcloudio/uni-app'; // 引入 onShareAppMessage
import { navto } from '@/utils';
import { store } from '@/store'; // 需要 store 获取 uid/token
import { likeCard, favoriteCard } from '@/api/index.js'; // 导入点赞和收藏API
import SharePopup from '@/components/share-popup/share-popup.vue';
import { requireLogin } from '@/utils/auth';

// --- Props Definition ---
const props = defineProps({
  cards: { // 从父组件接收已排序的卡片数组
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: true
  },
  error: {
    type: String,
    default: ''
  }
});

// --- State Refs ---
const dateList = ref([]); // 日期轴数据 [{date: 'YYYY-MM-DD', displayShort: 'MM-DD', displayDay: '周X', isToday: boolean, id: 'date-...'}]
const selectedDate = ref(''); // 当前选中的日期 YYYY-MM-DD
const currentSwiperIndex = ref(0); // 当前 Swiper 的索引
const dateScrollIntoView = ref(''); // 控制日期轴滚动
const showLoadingOverlay = ref(false); // 用于Swiper切换时的加载遮罩

// 分享状态
const currentShareCard = ref(null);
const showSharePopup = ref(false);

// 分享配置
onShareAppMessage(() => {
  try {
    if (!currentShareCard.value) {
      console.warn('日卡信息未设置，使用默认分享信息');
      return {
        title: '分享一张精美日卡',
        path: '/pages/bundle/world/card/index',
        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
      };
    }

    return {
      title: currentShareCard.value.description ?
        (currentShareCard.value.description.length > 30 ?
          currentShareCard.value.description.substring(0, 30) + '...' :
          currentShareCard.value.description) :
        '分享一张精美日卡',
      path: `/pages/bundle/world/card/detail?cardId=${currentShareCard.value.id}`,
      imageUrl: currentShareCard.value.background_image_url || store().$state.config?.img_config?.app_logo?.val || ''
    };
  } catch (error) {
    console.error('日卡分享配置失败:', error);
    return {
      title: '分享一张精美日卡',
      path: '/pages/bundle/world/card/index',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  }
});

// --- Computed Properties ---
const todayDateString = computed(() => {
  return formatYYYYMMDD(new Date());
});

// --- Utility Functions ---
const formatYYYYMMDD = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

const getWeekDay = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  return weekDays[date.getDay()];
};

/**
 * 根据卡片数据生成日期轴列表
 */
const generateDateList = (cardsData) => {
  const todayStr = todayDateString.value;
  dateList.value = cardsData.map((card, index) => {
    const dateParts = card.date.split('-');
    return {
      date: card.date,
      displayShort: `${dateParts[2]}`, // 只显示日期，不显示月份
      displayDay: getWeekDay(card.date),
      isToday: card.date === todayStr,
      id: `date-${index}` // 用于 scroll-into-view
    };
  });
  console.log("Generated date list:", dateList.value);
};

// --- Event Handlers ---
/**
 * Swiper 切换完成时触发
 * @param {object} e - 事件对象
 */
const onSwiperChange = (e) => {
  // --- Modify Safety Check to handle both event structures ---
  let index = -1;
  if (e && e.detail && typeof e.detail.current === 'number') {
    index = e.detail.current;
  } else if (e && typeof e.current === 'number') {
    // Handle the case where current is directly on the event object
    index = e.current;
    console.warn("onSwiperChange received event with direct 'current' property:", e);
  } else {
    console.error("onSwiperChange called with invalid or unexpected event object structure:", e);
    return; // Exit if we cannot determine the index
  }
  // --- End Modified Safety Check ---

  console.log("Swiper changed to index:", index);
  if (props.cards && props.cards[index]) {
      // currentSwiperIndex.value = index; // No longer needed to update if :current drives swiper
      const newSelectedDate = props.cards[index].date;
      if (selectedDate.value !== newSelectedDate) {
          selectedDate.value = newSelectedDate;
          // Scroll date axis
          nextTick(() => {
              dateScrollIntoView.value = `date-${index}`;
              // 添加日志
              console.log(`[${Date.now()}] Inside nextTick (SwiperChange) - Scrolling date axis to:`, dateScrollIntoView.value);
          });
      }
  } else {
       console.warn(`Swiper changed to invalid index ${index} or cards not ready.`);
  }
};

/**
 * 点击日期轴日期时触发
 * @param {object} dateItem - 被点击的日期对象
 * @param {number} index - 被点击日期的索引
 */
const handleDateSelect = (dateItem, index) => {
  console.log("Date selected:", dateItem.date, "at index:", index);
  if (selectedDate.value !== dateItem.date) {
    selectedDate.value = dateItem.date;
    // Find the index in cards array corresponding to the selected date
    const targetIndex = props.cards.findIndex(card => card.date === dateItem.date);
    if (targetIndex !== -1 && currentSwiperIndex.value !== targetIndex) {
        console.log("Setting swiper index to:", targetIndex);
         showLoadingOverlay.value = true; // 显示遮罩
         // 直接修改 currentSwiperIndex 驱动 Swiper
         currentSwiperIndex.value = targetIndex;
         // 短暂延时后隐藏遮罩，给 Swiper 切换一点时间
         setTimeout(() => {
            showLoadingOverlay.value = false;
         }, 150); // 150ms 应该足够

    } else if (targetIndex === -1) {
        console.warn("Selected date not found in cards data:", dateItem.date);
        // Optionally handle error or do nothing
    }
     // Ensure the selected date is visible in the scroll view
     // Use the original 'index' passed to handleDateSelect for scrolling date axis
     nextTick(() => {
         dateScrollIntoView.value = `date-${index}`; // 使用原始 index
         // 添加日志
         console.log(`[${Date.now()}] Inside nextTick (DateSelect) - Scrolling date axis to:`, dateScrollIntoView.value);
     });
  }
};

/**
 * 处理卡片单击事件
 * @param {number} cardId - 卡片ID
 */
const handleCardClick = (cardId) => {
  navto(`/pages/bundle/world/card/detail?cardId=${cardId}`);
};

/**
 * 处理评论按钮点击事件
 * @param {number} cardId - 卡片ID
 */
const handleCommentClick = (cardId, event) => {
    // 阻止事件冒泡
    if (event) {
        event.stopPropagation();
    }
    navto(`/pages/bundle/world/card/detail?cardId=${cardId}&showComments=true`);
};

/**
 * 处理点赞按钮点击事件
 * @param {number} cardId - 卡片ID
 * @param {Event} event - 事件对象
 */
const handleLikeClick = async (cardId, event) => {
    // 使用统一的登录校验
    if (!requireLogin('', '请先登录后再点赞')) {
        return;
    }

    // 找到当前卡片
    const currentCard = props.cards.find(card => card.id === cardId);
    if (!currentCard) {
        uni.showToast({ title: '卡片数据不存在', icon: 'none' });
        return;
    }

    // 阻止事件冒泡
    if (event) {
        event.stopPropagation();
    }

    // 乐观更新UI
    const originalLikedState = currentCard.isLiked;
    const originalLikeCount = currentCard.likeCount;

    currentCard.isLiked = !currentCard.isLiked;
    currentCard.likeCount += currentCard.isLiked ? 1 : -1;

    try {
        console.log('发送点赞请求:', {
            id: cardId,
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token
        });

        const res = await likeCard({
            id: cardId,
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token
        });

        console.log('点赞响应:', res);

        if (res.status !== 'ok') {
            // 恢复原状态
            currentCard.isLiked = originalLikedState;
            currentCard.likeCount = originalLikeCount;
            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
        } else {
            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
        }
    } catch (error) {
        // 恢复原状态
        currentCard.isLiked = originalLikedState;
        currentCard.likeCount = originalLikeCount;
        console.error('点赞失败:', error);
        uni.showToast({ title: '操作失败，请重试', icon: 'none' });
    }
};

/**
 * 处理收藏按钮点击事件
 * @param {number} cardId - 卡片ID
 */
const handleFavoriteClick = async (cardId, event) => {
    // 使用统一的登录校验
    if (!requireLogin('', '请先登录后再收藏')) {
        return;
    }

    // 阻止事件冒泡
    if (event) {
        event.stopPropagation();
    }

    // 找到当前卡片
    const currentCard = props.cards.find(card => card.id === cardId);
    if (!currentCard) {
        uni.showToast({ title: '卡片数据不存在', icon: 'none' });
        return;
    }

    // 乐观更新UI
    const originalFavoritedState = currentCard.isFavorited || false;
    currentCard.isFavorited = !currentCard.isFavorited;

    try {
        const res = await favoriteCard({
            id: cardId,
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token
        });

        if (res.status !== 'ok') {
            // 恢复原状态
            currentCard.isFavorited = originalFavoritedState;
            uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
        } else {
            uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
        }
    } catch (error) {
        // 恢复原状态
        currentCard.isFavorited = originalFavoritedState;
        console.error('收藏失败:', error);
        uni.showToast({ title: '操作失败，请重试', icon: 'none' });
    }
};

/**
 * 处理分享按钮点击事件
 * @param {number} cardId - 卡片ID
 * @param {Event} event - 事件对象
 */
const handleShareClick = (cardId, event) => {
    if (event) {
        event.stopPropagation();
    }

    // 获取当前卡片数据
    const currentCard = props.cards.find(card => card.id === cardId);
    if (!currentCard) {
        uni.showToast({ title: '卡片数据不存在', icon: 'none' });
        return;
    }

    // 设置当前分享的卡片数据
    currentShareCard.value = currentCard;

    // 显示分享弹窗
    showSharePopup.value = true;
};

// 处理分享成功
const handleShareSuccess = (result) => {
    console.log('分享成功:', result);
    uni.showToast({
        title: '分享成功',
        icon: 'success'
    });
};

// 处理分享错误
const handleShareError = (error) => {
    console.error('分享失败:', error);
    uni.showToast({
        title: '分享失败',
        icon: 'none'
    });
};

// 处理刷新按钮点击事件
const handleRefresh = () => {
    uni.showToast({
        title: '刷新中...',
        icon: 'loading',
        duration: 1000
    });

    // 这里可以触发父组件的刷新方法
    // 或者发送事件给父组件
    // emit('refresh');

    // 暂时使用简单的提示
    setTimeout(() => {
        uni.showToast({
            title: '刷新完成',
            icon: 'success'
        });
    }, 1000);
};



// --- Watchers ---
watch(() => props.cards, (newCards) => {
    console.log("Props cards updated in CardIndex watcher:", newCards);
    if (newCards && newCards.length > 0) {
        generateDateList(newCards);
        const todayIndex = newCards.findIndex(card => card.date === todayDateString.value);
        const initialIndex = todayIndex !== -1 ? todayIndex : 0;

        // 只有在首次加载或数据完全重置时才设置初始 index 和 date
        // 避免在父组件更新(如点赞)时重置用户当前浏览的位置
        if (currentSwiperIndex.value === 0 && selectedDate.value === '') {
            currentSwiperIndex.value = initialIndex;
            selectedDate.value = newCards[initialIndex].date;
            console.log("CardIndex: Initial setup - Swiper index:", initialIndex, "Selected date:", selectedDate.value);
             // Scroll to initial date
             nextTick(() => {
                 dateScrollIntoView.value = `date-${initialIndex}`;
                 // 添加日志
                 console.log(`[${Date.now()}] Inside nextTick (Watch Initial) - Scrolling date axis to:`, dateScrollIntoView.value);
             });
        } else {
             // Data might have updated (e.g., likes), find current card's new index if needed
             // For now, assume the order is stable and index remains the same unless date changes
             console.log("CardIndex: Cards updated, maintaining current index:", currentSwiperIndex.value);
        }
    } else {
        dateList.value = [];
        selectedDate.value = '';
        currentSwiperIndex.value = 0;
    }
}, { immediate: true, deep: true }); // deep: true may not be necessary if only array reference changes

</script>

<template>
  <view class="card-page-container">

    <!-- Horizontal Date Axis -->
    <view class="date-axis-container">
       <scroll-view
            class="date-scroll-view"
            scroll-x
            :scroll-into-view="dateScrollIntoView"
            scroll-with-animation
       >
         <view class="date-item-wrapper" v-for="(item, index) in dateList" :key="item.date" :id="item.id">
             <view class="date-item" :class="{ active: item.date === selectedDate, today: item.isToday }" @click="handleDateSelect(item, index)">
               <text class="date-day">{{ item.displayDay }}</text>
               <text class="date-short">{{ item.displayShort }}</text>
             </view>
         </view>
       </scroll-view>
       <view class="refresh-icon" @click="handleRefresh">
            <u-icon name="reload" size="20" color="#999"></u-icon>
       </view>
    </view>

    <!-- Main Content Area -->
    <view class="main-content-area">
        <!-- Loading State -->
        <view v-if="loading && cards.length === 0" class="loading-state">
          <u-loading-icon mode="circle" size="24"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- Error State -->
        <view v-else-if="error" class="error-state">
           <u-empty mode="list" :text="error"></u-empty>
        </view>

        <!-- Empty State -->
        <view v-else-if="cards.length === 0" class="empty-state">
            <u-empty mode="list" text="暂无日卡数据"></u-empty>
        </view>

        <!-- Native Swiper for Cards -->
         <swiper
            v-else
            class="card-swiper"
            :current="currentSwiperIndex"
            @change="onSwiperChange"
            :circular="true"
            previous-margin="40rpx"
            next-margin="40rpx"
            :display-multiple-items="cards && cards.length > 0 ? 1 : 0"
         >
           <!-- Loop through cards using swiper-item -->
           <swiper-item v-for="(card, itemIndex) in cards" :key="card?.id || itemIndex" v-if="cards && cards.length > 0">
               <!-- Restore Original Content -->
               <view v-if="card" class="swiper-item-container" @click="handleCardClick(card.id)">
                   <view class="card-content-wrapper">
                       <!-- 上半部分：图片 -->
                       <view class="card-image-section">
                           <image
                               class="card-image"
                               :src="card.background_image_url || '/static/default-card-bg.png'"
                               mode="aspectFill"
                               lazy-load
                           />
                       </view>

                       <!-- 下半部分：文字和作者 -->
                       <view class="card-text-section">
                           <text class="card-description">{{ card.description }}</text>
                           <text v-if="card.author" class="card-author">— {{ card.author }}</text>
                       </view>

                       <!-- 操作栏 -->
                       <view class="card-action-bar">
                           <view class="action-item" @click.stop="handleCommentClick(card.id, $event)">
                               <u-icon name="chat" size="20" color="#666"></u-icon>
                               <text class="action-count">{{card.commentCount || 0}}</text>
                           </view>
                           <view class="action-item" @click.stop="handleLikeClick(card.id, $event)">
                               <u-icon :name="card.isLiked ? 'heart-fill' : 'heart'" size="20" :color="card.isLiked ? '#ff6b81' : '#666'"></u-icon>
                               <text class="action-count" :class="{ 'liked': card.isLiked }">{{card.likeCount || 0}}</text>
                           </view>
                           <view class="action-item" @click.stop="handleFavoriteClick(card.id, $event)">
                               <u-icon :name="card.isFavorited ? 'star-fill' : 'star'" size="20" :color="card.isFavorited ? '#FFD700' : '#666'"></u-icon>
                           </view>
                           <!-- 分享按钮已注释，使用微信右上角分享 -->
                           <!-- <view class="action-item" @click.stop="handleShareClick(card.id, $event)">
                               <u-icon name="share" size="20" color="#666"></u-icon>
                           </view> -->
                       </view>
                   </view>
               </view>
           </swiper-item>
        </swiper>

         <!-- Loading Overlay during date selection jump -->
        <view v-if="showLoadingOverlay" class="loading-overlay">
            <u-loading-icon mode="circle" size="30" color="#FFFFFF"></u-loading-icon>
        </view>
    </view>

    <!-- 分享弹窗 -->
    <share-popup
      :show="showSharePopup"
      title="分享日卡"
      :share-data="{
        image: currentShareCard?.background_image_url,
        content: currentShareCard?.description,
        author: currentShareCard?.author,
        date: currentShareCard?.date,
        template: 'card'
      }"
      :show-member-invite="store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1"
      @close="showSharePopup = false"
      @share-success="handleShareSuccess"
      @share-error="handleShareError"
    />

    <!-- 隐藏的Canvas元素用于生成分享图片 -->
    <canvas
      canvas-id="share-canvas"
      id="share-canvas"
      style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"
    ></canvas>

  </view>
</template>


<style lang="scss" scoped>
.card-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa; // 统一背景色
  overflow: hidden;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* Date Axis */
.date-axis-container {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 20rpx 0 20rpx 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
    flex-shrink: 0;
    border-bottom: 1rpx solid #f0f0f0;
    position: relative;
    z-index: 10;
}
.date-scroll-view {
    width: calc(100% - 80rpx); // Leave space for refresh icon
    white-space: nowrap;
    height: 110rpx; // 增加高度
}
.date-item-wrapper {
    display: inline-block; // Important for scroll-view
    margin-right: 24rpx;
    text-align: center;
}
.date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12rpx 24rpx;
    border-radius: 16rpx;
    transition: all 0.3s ease;
    height: 100%;
    box-sizing: border-box;
    min-width: 110rpx;

    .date-day {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
    }
    .date-short {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
    }

    &.active {
        background-color: #f0f7ff; // 浅蓝色背景
        .date-day {
            color: #576b95; // 微信蓝色
        }
        .date-short {
            color: #576b95; // 微信蓝色
            font-weight: bold;
        }
    }
    &.today .date-short::after { // Dot for today
        content: '';
        display: block;
        width: 8rpx;
        height: 8rpx;
        border-radius: 50%;
        background-color: #576b95; // 微信蓝色
        margin: 6rpx auto 0;
    }
}
.refresh-icon {
    width: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

/* Main Content Area */
.main-content-area {
    flex: 1;
    position: relative; // For loading overlay
    overflow: hidden; // Important for swiper height
    display: flex; // Ensure children fill height
    flex-direction: column;
}

/* Loading/Error/Empty States */
.loading-state,
.error-state,
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 28rpx;
  .empty-text,
  .loading-text {
      margin-top: 10rpx;
  }
}

/* Swiper */
.card-swiper {
  width: 100%;
  flex: 1; /* Ensure swiper stretches in flex container */
}

/* Restore original commented styles */
swiper-item {
  height: 100%;
  display: flex;
  align-items: stretch;
}

.swiper-item-container {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 15rpx 0;
    width: 100%;
}

.card-content-wrapper {
  width: calc(100% - 20rpx);
  height: 82%;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: scale(0.98);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;

  &:active {
    transform: scale(0.96);
  }
}

/* 新的卡片图片区域 */
.card-image-section {
  width: 100%;
  height: 55%;
  position: relative;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 新的卡片文字区域 */
.card-text-section {
  flex: 1;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16rpx;
}

.card-description {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333333;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 1rpx;
  text-align: left;
}

.card-author {
  font-size: 26rpx;
  color: #666666;
  font-style: italic;
  text-align: right;
  margin-top: 8rpx;
}

.card-action-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 30rpx;
    z-index: 4;
    background-color: rgba(255, 255, 255, 0.95);
    border-top: 1rpx solid #f0f0f0;
    backdrop-filter: blur(10px);
}

.action-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8rpx 12rpx;
    border-radius: 20rpx;
    transition: all 0.2s ease;

    &:active {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(0.95);
    }
}

.action-count {
    font-size: 22rpx;
    color: #666;
    margin-left: 8rpx;
    min-width: 30rpx;
    text-align: center;

    &.liked {
        color: #ff6b81;
        font-weight: 500;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

</style>