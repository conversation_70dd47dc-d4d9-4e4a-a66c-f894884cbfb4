<script setup>
import { ref } from 'vue';
import { getDiaryList, getQuoteList, publishFeed, publishQuote } from '@/api/index.js';
import { store } from '@/store';

const testResults = ref([]);

// 添加测试结果
const addResult = (title, result) => {
  testResults.value.push({
    title,
    result: JSON.stringify(result, null, 2),
    timestamp: new Date().toLocaleTimeString()
  });
};

// 测试获取日记列表
const testGetDiaryList = async () => {
  try {
    const params = {
      page: 1,
      page_size: 10,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      type: 'diary'
    };
    
    console.log('测试日记列表API参数:', params);
    const res = await getDiaryList(params);
    addResult('获取日记列表', res);
  } catch (error) {
    addResult('获取日记列表 - 错误', { error: error.message });
  }
};

// 测试获取摘录列表
const testGetQuoteList = async () => {
  try {
    const params = {
      page: 1,
      page_size: 20,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    };
    
    console.log('测试摘录列表API参数:', params);
    const res = await getQuoteList(params);
    addResult('获取摘录列表', res);
  } catch (error) {
    addResult('获取摘录列表 - 错误', { error: error.message });
  }
};

// 测试发布动态
const testPublishFeed = async () => {
  try {
    const params = {
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      content: '这是一条测试动态',
      images: [],
      location: '',
      tags: '测试',
      privacy: 'public',
      type: 'feed'
    };
    
    console.log('测试发布动态API参数:', params);
    const res = await publishFeed(params);
    addResult('发布动态', res);
  } catch (error) {
    addResult('发布动态 - 错误', { error: error.message });
  }
};

// 测试发布摘录
const testPublishQuote = async () => {
  try {
    const params = {
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      content: '这是一条测试摘录',
      author: '测试作者',
      source: '测试来源',
      tags: '测试',
      privacy: 'public'
    };
    
    console.log('测试发布摘录API参数:', params);
    const res = await publishQuote(params);
    addResult('发布摘录', res);
  } catch (error) {
    addResult('发布摘录 - 错误', { error: error.message });
  }
};

// 清空测试结果
const clearResults = () => {
  testResults.value = [];
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<template>
  <view class="test-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <u-icon name="arrow-left" size="44rpx" color="#333"></u-icon>
      </view>
      <view class="navbar-title">API测试</view>
      <view class="navbar-right">
        <view class="clear-btn" @click="clearResults">
          <text>清空</text>
        </view>
      </view>
    </view>

    <!-- 测试按钮 -->
    <view class="test-buttons">
      <view class="test-btn" @click="testGetDiaryList">
        <text>测试日记列表</text>
      </view>
      <view class="test-btn" @click="testGetQuoteList">
        <text>测试摘录列表</text>
      </view>
      <view class="test-btn" @click="testPublishFeed">
        <text>测试发布动态</text>
      </view>
      <view class="test-btn" @click="testPublishQuote">
        <text>测试发布摘录</text>
      </view>
    </view>

    <!-- 测试结果 -->
    <scroll-view scroll-y class="results-scroll">
      <view class="results-list">
        <view v-for="(result, index) in testResults" :key="index" class="result-item">
          <view class="result-header">
            <text class="result-title">{{ result.title }}</text>
            <text class="result-time">{{ result.timestamp }}</text>
          </view>
          <view class="result-content">
            <text class="result-text">{{ result.result }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.test-page {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  height: 88rpx;
  padding-top: var(--status-bar-height, 44rpx);
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.navbar-left, .navbar-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.clear-btn {
  padding: 12rpx 24rpx;
  background-color: #ff4757;
  border-radius: 20rpx;
  
  text {
    font-size: 24rpx;
    color: #fff;
  }
}

.test-buttons {
  padding: 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.test-btn {
  flex: 1;
  min-width: 300rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  
  text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.results-scroll {
  flex: 1;
  padding: 0 32rpx 32rpx;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.result-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
}

.result-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}
</style>
