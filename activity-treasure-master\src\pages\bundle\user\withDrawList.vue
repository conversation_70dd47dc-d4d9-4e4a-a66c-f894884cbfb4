<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_tixian_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
import { getItem } from "@/utils";

const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_tixian_list({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-pending';
    case 1: return 'status-success';
    case 2: return 'status-failed';
    default: return 'status-pending';
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 0: return '#FF8C00';
    case 1: return '#6AC086';
    case 2: return '#FF4757';
    default: return '#FF8C00';
  }
};
</script>
<template>
  <view class="withdraw-list-page">
    <!-- 表头 -->
    <view class="table-header">
      <view class="header-item">提现类型</view>
      <view class="header-item">提现日期</view>
      <view class="header-item">提现金额</view>
      <view class="header-item">状态</view>
    </view>

    <!-- 列表内容 -->
    <mescroll-uni
      class="list-container"
      :height="height"
      :up="{
        page: {
          num: 0,
          size: 20,
          time: null,
        },
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <!-- 空状态 -->
      <view v-if="goods.length === 0" class="empty-state">
        <u-icon name="file-text" color="#ccc" size="120rpx"></u-icon>
        <u-text color="#999" size="28rpx" text="暂无提现记录" margin="20rpx 0 0 0"></u-text>
      </view>

      <!-- 记录列表 -->
      <view v-else>
        <view
          class="record-item"
          v-for="(val, i) in goods"
          :key="i"
        >
          <view class="record-content">
            <view class="content-item">
              <view class="item-icon">
                <u-icon
                  name="coupon-fill"
                  color="#6AC086"
                  size="32rpx"
                ></u-icon>
              </view>
              <view class="item-text">
                <u-text
                  color="#333"
                  size="28rpx"
                  bold
                  lines="1"
                  :text="val.bank_info.bank_name"
                ></u-text>
              </view>
            </view>

            <view class="content-item">
              <view class="item-label">
                <u-text color="#666" size="24rpx" text="日期"></u-text>
              </view>
              <view class="item-text">
                <u-text
                  color="#333"
                  size="28rpx"
                  :text="val.time"
                ></u-text>
              </view>
            </view>

            <view class="content-item">
              <view class="item-label">
                <u-text color="#666" size="24rpx" text="金额"></u-text>
              </view>
              <view class="item-text">
                <u-text
                  mode="price"
                  color="#6AC086"
                  size="32rpx"
                  bold
                  :text="val.daozhang_money"
                ></u-text>
              </view>
            </view>

            <view class="content-item">
              <view class="status-badge" :class="getStatusClass(val.status)">
                <u-text
                  size="24rpx"
                  bold
                  :color="getStatusColor(val.status)"
                  :text="getItem(['未处理', '已通过', '已拒绝'], val.status)"
                ></u-text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less">
/* 页面容器 */
.withdraw-list-page {
  min-height: 100vh;
  background: #f8f7f5;
}

/* 表头 */
.table-header {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  backdrop-filter: blur(10rpx);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 列表容器 */
.list-container {
  padding: 0 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

/* 记录项 */
.record-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin: 20rpx 0;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.record-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.record-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.content-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
}

.item-icon {
  margin-bottom: 8rpx;
}

.item-label {
  margin-bottom: 8rpx;
  opacity: 0.8;
}

.item-text {
  min-height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 状态徽章 */
.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  min-width: 80rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
}

.status-pending {
  background: rgba(255, 140, 0, 0.1);
  border: 2rpx solid rgba(255, 140, 0, 0.3);
}

.status-success {
  background: rgba(106, 192, 134, 0.1);
  border: 2rpx solid rgba(106, 192, 134, 0.3);
}

.status-failed {
  background: rgba(255, 71, 87, 0.1);
  border: 2rpx solid rgba(255, 71, 87, 0.3);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .record-content {
    flex-direction: column;
    gap: 20rpx;
  }

  .content-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding: 0 20rpx;
  }

  .content-item:not(:last-child)::after {
    display: none;
  }

  .content-item::after {
    content: '';
    position: absolute;
    bottom: -10rpx;
    left: 20rpx;
    right: 20rpx;
    height: 2rpx;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
  }

  .content-item:last-child::after {
    display: none;
  }

  .item-icon {
    margin-bottom: 0;
    margin-right: 12rpx;
  }

  .item-label {
    margin-bottom: 0;
  }

  .status-badge {
    margin-left: auto;
  }
}

/* 动画效果 */
.record-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动优化 */
.list-container {
  scroll-behavior: smooth;
}

/* 毛玻璃效果增强 */
.table-header,
.record-item {
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .withdraw-list-page {
    background: #1a1a1a;
  }

  .table-header,
  .record-item {
    background: rgba(40, 40, 40, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .header-item {
    color: #fff;
  }
}
</style>
