<script setup>
import { ref, reactive, onMounted } from "vue";
import { onLoad, onShow, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app";
import { userget_points_log } from "@/api";
import { store } from "@/store";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";
import myTitle from "@/components/myTitle.vue";

const pointsLog = ref([]);
const loading = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = 20;
const total = ref(0);
const currentPoints = ref(0);

onLoad(() => {
  getPointsLog();
});

onShow(() => {
  // 页面显示时刷新数据
  refreshData();
});

onPullDownRefresh(() => {
  refreshData();
});

onReachBottom(() => {
  if (!finished.value && !loading.value) {
    loadMore();
  }
});

// 获取积分记录
const getPointsLog = async (isRefresh = false) => {
  if (loading.value) return;
  
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后查看积分记录')) {
    return;
  }

  loading.value = true;
  
  try {
    console.log('开始获取积分记录，参数:', {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      page: isRefresh ? 1 : page.value,
      page_size: pageSize
    });

    const res = await userget_points_log({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      page: isRefresh ? 1 : page.value,
      page_size: pageSize
    });

    console.log('积分记录API响应:', res);

    if (res?.status === 'ok') {
      const newRecords = res.data?.list || [];
      
      if (isRefresh) {
        pointsLog.value = newRecords;
        page.value = 1;
        finished.value = false;
      } else {
        pointsLog.value = [...pointsLog.value, ...newRecords];
      }
      
      total.value = res.data?.total || 0;
      currentPoints.value = res.data?.current_points || 0;
      
      // 检查是否还有更多数据
      if (newRecords.length < pageSize) {
        finished.value = true;
      } else {
        page.value++;
      }
    } else if (res?.status === 'empty') {
      if (isRefresh) {
        pointsLog.value = [];
      }
      finished.value = true;
      console.log('没有积分记录数据');
    } else {
      console.error('获取积分记录失败:', res?.msg || '未知错误');
      uni.$u.toast(res?.msg || '获取积分记录失败');
    }
  } catch (error) {
    console.error('获取积分记录异常:', error);
    uni.$u.toast('获取积分记录失败，请稍后重试');
  } finally {
    loading.value = false;
    uni.stopPullDownRefresh();
  }
};

// 刷新数据
const refreshData = () => {
  page.value = 1;
  finished.value = false;
  getPointsLog(true);
};

// 加载更多
const loadMore = () => {
  getPointsLog(false);
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  
  if (isNaN(time.getTime())) {
    return '时间格式错误';
  }
  
  return time.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取积分变动类型文本
const getSourceTypeText = (sourceType) => {
  const typeMap = {
    'member_init': '开通会员',
    'activity_checkin': '活动签到',
    'activity_complete': '活动完成',
    'daily_signin': '每日签到',
    'invite_friend': '邀请好友',
    'consume_points': '积分消费',
    'admin_adjust': '管理员调整'
  };
  return typeMap[sourceType] || '其他';
};

// 获取积分变动颜色
const getPointsColor = (pointsChange) => {
  return pointsChange > 0 ? '#6AC086' : '#FF6B35';
};

// 获取积分变动图标
const getPointsIcon = (pointsChange) => {
  return pointsChange > 0 ? 'plus-circle-fill' : 'minus-circle-fill';
};
</script>

<template>
  <view class="page">
    <myTitle
      title="积分记录"
      bg-color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      :titleStyle="{
        color: '#ffffff',
        fontWeight: '600',
        fontSize: '36rpx'
      }"
    ></myTitle>

    <view class="points-container">
      <!-- 当前积分显示 -->
      <view class="current-points-card">
        <view class="points-header">
          <u-icon name="star-fill" color="#ffffff" size="44rpx"></u-icon>
          <text class="points-title">当前积分</text>
        </view>
        <text class="points-value">{{ currentPoints }}</text>
        <text class="points-desc">积分可用于兑换奖品或参与活动</text>
      </view>
      
      <!-- 积分记录列表 -->
      <view v-if="pointsLog.length > 0" class="points-list">
        <view 
          v-for="(item, index) in pointsLog" 
          :key="item.id"
          class="points-item"
        >
          <!-- 记录头部 -->
          <view class="points-header">
            <view class="points-info">
              <view class="points-type">
                <u-icon 
                  :name="getPointsIcon(item.points_change)" 
                  :color="getPointsColor(item.points_change)" 
                  size="32rpx"
                ></u-icon>
                <text class="type-text">{{ getSourceTypeText(item.source_type) }}</text>
              </view>
              <text class="points-time">{{ formatTime(item.created_at) }}</text>
            </view>
            <view class="points-change" :style="{ color: getPointsColor(item.points_change) }">
              {{ item.points_change > 0 ? '+' : '' }}{{ item.points_change }}
            </view>
          </view>
          
          <!-- 记录内容 -->
          <view class="points-content">
            <text class="points-desc">{{ item.description }}</text>
            <text class="points-balance">余额：{{ item.points_balance }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-state">
        <u-icon name="star" color="#ccc" size="120rpx"></u-icon>
        <text class="empty-text">暂无积分记录</text>
        <text class="empty-desc">您的积分变动记录将在这里显示</text>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="loading && pointsLog.length === 0" class="loading-state">
        <u-loading-icon mode="circle" color="#6AC086" size="60rpx"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="loading && pointsLog.length > 0" class="load-more">
        <u-loading-icon mode="circle" color="#6AC086" size="40rpx"></u-loading-icon>
        <text class="load-more-text">加载中...</text>
      </view>
      
      <!-- 没有更多 -->
      <view v-if="finished && pointsLog.length > 0" class="no-more">
        <text class="no-more-text">没有更多记录了</text>
      </view>
    </view>
  </view>
</template>

<style lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
  padding-bottom: 40rpx;
}

.points-container {
  padding: 30rpx;
  margin-top: 220rpx; /* 调整顶部边距，确保不被标题遮挡 */
}

.current-points-card {
  background: linear-gradient(135deg, #6AC086 0%, #88D7A0 100%);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.25);
  color: #fff;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.current-points-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.points-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.points-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-left: 12rpx;
}

.points-value {
  font-size: 56rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  position: relative;
  z-index: 1;
}

.points-desc {
  font-size: 26rpx;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.points-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.points-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
}

.points-item .points-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.points-info {
  flex: 1;
}

.points-type {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.type-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-left: 8rpx;
}

.points-time {
  font-size: 22rpx;
  color: #999;
}

.points-change {
  font-size: 32rpx;
  font-weight: bold;
}

.points-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.points-item .points-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.points-balance {
  font-size: 22rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
  margin: 24rpx 0 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  line-height: 1.5;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
  margin-top: 16rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style>
