<script setup>
import { watch, ref, reactive } from "vue";
import {
  huodongget_info,
  huodongget_my_list,
  huodongcancel_huodong,
  huodongcancel_baoming,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto, getItem } from "@/utils";
import { requireLogin, isLoggedIn, getCurrentUser } from "@/utils/auth";

const tabList = ref([
  {
    name: "我的报名",
  },
  {
    name: "我的发布",
  },
  {
    name: "我的收藏",
  },
  {
    name: "我的点赞",
  },
  {
    name: "我的评价",
  },
]);
const type = ref(2);
const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
    onPageScroll,
    onReachBottom
);
const height = ref("");
const current = ref(0);

// 添加一个初始化函数，确保页面首次加载时显示正确的数据
const initPage = (optionType) => {
  if (optionType) {
    // 参数映射表
    const typeMap = {
      '1': 1, // 我的发布
      '2': 0, // 我的报名
      '3': 2, // 我的收藏
      '4': 4, // 我的评价
      '5': 3  // 我的点赞
    };
    
    // 如果有合法的type参数，设置对应的选项卡
    if (typeMap[optionType] !== undefined) {
      current.value = typeMap[optionType];
      type.value = parseInt(optionType);
    } else {
      current.value = 0; // 默认显示我的报名
      type.value = 2;
    }
  } else {
    current.value = 0; // 默认显示我的报名
    type.value = 2;
  }
  
  console.log(`初始化页面 - 选项卡索引: ${current.value}, 数据类型: ${type.value}`);
};

onLoad((e) => {
  console.log('页面加载参数:', e);

  // 检查登录状态
  if (!isLoggedIn()) {
    console.log('用户未登录，跳转到登录页面');
    requireLogin('/pages/bundle/user/myActivity', '请先登录后查看我的活动');
    return;
  }

  console.log('用户已登录，用户信息:', getCurrentUser());
  initPage(e.type);
});
onReady(async () => {
  height.value = (await setListHeight()) + "px";
});
onShow(() => {
  console.log('当前选项卡索引:', current.value);
  console.log('当前数据类型:', type.value);
  getMescroll().resetUpScroll(true);
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  try {
    console.log('请求我的活动列表，参数:', {
      page: mescroll.num,
      page_size: mescroll.size,
      type: type.value,
      userInfo: getCurrentUser()
    });

    const res = await huodongget_my_list({
      page: mescroll.num,
      page_size: mescroll.size,
      type: type.value,
    });

    console.log('我的活动列表API响应:', res);

    if (res.status === 'ok') {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
      console.log(`加载成功，当前页数据: ${curPageData.length} 条，总数: ${res.count}`);
    } else if (res.status === 'relogin') {
      console.warn('需要重新登录:', res.msg);
      mescroll.endErr();
      requireLogin('/pages/bundle/user/myActivity', res.msg || '登录已过期，请重新登录');
    } else {
      console.error('获取我的活动列表失败:', res);
      mescroll.endErr();
      if (mescroll.num == 1) {
        uni.showToast({ title: res.msg || '获取数据失败', icon: 'none' });
      }
    }
  } catch (error) {
    console.error('请求我的活动列表异常:', error);
    mescroll.endErr();
    if (mescroll.num == 1) {
      uni.showToast({ title: '网络错误，请检查网络连接', icon: 'none' });
    }
  }
};

// 添加格式化日期的函数
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // 计算日期是否在近10天内
  const now = new Date();
  const diffTime = Math.abs(date - now);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()];

  return diffDays <= 10 ? `${month}月${day}日 ${weekday}` : `${month}月${day}日`;
};

// 修改changeTabs函数，处理顺序变化
const changeTabs = (e) => {
  // 重新映射tab索引到type值
  const typeMap = {
    0: 2, // 我的报名
    1: 1, // 我的发布
    2: 3, // 我的收藏
    3: 5, // 我的点赞
    4: 4  // 我的评价
  };

  type.value = typeMap[e.index];
  current.value = e.index;
  goods.value = [];
  
  console.log(`切换选项卡 - 新索引: ${e.index}, 新数据类型: ${type.value}`);
  
  getMescroll().resetUpScroll(true);
};

// 取消活动
const cancel = async (val, i) => {
  const res = await huodongcancel_huodong({ huodong_id: val.huodong_id });
  if (res.status === "ok") getMescroll().resetUpScroll(true);
  else uni.$u.toast(res.msg);
};
// 取消报名
const cancelHuodong = async (order_id) => {
  const res = await huodongcancel_baoming({ order_id });
  if (res.status === "ok") getMescroll().resetUpScroll(true);
  else uni.$u.toast(res.msg);
};

// 图片加载错误处理
const imageError = (i) => {
  // 当图片加载失败时，设置一个默认图片
  if (goods.value[i] && goods.value[i].huodong_info) {
    goods.value[i].huodong_info.img_url_fallback = true;
    console.log(`活动图片加载失败，已切换到默认图片: 索引${i}`);
  }
};

/**
 * 处理列表项点击，根据不同类型跳转到相应的详情页
 * @param {Object} item - 列表项数据
 * @param {Number} index - 列表项索引
 */
const handleItemClick = (item, index) => {
  console.log('点击项目:', item, '类型:', type.value);
  
  // 不同选项卡对应不同类型内容
  switch (current.value) {
    case 0: // 我的报名 - 活动详情
      if (item.huodong_id) {
        navto(`/pages/bundle/index/activeInfo?id=${item.huodong_id}`);
      }
      break;
      
    case 1: // 我的发布 - 活动详情
      if (item.huodong_id) {
        navto(`/pages/bundle/index/activeInfo?id=${item.huodong_id}`);
      }
      break;
      
    case 2: // 我的收藏
      if (item.huodong_id) {
        navto(`/pages/bundle/index/activeInfo?id=${item.huodong_id}`);
      } else if (item.feed_id) {
        navto(`/pages/bundle/world/feed/detail?id=${item.feed_id}`);
      } else if (item.card_id) {
        navto(`/pages/bundle/world/card/detail?cardId=${item.card_id}`);
      } else if (item.quote_id) {
        navto(`/pages/bundle/world/quote/detail?id=${item.quote_id}`);
      }
      break;
      
    case 3: // 我的点赞
      if (item.huodong_id) {
        navto(`/pages/bundle/index/activeInfo?id=${item.huodong_id}`);
      } else if (item.feed_id) {
        navto(`/pages/bundle/world/feed/detail?id=${item.feed_id}`);
      } else if (item.card_id) {
        navto(`/pages/bundle/world/card/detail?cardId=${item.card_id}`);
      } else if (item.quote_id) {
        navto(`/pages/bundle/world/quote/detail?id=${item.quote_id}`);
      }
      break;
      
    case 4: // 我的评论
      if (item.huodong_id) {
        navto(`/pages/bundle/index/activeInfo?id=${item.huodong_id}`);
      } else if (item.feed_id) {
        navto(`/pages/bundle/world/feed/detail?id=${item.feed_id}`);
      } else if (item.card_id) {
        navto(`/pages/bundle/world/card/detail?cardId=${item.card_id}`);
      } else if (item.quote_id) {
        navto(`/pages/bundle/world/quote/detail?id=${item.quote_id}`);
      }
      break;
      
    default:
      console.log('未知选项卡类型');
      break;
  }
};
</script>
<template>
  <view class="page">
    <myTitle
        bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        height="176rpx"
        title="我的活动"
        color="#ffffff"
        :blod="true"
    ></myTitle>
    <u-tabs
        :current="current"
        :list="tabList"
        :active-style="{
        borderRadius: '24rpx',
        textAlign: 'center',
        lineHeight: '48rpx',
        fontSize: '28rpx',
        color: '#ffffff',
        fontWeight: '600',
        backgroundColor: '#6AC086',
        boxShadow: '0 2rpx 8rpx rgba(106, 192, 134, 0.3)'
      }"
        :inactiveStyle="{
        fontSize: '26rpx',
        color: '#6c757d',
        fontWeight: '400'
      }"
        :itemStyle="{
        padding: '16rpx 24rpx',
        margin: '0 8rpx'
      }"
        lineWidth="0"
        @click="changeTabs"
    ></u-tabs>
    <view class="px30">
      <mescroll-uni
          class="list"
          :height="height"
          :down="{
          auto: false,
        }"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @topclick="$event.scrollTo(0)"
      >
        <view class="activity-item" v-for="(val, i) in goods" :key="i" @click="handleItemClick(val, i)">
          <!-- 左侧固定宽高的缩略图 -->
          <view class="activity-thumb">
            <image
                class="activity-image"
                :src="val.huodong_info?.img_url_fallback ? `${store().$state.url}default_activity.png` : val.huodong_info?.img_url"
                mode="aspectFill"
                lazy-load
                @error="imageError(i)"
            ></image>
          </view>

          <!-- 右侧活动信息 -->
          <view class="activity-info">
            <!-- 活动状态标签 -->
            <view class="activity-status-tags">
              <view class="status-tag" v-if="current === 0 || current === 1">
                <text>{{
                    getItem(
                      ['未开始', '报名中', '已结束'],
                      Date.now() * 1 < new Date(val.huodong_id?.baoming_start_time) * 1
                        ? 0
                        : Date.now() * 1 > new Date(val.huodong_id?.baoming_end_time) * 1
                        ? 2
                        : 1
                    )
                  }}</text>
              </view>

              <view class="status-tag secondary" v-if="current === 0">
                <text>{{
                    getItem(
                      ['未支付', '已报名', '已取消', '退款中', '退款成功', '退款失败'],
                      val.status
                    )
                  }}</text>
              </view>

              <view class="status-tag secondary" v-if="current === 1">
                <text>{{
                    getItem(
                      ['审核中', '审核通过', '审核未通过', '活动已取消'],
                      val.huodong_info?.status
                    )
                  }}</text>
              </view>
            </view>

            <!-- 活动标题 -->
            <view class="activity-title">
              <u-text
                  size="30rpx"
                  :bold="true"
                  lines="1"
                  :text="val.huodong_info?.name"
              ></u-text>
            </view>

            <!-- 活动日期 -->
            <view class="activity-date">
              <view class="date-line">
                <u-icon
                    :name="`${store().$state.url}time.png`"
                    size="24rpx"
                    :label="formatDate(val.huodong_info?.start_time)"
                    space="8rpx"
                    color="#666666"
                    label-size="24rpx"
                    label-color="#666666"
                ></u-icon>
                <view class="date-line-hr"></view>
              </view>
            </view>

            <!-- 活动位置 -->
            <view class="activity-location">
              <u-text
                  :prefix-icon="`${store().$state.url}place.png`"
                  icon-style="margin-right:8rpx;width:24rpx;height:24rpx;"
                  :text="
                  val.huodong_info?.sheng +
                  val.huodong_info?.shi +
                  val.huodong_info?.qu +
                  val.huodong_info?.addr
                "
                  lines="1"
                  color="#666666"
                  size="24rpx"
              ></u-text>
            </view>

            <!-- 报名人数 -->
            <view class="activity-participants">
              <u-icon
                  :name="`${store().$state.url}man.png`"
                  size="24rpx"
                  :label="`${val.huodong_info?.baoming_num}人报名`"
                  label-color="#666666"
                  space="8rpx"
                  label-size="24rpx"
              ></u-icon>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="activity-actions" v-if="current == 1 && val.huodong_info?.status != 3">
            <!--
            <u-button
              text="取消活动"
              shape="circle"
              color="#aaa"
              plain
              :customStyle="{
                margin: '10rpx 10rpx 0',
                width: '150rpx',
                height: '50rpx',
                fontSize: '24rpx',
              }"
              @click.stop="cancel(val, i)"
            ></u-button>
            -->
            <u-button
                text="修改活动"
                shape="circle"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :customStyle="{
                margin: '10rpx 10rpx 0',
                width: '160rpx',
                height: '50rpx',
                fontSize: '24rpx',
                color: '#fff',
                fontWeight: '500',
                boxShadow: '0 2rpx 8rpx rgba(106, 192, 134, 0.3)'
              }"
                @click.stop="
                navto(
                  `/pages/bundle/index/addActive?huodong_id=${val.huodong_id}`
                )
              "
            ></u-button>
          </view>

          <view class="activity-actions" v-if="current === 0">
            <u-button
                v-if="val.status === 1"
                text="取消报名"
                shape="circle"
                color="linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
                :customStyle="{
                margin: '10rpx 10rpx 0',
                width: '150rpx',
                height: '50rpx',
                fontSize: '24rpx',
                color: '#fff',
                fontWeight: '500',
                boxShadow: '0 2rpx 8rpx rgba(220, 53, 69, 0.25)'
              }"
                @click.stop="cancelHuodong(val.order_id)"
            ></u-button>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
}

.activity-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 24rpx;
  padding-bottom: 80rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.activity-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
}

.activity-thumb {
  flex-shrink: 0;
  width: 160rpx;
  height: 160rpx;
  margin-right: 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.activity-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.activity-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 50rpx;
}

.activity-status-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.status-tag {
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 6rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #88D7A0, #6AC086);
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(106, 192, 134, 0.3);
  position: relative;
  display: flex;
  align-items: center;

  /* 移除状态指示器圆点 */
  &::before {
    display: none;
  }
}

.status-tag.secondary {
  background: linear-gradient(135deg, #A8E6C1, #88D7A0);
  color: #ffffff;

  /* 移除状态指示器圆点 */
  &::before {
    display: none;
  }
}

/* 全局移除可能的状态圆点 */
.status-tag text::before,
.status-tag text::after {
  display: none !important;
}

/* 移除可能的uView组件默认圆点 */
.u-text::before,
.u-text::after {
  display: none !important;
}

.activity-title {
  margin-bottom: 16rpx;
  font-weight: 600;
  font-size: 30rpx;
  color: #212529;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.activity-date {
  margin-bottom: 12rpx;
}

.date-line {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.date-line-hr {
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, #dee2e6, transparent);
  margin-left: 12rpx;
}

.activity-location {
  margin-bottom: 12rpx;
  color: #6c757d;
  font-size: 24rpx;
}

.activity-participants {
  color: #6c757d;
  margin-bottom: 10rpx;
  padding-right: 180rpx;
  font-size: 24rpx;
}

.activity-actions {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  display: flex;
  justify-content: flex-end;
  z-index: 5;
  gap: 12rpx;
}
</style>
