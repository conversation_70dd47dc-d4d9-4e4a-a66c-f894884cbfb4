<?php
namespace controller;
use core\Controller;
use core\Db;
use core\Upload;
/*
 * @className 系统
*/
class Sys extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 配置
	* @method config
	*/
	public function config(){
		dbConn();
		if(isAjax()){
			responseType("json");
			if(
				!empty($_POST['id']) &&
				isset($_POST['val']) &&
				check($_POST['id'],"intgt0")
			){
				try{
					$id = (int)$_POST['id'];
					$val = htmlspecialchars(trim($_POST['val']));
					$data = ["val"=>":val"];
					$prepareParam = [":val"=>$val];
					$res = Db()->table("config")->where("id={$id}")->prepareParam($prepareParam)->update($data);
					if($res){
						$this->root_log("修改系统配置,id:[ {$id} ],值:[ {$val} ]");
						return ["status"=>"ok"];
					}
				}catch(\Exception $e){
					return ["status"=>"error","msg"=>$e->getMessage()];
				}
			}
			return ["status"=>"error","msg"=>"操作失败"];
		}
		$data = Db()->table("config")->where("is_show=1")->fetchAll();
		assign("data",$data);
		return view(['header','sys/config','footer']);
	}
	/*
	* @name 数据库备份信息
	* @method databak
	*/
	public function databak(){
		$path =  BASE_PATH . config("sys.databak");
		if(is_dir($path)){
			$arr = scandir($path);
		}else{
			$arr = [];
		}
		$files = [];
		foreach($arr as $v){
			if(substr($v,-8,8) == ".sql.zip" || substr($v,-4,4) == ".sql"){
				$filesize = filesize($path.DS.$v);
				if($filesize > 1024 * 1024 * 1024){
					$size = number_format($filesize/1024/1024/1024,2)."G";
				}else if($filesize > 1024 * 1024){
					$size = number_format($filesize/1024/1024,2)."M";
				}else if($filesize > 1024){
					$size = number_format($filesize/1024,2)."K";
				}else{
					$size = number_format($filesize,2)."B";
				}
				$filemtime = filemtime($path.DS.$v);
				$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
				$tmp = explode(".", $v);
				$ext = end($tmp);
				$code = substr($v, -20,-4);$c = $code;
				$code = "";
				$databak_code = config("sys.databak_code");
				foreach($databak_code as $dc_k=>$dc_v){
					$code .= $c[$dc_v];
				}
				$baktime = substr($v, -40,-21);
				$baktime_show = substr($baktime,0,4) . "-" . substr($baktime,5,2) . "-" . substr($baktime,8,2) . " " . substr($baktime,11,2) . ":" . substr($baktime,14,2) . ":" . substr($baktime,17,2) ;
				$dbname = substr($v,0,-41);
				$files[] = ["dbname"=>$dbname,"baktime"=>$baktime,"baktime_show"=>$baktime_show,"code"=>$code,"ext"=>$ext,"size"=>$size,"filemtime"=>$filemtime];
			}
		}
		rsort($files);
		assign("data",$files);
		return view(['header','sys/databackup','footer']);
	}
	/*
	* @name 数据库备份
	* @method backup
	*/
	public function backup(){
		responseType("json");
		mysqlDump();
		$this->root_log("备份数据库");
		return ["status"=>"ok"];
	}
	/*
	* @name 数据库还原
	* @method restore
	*/
	public function restore(){
		responseType("json");
		return ["status"=>"error","msg"=>"该功能已禁用"];exit;
		if(IP != "127.0.0.1"){
			return ["status"=>"error","msg"=>"仅支持本地访问"];exit;
		}
		if(empty($_GET['dbname']) || empty($_GET['baktime']) || empty($_GET['code']) || strlen($_GET['code']) != 16 || empty($_GET['ext']) || ($_GET['ext'] != "sql" && $_GET['ext'] != "zip")){
			return ["status"=>"error","msg"=>"权限不足"];
		}
		if($_SESSION['root_info']['r_name'] != "root"){
			return ["status"=>"error","msg"=>"权限不足"];
		}
		$dbname = trim($_GET['dbname']);
		$baktime = trim($_GET['baktime']);
		$code = trim($_GET['code']);
		$ext = trim($_GET['ext']);

		$c = $code;
		$code = "";
		$databak_code = config("sys.databak_code");
		asort($databak_code);
		foreach($databak_code as $dc_k=>$dc_v){
			$code .= $c[$dc_k];
		}
		$filename = $dbname . "_" . $baktime . "_" . $code . "." . $ext;

		$path = BASE_PATH . config("sys.databak") . DS . $filename;
		$config = \core\Config::getDatabaseConfig();
		$useConfig = "";
		foreach($config as $k=>$v){
			if(($v["host"] == "127.0.0.1" || $v["host"] == "localhost") && $v['database'] == $dbname){
				$useConfig = $k;
				break;
			}
		}
		if(empty($useConfig))return ["status"=>"error","msg"=>"还原数据库未找到"];
		$res = mysqlImport($path,$useConfig);
		if($res !== true && is_string($res)){
			return ["status"=>"error","msg"=>$res];
		}
		$this->root_log("还原数据库【{$path}】");
		return ["status"=>"ok"];
	}
	/*
	* @name 数据库备份下载
	* @method databak_download
	*/
	public function databak_download(){

		if(empty($_GET['dbname']) || empty($_GET['baktime']) || empty($_GET['code']) || strlen($_GET['code']) != 16 || empty($_GET['ext']) || ($_GET['ext'] != "sql" && $_GET['ext'] != "zip")){
			return view(['header','noprivileges','footer'],["alertMsg"=>"权限不足"]);
		}
		if($_SESSION['root_info']['r_name'] != "root"){
			return view(['header','noprivileges','footer'],["alertMsg"=>"权限不足"]);
		}
		$dbname = trim($_GET['dbname']);
		$baktime = trim($_GET['baktime']);
		$code = trim($_GET['code']);
		$ext = trim($_GET['ext']);

		$c = $code;
		$code = "";
		$databak_code = config("sys.databak_code");
		asort($databak_code);
		foreach($databak_code as $dc_k=>$dc_v){
			$code .= $c[$dc_k];
		}
		$filename = $dbname . "_" . $baktime . "_" . $code . "." . $ext;

		$path = BASE_PATH . config("sys.databak") . DS . $filename;
		if(!file_exists($path)){
			return view(['header','noprivileges','footer'],["alertMsg"=>"权限不足"]);
		}

		header('Content-disposition: attachment; filename='.$filename); //文件名
		//header("Content-Type: application/".substr($filename,-4)); //zip格式的
		header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
	    header('Content-Length: '. filesize($path)); //告诉浏览器，文件大小
		@readfile($path);
		exit;
	}
	/*
	* @name 删除数据库备份文件
	* @method databak_del
	*/
	public function databak_del(){
		responseType("json");
		if(empty($_POST['dbname']) || empty($_POST['baktime']) || empty($_POST['code']) || strlen($_POST['code']) != 16 || empty($_POST['ext']) || ($_POST['ext'] != "sql" && $_POST['ext'] != "zip")){
			return ["status"=>"error"];
		}
		if($_SESSION['root_info']['r_name'] != "root"){
			return ["status"=>"error"];
		}
		if(isAjax()){
			$dbname = trim($_POST['dbname']);
			$baktime = trim($_POST['baktime']);
			$code = trim($_POST['code']);
			$ext = trim($_POST['ext']);
			$c = $code;
			$code = "";
			$databak_code = config("sys.databak_code");
			asort($databak_code);
			foreach($databak_code as $dc_k=>$dc_v){
				$code .= $c[$dc_k];
			}			
			$filename = $dbname . "_" . $baktime . "_" . $code . "." . $ext;
			$path = BASE_PATH . config("sys.databak") . DS . $filename;
			if(!file_exists($path)){
				return ["status"=>"error"];
			}
			@unlink($path);
			$this->root_log("删除数据库备份文件[{$dbname}-{$baktime}]");
		}
		
		return ["status"=>"ok"];
	}
	/*
	* @name 异常记录
	* @method exception
	*/
	public function exception(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		//
		$start_date = date("Y-m-d",strtotime("-15 day"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//
		if(!empty($_REQUEST['ip'])){
			$where .= " AND ip=:ip";
			$prepareParam[":ip"]=$_REQUEST['ip'];
			$pageParams['ip'] = $_REQUEST['ip'];
		}
		if(!empty($_REQUEST['keyword'])){
			$keyword_sth = "%".urldecode($_REQUEST['keyword'])."%";
			$where .= " AND msg LIKE :keyword_sth";
			$prepareParam[":keyword_sth"]=$keyword_sth;
			$pageParams['keyword'] = urlencode($_REQUEST['keyword']);
		}
		$data = Db()->table("exception_log")->where($where)->prepareParam($prepareParam)->order("id desc")->page($page,$page_size,$pageParams);
		assign("data",$data);	
		return view(['header','sys/exception','footer']);
	}
	/*
	* @name 访问统计
	* @method access_log
	*/
	public function access_log(){
		if(stripos(strtolower(PHP_OS),"linux") === false){
			if(isAjax()){
				responseType("json");
				$data = ["status"=>"error","msg"=>"只支持Linux系统"];
				return $data;
			}else{
				assign("msg","只支持Linux系统");
				return view(['header','noprivileges','footer']);
			}
		}
		//
		if(!empty($_POST['ajax'])){
			responseType("json");
			$type = !empty($_POST['type']) && check($_POST['type'],"intgt0") ? (int)$_POST['type'] : 1;
			$log_path = !empty($_POST['log_path']) ? base64_decode($_POST['log_path']) : "";
			$date = !empty($_POST['date']) ? $_POST['date'] : "";
			$ip = !empty($_POST['ip']) ? $_POST['ip'] : "";
			return nginx_access($type,$log_path,$date,$ip);
		}
		//
		$path =  BASE_PATH . "access_log";
		if(!file_exists($path) || !is_dir($path)){
			@mkdir($path, 0777, true);
		}
		$arr = scandir($path);
		$logs = [];
		foreach($arr as $v){
			if($v == "." || $v == ".." || strlen($v) <= 4 || substr($v,0,1) == ".")continue;
			if(substr($v,-4) == ".log"){
				$c_file = $path . DS . $v;
				$row = [
					"path"=>base64_encode($c_file),
					"file_name"=>$v,
					"size"=>$this->getFileSize($c_file),
				];
				$logs[] = $row;				
			}
		}
		assign("logs",$logs);
		return view(['header','sys/access_log','footer']);
	}
	/*
	* @name 文档查看
	* @method document
	*/
	public function document(){
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		dbConn();
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];
		if(!empty($_REQUEST['filename'])){
			$filename_tmp = trim($_REQUEST['filename']);
			$filename = "%".urldecode($filename_tmp)."%";
			$where .= " AND `filename` LIKE :filename";
			$prepareParam[":filename"] = $filename;
			$pageParams['filename'] = urlencode($filename_tmp);
		}
		//dump($where);
		$data = Db()->table("files")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		$files_domain = config("sys.files_domain");
		if(empty($files_domain))$files_domain = HTTP_HOST;
		assign("files_domain",$files_domain);
		return view(['header','sys/document','footer']);
	}
	/*
	* @name 文档上传
	* @method document_upload
	*/
	public function document_upload(){
		$allow_ext = ["zip","rar","7z","gz","tar","txt","doc","docx","xls","xlsx","ppt","pptx","jpg","png","jpeg","gif","apk","ipa"];
		$size_k = 50 * 1024;
		if(!empty($_FILES['file']) && isset($_FILES['file']['size']) && $_FILES['file']['size'] > 0){
			responseType("json");
			$filename = basename($_FILES['file']['name']);
			$tmp = explode('.', $filename);
			$filename = mb_substr($filename,0,60);
			$ext = strtolower(end($tmp));
			$date = date("Y_m_d");
			$saveName = md5(uniqid(microtime(true),true));
			$pathName = "files" . DS . $date . DS . $saveName;
			//
			$path = BASE_PATH . "files" . DS . $date;
			$pathFile = $path . DS . $saveName;
			//
			if(!is_dir($path))@mkdir($path, 0777, true);
			if(!in_array($ext,$allow_ext) || $_FILES['file']['size'] > $size_k * 1024 || mb_strlen($ext) > 5){
				return ["status"=>"error","msg"=>"文件格式或大小错误"];
			}else{
				if(move_uploaded_file($_FILES['file']['tmp_name'],$pathFile)){
					$size_b = $_FILES['file']['size'];
					if($size_b > 1024 * 1024 * 1024){
						$size = number_format($size_b/1024/1024/1024,2)."G";
					}else if($size_b > 1024 * 1024){
						$size = number_format($size_b/1024/1024,2)."M";
					}else if($size_b > 1024){
						$size = number_format($size_b/1024,2)."K";
					}else{
						$size = number_format($size_b,2)."B";
					}
					dbConn();
					$data = [
						"filename"=>$filename,
						"path"=>$pathName,
						"size"=>$size,
						"ext"=>$ext,
					];
					if(Db()->table("files")->insert($data)){
						return ["status"=>"ok","msg"=>"上传成功"];
					}
				}
			}
			return ["status"=>"error","msg"=>"上传失败"];
		}
		assign("allow_ext",$allow_ext);
		assign("size_k",$size_k);
		return view(['header','sys/document_upload','footer']);

	}
	/*
	* @name 文档下载
	* @method document_download
	* @ignore
	*/
	public function document_download(){
		if(empty($_GET['id']) || !check($_GET['id'],"intgt0")){
			die(404);exit();
		}
		$id = (int)$_GET['id'];
		dbConn();
		$info = Db()->table("files")->where("id={$id}")->fetch();
		if(empty($info)){
			die(404);exit();
		}
		$path = BASE_PATH . $info['path'];
		if(!file_exists($path)){
			die(404);exit();
		}
		//
		$sql = "UPDATE `files` SET `downtimes`=`downtimes`+1 WHERE id={$id}";
		Db()->_exec($sql);
		//
		header('Content-disposition: attachment; filename='.$info['filename']); //文件名
		header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
	    header('Content-Length: '. filesize($path)); //告诉浏览器，文件大小
		@readfile($path);
	}
	/*
	* @name 删除文档
	* @method document_del
	*/
	public function document_del(){
		$params = getRouteParams();
		unset($params['remove']);
		dbConn();
		if(isset($_GET['remove']) && check($_GET['remove'],"intgt0")){
			$id = (int)$_GET['remove'];
			$info = Db()->table("files")->select("path,filename")->where("id={$id}")->fetch();
			if($info){
				if(Db()->table("files")->where("id={$id}")->del()){
					@unlink(BASE_PATH . $info['path']);
					$params['alert'] = $id;
					$this->root_log("删除文档[ {$info['filename']} ]");
				}else{
					$params['alert'] = 0;
				}
			}else{
				$params['alert'] = 0;
			}
			header("location:".url("sys/document",$params));
		}else if(!empty($_POST['ids'])){
			responseType("json");
			$id_arr = explode(",",$_POST['ids']);
			$ids = [];
			foreach($id_arr as $id){
				if(is_numeric($id)){
					$id = (int)$id;
					if($id > 0)$ids[] = $id;
				}
			}
			if(empty($ids))return ["status"=>"error","msg"=>"参数错误"];
			$ids_str = implode(",",$ids);
			$rowCount = 0;
			$data = Db()->table("files")->select("id,path,filename")->where("id IN ({$ids_str})")->fetchAll();
			if(!empty($data)){
				foreach($data as $info){
					if(Db()->table("files")->where("id={$info['id']}")->del()){
						@unlink(BASE_PATH . $info['path']);
						$rowCount ++;
					}
				}
			}
			if($rowCount){
				$this->root_log("删除文档,id:{$ids_str}");
				return ["status"=>"ok","msg"=>"删除行数[ {$rowCount} ]"];
			}
			return ["status"=>"error","msg"=>"删除行数为零"];
		}
	}
	/*
	* @name 清理缓存
	* @method clear_cache
	*/
	public function clear_cache(){
		$path = BASE_PATH . "cache" . DS;
		if(!is_dir($path))@mkdir($path, 0777, true);
		$file_arr = scandir($path);
		$cache_num = count($file_arr) - 2;
		if(!empty($_POST['ajax'])){
			responseType("json");
			if($cache_num < 1){
				$msg = "<p>系统暂无缓存</p><br />";
				return ["status"=>"ok","msg"=>$msg];
			}
			$msg = "<p>处理缓存数量 [ {$cache_num} ]:</p><br />";
			foreach($file_arr as $file){
				if($file == "." || $file == ".." || is_dir($file))continue;
				$filePath = $path . $file;
				if(@unlink($filePath)){
					$msg .= "<p>" . $filePath . "&nbsp;&nbsp; [ 成功 ]</p>";
				}else{
					$msg .= "<p>" . $filePath . "&nbsp;&nbsp; [ 失败 ]</p>";
				}
			}
			return ["status"=>"ok","msg"=>$msg];
		}
		assign("cache_num",$cache_num);
		return view(['header','sys/clear_cache','footer']);
	}
	/*
	* @name 守护进程
	* @method daemon
	*/
	public function daemon(){
		if(!empty($_POST['ajax']) && !empty($_POST['action'])){
			responseType("json");
			$action = trim($_POST['action']);
			$msg = " <br />";
			if($action == "status"){
				//
				$pid_file = BASE_PATH . "daemon.pid";
				$time_file = BASE_PATH . "daemon.time";
				$err_file = BASE_PATH . "daemon.err";
				$start_file = BASE_PATH . "daemon.start";
				//
				$cmd = "ps -ef | grep start_daemon_huodongbao | grep -vc grep";
				$result = shell_exec($cmd);
				$msg .= "<p>{$cmd} : &nbsp;&nbsp; {$result}</p> <br />";
				//
				if(file_exists($pid_file)){
					$pid = file_get_contents($pid_file);
					$msg .= "<p>[ {$pid_file} ] : &nbsp;&nbsp; [ {$pid} ]</p> <br />";
				}
				//
				if(file_exists($time_file)){
					$time_arr = file($time_file);
					$count = count($time_arr);
					$time_msg = "";
					for($i=$count-1;$i>=$count-10;$i--){
						if(isset($time_arr[$i])){
							$time_msg .= " * {$time_arr[$i]}<br />";
						}
					}
					$msg .= "<p> [ {$time_file} ] : <br /> {$time_msg}</p> <br />";
				}
				//
				if(file_exists($err_file)){
					$err_arr = file($err_file);
					$count = count($err_arr);
					$err_msg = "";
					for($i=$count-1;$i>=$count-10;$i--){
						if(isset($err_arr[$i])){
							$err_msg .= " * {$err_arr[$i]}<br />";
						}
					}
					$msg .= "<p> [ {$err_file} ] : <br /> {$err_msg}</p> <br />";
				}
				//
				if(file_exists($start_file)){
					$start_arr = file($start_file);
					$count = count($start_arr);
					$start_msg = "";
					for($i=$count-1;$i>=$count-10;$i--){
						if(isset($start_arr[$i])){
							$start_msg .= " * {$start_arr[$i]}<br />";
						}
					}
					$msg .= "<p> [ {$start_file} ] : <br /> {$start_msg}</p> <br />";
				}
				//
			}else if($action == "start"){
				$cmd = BASE_PATH."process_server.sh";
				$result = shell_exec($cmd);
				$msg .= "<p>执行命令[ {$cmd} ] : &nbsp;&nbsp; {$result}</p>";
			}else if($action == "stop"){
				$cmd = "cd ".BASE_PATH."access && php cli controller=daemon action=stop";
				$result = shell_exec($cmd);
				$msg .= "<p>执行命令[ {$cmd} ] : &nbsp;&nbsp; {$result}</p>";
			}else{
				$msg = "未知操作";
			}
			return ["status"=>"ok","msg"=>$msg];
		}
		return view(['header','sys/daemon','footer']);
	}
	/*
	* @name 系统日志
	* @method sys_log
	*/
	public function sys_log(){
		$max_yulan_size = 20 * 1024 * 1024;
		assign("max_yulan_size",$max_yulan_size);
		//
		if(!empty($_POST['clear'])){
			responseType("json");
			$log_path = base64_decode($_POST['clear']);
			if(substr($log_path,-4) != ".log"){
				return ["status"=>"error","msg"=>"只支持清空日志文件"];
			}
			if(!file_exists($log_path)){
				return ["status"=>"error","msg"=>"日志文件【{$log_path}】不存在"];
			}
			if(!is_writeable($log_path)){
				return ["status"=>"error","msg"=>"日志文件【{$log_path}】无写入权限"];
			}
			$res = file_put_contents($log_path,"");
			if($res === false){
				return ["status"=>"ok","msg"=>"操作失败"];
			}
			return ["status"=>"ok","msg"=>"操作成功"];
		}
		//
		if(!empty($_GET['download'])){
			$log_path = base64_decode($_GET['download']);
			if(substr($log_path,-4) != ".log"){
				echo "只支持下载日志文件";
				exit;
			}
			if(!file_exists($log_path)){
				echo "日志文件【{$log_path}】不存在";
				exit;
			}
			if(!is_readable($log_path)){
				echo "日志文件【{$log_path}】无可读权限";
				exit;				
			}
			$tmp = explode(DS,$log_path);
			$file_path_name = end($tmp);
			$file_name = substr($file_path_name,0,-4)."_".date("Y_m_d_H_i_s").substr($file_path_name,-4);
			header("Content-disposition: attachment; filename={$file_name}"); //文件名
			header("Content-Transfer-Encoding: binary"); //告诉浏览器，这是二进制文件
			header('Content-Length: '. filesize($log_path)); //告诉浏览器，文件大小
			@readfile($log_path);
			exit;
		}
		//
		if(!empty($_GET['read'])){
			$log_path = base64_decode($_GET['read']);
			if(substr($log_path,-4) != ".log"){
				echo "只支持预览日志文件";
				exit;
			}
			if(!file_exists($log_path)){
				echo "日志文件【{$log_path}】不存在";
				exit;
			}
			if(!is_readable($log_path)){
				echo "日志文件【{$log_path}】无可读权限";
				exit;				
			}
			if(filesize($log_path) > $max_yulan_size){
				echo "日志文件【{$log_path}】体积过大，不支持预览，请下载后打开";
				exit;				
			}
			$tmp = explode(DS,$log_path);
			$title = end($tmp);
			echo "<html><head><title>{$title}</title></head><body>";
			echo "<pre>";
			$contents = htmlspecialchars(file_get_contents($log_path));
			echo $contents;
			echo "</pre>";
			echo "</body></html>";
			exit;
		}
		//
		$logs = [];
		//目录日志
		$www_dir = dirname(dirname(__DIR__));
		//
		$curent_item_arr = explode("_",basename(dirname(__DIR__)));
		$curent_item = $curent_item_arr[0];
		//
		if(is_readable($www_dir)){
			$dirs = scandir($www_dir);
			foreach($dirs as $dir){
				if($dir == "." || $dir == ".." || substr($dir,0,1) == ".")continue;
				//
				if(mb_substr($dir,0,mb_strlen($curent_item)) !== $curent_item)continue;
				//
				$c_dir = $www_dir . DS . $dir. DS . "access";
				if(!file_exists($c_dir) || !is_dir($c_dir) || !is_readable($c_dir))continue;
				$files = scandir($c_dir);
				foreach($files as $file){
					if($file == "." || $file == ".." || strlen($file)<=4 || substr($file,-4) != ".log")continue;
					$c_file = $c_dir . DS . $file;
					if(is_readable($c_file) && is_file($c_file)){
						$filemtime = filemtime($c_file);
						$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
						$row = [
							"type"=>"运行日志",
							"path"=>base64_encode($c_file),
							"file_name"=>$file,
							"size"=>$this->getFileSize($c_file),
							"filemtime"=>$filemtime,
						];
						$logs[] = $row;
					}
				}
			}
		}
		//访问日志
		$access_log_path = BASE_PATH . "access_log";
		if(is_readable($access_log_path)){
			$files = scandir($access_log_path);
			foreach($files as $file){
				if($file == "." || $file == "..")continue;
				$c_file = $access_log_path . DS . $file;
				if(mb_strlen($file) > 4 && mb_substr($file,-4) == ".log" && is_readable($c_file)){
					$filemtime = filemtime($c_file);
					$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
					$row = [
						"type"=>"访问日志",
						"path"=>base64_encode($c_file),
						"file_name"=>$file,
						"size"=>$this->getFileSize($c_file),
						"filemtime"=>$filemtime,
					];
					$logs[] = $row;
				}
			}
		}
		//public默认目录
		$default_access = $www_dir . DS . "public";
		if(is_readable($default_access)){
			$files = scandir($default_access);
			foreach($files as $file){
				if($file == "." || $file == "..")continue;
				$c_file = $default_access . DS . $file;
				if(mb_strlen($file) > 4 && mb_substr($file,-4) == ".log" && is_readable($c_file)){
					$filemtime = filemtime($c_file);
					$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
					$row = [
						"type"=>"访问日志",
						"path"=>base64_encode($c_file),
						"file_name"=>$file,
						"size"=>$this->getFileSize($c_file),
						"filemtime"=>$filemtime,
					];
					$logs[] = $row;
				}
			}
		}
		//
		assign("logs",$logs);
		//
		return view(['header','sys/sys_log','footer']);
	}
	/*
	* @name 服务器环境
	* @method sys_env
	*/
	public function sys_env(){
		if(stripos(strtolower(PHP_OS),"linux") === false){
			if(isAjax()){
				responseType("json");
				$data = ["status"=>"error","msg"=>"只支持Linux系统"];
				return $data;
			}else{
				assign("msg","只支持Linux系统");
				return view(['header','noprivileges','footer']);
			}
		}
		//读
		if(!empty($_POST['read'])){
			responseType("json");
			$file_path = base64_decode($_POST['read']);
			if(!file_exists($file_path)){
				return ["status"=>"error","msg"=>"文件【{$file_path}】不存在"];
			}else if(!is_readable($file_path)){
				return ["status"=>"error","msg"=>"文件【{$file_path}】无可读权限"];
			}
			$tmp = explode(DS,$file_path);
			$title = end($tmp);
			$contents = file_get_contents($file_path);
			$data = ["title"=>$title,"contents"=>$contents];
			return ["status"=>"ok","data"=>$data];
		}
		//写
		if(!empty($_POST['path']) && isset($_POST['contents'])){
			responseType("json");
			$file_path = base64_decode($_POST['path']);
			if(!file_exists($file_path)){
				return ["status"=>"error","msg"=>"文件【{$file_path}】不存在"];
			}else if(!is_writeable($file_path)){
				return ["status"=>"error","msg"=>"文件【{$file_path}】无可写权限"];	
			}
			$old_file = $file_path."_".date("YmdHis");
			$dirname = dirname($file_path);
			if(!is_writeable($dirname)){
				return ["status"=>"error","msg"=>"备份旧文件过程中，目录【{$dirname}】没有写入权限"];
			}
			$res = rename($file_path,$old_file);
			if(empty($res)){
				return ["status"=>"error","msg"=>"备份旧文件失败"];
			}
			$contents = $_POST['contents'];
			$res = file_put_contents($file_path,$contents);
			if($res){
				return ["status"=>"ok","msg"=>"操作成功"];	
			}else{
				return ["status"=>"error","msg"=>"操作失败"];	
			}
		}
		//
		$files_arr = [];
		//
		$config = [
			"/usr/local/php/etc/php.ini",
			"/usr/local/php/etc/php-fpm.conf",
			"/usr/local/nginx/conf/nginx.conf",
			"/usr/local/nginx/conf/fastcgi.conf",
			"/usr/local/nginx/conf/enable-php.conf",
			"/usr/local/nginx/conf/enable-php-pathinfo.conf",
			"/usr/local/nginx/conf/pathinfo.conf",
			"/etc/my.cnf",
		];
		foreach($config as $c_file){
			if(is_readable($c_file) && is_file($c_file)){
				$tmp = explode(DS, $c_file);
				$file_name = end($tmp);
				$filemtime = filemtime($c_file);
				$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
				$row = [
					"type"=>"config",
					"path"=>base64_encode($c_file),
					"file_name"=>$file_name,
					"size"=>$this->getFileSize($c_file),
					"filemtime"=>$filemtime,
				];
				$files_arr[] = $row;
			}
		}
		//vhost
		$dir = "/usr/local/nginx/conf/vhost";
		if(is_readable($dir)){
			$files = scandir($dir);
			foreach($files as $file){
				if($file == "." || $file == "..")continue;
				$c_file = $dir . DS . $file;
				if(is_readable($c_file) && is_file($c_file)){
					$filemtime = filemtime($c_file);
					$filemtime = $filemtime ? date("Y-m-d H:i:s",$filemtime) : "-";
					$row = [
						"type"=>"vhost",
						"path"=>base64_encode($c_file),
						"file_name"=>$file,
						"size"=>$this->getFileSize($c_file),
						"filemtime"=>$filemtime,
					];
					$files_arr[] = $row;
				}
			}
		}
		//
		assign("files_arr",$files_arr);
		//
		return view(['header','sys/sys_env','footer']);
	}
	private function getFileSize($file){
		if(!is_readable($file))return 0;
		$filesize = filesize($file);
		if($filesize > 1024 * 1024 * 1024){
			$size = number_format($filesize/1024/1024/1024,2)."G";
		}else if($filesize > 1024 * 1024){
			$size = number_format($filesize/1024/1024,2)."M";
		}else if($filesize > 1024){
			$size = number_format($filesize/1024,2)."K";
		}else{
			$size = number_format($filesize,2)."B";
		}
		return $size;
	}
	public function _empty(){

	}
	function __destruct(){

	}
}
