<?php
namespace core;
use \core\Route;
class Page{
	static public $count = 0;
    static public $pageSize = 10;
    static public $zPage = 0;
	static public $pageSizeSelect = [2,5,10,15,20,30,50,75,100,150,200];
	static public $pageParams = null;
	
	public function __construct(){
		if(!in_array(self::$pageSize,self::$pageSizeSelect)){
			self::$pageSizeSelect[] = self::$pageSize;
		}
	}
	//返回渲染后的 html
	static public function render(){
		if(self::$pageSize < 1)return false;
		if(self::$count < 1)return false;
		$out = self::pageCreate();
		return $out;
	}

	//创建页面链接
	static private function pageCreate(){
		$params = is_array(self::$pageParams) ? self::$pageParams : $_REQUEST;
		$page = isset($params["page"]) ? $params["page"] : 1;
		$zPage = ceil(self::$count / self::$pageSize);
		self::$zPage = $zPage;
		if($page > $zPage)$page = $zPage;
		if($page < 1)$page = 1;
		//设置页码url
		$params["page"] = $page;
		$page_cur_url = url("",$params);
		//
		if($page == 1){
			$page_first_url = "#";
			$first_disabled = "layui-disabled";
		}else{
			$params["page"] = 1;
			$page_first_url = url("",$params);
			$first_disabled = "";			
		}
		//
		if($page == $zPage){
			$last_disabled = "layui-disabled";
			$page_last_url = "#";
		}else{
			$last_disabled = "";
			$params["page"] = $zPage;
			$page_last_url = url("",$params);			
		}
		//
		$pre_disabled = "";
		$pre_page = $page - 1;
		if($pre_page < 1){
			$pre_disabled = "layui-disabled";
			$page_pre_url = "#";
		}else{
			$params["page"] = $pre_page;
			$page_pre_url = url("",$params);
		}
		//
		$next_disabled = "";
		$next_page = $page + 1;
		if($next_page > $zPage){
			$next_disabled = "layui-disabled";
			$page_next_url = "#";
		}else{
			$params["page"] = $next_page;
			$page_next_url = url("",$params);
		}
		//
		
		//
		$count = self::$count;
		$result = <<<EOF
    <div class="layui-table-page">
        <div>
            <div class="layui-box layui-laypage layui-laypage-default">
                <a class="{$first_disabled} "href="{$page_first_url}"><i class="fa fa-fast-backward"></i></a>
                <a class="{$pre_disabled}" href="{$page_pre_url}"><i class="fa fa-backward"></i></a>
                <a href="{$page_cur_url}">{$page} / {$zPage}</a>
                <a class="{$next_disabled}" href="{$page_next_url}"><i class="fa fa-forward"></i></a>
                <a class="{$last_disabled}" href="{$page_last_url}"><i class="fa fa-fast-forward"></i></a>
                <span class="layui-laypage-skip">到第
                    <input value="{$page}" id="page_val" class="layui-input">页
                    <button type="button" onclick="change_page_number('#page_val');" class="layui-laypage-btn">GO</button>
                </span>
                <span class="layui-laypage-count">共 {$count} 条</span>
                <span class="layui-laypage-limits">
                    <select lay-ignore onchange="change_page_size(this.value);">
EOF;
		foreach(self::$pageSizeSelect as $page_size_option){
			if($page_size_option == self::$pageSize){
				$result .= '<option value="'.$page_size_option.'" selected>'.$page_size_option.' 条/页</option>';
			}else{
				$result .= '<option value="'.$page_size_option.'">'.$page_size_option.' 条/页</option>';
			}
		}
		$result .= <<<EOF
                    </select>
                </span>
            </div>
        </div>
    </div>		
EOF;
		return $result;
	}

	public function __destruct(){

	}
}
