<script setup>
import { watch, ref, computed, nextTick } from "vue";
import {
  huodongget_info,
  configget_shengshiqu_id,
  huodongadd_huodong,
  huodongupdate_huodong,
  huodongget_type,
  getCoordinate,
  getAddr,
  upload_img
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onUnload,
  onBackPress
} from "@dcloudio/uni-app";
import {store} from "@/store";
import {navto, getListHeight, back, timeSetResult} from "@/utils";
import { requireLogin } from "@/utils/auth";
// 🔧 P1-3修复：使用统一的权限检查工具函数
import { hasActivityPublishPermission } from "@/utils/permissions";
import china from "@/utils/china.js";
import myTitle from "@/components/myTitle.vue";



const columns = ref([[]]); // 选择器的数据
const formRef = ref();
const form = ref({
  name: "",
  title: "",
  type_id: null,
  sheng_id: "",
  shi_id: "",
  qu_id: "",
  addr: "",
  lng: "",
  lat: "",
  num: "",
  money: "",
  member_money: "",
  img_url: "",
  contents: "",
  start_time: "",
  end_time: "",
  pay_type: 2, // 🆕 修改：默认选择线下（无收费权限用户保持原有逻辑）
  is_online: 0,
  lianxi_name: "",
  lianxi_mobile: "",
  lianxi_qrcode: "",
  qun_qrcode: "",
  imgs_url: [],
  imgs: [],
  liucheng: []
});
const rules = ref({
  name: {
    type: "string",
    required: true,
    message: "请输入2-50个字符的活动名称",
    trigger: ["blur", "change"]
  },
  title: {
    type: "string",
    required: true,
    message: "请输入2-50个字符的活动标题",
    trigger: ["blur", "change"]
  },
  type_id: {
    type: "number",
    required: true,
    message: "请选择一个活动类型（如聚餐、运动、学习等）",
    trigger: ["blur", "change"]
  },
  sheng_id: {
    type: "number",
    required: true,
    message: "请在地图上选择活动举办的具体位置",
    trigger: ["blur", "change"]
  },
  lng: {
    type: "number",
    required: true,
    validator: (rule, value) => {
      return value && !isNaN(value) && value > 0;
    },
    message: "请在地图上点击选择活动地点以获取准确坐标",
    trigger: ["blur", "change"]
  },
  lat: {
    type: "number",
    required: true,
    validator: (rule, value) => {
      return value && !isNaN(value) && value > 0;
    },
    message: "请在地图上点击选择活动地点以获取准确坐标",
    trigger: ["blur", "change"]
  },
  start_time: [
    {
      type: "string",
      required: true,
      message: "请选择活动开始的具体日期和时间",
      trigger: ["change"]
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.date(value);
      },
      message: "请选择正确的日期时间格式（如：2024-01-01 14:00）",
      trigger: ["change"]
    }
  ],
  num: [
    {
      type: "number",
      required: true,
      message: "请输入活动参与人数（如：10人、50人）",
      trigger: ["blur", "change"]
    },
    {
      validator: (rule, value, callback) => {
        return uni.$u.test.digits(value);
      },
      message: "请输入有效的数字，范围1-9999人",
      trigger: ["change", "blur"]
    }
  ],
  money: [
    {
      type: "number",
      required: false,
      validator: (rule, value) => {
        return value === '' || value === null || value === undefined || (!isNaN(value) && parseFloat(value) >= 0 && /^\d+(\.\d{1,2})?$/.test(value));
      },
      message: "请输入正确的费用金额（如：0、50、99.99），最多两位小数",
      trigger: ["change", "blur"]
    }
  ],
  member_money: [
    {
      type: "number",
      required: false,
      validator: (rule, value) => {
        return value === '' || (!isNaN(value) && parseFloat(value) >= 0 && /^\d+(\.\d{1,2})?$/.test(value));
      },
      message: "请输入正确的会员优惠价格（如：0、30、79.99），最多两位小数",
      trigger: ["change", "blur"]
    }
  ],
  img_url: {
    type: "string",
    required: true,
    validator: (rule, value) => {
      return value && uni.$u.test.url(value);
    },
    message: "请上传一张吸引人的活动封面图片（支持JPG、PNG格式）",
    trigger: ["blur", "change"]
  },
  contents: {
    type: "string",
    required: true,
    message: "请详细描述活动内容、流程和注意事项（至少10个字）",
    trigger: ["blur", "change"]
  },
  lianxi_name: {
    type: "string",
    required: false,
    message: "请输入活动发起人的真实姓名（2-10个字符）",
    trigger: ["blur", "change"]
  },
  lianxi_qrcode: {
    type: "string",
    required: true,
    validator: (rule, value) => {
      return value && uni.$u.test.url(value);
    },
    message: "请上传您的微信二维码图片，方便参与者联系您",
    trigger: ["blur", "change"]
  },
  qun_qrcode: {
    type: "string",
    required: true,
    validator: (rule, value) => {
      return value && uni.$u.test.url(value);
    },
    message: "请上传活动微信群二维码，方便参与者加入讨论",
    trigger: ["blur", "change"]
  },
  addr: {
    type: "string",
    required: true,
    message: "请输入详细的活动地址（如：XX大厦XX楼XX室）",
    trigger: ["blur", "change"]
  }
});
const showDatePicker = ref(false);
const date = ref(Date.now());
const pickerShow = ref(false);
const type = ref(1); // 1是活动日期，2是流程时间，3是报名开始时间，4是报名结束时间，5是活动开始时间，6是活动结束时间
const mode = ref("datetime");
const liuchengIndex = ref(0);
const activeType = ref(""); // 活动分类名称
const gapHeight = ref(0);
const fileList = ref([]);
const fileList1 = ref([]);
const fileList2 = ref([]);
const fileList3 = ref([]);
const place = ref(""); // 位置名字
const edit = ref(false); // 是否修改
const is_choujiang = ref(false); // 是否抽奖
const pay_type = ref(false); // 是否抽奖
const readOnly = ref(true); // 富文本编辑器是否只读
const values = ref("");
const editorHtml = ref();
const editorReady = ref(false); // 编辑器是否准备就绪
const dateTimePicker = ref(false);
const minDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1)).getTime(); // 最小可选日期为当前时间
const maxDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime(); // 最大可选日期为一年后
const currentStep = ref(1); // 当前步骤，1为第一页（基础选择），2为第二页（详细信息），3为第三页（发布确认）
const showMemberModal = ref(false); // 会员提示弹窗状态
const showDraftDialog = ref(false); // 是否显示草稿确认对话框
const notifyRef = ref(null); // 美观提示组件引用
const autoSaveTimer = ref(null); // 自动保存定时器
const lastSaveTime = ref(0); // 上次保存时间
const isDraftLoaded = ref(false); // 是否已加载草稿
const DEBUG_MODE = false; // 调试模式开关，生产环境设为false

// 调试日志函数
const debugLog = (message, data = null) => {
  if (DEBUG_MODE) {
    if (data) {
      console.log(message, data);
    } else {
      console.log(message);
    }
  }
};

// 防重复提交状态
const isSubmitting = ref(false);
const lastSubmitTime = ref(0);
const SUBMIT_COOLDOWN = 3000; // 3秒冷却时间




const formatter = (type, value) => {
  if (type === 'year') {
    return `${value}年`;
  }
  if (type === 'month') {
    return `${value}月`;
  }
  if (type === 'day') {
    return `${value}日`;
  }
  if (type === 'hour') {
    return `${value}时`;
  }
  if (type === 'minute') {
    return `${value}分`;
  }
  return value;
};

const confirm = (e) => {
  const selectedTime = e.value || e;
  const formattedTime = formatSelectedTime(selectedTime);
  form.value.start_time = formattedTime;
  showDatePicker.value = false;
};

const formatSelectedTime = (time) => {
  // 确保传入的是有效的时间值
  const timestamp = typeof time === 'number' ? time : Date.parse(time);
  const datef = new Date(timestamp);

  if (isNaN(datef.getTime())) {
    return '';
  }

  const year = datef.getFullYear();
  const month = String(datef.getMonth() + 1).padStart(2, '0');
  const day = String(datef.getDate()).padStart(2, '0');
  const hour = String(datef.getHours()).padStart(2, '0');
  const minute = String(datef.getMinutes()).padStart(2, '0');
  const ss = String('00');

  return `${year}-${month}-${day} ${hour}:${minute}:${ss}`;
};

onPageScroll((e) => {
  if (e.scrollTop > 420) readOnly.value = false;
});



// 防抖的自动保存函数
const debouncedAutoSave = debounce(() => {
  autoSaveDraft();
}, 5000); // 5秒防抖，减少触发频率

// 防抖函数实现（带cancel方法）
function debounce(func, wait) {
  let timeout;
  const executedFunction = function(...args) {
    const later = () => {
      clearTimeout(timeout);
      timeout = null;
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };

  // 添加cancel方法
  executedFunction.cancel = function() {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  return executedFunction;
}
onLoad(async (e) => {
  try {
    // 显示加载状态
    uni.showLoading({ title: '加载中...', mask: true });

    // 检查是否为编辑模式 - 只有明确传入huodong_id参数时才进入编辑模式
    let huodong_id = e.huodong_id;

    // 如果不是编辑模式，确保表单是完全干净的
    if (!huodong_id) {
      // 重置表单到初始状态，确保没有残留数据
      resetFormToInitial();
    }

    // 自动设置联系人姓名为当前用户昵称（在表单重置后设置）
    form.value.lianxi_name = store().$state.userInfo?.nickname || "";

    // 清理可能存在的编辑状态缓存，确保新建活动时不会误读取编辑数据
    try {
      uni.removeStorageSync('activity_edit_state');
    } catch (error) {
      // 忽略清理错误
    }

    // 并行化API请求优化
    const promises = [];

    // 1. 活动类型数据请求
    const storeActiveTypes = store().$state.activeTypeList;
    if (!storeActiveTypes || !Array.isArray(storeActiveTypes) || storeActiveTypes.length === 0) {
      promises.push(
        huodongget_type().then(res => ({ type: 'activityTypes', data: res })).catch(err => ({ type: 'activityTypes', error: err }))
      );
    } else {
      columns.value = [storeActiveTypes];
    }

    // 2. 编辑模式下的活动详情请求
    if (huodong_id) {
      edit.value = true;
      form.value.huodong_id = huodong_id;

      const apiParams = {
        huodong_id: huodong_id,
        uid: store().$state.userInfo?.uid || 0,
        token: store().$state.userInfo?.token || ''
      };

      promises.push(
        huodongget_info(apiParams).then(res => ({ type: 'activityInfo', data: res })).catch(err => ({ type: 'activityInfo', error: err }))
      );
    }

    // 并行执行所有API请求
    if (promises.length > 0) {
      const results = await Promise.allSettled(promises);

      // 处理活动类型数据
      const typeResult = results.find(r => r.value?.type === 'activityTypes');
      if (typeResult && typeResult.status === 'fulfilled' && !typeResult.value.error) {
        const res = typeResult.value.data;
        if (res.status === "ok" && res.data && Array.isArray(res.data)) {
          columns.value = [res.data];
          store().changeActiveTypeList(res.data);
        } else {
          uni.$u.toast('活动类型数据格式异常');
        }
      }

      // 处理活动详情数据
      const infoResult = results.find(r => r.value?.type === 'activityInfo');
      if (infoResult && infoResult.status === 'fulfilled' && !infoResult.value.error) {
        const res = infoResult.value.data;

        if (res.status === "ok" && res.data) {
          const info = res.data;

          // 数据回填逻辑（排除图片字段，避免错误赋值）
          const excludeFields = ['img_url', 'lianxi_qrcode', 'qun_qrcode', 'imgs_url', 'imgs'];
          for (let i in form.value) {
            if (info[i] !== undefined && info[i] !== null && !excludeFields.includes(i)) {
              form.value[i] = info[i];
            }
          }

          // 设置页面标题
          uni.setNavigationBarTitle({title: "修改活动"});

          // 内容回填
          values.value = info.contents || '';
          form.value.contents = info.contents || '';

          console.log('活动数据加载完成，内容长度:', (info.contents || '').length);

          // 如果编辑器已经准备好，设置内容
          if (editorReady.value && editorHtml.value) {
            nextTick(() => {
              setTimeout(() => {
                setEditorContent(form.value.contents);
              }, 200);
            });
          }

          // 活动类型回填（确保在活动类型数据加载完成后执行）
          if (info.type_id && columns.value[0]) {
            const typeInfo = columns.value[0].find(val => val.id === info.type_id);
            if (typeInfo) {
              activeType.value = typeInfo.name;
            }
          }

          // 图片字段明确赋值（确保正确的图片字段对应）
          // 活动封面图片
          if (info.img_url) {
            form.value.img_url = info.img_url;
            fileList.value = [{url: info.img_url, status: 'success'}];
          } else {
            form.value.img_url = '';
            fileList.value = [];
          }

          // 联系人二维码
          if (info.lianxi_qrcode) {
            form.value.lianxi_qrcode = info.lianxi_qrcode;
            fileList1.value = [{url: info.lianxi_qrcode, status: 'success'}];
          } else {
            form.value.lianxi_qrcode = '';
            fileList1.value = [];
          }

          // 群二维码
          if (info.qun_qrcode) {
            form.value.qun_qrcode = info.qun_qrcode;
            fileList3.value = [{url: info.qun_qrcode, status: 'success'}];
          } else {
            form.value.qun_qrcode = '';
            fileList3.value = [];
          }

          // 活动图片集
          if (info.imgs && Array.isArray(info.imgs)) {
            form.value.imgs_url = [...info.imgs];
            form.value.imgs = [...info.imgs];
            fileList2.value = info.imgs.map((val) => ({url: val, status: 'success'}));
          } else {
            form.value.imgs_url = [];
            form.value.imgs = [];
            fileList2.value = [];
          }

          // 地址信息回填
          place.value = info.sheng && info.shi && info.qu ? `${info.sheng}-${info.shi}-${info.qu}` : "";

          // 抽奖设置回填
          is_choujiang.value = info.is_choujiang ? true : false;

          // 其他字段回填
          if (info.is_online !== undefined) form.value.is_online = info.is_online;
          if (info.money !== undefined) form.value.money = info.money;
          if (info.member_money !== undefined) form.value.member_money = info.member_money;
          if (info.num !== undefined) form.value.num = info.num;
          if (info.start_time !== undefined) form.value.start_time = info.start_time;

          // 编辑模式下也进行图片数据同步验证
          syncImageDataAfterLoad();

          console.log('编辑模式数据加载完成，图片数据:', {
            img_url: form.value.img_url,
            fileListLength: fileList.value.length,
            lianxi_qrcode: form.value.lianxi_qrcode,
            qun_qrcode: form.value.qun_qrcode
          });
          if (info.baoming_start_time !== undefined) form.value.baoming_start_time = info.baoming_start_time;
          if (info.baoming_end_time !== undefined) form.value.baoming_end_time = info.baoming_end_time;

        } else {
          const errorMsg = res.msg || res.message || '获取活动信息失败';
          uni.$u.toast(errorMsg);
          setTimeout(() => uni.navigateBack(), 1500);
        }
      } else if (infoResult && infoResult.status === 'rejected') {
        const errorMsg = '网络连接失败，请检查网络后重试';
        uni.$u.toast(errorMsg);
        setTimeout(() => uni.navigateBack(), 1500);
      }
    }

    // 非编辑模式下检查草稿
    if (!huodong_id) {
      checkAndLoadDraft();
    }

    // 页面加载完成后，进行一次全局的图片数据检查
    setTimeout(() => {
      syncImageDataAfterLoad();
      console.log('页面加载完成，最终图片数据状态:', {
        img_url: form.value.img_url,
        fileList_length: fileList.value.length,
        lianxi_qrcode: form.value.lianxi_qrcode,
        fileList1_length: fileList1.value.length,
        qun_qrcode: form.value.qun_qrcode,
        fileList3_length: fileList3.value.length,
        imgs_url_length: Array.isArray(form.value.imgs_url) ? form.value.imgs_url.length : 0,
        fileList2_length: fileList2.value.length
      });
    }, 500);

    // 隐藏加载状态
    uni.hideLoading();
  } catch (error) {
    // 隐藏加载状态
    uni.hideLoading();

    // 简化错误处理，移除详细日志
    uni.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          uni.$u.toast('网络连接异常，请检查网络设置');
        } else {
          uni.$u.toast('页面加载失败，请重试');
        }
      },
      fail: () => {
        uni.$u.toast('页面加载失败，请重试');
      }
    });
  }
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;

  // 页面准备完成后，再次检查编辑器内容设置
  setTimeout(() => {
    if (edit.value && form.value.contents && editorReady.value && editorHtml.value) {
      console.log('页面准备完成，检查编辑器内容设置');
      setEditorContent(form.value.contents);
    }
  }, 500);
});
// 页面卸载时清理资源
onUnload(() => {
  // 保存编辑状态到本地存储，避免数据丢失
  if (edit.value && form.value.huodong_id) {
    try {
      const editState = {
        huodong_id: form.value.huodong_id,
        isEdit: true,
        timestamp: Date.now()
      };
      uni.setStorageSync('activity_edit_state', editState);
    } catch (error) {
      // 保存失败不影响用户操作
    }
  } else {
    // 非编辑模式下，检查是否需要清除草稿
    // 如果用户是通过发布成功后跳转离开的，草稿应该已经被清除
    // 这里作为备用机制，检查是否有残留草稿需要清理
    try {
      const draftId = 'activity_publish_draft';
      const draftData = store().getActivityDraft(draftId);

      // 如果草稿存在且表单为空（说明可能是发布成功后的残留），则清除
      const formIsEmpty = !form.value.name && !form.value.contents && !form.value.type_id;
      if (draftData && formIsEmpty) {
        console.log('检测到可能的残留草稿，清除中...');
        store().removeActivityDraft(draftId);
      }
    } catch (error) {
      // 草稿检查失败不影响页面卸载
    }
  }

  // 清理编辑状态（但不影响本地存储）
  store().editActive({}, false);

  // 清理所有定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value);
    autoSaveTimer.value = null;
  }

  // 清理防抖定时器
  if (debouncedAutoSave && debouncedAutoSave.cancel) {
    debouncedAutoSave.cancel();
  }

  // 清理loading状态
  uni.hideLoading();
});

// 专门的内容设置函数，带重试机制
const setEditorContent = (content, retryCount = 0) => {
  if (!editorHtml.value || !content) {
    return;
  }

  try {
    editorHtml.value.setContents({
      html: content
    });
  } catch (error) {
    console.error('设置编辑器内容失败:', error);
    if (retryCount < 3) {
      setTimeout(() => {
        setEditorContent(content, retryCount + 1);
      }, 300 * (retryCount + 1));
    }
  }
};

const onEditorReady = () => {
  uni
      .createSelectorQuery()
      .select("#editor")
      .context((res) => {
        editorHtml.value = res.context;
        editorReady.value = true;
        console.log('编辑器准备就绪');

        // 设置编辑器内容，优先使用values.value（编辑模式下的内容）
        const content = values.value || form.value.contents || '';
        if (content) {
          // 延迟设置内容，确保编辑器完全准备好
          nextTick(() => {
            setTimeout(() => {
              setEditorContent(content);
            }, 300);
          });
        }
      })
      .exec();
};
const htmlInput = (e) => {
  form.value.contents = e.detail.html.replaceAll(" ", "&nbsp;");
};

// 获取地图
const getMap = () => {
  try {
    uni.authorize({
      scope: "scope.userLocation",
      success() {
        getAddressId();
      },
      fail: () => {

        uni.showModal({
          title: "是否授权当前位置",
          content: "需要获取您的地理位置，请确认授权，否则地图功能将无法使用",
          success: (tip) => {
            if (tip.confirm) {
              // 如果用户同意授权地理信息，则打开授权设置页面，判断用户的操作
              try {
                uni.openSetting({
                  success: (data) => {
                    // 如果用户授权了地理信息在，则提示授权成功
                    if (data.authSetting["scope.userLocation"] === true) {
                      uni.$u.toast("授权成功");
                      // 授权成功后，然后再次chooseLocation获取信息
                      getAddressId();
                    } else {
                      uni.$u.toast("授权失败");
                    }
                  },
                  fail: () => {

                    uni.$u.toast("无法打开设置页面，请手动在设置中开启位置权限");
                  }
                });
              } catch (error) {

                uni.$u.toast("位置功能暂时不可用，请稍后重试");
              }
            }
          }
        });
      }
    });
  } catch (error) {

    uni.$u.toast("位置功能初始化失败，请重试");
  }
};
// 获取地址及地址id
const getAddressId = async () => {
  try {
    uni.chooseLocation({
      success: async (res) => {
        try {
          if (res.address) {
            form.value.lat = res.latitude;
            form.value.lng = res.longitude;

            // 显示加载提示
            uni.showLoading({
              title: '正在获取地址信息...'
            });

            const addrRes = await getAddr({
              location: `${res.longitude},${res.latitude}`
            });

            if (addrRes.status == 1) {
              const addressComponent = addrRes.regeocode.addressComponent;
              const arr = [
                addressComponent.province.indexOf("市") !== -1
                    ? addressComponent.province.substring(
                        0,
                        addressComponent.province.indexOf("市")
                    )
                    : addressComponent.province,
                uni.$u.test.isEmpty(addressComponent.city)
                    ? addressComponent.province
                    : addressComponent.city,
                addressComponent.district
              ];
              form.value.addr = addrRes.regeocode.formatted_address.slice(
                  addrRes.regeocode.formatted_address.indexOf(
                      addressComponent.township
                  )
              );
              place.value = `${addressComponent.province}-${addressComponent.city}-${addressComponent.district}`;

              // 通过位置信息换取id
              try {
                for (let i = 0; i < arr.length; i++) {
                  const val = arr[i];
                  const addResId = await getAddrId(val, i + 1);
                  if (addResId.status == "ok" && addResId.data) {
                    switch (i) {
                      case 0:
                        form.value.sheng_id = addResId.data.id;
                        break;
                      case 1:
                        form.value.shi_id = addResId.data.id;
                        break;
                      case 2:
                        form.value.qu_id = addResId.data.id;
                        break;
                    }
                  } else {

                  }
                }
                uni.hideLoading();
                uni.$u.toast('地址选择成功');
              } catch (error) {

                uni.hideLoading();
                uni.$u.toast('地区信息获取失败，但位置已保存');
              }
            } else {
              uni.hideLoading();
              uni.$u.toast('获取地址详情失败，请重新选择');
            }
          } else {
            uni.$u.toast(
                "请重新选择位置并勾选详细地址列表,3秒后将重新进入地图选择页面"
            );
            setTimeout(() => {
              getAddressId();
            }, 3000);
          }
        } catch (error) {
          uni.hideLoading();

          uni.$u.toast('地址处理失败，请重试');
        }
      },
      fail: () => {

        uni.$u.toast('选择位置失败，请检查位置权限');
      }
    });
  } catch (error) {

    uni.$u.toast('地图功能暂时不可用，请稍后重试');
  }
};
// 获取位置id
const getAddrId = async (name, type) => {
  try {
    const result = await configget_shengshiqu_id({name, type});
    if (result.status === "ok" && result.data) {
      return result;
    } else {

      return {status: "error", msg: "获取地区信息失败"};
    }
  } catch (error) {

    return {status: "error", msg: "网络错误，请重试"};
  }
};

// 草稿管理相关函数
// 检查并加载草稿
const checkAndLoadDraft = () => {
  try {
    // 从全局状态管理获取草稿
    const draftId = 'activity_publish_draft';
    const draftData = store().getActivityDraft(draftId);

    if (draftData && draftData.formData) {
      // 检查草稿数据是否有有效内容
      const formData = draftData.formData;
      const hasDraftData = !!(formData.form?.name || formData.form?.type_id || formData.form?.img_url ||
                             formData.form?.contents || formData.form?.start_time || formData.form?.num);

      if (hasDraftData) {
        // 检查草稿是否过期（7天）
        const isExpired = (Date.now() - draftData.lastSaved) > (7 * 24 * 60 * 60 * 1000);

        if (!isExpired) {
          // 额外检查：如果草稿保存时间很近（5分钟内），可能是发布成功后的残留
          const isRecentDraft = (Date.now() - draftData.lastSaved) < (5 * 60 * 1000);

          if (isRecentDraft) {
            // 检查是否有发布成功的标记
            try {
              const publishSuccess = uni.getStorageSync('last_publish_success');
              if (publishSuccess && (Date.now() - publishSuccess) < (10 * 60 * 1000)) {
                // 10分钟内有发布成功记录，清除可能的残留草稿
                console.log('检测到近期发布成功，清除可能的残留草稿');
                store().removeActivityDraft(draftId);
                uni.removeStorageSync('last_publish_success');
                isDraftLoaded.value = true;
                return;
              }
            } catch (error) {
              // 检查发布标记失败，继续正常流程
            }
          }

          showDraftDialog.value = true;
        } else {
          // 清除过期草稿
          store().removeActivityDraft(draftId);
        }
      } else {
        // 草稿数据无效，清除
        store().removeActivityDraft(draftId);
      }
    }

    // 标记草稿检查完成
    isDraftLoaded.value = true;
  } catch (e) {
    console.error('草稿检查失败:', e);
    isDraftLoaded.value = true;
  }
};



// 自动保存草稿
const autoSaveDraft = () => {
  try {
    // 检查是否有有效数据需要保存
    const hasValidData = !!(form.value.name || form.value.type_id || form.value.img_url ||
                           form.value.contents || form.value.start_time || form.value.num ||
                           form.value.addr || form.value.lianxi_name || form.value.money);

    if (!hasValidData) {
      return; // 没有有效数据，不保存
    }

    const draftId = 'activity_publish_draft';
    const draftData = {
      form: { ...form.value },
      activeType: activeType.value,
      place: place.value,
      values: values.value,
      fileList: fileList.value,
      fileList1: fileList1.value,
      fileList2: fileList2.value,
      fileList3: fileList3.value,
      currentStep: currentStep.value
    };

    // 保存到全局状态管理
    store().saveActivityDraft(draftId, draftData);

    lastSaveTime.value = Date.now();
  } catch (error) {
    // 自动保存失败不影响用户操作
  }
};



// 清除草稿（增强版本，确保彻底清除）
const clearDraft = async () => {
  try {
    const draftId = 'activity_publish_draft';

    // 清除store中的草稿
    store().removeActivityDraft(draftId);

    // 验证草稿是否真正被清除
    await new Promise((resolve) => {
      setTimeout(() => {
        const remainingDraft = store().getActivityDraft(draftId);
        if (remainingDraft) {
          // 如果还有残留，再次尝试清除
          console.warn('草稿清除不完整，重试清除');
          store().removeActivityDraft(draftId);
        }
        resolve();
      }, 100); // 等待100ms确保存储操作完成
    });

    console.log('草稿清除完成');
  } catch (error) {
    console.error('清除草稿失败:', error);
    // 即使清除失败也不影响用户操作，但记录错误
  }
};















// 删除图片
const deletePic = (e) => {
  const uploadType = typeof e.name === 'string' ? parseInt(e.name) : e.name;

  console.log('删除图片:', { uploadType, index: e.index, name: e.name });

  switch (uploadType) {
    case 1:
      // 删除活动封面图
      form.value.img_url = "";
      fileList.value = [];
      break;
    case 2:
      // 删除联系人二维码
      form.value.lianxi_qrcode = "";
      fileList1.value = [];
      break;
    case 3:
      // 删除活动图片（多张）- 只删除指定索引的图片
      if (Array.isArray(form.value.imgs_url) && e.index >= 0 && e.index < form.value.imgs_url.length) {
        form.value.imgs_url.splice(e.index, 1);
        console.log('删除后的imgs_url:', form.value.imgs_url);
      }
      if (Array.isArray(fileList2.value) && e.index >= 0 && e.index < fileList2.value.length) {
        fileList2.value.splice(e.index, 1);
        console.log('删除后的fileList2:', fileList2.value.length);
      }
      break;
    case 4:
      // 删除活动群二维码
      form.value.qun_qrcode = "";
      fileList3.value = [];
      break;
    default:
      console.warn('未知的上传类型:', uploadType);
      return;
  }
};
// 新增图片
const afterReadImg = async (e) => {
  let list;
  const uploadType = typeof e.name === 'string' ? parseInt(e.name) : e.name;

  switch (uploadType) {
    case 1:
      list = fileList.value;
      break;
    case 2:
      list = fileList1.value;
      break;
    case 3:
      list = fileList2.value;
      break;
    case 4:
      list = fileList3.value;
      break;
    default:
      return;
  }

  let lists = [].concat(e.file);
  let fileListLen = list.length;

  lists.map((item) => {
    list.push({
      ...item,
      status: "uploading",
      message: "上传中"
    });
  });

  for (let i = 0; i < lists.length; i++) {
    const res = await upload_img(lists[i].url);
    let item = list[fileListLen];

    if (res.status === "ok") {
      list.splice(fileListLen, 1, {
        ...item,
        status: "success",
        message: "",
        url: res.data
      });

      switch (uploadType) {
        case 1:
          form.value.img_url = res.data;
          fileList.value = [...list];
          console.log('活动封面图片上传成功:', {
            img_url: form.value.img_url,
            fileList_length: fileList.value.length
          });
          break;
        case 2:
          form.value.lianxi_qrcode = res.data;
          fileList1.value = [...list];
          console.log('联系人二维码上传成功:', {
            lianxi_qrcode: form.value.lianxi_qrcode,
            fileList1_length: fileList1.value.length
          });
          break;
        case 3:
          if (!Array.isArray(form.value.imgs_url)) {
            form.value.imgs_url = [];
          }
          form.value.imgs_url.push(res.data);
          fileList2.value = [...list];
          console.log('活动图片上传成功:', {
            imgs_url_length: form.value.imgs_url.length,
            fileList2_length: fileList2.value.length
          });
          break;
        case 4:
          form.value.qun_qrcode = res.data;
          fileList3.value = [...list];
          console.log('群二维码上传成功:', {
            qun_qrcode: form.value.qun_qrcode,
            fileList3_length: fileList3.value.length
          });
          break;
      }
    } else {
      list.splice(fileListLen, 1);
      uni.$u.toast('上传失败');
    }

    fileListLen++;
  }
};

// 选择分类
const chooseType = (e) => {
  if (e && e.value && Array.isArray(e.value) && e.value[0] && e.value[0].id && e.value[0].name) {
    form.value.type_id = e.value[0].id;
    activeType.value = e.value[0].name;
    pickerShow.value = false;
  } else {
    uni.$u.toast('选择失败，请重试');
  }
};
// 时间选择器确定
/*const confirm = (e) => {
  switch (+type.value) {
    case 1:
      form.value.start_time = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
      break;
   /!* case 2:
      if (form.value.end_time && form.value.end_time < e.value)
        return uni.$u.toast("活动开始时间不能大于活动结束时间");
      form.value.liucheng[liuchengIndex.value].time = uni.$u.timeFormat(
          e.value,
          "yyyy-mm-dd hh:MM"
      );
      break;
    case 3:
      if (
          form.value.baoming_end_time &&
          new Date(form.value.baoming_end_time) * 1 <= e.value
      )
        return uni.$u.toast("报名结束时间必须大于报名开始时间");
      form.value.baoming_start_time = uni.$u.timeFormat(
          e.value,
          "yyyy-mm-dd hh:MM:00"
      );
      break;
    case 4:
      if (!form.value.baoming_start_time)
        return uni.$u.toast("请先选择报名开始时间");
      if (new Date(form.value.baoming_start_time) * 1 >= e.value)
        return uni.$u.toast("报名结束时间必须大于报名开始时间");
      form.value.baoming_end_time = uni.$u.timeFormat(
          e.value,
          "yyyy-mm-dd hh:MM:00"
      );
      break;
    case 5:
      if (form.value.end_time && form.value.end_time <= e.value)
        return uni.$u.toast("活动结束时间必须大于活动开始时间");
      form.value.start_time = uni.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:00");
      break;
    case 6:
      if (!form.value.start_time) return uni.$u.toast("请先选择活动开始时间");
      if (new Date(form.value.start_time) * 1 >= e.value)
        return uni.$u.toast("活动结束时间必须大于活动开始时间");
      form.value.end_time = uni.$u.timeFormat(e.value, "yyyy-mm-dd hh:MM:00");
      break;*!/
  }
  showDatePicker.value = false;
};*/
// 提交
const submit = async () => {
  // 防重复提交检查
  const now = Date.now();
  if (isSubmitting.value) {
    uni.$u.toast("正在提交中，请稍候...");
    return;
  }

  if (now - lastSubmitTime.value < SUBMIT_COOLDOWN) {
    const remainingTime = Math.ceil((SUBMIT_COOLDOWN - (now - lastSubmitTime.value)) / 1000);
    uni.$u.toast(`请等待${remainingTime}秒后再次提交`);
    return;
  }

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再发布活动')) {
    return;
  }

  // 设置提交状态
  isSubmitting.value = true;
  lastSubmitTime.value = now;

  form.value.title = form.value.name;

  // 确保费用字段有默认值
  if (!form.value.money || form.value.money === '') {
    form.value.money = '0';
  }
  if (!form.value.member_money || form.value.member_money === '') {
    form.value.member_money = '0';
  }

  // Force fees to 0 if user role is not 0
  if (userRoleType.value !== 0) {
    form.value.money = '0';
    form.value.member_money = '0';
  }

  try {
    // 先验证第三页字段
    await validateStep3();

    // 然后进行完整表单验证
    formRef.value
        .validate()
        .then(async () => {
          // 显示提交中的加载状态
          uni.showLoading({
            title: edit.value ? '正在更新活动...' : '正在提交活动...'
          });

          try {
            const api = edit.value ? huodongupdate_huodong : huodongadd_huodong;

            // 确保所有数组参数都转换为字符串，避免Content-Type判断错误
            const submitData = {
              ...form.value,
              imgs_url: Array.isArray(form.value.imgs_url) ? form.value.imgs_url.join(",") : form.value.imgs_url,
              imgs: Array.isArray(form.value.imgs) ? form.value.imgs.join(",") : form.value.imgs,
              liucheng: Array.isArray(form.value.liucheng) ? JSON.stringify(form.value.liucheng) : form.value.liucheng,
              yongjin_bili: 0 // 添加默认佣金比例字段，修复后端API验证错误
            };


            const activeRes = await api(submitData);

            uni.hideLoading();

            if (activeRes.status === "ok") {
              // 标记发布成功，用于草稿清理判断
              try {
                uni.setStorageSync('last_publish_success', Date.now());
              } catch (error) {
                // 标记保存失败不影响主流程
              }

              // 清除草稿（等待清除完成）
              await clearDraft();

              // 清除编辑状态（如果是编辑模式）
              if (edit.value) {
                try {
                  uni.removeStorageSync('activity_edit_state');
                } catch (error) {
                  // 清除编辑状态失败不影响用户操作
                }
                back({tip: `${activeRes.msg},即将返回上级页面`, time: 2000});
              } else {
                uni.$u.toast("活动正在审核中，敬请期待!");
                // 确保草稿清除完成后再跳转
                setTimeout(() => uni.reLaunch({url: "/pages/index"}), 2000);
              }
            } else if (activeRes.status === "relogin") {
              uni.$u.toast("登录已过期，请重新登录");
              setTimeout(() => navto("/pages/bundle/common/login"), 1500);
            } else {
              uni.$u.toast(activeRes.msg || "提交失败，请重试");
            }

            // 重置提交状态
            isSubmitting.value = false;
          } catch (submitError) {
            uni.hideLoading();

            uni.$u.toast('网络错误，请检查网络连接后重试');
            // 重置提交状态
            isSubmitting.value = false;
          }
        })
        .catch((errors) => {
          if (Array.isArray(errors) && errors.length > 0 && errors[0].message) {
            uni.$u.toast(errors[0].message);
          } else {
            uni.$u.toast("表单验证失败，请检查必填项");
          }
          // 重置提交状态
          isSubmitting.value = false;
        });
  } catch (errors) {
    // 第三页验证失败，不进行提交

    // 重置提交状态
    isSubmitting.value = false;
  }
};
const showDateTimePicker = () => {
  showDatePicker.value = true;
  mode.value = 'datetime';
  date.value = Date.now();
};



// 用户友好的错误提示系统
const errorMessages = {
  'required_field': '请填写必填信息',
  'invalid_format': '格式不正确，请检查输入',
  'network_error': '网络连接异常，请稍后重试',
  'permission_denied': '权限不足，请联系管理员',
  'file_too_large': '文件过大，请选择较小的文件',
  'upload_failed': '上传失败，请重新尝试'
};

// 显示用户友好的验证错误提示
const showValidationError = (messageKey = '', customMessage = '', type = 'error') => {
  // 优化消息处理逻辑：优先使用自定义消息，然后是预定义消息，最后是messageKey本身
  let friendlyMessage = '';

  if (customMessage && customMessage.trim() !== '') {
    friendlyMessage = customMessage;
  } else if (messageKey && errorMessages[messageKey]) {
    friendlyMessage = errorMessages[messageKey];
  } else if (messageKey && messageKey.trim() !== '') {
    friendlyMessage = messageKey;
  } else {
    friendlyMessage = '请检查输入信息';
  }

  console.log('显示验证错误:', { messageKey, customMessage, friendlyMessage, type });

  // 使用nextTick确保组件已经渲染
  nextTick(() => {
    if (notifyRef.value && typeof notifyRef.value.show === 'function') {
      notifyRef.value.show({
        type: type,
        message: friendlyMessage,
        duration: 3000,
        safeAreaInsetTop: true
      });
    } else {
      // 降级到普通toast
      uni.showToast({
        title: friendlyMessage,
        icon: type === 'error' ? 'none' : 'success',
        duration: 3000
      });
    }
  });
};

// 获取第一页需要验证的字段（基础选择）
const getStep1Fields = () => {
  return ['name', 'type_id', 'img_url']; // 验证活动标题、活动类型选择和活动封面图片
};

// 获取第二页需要验证的字段（详细信息）
const getStep2Fields = () => {
  const baseFields = ['start_time', 'num', 'contents'];

  // 如果是线下活动，添加位置相关字段
  if (form.value.is_online === 0) {
    baseFields.push('sheng_id', 'lng', 'lat', 'addr');
  }

  // 🆕 修改：如果有活动权限且选择收费，添加费用验证
  if (hasActivityPermission() && (form.value.pay_type === 1 || form.value.pay_type === 2)) {
    baseFields.push('money');
  }

  return baseFields;
};

// 获取第三页需要验证的字段（发布确认）
const getStep3Fields = () => {
  return ['qun_qrcode', 'lianxi_qrcode'];
};

// 验证第一页字段
const validateStep1 = () => {
  return new Promise((resolve, reject) => {
    // 首先进行自定义验证
    const customValidationErrors = [];

    // 验证活动标题
    if (!form.value.name || form.value.name.trim() === '') {
      customValidationErrors.push('请填写活动名称');
    } else if (form.value.name.trim().length < 2 || form.value.name.trim().length > 50) {
      customValidationErrors.push('请输入2-50个字符的活动名称');
    }

    // 验证活动类型选择
    if (!form.value.type_id || form.value.type_id === 0) {
      customValidationErrors.push('请选择一个活动类型');
    }

    // 先尝试从fileList同步数据到form
    if ((!form.value.img_url || form.value.img_url.trim() === '') &&
        fileList.value.length > 0 && fileList.value[0].url) {
      form.value.img_url = fileList.value[0].url;
      console.log('从fileList同步封面图片URL:', form.value.img_url);
    }

    // 验证封面图片
    if (!form.value.img_url ||
        (typeof form.value.img_url === 'string' && form.value.img_url.trim() === '') ||
        (typeof form.value.img_url !== 'string')) {
      customValidationErrors.push('请上传一张吸引人的活动封面图片（支持JPG、PNG格式）');
      console.log('封面图片验证失败:', form.value.img_url);
    } else {
      console.log('封面图片验证通过:', form.value.img_url);
    }

    // 如果有自定义验证错误，直接显示并拒绝
    if (customValidationErrors.length > 0) {
      showValidationError('', customValidationErrors[0], 'error');
      reject(customValidationErrors);
      return;
    }

    // 进行表单字段验证
    const fieldsToValidate = getStep1Fields();
    formRef.value.validateField(fieldsToValidate, (errors) => {
      if (errors && errors.length > 0) {
        // 显示美观的错误提示
        showValidationError('', errors[0].message, 'error');
        reject(errors);
      } else {
        resolve(true);
      }
    });
  });
};

// 验证第二页字段
const validateStep2 = () => {
  return new Promise((resolve, reject) => {
    // 首先进行自定义验证
    const customValidationErrors = [];

    // 验证活动时间
    if (!form.value.start_time || form.value.start_time.trim() === '') {
      customValidationErrors.push('请选择活动开始的具体日期和时间');
    }

    // 验证活动人数
    if (!form.value.num || form.value.num <= 0) {
      customValidationErrors.push('请输入活动参与人数（如：10人、50人）');
    }

    // 验证活动描述
    if (!form.value.contents || form.value.contents.trim() === '') {
      customValidationErrors.push('请详细描述活动内容、流程和注意事项（至少10个字）');
    } else if (form.value.contents.trim().length < 10) {
      customValidationErrors.push('活动内容描述至少需要10个字符');
    }

    // 验证线下活动的地址信息
    if (form.value.is_online === 0) {
      if (!form.value.addr || form.value.addr.trim() === '') {
        customValidationErrors.push('请输入详细的活动地址（如：XX大厦XX楼XX室）');
      }
      if (!form.value.lng || !form.value.lat || form.value.lng <= 0 || form.value.lat <= 0) {
        customValidationErrors.push('请在地图上点击选择活动地点以获取准确坐标');
      }
    }

    // 如果有自定义验证错误，直接显示并拒绝
    if (customValidationErrors.length > 0) {
      showValidationError('', customValidationErrors[0], 'error');
      reject(customValidationErrors);
      return;
    }

    // 进行表单字段验证
    const fieldsToValidate = getStep2Fields();
    formRef.value.validateField(fieldsToValidate, (errors) => {
      if (errors && errors.length > 0) {
        // 显示美观的错误提示
        showValidationError('', errors[0].message, 'error');
        reject(errors);
      } else {
        resolve(true);
      }
    });
  });
};

// 验证第三页字段
const validateStep3 = () => {
  return new Promise((resolve, reject) => {
    // 首先进行自定义验证
    const customValidationErrors = [];

    console.log('验证第三页二维码:', {
      lianxi_qrcode: form.value.lianxi_qrcode,
      qun_qrcode: form.value.qun_qrcode,
      fileList1_length: fileList1.value.length,
      fileList3_length: fileList3.value.length
    });

    // 先尝试从fileList同步数据到form
    if ((!form.value.lianxi_qrcode || form.value.lianxi_qrcode.trim() === '') &&
        fileList1.value.length > 0 && fileList1.value[0].url) {
      form.value.lianxi_qrcode = fileList1.value[0].url;
      console.log('从fileList1同步联系人二维码URL:', form.value.lianxi_qrcode);
    }

    if ((!form.value.qun_qrcode || form.value.qun_qrcode.trim() === '') &&
        fileList3.value.length > 0 && fileList3.value[0].url) {
      form.value.qun_qrcode = fileList3.value[0].url;
      console.log('从fileList3同步群二维码URL:', form.value.qun_qrcode);
    }

    // 验证联系二维码
    if (!form.value.lianxi_qrcode ||
        (typeof form.value.lianxi_qrcode === 'string' && form.value.lianxi_qrcode.trim() === '') ||
        (typeof form.value.lianxi_qrcode !== 'string')) {
      customValidationErrors.push('请上传您的微信二维码图片，方便参与者联系您');
      console.log('联系人二维码验证失败:', form.value.lianxi_qrcode);
    }

    // 验证群二维码
    if (!form.value.qun_qrcode ||
        (typeof form.value.qun_qrcode === 'string' && form.value.qun_qrcode.trim() === '') ||
        (typeof form.value.qun_qrcode !== 'string')) {
      customValidationErrors.push('请上传活动微信群二维码，方便参与者加入讨论');
      console.log('群二维码验证失败:', form.value.qun_qrcode);
    }

    // 如果有自定义验证错误，直接显示并拒绝
    if (customValidationErrors.length > 0) {
      showValidationError('', customValidationErrors[0], 'error');
      reject(customValidationErrors);
      return;
    }

    // 进行表单字段验证
    const fieldsToValidate = getStep3Fields();
    formRef.value.validateField(fieldsToValidate, (errors) => {
      if (errors && errors.length > 0) {
        // 显示美观的错误提示
        showValidationError('', errors[0].message, 'error');
        reject(errors);
      } else {
        resolve(true);
      }
    });
  });
};

// 检查会员状态
const checkMemberStatus = () => {
  const userInfo = store().$state.userInfo;

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再发布活动')) {
    return false;
  }

  // 检查会员状态 - 修复类型比较问题
  const isHuiyuan = userInfo.is_huiyuan;
  if (isHuiyuan !== 1 && isHuiyuan !== '1' && isHuiyuan !== true) {
    showMemberModal.value = true;
    return false;
  }

  return true;
};

// 跳转到会员页面
const navigateToVip = () => {
  showMemberModal.value = false;
  navto('/pages/bundle/user/vip');
};

// 取消会员操作
const cancelMemberAction = () => {
  showMemberModal.value = false;
};

// 步骤切换方法
const nextStep = async () => {
  if (currentStep.value === 1) {
    try {
      await validateStep1();
      currentStep.value = 2;
    } catch (errors) {
      // 验证失败，不切换页面

    }
  } else if (currentStep.value === 2) {
    try {
      await validateStep2();

      // 检查会员状态
      if (!checkMemberStatus()) {
        return; // 非会员，显示弹窗，不进行下一步
      }

      currentStep.value = 3;
    } catch (errors) {
      // 验证失败，不切换页面

    }
  }
};

const prevStep = () => {
  if (currentStep.value === 2) {
    currentStep.value = 1;
  } else if (currentStep.value === 3) {
    currentStep.value = 2;
  }
};

// 草稿相关方法 - 统一使用store管理
const loadDraft = () => {
  try {
    // 使用store获取草稿数据
    const draftId = 'activity_publish_draft';
    const draftData = store().getActivityDraft(draftId);

    if (draftData && draftData.formData) {
      const formData = draftData.formData;

      // 恢复表单数据
      if (formData.form) {
        form.value = { ...form.value, ...formData.form };
      }

      // 恢复文件列表
      fileList.value = formData.fileList || [];
      fileList1.value = formData.fileList1 || [];
      fileList2.value = formData.fileList2 || [];
      fileList3.value = formData.fileList3 || [];

      // 数据同步验证和修复
      syncImageDataAfterLoad();

      // 恢复其他状态
      activeType.value = formData.activeType || '';
      place.value = formData.place || '';
      values.value = formData.values || '';
      currentStep.value = formData.currentStep || 1;

      debugLog('草稿恢复完成，图片数据:', {
        img_url: form.value.img_url,
        fileListLength: fileList.value.length,
        lianxi_qrcode: form.value.lianxi_qrcode,
        qun_qrcode: form.value.qun_qrcode
      });

      return true;
    }
  } catch (e) {
    console.error('草稿加载失败:', e);
  }
  return false;
};

// 图片数据同步验证和修复函数
const syncImageDataAfterLoad = () => {
  try {
    // 修复活动封面图片数据同步
    if (fileList.value.length > 0 && fileList.value[0].url) {
      const imageUrl = fileList.value[0].url;
      if (!form.value.img_url || form.value.img_url.trim() === '') {
        form.value.img_url = imageUrl;
        debugLog('修复活动封面图片URL:', imageUrl);
      }
    } else if (form.value.img_url && form.value.img_url.trim() !== '') {
      // 如果form有图片URL但fileList为空，重建fileList
      fileList.value = [{url: form.value.img_url, status: 'success'}];
      debugLog('重建活动封面图片fileList:', form.value.img_url);
    }

    // 修复联系人二维码数据同步
    if (fileList1.value.length > 0 && fileList1.value[0].url) {
      const qrUrl = fileList1.value[0].url;
      if (!form.value.lianxi_qrcode || form.value.lianxi_qrcode.trim() === '') {
        form.value.lianxi_qrcode = qrUrl;
        debugLog('修复联系人二维码URL:', qrUrl);
      }
    } else if (form.value.lianxi_qrcode && form.value.lianxi_qrcode.trim() !== '') {
      fileList1.value = [{url: form.value.lianxi_qrcode, status: 'success'}];
      debugLog('重建联系人二维码fileList:', form.value.lianxi_qrcode);
    }

    // 修复群二维码数据同步
    if (fileList3.value.length > 0 && fileList3.value[0].url) {
      const groupQrUrl = fileList3.value[0].url;
      if (!form.value.qun_qrcode || form.value.qun_qrcode.trim() === '') {
        form.value.qun_qrcode = groupQrUrl;
        debugLog('修复群二维码URL:', groupQrUrl);
      }
    } else if (form.value.qun_qrcode && form.value.qun_qrcode.trim() !== '') {
      fileList3.value = [{url: form.value.qun_qrcode, status: 'success'}];
      debugLog('重建群二维码fileList:', form.value.qun_qrcode);
    }

    // 修复活动图片集数据同步
    if (fileList2.value.length > 0) {
      const imageUrls = fileList2.value.map(item => item.url).filter(url => url);
      if (!Array.isArray(form.value.imgs_url) || form.value.imgs_url.length === 0) {
        form.value.imgs_url = imageUrls;
        debugLog('修复活动图片集URLs:', imageUrls);
      }
    } else if (Array.isArray(form.value.imgs_url) && form.value.imgs_url.length > 0) {
      fileList2.value = form.value.imgs_url.map(url => ({url, status: 'success'}));
      debugLog('重建活动图片集fileList:', form.value.imgs_url);
    }
  } catch (error) {
    debugLog('图片数据同步修复失败:', error);
  }
};

const continueFill = () => {
  showDraftDialog.value = false;
  const success = loadDraft();
  if (success) {
    uni.$u.toast('草稿已恢复');
  } else {
    uni.$u.toast('草稿恢复失败');
  }
};

const restartFill = async () => {
  showDraftDialog.value = false;
  // 彻底清除草稿
  await clearDraft();
  // 重置表单到初始状态
  resetForm();
  uni.$u.toast('已清除草稿，开始新的填写');
};

const resetForm = () => {
  form.value = {
    name: "",
    title: "",
    type_id: null,
    sheng_id: "",
    shi_id: "",
    qu_id: "",
    addr: "",
    lng: "",
    lat: "",
    num: "",
    money: "",
    member_money: "",
    img_url: "",
    contents: "",
    start_time: "",
    end_time: "",
    pay_type: 2,
    is_online: 0,
    lianxi_name: store().$state.userInfo?.nickname || "",
    lianxi_mobile: "",
    lianxi_qrcode: "",
    qun_qrcode: "",
    imgs_url: [],
    imgs: [],
    liucheng: []
  };
  fileList.value = [];
  fileList1.value = [];
  fileList2.value = [];
  fileList3.value = [];
  activeType.value = "";
  place.value = "";
  currentStep.value = 1;
};

// Computed property for user role type
const userRoleType = computed(() => {
  const roleType = store().$state.userInfo?.role_type;
  // Ensure comparison is robust, handle potential string '0' if needed, but prefer number
  const finalRoleType = roleType === undefined || roleType === null ? null : Number(roleType);
  return finalRoleType;
});

// Computed property for member discount text
const memberDiscountText = computed(() => {
  const money = parseFloat(form.value.money);
  const memberMoney = parseFloat(form.value.member_money);
  if (!isNaN(money) && money > 0 && !isNaN(memberMoney) && memberMoney >= 0 && memberMoney < money) {
    const discount = (money - memberMoney).toFixed(2);
    return `会员价立减 ${discount} 元`;
  }
  return ''; // Return empty string if not applicable
});

const hasActivityPermission = () => {
  return hasActivityPublishPermission();
};

// 🆕 新增：选择支付方式
const selectPaymentType = (type) => {
  form.value.pay_type = type;

  // 如果选择免费活动，清空费用
  if (type === 0) {
    form.value.money = "";
    form.value.member_money = "";
  }
};

// Watch for changes in is_online to reset location fields and update validation rules
watch(() => form.value.is_online, (isOnline) => {
  const locationFields = ['sheng_id', 'shi_id', 'qu_id', 'addr', 'lng', 'lat'];
  const locationRules = ['sheng_id', 'addr', 'lng', 'lat']; // Rules to make non-required for online

  if (isOnline === 1) { // 切换到线上
    locationFields.forEach(field => {
      if (['sheng_id', 'shi_id', 'qu_id', 'lng', 'lat'].includes(field)) {
         form.value[field] = 0; // Reset numeric fields to 0 for online
      } else {
         form.value[field] = ""; // Reset string fields to empty string
      }
    });
    place.value = ""; // Reset display text for location picker

    // Update validation rules to not required
    locationRules.forEach(ruleKey => {
      if (rules.value[ruleKey]) {
        // Assuming the required rule is the first one or the only one
        if (Array.isArray(rules.value[ruleKey])) {
           rules.value[ruleKey][0].required = false;
        } else {
           rules.value[ruleKey].required = false;
        }
      }
    });

    // Clear validation for hidden fields
    if (formRef.value) { // Check if formRef is available
        formRef.value.clearValidate(locationRules);
    }
  } else { // 切换到线下
     // Update validation rules back to required
     locationRules.forEach(ruleKey => {
       if (rules.value[ruleKey]) {
         if (Array.isArray(rules.value[ruleKey])) {
           rules.value[ruleKey][0].required = true;
         } else {
           rules.value[ruleKey].required = true;
         }
       }
     });
     // Optional: You might want to trigger validation if fields are required
     // when switching back to offline, but clearValidate is often sufficient.
  }
});

// 移除背景图片逻辑，使用统一的白色背景


// 跳转到首页
const goToHome = () => {
  uni.reLaunch({
    url: '/pages/index'
  });
};

// 重置表单到初始状态
const resetFormToInitial = () => {
  form.value = {
    name: "",
    title: "",
    type_id: null,
    sheng_id: "",
    shi_id: "",
    qu_id: "",
    addr: "",
    lng: "",
    lat: "",
    num: "",
    money: "",
    member_money: "",
    img_url: "",
    contents: "",
    start_time: "",
    end_time: "",
    pay_type: 2,
    is_online: 0,
    lianxi_name: "",
    lianxi_mobile: "",
    lianxi_qrcode: "",
    qun_qrcode: "",
    imgs_url: [],
    imgs: [],
    liucheng: []
  };

  // 重置其他相关状态
  activeType.value = "";
  place.value = "";
  values.value = "";
  fileList.value = [];
  fileList1.value = [];
  fileList2.value = [];
  fileList3.value = [];
  currentStep.value = 1;
  edit.value = false;

  console.log('表单已重置到初始状态');
};

// 监听编辑器准备状态和内容，确保内容正确回填
watch([editorReady, () => form.value.contents], ([ready, content]) => {
  if (ready && content && editorHtml.value) {
    console.log('监听到编辑器准备就绪且有内容，开始设置');
    nextTick(() => {
      setTimeout(() => {
        setEditorContent(content);
      }, 100);
    });
  }
}, { immediate: true });

// 监听表单关键字段变化，自动保存草稿（优化性能：避免深度监听整个form对象）
const watchedFields = ['name', 'type_id', 'contents', 'start_time', 'num', 'addr', 'money'];
watchedFields.forEach(field => {
  watch(() => form.value[field], () => {
    if (!edit.value && isDraftLoaded.value) { // 仅在非编辑模式且草稿已加载后保存草稿
      debouncedAutoSave(); // 使用防抖的自动保存
    }
  }, { immediate: false });
});
</script>
<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goToHome">
          <u-icon name="home-fill" size="60rpx" color="#ffffff"></u-icon>
        </view>
        <view class="navbar-center">
          <text class="navbar-title">{{ edit ? '编辑活动' : '发布活动' }}</text>
        </view>
        <view class="navbar-right">
          <!-- 占位，保持布局平衡 -->
        </view>
      </view>
    </view>

    <u-form
        labelPosition="left"
        :model="form"
        label-width="260rpx"
        :rules="rules"
        :label-style="{ padding: '15rpx 0', fontSize: '28rpx', color: '#333333', fontWeight: 'normal' }"
        ref="formRef"
        errorType="toast"
        class="form-container"
    >
      <!-- 第一页内容：基础选择 -->
      <view v-show="currentStep === 1">
        <!-- 上传活动封面图 -->
        <view class="px30 cover-upload-wrapper">
          <u-form-item
              prop="img_url"
              borderBottom
              class="form-item-upload cover-upload-container"
          >
            <view class="upload-wrapper">
              <view class="upload-container">
                <u-upload
                      :fileList="fileList"
                      @afterRead="afterReadImg"
                      @delete="deletePic"
                      name="1"
                      :maxCount="1"
                      :width="320"
                      :height="180"
                      uploadIcon="photo"
                      uploadText=""
                      :uploadIconColor="'#a8b8a8'"
                      :previewSize="320"
                      :previewFullImage="true"
                      :deletable="true"
                      class="cover-upload-component"
                  >
                </u-upload>
              </view>
              <view class="upload-hint-text">
                请为你的活动选一个好看的封面吧！
              </view>
            </view>
          </u-form-item>
        </view>

        <!-- 活动标题 -->
        <view class="px30">
          <u-form-item label="活动标题" prop="name" labelPosition="top" class="required-field">
            <view class="input-with-icon">
              <u-input
                  :customStyle="{ 'padding': '0', 'background': 'transparent', 'border': 'none', fontSize: '28rpx', color: '#333333' }"
                  v-model="form.name"
                  placeholder="为活动取一个响亮的标题吧！"
                  border="none"
                  placeholder-style="font-size:28rpx;color:#999999"
              ></u-input>
            </view>
          </u-form-item>
        </view>

        <!-- 活动类型选择 -->
        <view class="px30">
          <u-form-item label="活动类型" prop="type_id" labelPosition="top" class="required-field">
            <view class="input-with-icon" @click="pickerShow = true">
              <u-text
                  align="left"
                  size="28rpx"
                  :color="activeType ? '#333333' : '#999999'"
                  :text="activeType || '请选择活动类型'"
              ></u-text>
              <u-icon
                  name="arrow-down"
                  size="24rpx"
                  color="#999999"
                  class="dropdown-icon"
                  @click.stop="pickerShow = true"
              ></u-icon>
            </view>
          </u-form-item>
        </view>

        <!-- 活动性质选择 -->
        <view class="px30">
          <u-form-item label="活动性质" labelPosition="top" class="required-field">
            <view class="activity-nature-buttons">
              <view
                class="nature-button"
                :class="{ 'nature-button-active': form.is_online === 0 }"
                @click="form.is_online = 0"
              >
                线下
              </view>
              <view
                class="nature-button"
                :class="{ 'nature-button-active': form.is_online === 1 }"
                @click="form.is_online = 1"
              >
                线上
              </view>
            </view>
          </u-form-item>
        </view>
      </view>

      <!-- 第二页内容：详细信息 -->
      <view v-show="currentStep === 2">
        <view class="px30">
        <template v-if="form.is_online === 0">
          <u-form-item
              label="活动地点"
              class="required-field"
              prop="sheng_id"
              labelPosition="top"
          >
            <view class="input-with-icon" @click="getMap">
              <u-text
                  align="left"
                  size="28rpx"
                  :color="place ? '#333333' : '#999999'"
                  :text="place || '应该在哪片云朵下等你呢？'"
              ></u-text>
              <u-icon
                  name="arrow-down"
                  size="24rpx"
                  color="#999999"
                  class="dropdown-icon"
                  @click.stop="getMap"
              ></u-icon>
            </view>
          </u-form-item>
          <u-form-item label="活动详细地址" labelPosition="top" class="required-field">
            <view class="input-with-icon">
              <u-input
                  :customStyle="{ 'padding': '0', 'background': 'transparent', 'border': 'none', fontSize: '28rpx', color: '#333333' }"
                  v-model="form.addr"
                  placeholder="请输入活动详细地址"
                  border="none"
                  placeholder-style="font-size:28rpx;color:#999999"
              ></u-input>
            </view>
          </u-form-item>
        </template>
        <u-form-item
            label="活动开始时间"
            prop="start_time"
            labelPosition="top"
            class="required-field"
        >
          <view class="input-with-icon" @click="showDateTimePicker">
            <u-text
                align="left"
                size="28rpx"
                :color="form.start_time ? '#333333' : '#999999'"
                :text="form.start_time || '请选择活动开始时间'"
            ></u-text>
            <u-icon
                name="arrow-down"
                size="24rpx"
                color="#999999"
                class="dropdown-icon"
                @click.stop="showDateTimePicker"
            ></u-icon>
          </view>
        </u-form-item>
        <u-form-item label="参与人数" prop="num" labelPosition="top">
          <view class="input-with-icon">
            <u-input
                :customStyle="{ 'padding': '0', 'background': 'transparent', 'border': 'none', fontSize: '28rpx', color: '#333333' }"
                v-model="form.num"
                type="number"
                placeholder="请输入参与人数"
                border="none"
                suffix-icon="人"
                suffix-icon-style="fontSize:28rpx;color:#666666"
                placeholder-style="font-size:28rpx;color:#999999"
            ></u-input>
          </view>
        </u-form-item>

        <!-- 🆕 新增：支付方式和费用设置 -->
        <template v-if="hasActivityPermission()">
          <!-- 支付方式选择 -->
          <u-form-item label="支付方式" labelPosition="top" class="required-field">
            <view class="payment-type-selector">
              <view
                class="payment-option"
                :class="{ active: form.pay_type === 1 }"
                @click="selectPaymentType(1)"
              >
                <view class="option-content">
                  <u-icon name="credit-card" size="32rpx" :color="form.pay_type === 1 ? '#6AC086' : '#999'"></u-icon>
                  <u-text
                    text="线上收费"
                    size="28rpx"
                    :color="form.pay_type === 1 ? '#6AC086' : '#333'"
                    :bold="form.pay_type === 1"
                  ></u-text>
                </view>
                <u-text
                  text="用户在线支付，自动分配收入"
                  size="24rpx"
                  color="#999"
                ></u-text>
              </view>
              <view
                class="payment-option"
                :class="{ active: form.pay_type === 2 }"
                @click="selectPaymentType(2)"
              >
                <view class="option-content">
                  <u-icon name="money" size="32rpx" :color="form.pay_type === 2 ? '#6AC086' : '#999'"></u-icon>
                  <u-text
                    text="线下收费"
                    size="28rpx"
                    :color="form.pay_type === 2 ? '#6AC086' : '#333'"
                    :bold="form.pay_type === 2"
                  ></u-text>
                </view>
                <u-text
                  text="现场收费，无需在线支付"
                  size="24rpx"
                  color="#999"
                ></u-text>
              </view>
            </view>
          </u-form-item>

          <!-- 费用设置 - 只在选择收费时显示 -->
          <template v-if="form.pay_type === 1 || form.pay_type === 2">
            <u-form-item label="普通用户费用" prop="money" labelPosition="top" class="required-field">
              <view class="input-with-icon">
                <u-input
                    :customStyle="{ 'padding': '0', 'background': 'transparent', 'border': 'none', fontSize: '28rpx', color: '#333333' }"
                    v-model="form.money"
                    type="digit"
                    placeholder="请输入普通用户报名费用"
                    border="none"
                    prefixIcon="rmb"
                    prefixIconStyle="font-size: 28rpx; color: #666666"
                    placeholder-style="font-size:28rpx;color:#999999"
                ></u-input>
              </view>
            </u-form-item>

            <u-form-item label="会员用户费用" prop="member_money" labelPosition="top">
              <view style="display: flex; flex-direction: column; width: 100%;">
                <view class="input-with-icon">
                  <u-input
                      :customStyle="{ 'padding': '0', 'background': 'transparent', 'border': 'none', fontSize: '28rpx', color: '#333333' }"
                      v-model="form.member_money"
                      type="digit"
                      placeholder="选填，会员优惠价格"
                      border="none"
                      prefixIcon="rmb"
                      prefixIconStyle="font-size: 28rpx; color: #666666"
                      placeholder-style="font-size:28rpx;color:#999999"
                  ></u-input>
                </view>
                <u-text v-if="memberDiscountText" :text="memberDiscountText" size="24rpx" color="#666666" margin="5rpx 0 0 0"></u-text>
              </view>
            </u-form-item>
          </template>
        </template>

        <!-- 活动描述 -->
        <view class="activity-description-container">
          <u-form-item label="活动描述" prop="contents" class="description-form-item required-field">
          </u-form-item>
          <view class="editor-wrapper">
            <editor
                id="editor"
                class="ql-container"
                placeholder="请输入内容..."
                show-img-size
                show-img-toolbar
                show-img-resize
                space="nbsp"
                @input="htmlInput"
                @ready="onEditorReady"
            >
            </editor>
          </view>
        </view>
        </view>
      </view>

      <!-- 第三页内容：发布确认 -->
      <view v-show="currentStep === 3">
      <view class="px30">
        <u-form-item
            label-width="320rpx"
            label="上传活动群微信二维码"
            labelPosition="top"
            prop="qun_qrcode"
            class="qrcode-container required-field"
        >
          <view class="qrcode-wrapper">
            <u-upload
                :fileList="fileList3"
                @afterRead="afterReadImg"
                @delete="deletePic"
                name="4"
                :maxCount="1"
                :width="160"
                :height="160"
                :uploadIconColor="'#a8b8a8'"
                :previewSize="160"
            >
            </u-upload>
          </view>
        </u-form-item>
        <u-form-item
            label-width="320rpx"
            label="上传联系人微信二维码"
            labelPosition="top"
            prop="lianxi_qrcode"
            class="qrcode-container required-field"
        >
          <view class="qrcode-wrapper">
            <u-upload
                :fileList="fileList1"
                @afterRead="afterReadImg"
                @delete="deletePic"
                name="2"
                :width="160"
                :height="160"
                :maxCount="1"
                :uploadIconColor="'#a8b8a8'"
                :previewSize="160"
            >
            </u-upload>
          </view>
        </u-form-item>
        <u-form-item
            label="活动图片(不超过9张)"
            labelPosition="top"
            class="activity-images-container required-field"
        >
        </u-form-item>
        <view class="activity-images-wrapper">
          <u-upload
              :fileList="fileList2"
              @afterRead="afterReadImg"
              @delete="deletePic"
              name="3"
              multiple
              :width="100"
              :height="100"
              :maxCount="9"
              :uploadIconColor="'#a8b8a8'"
              :previewSize="100"
          >
          </u-upload>
        </view>
      </view>
      </view>
    </u-form>
    <u-gap :height="gapHeight + 20"></u-gap>

    <!-- 底部按钮区域 -->
    <view class="pfx w690 bottom0 tl50 left50 bottomBox z20">
      <!-- 第一页按钮 -->
      <view v-if="currentStep === 1">
        <u-button
            text="下一页"
            shape="circle"
            color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
            :customStyle="{
            height: '98rpx',
            color: '#fff',
            fontWeight: '600',
            boxShadow: '0 4rpx 16rpx rgba(106, 192, 134, 0.3)'
          }"
            @click="nextStep"
        >
        </u-button>
      </view>

      <!-- 第二页按钮 -->
      <view v-if="currentStep === 2" class="step2-buttons">
        <u-button
            text="上一页"
            shape="circle"
            color="#ffffff"
            :customStyle="{
            height: '98rpx',
            color: '#333333',
            fontWeight: '600',
            border: '2rpx solid #e6e6e6',
            boxShadow: '0 4rpx 16rpx rgba(0, 0, 0, 0.1)',
            marginRight: '20rpx',
            flex: '1'
          }"
            @click="prevStep"
        >
        </u-button>
        <u-button
            text="下一页"
            shape="circle"
            color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
            :customStyle="{
            height: '98rpx',
            color: '#fff',
            fontWeight: '600',
            boxShadow: '0 4rpx 16rpx rgba(106, 192, 134, 0.3)',
            flex: '1'
          }"
            @click="nextStep"
        >
        </u-button>
      </view>

      <!-- 第三页按钮 -->
      <view v-if="currentStep === 3" class="step2-buttons">
        <u-button
            text="上一页"
            shape="circle"
            color="#ffffff"
            :customStyle="{
            height: '98rpx',
            color: '#333333',
            fontWeight: '600',
            border: '2rpx solid #e6e6e6',
            boxShadow: '0 4rpx 16rpx rgba(0, 0, 0, 0.1)',
            marginRight: '20rpx',
            flex: '1'
          }"
            @click="prevStep"
        >
        </u-button>
        <u-button
            :text="isSubmitting ? '提交中...' : '发布'"
            shape="circle"
            color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
            :customStyle="{
            height: '98rpx',
            color: '#fff',
            fontWeight: '600',
            boxShadow: '0 4rpx 16rpx rgba(106, 192, 134, 0.3)',
            flex: '1'
          }"
            :loading="isSubmitting"
            :disabled="isSubmitting"
            @click="submit"
        >
        </u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
    <u-datetime-picker
        :show="showDatePicker"
        v-model="date"
        type="datetime"
        :min-date="minDate"
        :max-date="maxDate"
        confirm-color="#6AC086"
        cancel-color="#999999"
        @confirm="confirm"
        @cancel="showDatePicker = false"
        @close="showDatePicker = false"
        :formatter="formatter"
    ></u-datetime-picker>
    <u-picker
        :show="pickerShow"
        :columns="columns"
        :close-on-click-overlay="true"
        key-name="name"
        confirm-color="#6AC086"
        cancel-color="#999999"
        @cancel="pickerShow = false"
        @close="pickerShow = false"
        @confirm="chooseType"
    ></u-picker>

    <!-- 草稿确认对话框 -->
    <u-modal
        :show="showDraftDialog"
        title="发现未完成的活动"
        content="您有未发布的活动，是否继续填写？"
        :show-cancel-button="true"
        cancel-text="重新填写"
        confirm-text="继续填写"
        @cancel="restartFill"
        @confirm="continueFill"
        @close="showDraftDialog = false"
    ></u-modal>

    <!-- 会员提示弹窗 -->
    <u-modal
      :show="showMemberModal"
      title="会员功能"
      :closeOnClickOverlay="false"
      :showCancelButton="true"
      confirmText="成为会员"
      cancelText="取消"
      :confirmColor="'#6AC086'"
      :cancelColor="'#666666'"
      @confirm="navigateToVip"
      @cancel="cancelMemberAction"
    >
      <view class="member-modal-content">
        <text class="member-modal-text">
          本功能为会员使用，你目前不是会员{{ store().$state.userInfo?.is_huiyuan === 0 ? '(或已到期)' : '' }} 是否成为会员？
        </text>
      </view>
    </u-modal>

    <!-- 美观的验证提示组件 -->
    <u-notify
        ref="notifyRef"
        :safeAreaInsetTop="true"
        :duration="3000"
    ></u-notify>
  </view>
</template>

<style lang="less">
/* uni-app页面级背景设置 */
page {
  background-color: #ffffff !important;
  height: 100%;
}

/* 确保所有容器都是白色背景 */
view,
.page,
.form-container {
  box-sizing: border-box;
}

view:not(.custom-navbar):not(.navbar-content):not(.navbar-left):not(.navbar-center):not(.navbar-right):not(.nature-button):not(.nature-button-active):not(.u-notify):not(.u-toast):not(.uni-toast),
.page,
.form-container,
.px30 {
  background-color: #ffffff;
}

/* CSS变量定义 - 统一白色背景主题 */
:root {
  --color-primary: #6AC086;
  --color-primary-light: #88D7A0;
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-text-primary: #333;
  --color-text-secondary: #666;
  --color-text-placeholder: #999;
  --color-border: rgba(106, 192, 134, 0.1);
  --color-border-light: #e0e0e0;

  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 20rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 30rpx;
  --spacing-xxl: 40rpx;

  --radius-sm: 12rpx;
  --radius-md: 20rpx;
  --radius-lg: 24rpx;
  --radius-xl: 30rpx;
  --radius-round: 50rpx;

  --shadow-sm: 0 12rpx 32rpx rgba(106, 192, 134, 0.08);
  --shadow-md: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  --shadow-lg: 0 12rpx 32rpx rgba(106, 192, 134, 0.25);
  --shadow-button: 0 8rpx 24rpx rgba(106, 192, 134, 0.3);
}

.u-form :deep(.u-textarea--radius) {
  margin-top: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--color-surface);
  font-size: 28rpx;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
}

.custom-button {
  background: linear-gradient(135deg, #6AC086 0%, #5BA876 100%);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(106, 192, 134, 0.3);
  border-radius: 24rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.ql-container {
  height: 760rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
}

/* 页面基础样式 - 统一白色背景 */
.page {
  min-height: 100vh;
  background: #ffffff !important;
  position: relative;
  padding-bottom: 200rpx;
  // 为自定义底部导航栏预留空间
  padding-bottom: calc(140rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  // 确保页面可以完整滚动
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* 微信小程序兼容性 - 确保白色背景 */
.page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  z-index: -1;
}

/* 自定义导航栏样式 - 绿色背景 */
.custom-navbar {
  background: #6AC086 !important;
  height: 200rpx;
  color: #ffffff;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.3);
  position: relative;
  display: flex;
  align-items: flex-end;
  padding-bottom: 20rpx;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  position: relative;
  background: transparent !important;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  margin-left: 20rpx; /* 向右移动20rpx */
  background: transparent !important;
}

.navbar-text {
  font-size: 28rpx;
  color: #ffffff;
}

.navbar-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: transparent !important;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
}

.navbar-right {
  background: transparent !important;
}

/* 确保导航栏子元素都不受通用白色背景影响，但保持主容器绿色背景 */
.custom-navbar view,
.navbar-content,
.navbar-left,
.navbar-center,
.navbar-right {
  background: transparent !important;
}

.step-indicator {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 500;
}

/* 进度指示器样式 */
.progress-container {
  padding: var(--spacing-xl);
  background: var(--color-surface);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

.progress-bar {
  height: 6rpx;
  background: var(--color-border-light);
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transition: width 0.3s ease;
  border-radius: 3rpx;
}

.step-dots {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-dot {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--color-border-light);
  color: var(--color-text-placeholder);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.step-dot.active {
  background: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.step-dot.current {
  transform: scale(1.1);
  box-shadow: var(--shadow-button);
  border-color: rgba(255, 255, 255, 0.3);
}

.section-header {
  padding: 40rpx 30rpx 30rpx 30rpx;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  font-size: 28rpx;
  font-weight: normal;
  color: #333333;
  margin: 0;
}

/* 表单容器样式 - 统一白色背景 */
.form-container {
  background: #ffffff;
  min-height: 100vh;
}

/* 步骤容器样式 - 确保所有步骤都有白色背景 */
.form-container > view {
  background: #ffffff;
}

/* 内容包装容器样式 */
.form-container .px30,
.form-container view {
  background: #ffffff;
}

/* 确保所有可能的容器都是白色背景 */
.step-content,
.upload-wrapper,
.activity-description-container,
.editor-wrapper,
.datetime-wrapper {
  background: #ffffff;
}

/* 滚动区域背景 */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* 步骤内容区域 */
.step-content {
  flex: 1;
  padding: 0 var(--spacing-xl);
}

/* 表单分组样式 - 统一白色背景 */
.form-section-new {
  background: #ffffff;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-md);
  border: 1rpx solid #e0e0e0;
}

.form-item-new {
  display: flex;
  align-items: center;
  padding: var(--spacing-xl);
  margin-bottom: 15rpx;
  transition: all 0.3s ease;
}

.form-item-new:active {
  background: rgba(106, 192, 134, 0.02);
}

.form-label-new {
  font-size: 28rpx;
  color: var(--color-text-primary);
  font-weight: 500;
  min-width: 160rpx;
}

.form-value-new {
  flex: 1;
  font-size: 28rpx;
  color: var(--color-text-primary);
  text-align: right;
  margin-right: var(--spacing-md);
}

.form-value-new.placeholder {
  color: var(--color-text-placeholder);
}

.section-title-new {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-md);
}

/* 单选按钮组样式 */
.radio-group-new {
  display: flex;
  gap: var(--spacing-xxl);
  margin-left: auto;
}

.radio-item-new {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 28rpx;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.radio-item-new.active {
  color: var(--color-primary);
  background: rgba(106, 192, 134, 0.05);
}

.radio-dot-new {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid var(--color-border-light);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.radio-dot-new.checked {
  border-color: var(--color-primary);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.radio-dot-new.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background: var(--color-primary);
  border-radius: 50%;
}

/* 上传组件样式 - 统一白色背景 */
.form-item-upload {
  padding: 30rpx 60rpx;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.section-header-center {
  text-align: left;
}

/* 二维码容器样式 - 统一白色背景 */
.qrcode-container {
  background: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 0;
}

.qrcode-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 30rpx 0;
  min-height: 200rpx;
}

/* 活动图片容器样式 - 统一白色背景 */
.activity-images-container {
  background: #ffffff;
  padding: 35rpx 30rpx 0 30rpx;
  margin-bottom: 0;
}

.activity-images-container :deep(.u-form-item__label) {
  text-align: left;
  margin-bottom: 15rpx;
  font-weight: 600;
  color: #333333;
  font-size: 22rpx;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  width: 100%;
  min-width: 350rpx;
  line-height: 1.2;
}

/* 确保活动图片容器的必填标识也能正确显示 */
.activity-images-container.required-field :deep(.u-form-item__label)::after {
  content: '*' !important;
  color: #333333 !important;
  margin-left: 4rpx;
  font-weight: normal;
  display: inline !important;
}

/* 带图标的输入框容器样式 */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 29rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 90rpx;
}

.input-with-icon:active {
  border-color: #6AC086;
  box-shadow: 0 4rpx 16rpx rgba(106, 192, 134, 0.2);
}

/* input-with-icon容器内的u-input样式重置 */
.input-with-icon :deep(.u-input) {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  padding: 0 !important;
  box-shadow: none !important;
  min-height: auto !important;
  width: 100% !important;
  flex: 1 !important;
}

.input-with-icon :deep(.u-input:focus) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* 下拉图标样式 */
.dropdown-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  z-index: 2;
}

.dropdown-icon:active {
  opacity: 0.6;
  transform: translateY(-50%) scale(0.9);
}

.activity-images-wrapper {
  padding: 20rpx 30rpx 30rpx 30rpx;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}

/* 时间选择器样式 */
.datetime-item {
  background: var(--color-surface);
  backdrop-filter: blur(10px);
}

/* datetime-wrapper样式被input-with-icon覆盖，保持兼容性 */

/* 输入框样式 */
.form-input-bordered {
  border-radius: var(--radius-sm);
  background: rgba(106, 192, 134, 0.02);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

.form-input-bordered:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  background: rgba(106, 192, 134, 0.05);
}

/* 底部按钮区域样式 - 统一白色背景 */
.bottomBox {
  background: #ffffff;
  border-top: 1px solid #e8f0e8;
  padding: 30rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 按钮容器样式 */
.button-container-new {
  padding: var(--spacing-xxl) 0;
}

.button-group-new {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-xxl) 0;
}

/* 按钮样式 */
.btn-primary-new {
  flex: 1;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  padding: 28rpx;
  border-radius: var(--radius-round);
  box-shadow: var(--shadow-button);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.btn-primary-new:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.4);
}

.btn-secondary-new {
  flex: 1;
  background: var(--color-surface);
  color: var(--color-text-secondary);
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  padding: 28rpx;
  border-radius: var(--radius-round);
  border: 2rpx solid var(--color-border-light);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary-new:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(106, 192, 134, 0.05);
  border-color: var(--color-primary);
}

.btn-publish-new {
  flex: 1;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  padding: 28rpx;
  border-radius: var(--radius-round);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.btn-publish-new:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

/* 输入框焦点样式 */
.form-input-bordered .u-input__content--focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

/* 第二页和第三页按钮样式 */
.step2-buttons {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xxl);
}

/* 封面上传组件样式 - 参考副本文件 */
.cover-upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
}

.cover-upload-component {
  max-width: 100%;
  display: flex;
  justify-content: center;
}

.step2-buttons .u-button {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.step2-buttons .u-button--primary {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-button);
}

.step2-buttons .u-button--primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.4);
}

/* 上一步按钮白色背景样式 */
.step2-buttons .u-button--default {
  background: #ffffff !important;
  color: #333333 !important;
  border: 2rpx solid #e6e6e6 !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
}

.step2-buttons .u-button--default:active {
  background: #f5f5f5 !important;
  border-color: #d0d0d0 !important;
  transform: translateY(2rpx) scale(0.98);
}

.step2-buttons .u-button--default:hover {
  background: #f8f8f8 !important;
  border-color: #d0d0d0 !important;
}

/* 🆕 支付方式选择器样式 */
.payment-type-selector {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.payment-option {
  padding: 32rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 20rpx;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.payment-option.active {
  border-color: #6AC086;
  background: rgba(106, 192, 134, 0.05);
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.15);
}

.payment-option:active {
  transform: scale(0.98);
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.payment-option .u-icon {
  transition: color 0.3s ease;
}

.payment-option.active .u-icon {
  color: #6AC086 !important;
}

/* 会员弹窗样式 */
.member-modal-content {
  padding: var(--spacing-md) 0;
  text-align: center;
}

.member-modal-text {
  font-size: 32rpx;
  color: var(--color-text-primary);
  line-height: 1.6;
  display: block;
  font-weight: 500;
}

/* 工具类样式 */
.px30 {
  padding: 0 var(--spacing-xl) 0 calc(var(--spacing-xl) + 20rpx);
  background: #ffffff;
}

/* 活动封面上传组件保持原有位置 */
.px30.cover-upload-wrapper {
  padding: 0 var(--spacing-xl);
}

.px60 {
  padding: 0 60rpx;
}

.py30 {
  padding: var(--spacing-xl) 0;
}

.mb20 {
  margin-bottom: var(--spacing-md);
}

.mb30 {
  margin-bottom: var(--spacing-xl);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.w-100 {
  width: 100%;
}

/* 表单项样式优化 - 统一白色背景 */
.u-form-item {
  background: #ffffff;
  border-radius: 0;
  margin-bottom: 0;
  padding: 30rpx;
  box-shadow: none;
}

/* 分行显示的表单项样式优化 */
.u-form-item[labelPosition="top"] {
  padding: 35rpx 30rpx;
  margin-bottom: 0;
}

/* 分行显示时label的样式 */
.u-form-item[labelPosition="top"] :deep(.u-form-item__label) {
  margin-bottom: 15rpx;
  font-weight: 600;
  color: #333333;
  font-size: 26rpx;
}

/* 必填字段标识 - 黑色星号 */
.required-field :deep(.u-form-item__label)::after {
  content: '*' !important;
  color: #333333 !important;
  margin-left: 4rpx;
  font-weight: normal;
  display: inline !important;
}

/* 确保活动描述的必填标识也能正确显示 */
.description-form-item.required-field :deep(.u-form-item__label)::after {
  content: '*' !important;
  color: #333333 !important;
  margin-left: 4rpx;
  font-weight: normal;
  display: inline !important;
}

/* 二维码容器label样式 */
.qrcode-container :deep(.u-form-item__label) {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}

/* 确保二维码容器的必填标识也能正确显示 */
.qrcode-container.required-field :deep(.u-form-item__label)::after {
  content: '*' !important;
  color: #333333 !important;
  margin-left: 4rpx;
  font-weight: normal;
  display: inline !important;
}

/* 分行显示时input容器的样式 */
.u-form-item[labelPosition="top"] :deep(.u-form-item__body) {
  width: 100%;
}

/* 输入框样式优化 - 与input-with-icon组件保持一致 */
.u-form-item[labelPosition="top"] :deep(.u-input) {
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 24rpx;
  padding: 29rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-height: 90rpx;
  box-sizing: border-box;
}

.u-form-item[labelPosition="top"] :deep(.u-input:focus) {
  border-color: #6AC086;
  border-width: 2rpx;
  box-shadow: 0 4rpx 16rpx rgba(106, 192, 134, 0.2);
}

/* 通用输入框样式 */
.u-input {
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 24rpx;
  padding: 29rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-height: 90rpx;
  box-sizing: border-box;
}

/* 输入框焦点状态统一样式 */
.u-input:focus,
.u-input:focus-within {
  border-color: #6AC086;
  border-width: 2rpx;
  box-shadow: 0 4rpx 16rpx rgba(106, 192, 134, 0.2);
}

/* 确保所有输入框都应用卡片样式 */
.form-input-bordered {
  background: #f8f9fa !important;
  border: 2rpx solid #e0e0e0 !important;
  border-radius: 24rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
  padding: 29rpx 24rpx !important;
  min-height: 90rpx !important;
}

/* 富文本编辑器样式 - 与input-with-icon组件保持一致 */
.ql-container {
  height: 330rpx; /* 从380rpx减少到330rpx，减少50rpx */
  width: 100%;
  border: 2rpx solid #e0e0e0; /* 与input-with-icon一致的边框 */
  border-radius: 24rpx; /* 与input-with-icon一致的圆角 */
  padding: 29rpx 24rpx; /* 与input-with-icon一致的内边距 */
  background: #f8f9fa; /* 与input-with-icon一致的背景色 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); /* 统一的阴影效果 */
  transition: all 0.3s ease;
  box-sizing: border-box; /* 确保padding不影响总宽度 */
  text-align: left; /* 编辑器内容左对齐 */
}

/* 编辑器内容左对齐 */
.ql-container :deep(.ql-editor) {
  text-align: left;
}

.ql-container :deep(.ql-editor p) {
  text-align: left;
}

.ql-container:focus-within {
  border-color: #6AC086; /* 与input-with-icon一致的聚焦边框色 */
  border-width: 2rpx; /* 保持边框宽度 */
  box-shadow: 0 4rpx 16rpx rgba(106, 192, 134, 0.2); /* 与input-with-icon一致的聚焦阴影 */
}

/* 活动描述容器样式 - 左对齐 */
.activity-description-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  background: #ffffff;
  padding: 30rpx 0;
  margin: 0;
}

/* 活动描述表单项样式 */
.description-form-item {
  width: 100%;
  text-align: left;
  margin-bottom: 0;
  padding-bottom: 20rpx;
}

.description-form-item :deep(.u-form-item__label) {
  width: 100%;
  text-align: left;
  justify-content: flex-start;
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
}

/* 编辑器包装器样式 - 与input-with-icon组件对齐 */
.editor-wrapper {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 750rpx) {
  .editor-wrapper {
    max-width: 100%;
    padding: 0 20rpx;
  }

  .activity-description-container {
    padding: 20rpx 0;
    width: 100%;
  }

  .ql-container {
    height: 280rpx; /* 小屏幕下减少高度，从330rpx按比例减少到280rpx */
  }

  .u-input {
    padding: 25rpx 20rpx;
    min-height: 80rpx;
  }

  .activity-nature-buttons {
    gap: 15rpx;
  }

  .u-form-item {
    padding: 25rpx;
  }

  .u-form-item[labelPosition="top"] {
    padding: 30rpx 25rpx;
  }

  /* 小屏幕下的输入框图标适配 */
  .input-with-icon {
    padding: 25rpx 20rpx;
    min-height: 80rpx;
  }

  .dropdown-icon {
    right: 15rpx;
    size: 20rpx;
  }

  .nature-button {
    padding: 12rpx 34rpx;
    min-width: 120rpx;
  }

  /* 小屏幕下活动图片布局适配 */
  .activity-images-wrapper {
    padding: 15rpx 20rpx 20rpx 20rpx;
    gap: 10rpx;
  }

  .activity-images-wrapper ::v-deep .u-upload__preview-wrap {
    width: calc(33.33% - 7rpx) !important;
    margin: 0 0 10rpx 0;
  }

  .activity-images-wrapper ::v-deep .u-upload__preview-image {
    height: 160rpx !important;
  }

  .activity-images-wrapper ::v-deep .u-upload__add-wrap {
    width: calc(33.33% - 7rpx) !important;
    height: 160rpx !important;
    margin: 0 0 10rpx 0;
  }

  .activity-images-container :deep(.u-form-item__label) {
    font-size: 20rpx;
    min-width: 320rpx;
    line-height: 1.1;
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: unset !important;
    width: 100% !important;
  }
}

/* 上传组件容器样式 */
.upload-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.upload-container {
  border: 2rpx solid #e6e6e6;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  background: #ffffff;
  margin-bottom: 10rpx;
}

.upload-hint-text {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  margin-top: 10rpx;
  margin-bottom: 0;
  padding-bottom: 20rpx;
}

/* 上传组件样式优化 - 居中显示 */
.form-item-upload :deep(.u-upload) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.form-item-upload :deep(.u-upload__wrap) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* 活动封面上传按钮居中 */
.form-item-upload :deep(.u-upload__button) {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 活动封面预览图片居中 */
.form-item-upload :deep(.u-upload__preview) {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 活动封面上传区域整体居中 */
.form-item-upload :deep(.u-upload__preview-wrap) {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 活动性质按钮样式 */
.activity-nature-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 20rpx;
  width: 100%;
}

.nature-button {
  padding: 16rpx 42rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 50rpx;
  background: #ffffff;
  color: #666666;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 140rpx;
  cursor: pointer;
}

.nature-button-active {
  border-color: #6AC086 !important;
  background: #6AC086 !important;
  background-color: #6AC086 !important;
  color: #ffffff !important;
  box-shadow: 0 8rpx 20rpx rgba(106, 192, 134, 0.3);
}

.nature-button:hover {
  border-color: #6AC086;
  color: #6AC086;
}

.nature-button-active:hover {
  background: #5aa876;
  border-color: #5aa876;
  color: #ffffff;
}

/* 微信小程序兼容性优化 */
@media (max-width: 750rpx) {
  .upload-container {
    margin: 0 20rpx 20rpx 20rpx;
    padding: 15rpx;
  }

  .activity-nature-buttons {
    gap: 15rpx;
  }

  .nature-button {
    padding: 12rpx 34rpx;
    font-size: 26rpx;
    min-width: 120rpx;
  }
}

/* 确保在微信小程序中的触摸反馈 */
.nature-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 表单验证错误提示样式 */
:deep(.u-toast) {
  background: #ff4757 !important;
  color: #ffffff !important;
  border-radius: 16rpx !important;
  padding: 24rpx 32rpx !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4) !important;
  border: none !important;
}

:deep(.u-toast__content) {
  background: #ff4757 !important;
  color: #ffffff !important;
  border-radius: 16rpx !important;
  padding: 0 !important;
}

:deep(.u-toast__text) {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* 确保所有toast相关元素都使用红色背景白色文字 */
:deep(.uni-toast) {
  background: #ff4757 !important;
  color: #ffffff !important;
  border-radius: 16rpx !important;
}

:deep(.uni-toast .uni-toast__content) {
  background: #ff4757 !important;
  color: #ffffff !important;
}

/* u-notify组件样式 - 红色背景白色文字，全屏宽度 */
:deep(.u-notify) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
  border-radius: 0 !important;
  padding: 24rpx 32rpx !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4) !important;
  border: none !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  margin: 0 !important;
}

:deep(.u-notify__content) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
  border-radius: 0 !important;
  padding: 0 !important;
  width: 100% !important;
}

:deep(.u-notify__text) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
  font-weight: 500 !important;
}

:deep(.u-notify--error) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
  width: 100% !important;
}

:deep(.u-notify--error .u-notify__content) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
}

/* 强制覆盖所有可能的文字容器背景 */
:deep(.u-notify .u-notify__content),
:deep(.u-notify .u-notify__text),
:deep(.u-notify text),
:deep(.u-notify view) {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  color: #ffffff !important;
}

/* 确保提示组件不被覆盖的额外保护 */
:deep(.u-notify),
:deep(.u-toast),
:deep(.uni-toast) {
  z-index: 10000 !important;
  position: fixed !important;
}

/* 确保提示组件内容不被全局样式影响 - 微信小程序兼容版本 */
:deep(.u-notify__content),
:deep(.u-notify__text),
:deep(.u-toast__content),
:deep(.u-toast__text),
:deep(.uni-toast__content) {
  background: inherit !important;
  color: inherit !important;
}

.upload-container:active {
  transform: scale(0.98);
}

/* 强制居中所有上传相关元素 */
.form-item-upload :deep(.u-upload__add-wrap) {
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.form-item-upload :deep(.u-upload__preview-image) {
  margin: 0 auto !important;
  display: block !important;
}

.form-item-upload :deep(.u-upload__deletable) {
  position: absolute !important;
  top: -10rpx !important;
  right: -10rpx !important;
}

.qrcode-wrapper ::v-deep .u-upload {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.qrcode-wrapper ::v-deep .u-upload__wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.activity-images-wrapper :deep(.u-upload__preview-wrap) {
  width: calc(33.33% - 8rpx) !important;
  margin: 0 0 12rpx 0 !important;
  box-sizing: border-box !important;
}

.activity-images-wrapper :deep(.u-upload__preview-image) {
  width: 100% !important;
  height: 90rpx !important;
  border-radius: 12rpx !important;
  object-fit: cover !important;
}

.activity-images-wrapper :deep(.u-upload__add-wrap) {
  width: calc(33.33% - 8rpx) !important;
  height: 90rpx !important;
  margin: 0 0 12rpx 0 !important;
  border-radius: 12rpx !important;
  border: 2px dashed #d0d7de !important;
  background: #ffffff !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.activity-images-wrapper :deep(.u-upload__wrap) {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  flex-wrap: wrap !important;
  width: 100% !important;
}

/* 确保活动图片上传组件本身不受限制 */
.activity-images-wrapper :deep(.u-upload) {
  width: 100% !important;
}

/* 移除活动类型选择区域的特殊样式，使用统一的表单项样式 */

/* 活动类型选择器样式 */
.activity-type-selector {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  transition: all 0.3s ease;
}

.activity-type-selector:active {
  opacity: 0.7;
}

/* 选择器主题样式 */
::v-deep .u-picker {
  .u-picker__confirm {
    color: var(--color-primary) !important;
  }

  .u-picker__cancel {
    color: #999999 !important;
  }
}

::v-deep .u-datetime-picker {
  .u-picker__confirm {
    color: var(--color-primary) !important;
  }

  .u-picker__cancel {
    color: #999999 !important;
  }
  gap: 20rpx;
  width: 100%;
}
</style>
