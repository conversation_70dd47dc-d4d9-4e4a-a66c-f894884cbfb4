<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 轮播管理 
*/
class Lunbotu extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/		
	public function index(){
		dbConn();
		$where = "1";
		$data = Db()->table("lunbotu")->where($where)->order("sort DESC,id ASC")->fetchAll();
		assign("data",$data);
		if(isset($_GET['alert'])){
			if(!empty($_GET['alert'])){
				assign("alertTpl",true);
			}else{
				assign("alertTpl",false);
			}
		}
		$position_arr = [1=>"首页"];
		assign("position_arr",$position_arr);
		$type_arr = [0=>"全部",1=>"会员",2=>"非会员"];
		assign("type_arr",$type_arr);
		$url_type_arr = [0=>"不跳转",1=>"网址",2=>"活动",3=>"小程序地址"];
		assign("url_type_arr",$url_type_arr);
		return view(['header','lunbotu/index','footer']);
	}
	/*
	* @name 添加
	* @method add
	*/		
	public function add(){
		responseType("json");
		if(
			!isset($_POST['position']) ||
			!check($_POST['position'],"integt0") ||
			!isset($_POST['url_type']) ||
			!check($_POST['url_type'],"integt0") ||
			!isset($_POST['type']) ||
			!check($_POST['type'],"integt0") ||
			!isset($_POST['sort']) ||
			!check($_POST['sort'],"integt0") ||
			!isset($_POST['is_show']) ||
			!in_array($_POST['is_show'],["0","1"]) ||
			empty($_POST['img_url'])
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		dbConn();
		$data = [
			"title"=>":title",
			"img_url"=>":img_url",
			"url_params"=>":url_params",
			"sort"=>(int)$_POST['sort'],
			"type"=>(int)$_POST['type'],
			"is_show"=>(int)$_POST['is_show'],
			"position"=>(int)$_POST['position'],
			"url_type"=>(int)$_POST['url_type'],
		];
		$prepareParam = [
			":title"=>isset($_POST['title']) ? htmlspecialchars($_POST['title']) : "",
			":img_url"=>isset($_POST['img_url']) ? htmlspecialchars($_POST['img_url']) : "",
			":url_params"=>isset($_POST['url_params']) ? htmlspecialchars($_POST['url_params']) : "",
		];
		Db::begin();
		try{
			$res = Db()->table("lunbotu")->prepareParam($prepareParam)->insert($data);
			$insert_id = Db()->insertId();
			$this->root_log("添加轮播图,id:[ {$insert_id} ]");
			Db::commit();
			return ["status"=>"ok","msg"=>"添加成功"];
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 编辑
	* @method edit
	*/	
	public function edit(){
		responseType("json");
		if(
			!isset($_POST['position']) ||
			!check($_POST['position'],"integt0") ||
			!isset($_POST['url_type']) ||
			!check($_POST['url_type'],"integt0") ||
			!isset($_POST['type']) ||
			!check($_POST['type'],"integt0") ||
			!isset($_POST['sort']) ||
			!check($_POST['sort'],"integt0") ||
			!isset($_POST['is_show']) ||
			!in_array($_POST['is_show'],["0","1"]) ||
			empty($_POST['img_url'])
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = (int)$_POST['id'];
		$data = [
			"title"=>":title",
			"img_url"=>":img_url",
			"url_params"=>":url_params",
			"sort"=>(int)$_POST['sort'],
			"type"=>(int)$_POST['type'],
			"is_show"=>(int)$_POST['is_show'],
			"position"=>(int)$_POST['position'],
			"url_type"=>(int)$_POST['url_type'],
			"update_time"=>date("Y-m-d H:i:s")
		];
		$prepareParam = [
			":title"=>isset($_POST['title']) ? htmlspecialchars($_POST['title']) : "",
			":img_url"=>isset($_POST['img_url']) ? htmlspecialchars($_POST['img_url']) : "",
			":url_params"=>isset($_POST['url_params']) ? htmlspecialchars($_POST['url_params']) : "",
		];			
		dbConn();
		Db::begin();
		try{
			$res = Db()->table("lunbotu")->prepareParam($prepareParam)->where("id={$id}")->update($data);
			if(empty($res)){
				throw new \Exception("操作失败");
			}
			$this->root_log("编辑轮播图,id:{$id}");
			Db::commit();
			return ["status"=>"ok","msg"=>"编辑成功"];
		}catch(\Exception $e){
			Db::rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}
	/*
	* @name 删除
	* @method del
	*/	
	public function del(){
		responseType("json");
		if(empty($_POST['ids']))return ["status"=>"error","msg"=>"参数错误"];
		$ids = trim($_POST['ids']);
		$ids_arr = explode(",",$ids);
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		foreach($ids_arr as &$v){
			if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
		}
		$ids = implode(",",$ids_arr);
		if(empty($ids))return ["status"=>"error","msg"=>"参数错误"];
		dbConn();
		try{
			$sql = "DELETE FROM `lunbotu` WHERE id IN ({$ids})";
			$rowCount = Db()->_exec($sql);
			if($rowCount){
				$this->root_log("删除轮播图,ids[{$ids}]");
				return ["status"=>"ok","msg"=>"影响数量：{$rowCount}"];
			}else{
				return ["status"=>"error","msg"=>"影响数量为零行"];
			}
		}catch(\Exception $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
		return ["status"=>"error","msg"=>"操作失败"];
	}
	/*
	* @name 获取轮播图信息
	* @method get_info
	*/	
	public function get_info($id){
		responseType("json");
		if(!check($id,"intgt0")){
			return ["status"=>"error","msg"=>"获取信息失败"];
		}
		$id = intval($id);
		dbConn();
		$info = Db()->table("lunbotu")->where("id={$id}")->fetch();
		if(empty($info)){
			return ["status"=>"error","msg"=>"获取信息失败"];		
		}
		return ["status"=>"ok","data"=>$info];
	}
	public function _empty(){
		
	}
	function __destruct(){

	}
}
