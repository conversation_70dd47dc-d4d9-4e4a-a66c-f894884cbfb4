<?php
namespace controller;
use core\Controller;
use core\Db;
use core\upload;
/*
 * @className 投诉建议
*/
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}
	/*
	* @name 列表
	* @method index
	*/
	public function index(){
		dbConn();
		//
		$page = isset($_REQUEST['page']) && check($_REQUEST['page'],"intgt0") ? (int)$_REQUEST['page'] : 1;
		$page_size = isset($_REQUEST['page_size']) && check($_REQUEST['page_size'],"intgt0") ? (int)$_REQUEST['page_size'] : 20;
		assign("page_size",$page_size);
		assign("page",$page);
		$where = "1";
		$prepareParam = [];
		$pageParams = ["page"=>$page,"page_size"=>$page_size];

		if(!empty($_REQUEST['lianxifangshi'])){
			$lianxifangshi = "%".$_REQUEST['lianxifangshi']."%";
			$where .= " AND `lianxifangshi` LIKE :lianxifangshi";
			$prepareParam[':lianxifangshi'] = $lianxifangshi;
			$pageParams['lianxifangshi'] = $_REQUEST['lianxifangshi'];;
		}
		if(!empty($_REQUEST['keyword'])){
			$keyword = "%".$_REQUEST['keyword']."%";
			$where .= " AND `text` LIKE :keyword";
			$prepareParam[':keyword'] = $keyword;
			$pageParams['keyword'] = $_REQUEST['keyword'];
		}
		//
		$start_date = date("Y-m-d",strtotime("-30 days"));
		$end_date = date("Y-m-d");
		if(!empty($_REQUEST['start_date']) && !empty($_REQUEST['end_date'])){
			$start_date = date("Y-m-d",strtotime($_REQUEST['start_date']));
			$end_date = date("Y-m-d",strtotime($_REQUEST['end_date']));
		}
		$start_time = date("Y-m-d 0:0:0",strtotime($start_date));
		$end_time = date("Y-m-d 23:59:59",strtotime($end_date));
		$where .= " AND `time` BETWEEN '{$start_time}' AND '{$end_time}'";
		$pageParams['start_date'] = $start_date;
		$pageParams['end_date'] = $end_date;
		assign("start_date",$start_date);
		assign("end_date",$end_date);
		//dump($where);
		//
		$data = Db()->table("tousujianyi")->where($where)->prepareParam($prepareParam)->order("id DESC")->page($page,$page_size,$pageParams);
		assign("data",$data);
		return view(['header','tousujianyi/index','footer']);
	}

	/*
	* @name 删除
	* @method del
	*/		
	public function del(){
		responseType("json");
		if(
			empty($_POST['ids'])
		){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		//
		$ids_arr = [];
		if(check($_POST["ids"],"intgt0")){
			$id = (int)$_POST["ids"];
			$ids_arr[] = $id;
		}else{
			$ids = htmlspecialchars(trim($_POST['ids']));
			$ids_arr = explode(",",$ids);
			foreach($ids_arr as $v){
				if(!check($v,"intgt0"))return ["status"=>"error","msg"=>"参数错误"];
			}				
		}
		if(empty($ids_arr))return ["status"=>"error","msg"=>"参数错误"];
		$ids = implode(",",$ids_arr);
		//
		dbConn();
		$data = Db()->table("tousujianyi")->where("id IN ({$ids})")->fetchAll();
		if(empty($data)){
			return ["status"=>"error","msg"=>"内容不存在"];
		}
		Db()->begin();
		try{
			$sql = "DELETE FROM `tousujianyi` WHERE id IN ({$ids})";
			$rowCount = Db()->_exec($sql);
			if(empty($rowCount)){
				throw new \Exception("删除投诉建议失败");
			}
			//
			$this->root_log("删除投诉建议【{$ids}】");
			//
			foreach($data as $row){
				if(empty($row['imgs'])){
					$imgs = explode("|",$row['imgs']);
					foreach($imgs as $img){
						\core\Upload::delFile($img);
					}
				}
			}
			//
			Db()->commit();
			return ["status"=>"ok","msg"=>"删除数量:{$rowCount}"];
		}catch(\Exception $e){
			Db()->rollback();
			$this->exception_log($e->getMessage());
			return ["status"=>"error","msg"=>$e->getMessage()];
		}

	}
	
	public function _empty(){

	}
	function __destruct(){

	}
}
