<script setup>
import { watch, ref, reactive } from "vue";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app";
import { store } from "@/store";

const url = ref("");

onLoad((e) => {
  url.value = e.url;
});
</script>
<template>
  <view class="page">
    <web-view :src="url"></web-view>
  </view>
</template>

<style scoped lang="less"></style>
