<script setup>
import { watch, ref, reactive } from "vue";
import { userget_shejiao_list } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { store } from "@/store";
import { setListHeight, navto, callPhone } from "@/utils";

const goods = ref([
  { num: 0, money: 20, zongMoney: 0 },
  { num: 0, money: 30, zongMoney: 0 },
]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");

onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_shejiao_list({ page: mescroll.num, page_size: mescroll.size })
    .then((res) => {
      const curPageData = res.data || []; // 当前页数据
      if (mescroll.num == 1) goods.value = []; // 第一页需手动制空列表
      goods.value = goods.value.concat(curPageData); //追加新数据
      mescroll.endBySize(curPageData.length, res.count); // 请求成功, 结束加载
    })
    .catch(() => {
      mescroll.endErr(); // 请求失败, 结束加载
    });
};
</script>
<template>
  <view class="page">
    <myTitle
      img="cJianbianTopBg.png"
      height="320rpx"
      title="社交"
      :backShow="false"
    ></myTitle>
    <!-- <u-gap height="320rpx" bg-color=""></u-gap> -->
    <view class="pa px30 w" style="top: 178rpx">
      <mescroll-uni
        class="list"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
        :height="height"
      >
        <view class="mb10 pt20 pb20 px30 w b6f r30" v-for="(val, i) in goods" :key="i">
          <view class="mb20 df ais">
            <view
              class="df aic f1"
              @click="navto(`/pages/bundle/msg/personage?to_uid=${val.uid}`)"
            >
              <u-avatar size="68rpx" mode="aspectFill" :src="val.avatar"></u-avatar>
              <view class="df fdc jcsb ml20">
                <u-text size="28rpx" :text="val.nickname"></u-text>
                <u-text size="22rpx" :text="val.gexingqianming"></u-text>
              </view>
            </view>
            <u-button
              color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
              :customStyle="{
                width: '120rpx',
                height: '60rpx',
                borderRadius: '16rpx',
                color: '#000',
                fontSize: '20rpx',
                scale: '.8',
                transformOrigin: 'center',
              }"
              text="联系Ta"
              @click.stop="callPhone(val.mobile)"
            ></u-button>
          </view>
          <u-album
            v-if="val.imgs_url?.length > 0"
            :urls="val.imgs_url"
            maxCount="3"
            key-name="img_url"
            multipleSize="201rpx"
            singleSize="600rpx"
            radius="20rpx"
          ></u-album>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>

<style scoped lang="less">
:v-deep u-album__row__wrapper data-v-6fcabaad {
  border-radius: 30rpx;
}
</style>
