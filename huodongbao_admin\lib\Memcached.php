<?php
namespace lib;
use core\config;
class Memcached{
    static protected $instance = null;

    public function __construct(){
        self::connect();
    }

    static public function connect(){
        if(class_exists('\Memcached')){
			self::$instance = new \Memcached();
			//向服务器池中增加一个服务器
			self::$instance->addServer(Config::get("sys.memcached.host"), Config::get("sys.memcached.port"));
			//self::$instance->->setOption(\Memcached::OPT_COMPRESSION, false);
		}else{
            throw new \Exception("Memcached扩展未安装");
        }
    }

	static public function instance(){
		if(empty(self::$instance))self::connect();
        return self::$instance;
	}
    //简单常用操作
    static public function set($key,$value,$expiration=0){
        if(empty(self::$instance))self::connect();
        return self::$instance->set($key,$value,$expiration);
    }
    static public function get($key){
        if(empty(self::$instance))self::connect();
        return self::$instance->get($key);
    }
    static public function setMulti(array $items,$expiration=0){
        if(empty(self::$instance))self::connect();
        return self::$instance->setMulti($items,$expiration);
    }
    static public function getMulti($keys){
        if(empty(self::$instance))self::connect();
        return self::$instance->getMulti($keys);
    }
    static public function delete($key){
        if(empty(self::$instance))self::connect();
        return self::$instance->delete($key);
    }
    static public function increment($key,$offset=1){
        if(empty(self::$instance))self::connect();
        return self::$instance->increment($key,$offset);
    }
    static public function decrement($key,$offset=1){
        if(empty(self::$instance))self::connect();
        return self::$instance->decrement($key,$offset);
    }
    static public function flush(){
        if(empty(self::$instance))self::connect();
        return self::$instance->flush();
    }
    //
	public function __destruct(){

	}
}
?>
