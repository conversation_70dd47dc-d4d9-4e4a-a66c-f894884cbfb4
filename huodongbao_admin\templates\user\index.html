<fieldset class="table-search-fieldset">
	<legend>搜索信息</legend>
	<div style="margin: 10px 10px 10px 10px">
		<form class="layui-form layui-form-pane" enctype="multipart/form-data" action="" method="post" id="search">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">用户编号</label>
					<div class="layui-input-inline">
						<input type="text" name="uid" value="<?php if(!empty($_REQUEST['uid']))echo $_REQUEST['uid']; ?>" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">昵称</label>
					<div class="layui-input-inline">
						<input type="text" name="nickname" value="<?php if(!empty($_REQUEST['nickname']))echo $_REQUEST['nickname']; ?>"  class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">手机号</label>
					<div class="layui-input-inline">
						<input type="text" name="mobile" value="<?php if(!empty($_REQUEST['mobile']))echo $_REQUEST['mobile']; ?>"  class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">性别</label>
					<div class="layui-input-inline">
						<select name="sex" value="<?php if(isset($sex))echo $sex; ?>">
							<option value="all">全部</option>
							<option value="1">男</option>
							<option value="2">女</option>
							<option value="0">未知</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">冻结</label>
					<div class="layui-input-inline">
						<select name="is_dongjie" value="<?php if(isset($is_dongjie))echo $is_dongjie; ?>">
							<option value="all">全部</option>
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">会员</label>
					<div class="layui-input-inline">
						<select name="is_huiyuan" value="<?php if(isset($is_huiyuan))echo $is_huiyuan; ?>">
							<option value="all">全部</option>
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">上级编号</label>
					<div class="layui-input-inline">
						<input type="text" name="p_uid" value="<?php if(isset($_REQUEST['p_uid']))echo $_REQUEST['p_uid']; ?>" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">排序</label>
					<div class="layui-input-inline">
						<select name="sort_k" value="<?php if(isset($sort_k))echo $sort_k; ?>">
							<?php foreach($sort_arr as $k=>$sort_data){ ?>
							<option value="<?php echo $k; ?>"><?php echo $sort_data['name']; ?></option>
							<?php } ?>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<button type="button" class="layui-btn layui-btn-primary" onclick="submit_();"><i class="layui-icon"></i> 搜 索</button>
				</div>
			</div>
			<input type="hidden" name="page" value="1" />
			<input type="hidden" name="page_size" value="<?php echo isset($page_size) ? $page_size : ""; ?>" />
			<input type="hidden" id="daochu" name="daochu" value="0" />
		</form>
	</div>
</fieldset>
<div class="layui-form layui-border-box layui-table-view" >
    <div class="layui-table-tool">
        <div class="layui-table-tool-temp">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="bat_ac('dongjie','');">冻结</button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="bat_ac('cancel_dongjie','');">解冻</button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="bat_ac('reset_password','');">重置密码</button>
            </div>
        </div>
		<!--
        <div class="layui-table-tool-self">
            <div class="layui-inline" onclick="daochu();" title="导出"><i class="layui-icon layui-icon-export"></i></div>
        </div>
		-->
    </div>
	<div class="layui-table-body layui-table-main table_height_">
		<table cellspacing="0" cellpadding="0" border="0" class="layui-table" lay-even>
			<thead>
				<tr>
					<td lass="layui-table-col-special">
						<div class="layui-table-cell laytable-cell-checkbox">
							<input type="checkbox" lay-skin="primary"  id="quanxuan_ids[]" />
							<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
						</div>
					</td>
					<td><div class="layui-table-cell">编号</div></td>
					<td><div class="layui-table-cell">昵称</div></td>
					<td><div class="layui-table-cell">头像</div></td>
					<td><div class="layui-table-cell">出生日期</div></td>
					<td><div class="layui-table-cell">性别</div></td>
					<td><div class="layui-table-cell">手机号</div></td>
					<td><div class="layui-table-cell">余额</div></td>
					<td><div class="layui-table-cell">会员</div></td>
					<td><div class="layui-table-cell">会员期限</div></td>
					<td><div class="layui-table-cell">邀请人</div></td>
					<!-- 🆕 新增：角色列 -->
					<td><div class="layui-table-cell">用户角色</div></td>
					<td><div class="layui-table-cell">所属分会</div></td>

					<td><div class="layui-table-cell">照片数量</div></td>
					<td><div class="layui-table-cell">标签数量</div></td>
					<td><div class="layui-table-cell">注册时间</div></td>
					<td><div class="layui-table-cell get_ip_addr_all">注册IP</div></td>
					<td><div class="layui-table-cell">冻结</div></td>
					<td><div class="layui-table-col-special">操作</div></td>
				</tr>
			</thead>
			<tbody>
				<?php foreach($data as $v){ ?>
				<tr>
					<td lass="layui-table-col-special">
						<div class="layui-table-cell laytable-cell-checkbox">
							<input type="checkbox" value="<?php echo $v['uid']; ?>" name="ids[]" />
							<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
						</div>
					</td>
					<td><div class="layui-table-cell"><?php echo $v['uid']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['nickname']; ?></div></td>
					<td><div class="layui-table-cell"><img src="<?php echo $v['avatar']; ?>" width="25px" data-action="zoom" /></div></td>
					<td><div class="layui-table-cell"><?php echo $v['birthday']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo isset($sex_arr[$v['sex']]) ? $sex_arr[$v['sex']] : "-"; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['mobile']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['money']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['is_huiyuan'] ? "是" : "否"; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['huiyuan_end_time']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo isset($v['p_user']['nickname']) ? "{$v['p_uid']}【{$v['p_user']['nickname']}】" : "-"; ?></div></td>
					<!-- 🆕 新增：角色和分会信息显示 -->
					<td><div class="layui-table-cell">
						<?php
						$role_names = [
							0 => '<span style="color: #FF5722; font-weight: bold;">管理员</span>',
							1 => '<span style="color: #2196F3; font-weight: bold;">分会长</span>',
							2 => '<span style="color: #666;">普通用户</span>',
							3 => '<span style="color: #FF9800; font-weight: bold;">场地第三方</span>',
							4 => '<span style="color: #9C27B0; font-weight: bold;">城市分会长</span>',
							5 => '<span style="color: #4CAF50; font-weight: bold;">场地第三方-不管理分会</span>'
						];
						echo isset($role_names[$v['role_type']]) ? $role_names[$v['role_type']] : '未知角色';
						?>
					</div></td>
					<td><div class="layui-table-cell">
						<?php
						if (!empty($v['branch_name'])) {
							echo $v['branch_name'];
							if ($v['assignment_type'] === 'system') {
								echo ' <span style="color: #FF9800; font-size: 12px;">[系统分配]</span>';
							}
						} else {
							echo '-';
						}
						?>
					</div></td>

					<td><div class="layui-table-cell" style="cursor:pointer;" onclick="get_imgs(<?php echo $v['uid']; ?>);"><?php echo $v['img_num']; ?></div></td>
					<td><div class="layui-table-cell" style="cursor:pointer;" onclick="get_labels(<?php echo $v['uid']; ?>);"><?php echo $v['label_num']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['reg_time']; ?></div></td>
					<td><div class="layui-table-cell  get_ip_addr"><?php echo $v['reg_ip']; ?></div></td>
					<td><div class="layui-table-cell"><?php echo $v['is_dongjie'] ? "是" : "否"; ?></div></td>
					<td class="layui-table-col-special">
						<div class="layui-table-cell">
							<a href="<?php echo url("user/edit/id/{$v['uid']}",\core\Page::$pageParams); ?>" class="layui-btn layui-btn-normal layui-btn-xs">
								编辑
							</a>
						</div>
					</td>
				</tr>
				<?php } ?>
			</tbody>
		</table>
	</div>
    <?php echo pageRender(); ?>
</div>
<script>
function bat_ac(ac,id){
	var layer = layui.layer;
	if(id == ''){
		var ids_arr = new Array();
		$("input[name='ids[]']:checked").each(function(){  
			ids_arr.push($(this).val());//向数组中添加元素  
		});
		if(ids_arr.length == 0){
			layer.msg("请选择后再试");
			return false;
		}
		var ids_str = ids_arr.join(',');
	}else{
		ids_str = id;
	}
	//console.log(ids_str);
	if(ac == "dongjie" || ac == "cancel_dongjie"){
		var tips = "";
		if(ac == "dongjie"){
			tips = "冻结";
		}else if(ac == "cancel_dongjie"){
			tips = "取消冻结";
		}
		layui.layer.confirm("确认【" + tips + "】吗？",{title:"提示"},function(index){
			layui.layer.close(index);
			var send_data = {
				ac:ac,
				ids:ids_str
			};
			$.post("<?php echo url("user/dongjie"); ?>",send_data,function(data){
				if(data.status == "ok"){
					layer.msg(data.msg,{icon:1},function(){
						window.location.href="<?php echo url('',\core\Page::$pageParams); ?>";
					});
				}else{ 
					layer.msg(data.msg,{icon:2});
				}
			});			
		});
	}else if(ac == "reset_password"){
		layer.prompt({formType: 3,value:"pwd123456",title: "请输入新密码",maxlength: 50}, function(value, index, elem){
			layer.close(index);
			var send_data = {
				ac:ac,
				password:value,
				ids:ids_str
			};
			$.post("<?php echo url("user/reset_password"); ?>",send_data,function(data){
				if(data.status == "ok"){
					layer.msg(data.msg,{icon:1},function(){
						window.location.href="<?php echo url('',\core\Page::$pageParams); ?>";
					});
				}else{ 
					layer.msg(data.msg,{icon:2});
				}
			});		
		});
	}else{
		layer.msg("无效操作:"+ac);
		return false;
	}
}
function get_imgs(uid){
	$.getJSON("/user/get_imgs/uid/"+uid,function(json){
	  layer.photos({
		photos: json
		,anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
	  });
	}); 
}
function get_labels(uid){
	layui.layer.open({
		type: 2, 
		title:"标签列表",
		area: ['375px', '380px'],
		content: "/user/get_labels/uid/"+uid
	});
}
</script>
<script>
var clipboard = new ClipboardJS('.copy_btn');

clipboard.on('success', function(e) {
    //console.info('Action:', e.action);
    //console.info('Text:', e.text);
    //console.info('Trigger:', e.trigger);
	layer.msg("复制成功");
    e.clearSelection();
});

clipboard.on('error', function(e) {
    console.error('Action:', e.action);
    console.error('Trigger:', e.trigger);
	layer.msg("复制失败",{icon:2});
});
</script>