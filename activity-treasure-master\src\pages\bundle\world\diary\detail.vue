<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getDiaryDetail, deleteDiary } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import customNavbar from '@/components/customNavbar.vue';

// 🔧 移除点赞和评论相关导入

// 状态管理
const diary = ref(null);
const loading = ref(true);
const diaryId = ref('');
const isAuthor = ref(false);

// 获取URL参数
onLoad((options) => {
  if (options.id) {
    diaryId.value = options.id;
    loadDiaryDetail();
  } else {
    uni.showToast({ title: '参数错误', icon: 'none' });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 加载日记详情
const loadDiaryDetail = async () => {
  try {
    loading.value = true;
    
    const res = await getDiaryDetail({
      id: diaryId.value,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      diary.value = res.data;
      // 检查是否是作者
      isAuthor.value = diary.value.user_id === store().$state.userInfo?.uid;
    } else {
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  } catch (error) {
    console.error('加载日记详情失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 安全解析位置信息
const getLocationName = (locationStr) => {
  if (!locationStr) return '';

  try {
    // 如果是JSON字符串，尝试解析
    if (typeof locationStr === 'string' && locationStr.startsWith('{')) {
      const locationObj = JSON.parse(locationStr);
      return locationObj.name || locationObj.address || '未知位置';
    }
    // 如果是普通字符串，直接返回
    return locationStr;
  } catch (error) {
    console.warn('位置信息解析失败:', error);
    return locationStr || '未知位置';
  }
};

// 编辑日记
const editDiary = () => {
  navto(`/pages/bundle/world/diary/post?id=${diaryId.value}`);
};

// 删除日记
const deleteDiaryAction = () => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这篇日记吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deleteDiary({
            id: diaryId.value,
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token
          });
          
          if (result.status === 'ok') {
            uni.showToast({ title: '删除成功', icon: 'success' });
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          } else {
            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });
          }
        } catch (error) {
          console.error('删除日记失败:', error);
          uni.showToast({ title: '删除失败', icon: 'none' });
        }
      }
    }
  });
};

// 预览图片
const previewImage = (current, images) => {
  uni.previewImage({
    current: current,
    urls: images
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<template>
  <view class="diary-detail-page">
    <!-- 统一导航栏 -->
    <customNavbar title="日记详情" @back="goBack">
      <template #right>
        <view v-if="isAuthor" class="action-buttons">
          <view class="action-btn" @click="editDiary">
            <u-icon name="edit-pen" size="40rpx" color="#ffffff"></u-icon>
          </view>
          <view class="action-btn" @click="deleteDiaryAction">
            <u-icon name="trash" size="40rpx" color="#ffffff"></u-icon>
          </view>
        </view>
      </template>
    </customNavbar>

    <!-- 主要内容 -->
    <scroll-view scroll-y class="main-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 日记内容 -->
      <view v-else-if="diary" class="diary-content">
        <!-- 用户信息 -->
        <view class="diary-header">
          <image 
            :src="diary.user?.avatar_url || '/static/default-avatar.png'" 
            class="user-avatar"
            mode="aspectFill"
          ></image>
          <view class="user-info">
            <text class="user-nickname">{{ diary.user?.nickname || '匿名用户' }}</text>
            <text class="diary-time">{{ formatTime(diary.created_at) }}</text>
          </view>
          <!-- 私密标识 -->
          <view v-if="diary.privacy === 'private'" class="privacy-badge">
            <u-icon name="lock" size="14" color="#999"></u-icon>
            <text class="privacy-text">私密</text>
          </view>
        </view>

        <!-- 日记标题 -->
        <view v-if="diary.title" class="diary-title">
          <text class="title-text">{{ diary.title }}</text>
        </view>

        <!-- 日记正文 -->
        <view class="diary-text">
          <text class="content-text">{{ diary.content }}</text>
        </view>

        <!-- 图片展示 -->
        <view v-if="diary.images && diary.images.length > 0" class="diary-images">
          <image 
            v-for="(img, index) in diary.images" 
            :key="index"
            :src="img"
            class="diary-image"
            mode="aspectFill"
            @click="previewImage(img, diary.images)"
          ></image>
        </view>

        <!-- 位置信息 -->
        <view v-if="diary.location" class="diary-location">
          <u-icon name="map" size="16" color="#6AC086"></u-icon>
          <text class="location-text">{{ getLocationName(diary.location) }}</text>
        </view>

        <!-- 标签 -->
        <view v-if="diary.tags" class="diary-tags">
          <view 
            v-for="tag in diary.tags.split(',')" 
            :key="tag"
            class="tag-item"
          >
            <text class="tag-text">#{{ tag.trim() }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.diary-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
  padding-top: var(--status-bar-height, 44rpx);
  border-bottom: 1rpx solid rgba(237, 237, 237, 0.8);
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;

  .navbar-left,
  .navbar-right {
    width: 120rpx;
    display: flex;
    align-items: center;
    height: 88rpx;
  }

  .navbar-left {
    justify-content: flex-start;
  }

  .navbar-right {
    justify-content: flex-end;

    .action-buttons {
      display: flex;
      gap: 20rpx;

      .action-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #f8f9fa;
      }
    }
  }

  .navbar-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1a1a1a;
  }
}

.main-content {
  flex: 1;
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx) + 30rpx);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.diary-content {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.diary-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }
  
  .user-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .diary-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .privacy-badge {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    
    .privacy-text {
      font-size: 22rpx;
      color: #999;
      margin-left: 8rpx;
    }
  }
}

.diary-title {
  margin-bottom: 24rpx;
  
  .title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
  }
}

.diary-text {
  margin-bottom: 32rpx;
  
  .content-text {
    font-size: 32rpx;
    line-height: 1.8;
    color: #333;
    white-space: pre-wrap;
  }
}

.diary-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
  
  .diary-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
  }
}

.diary-location {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  
  .location-text {
    font-size: 26rpx;
    color: #6AC086;
    margin-left: 8rpx;
  }
}

.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  
  .tag-item {
    padding: 8rpx 16rpx;
    background: #f0f8f4;
    border-radius: 20rpx;
    
    .tag-text {
      font-size: 24rpx;
      color: #6AC086;
    }
  }
}
</style>
