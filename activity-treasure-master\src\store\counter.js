import { navto } from "@/utils";

export const state = () => {
  return {
    windowHeight: 0,
    config: { config: {}, img_config: {} },
    userInfo: {
      // uid: "12",
      // token: "f61f4c5067f1142996b1e2f8618db75b"
    },
    pid: "",
    url: "https://api.linqingkeji.com/img/", // 图片地址
    level: {}, // 级别
    addr: {},
    edit: {}, // 修改内容
    goodsList: [],
    goodInfo: {},
    activeTypeList: [],
    activeForm: {},
    addActiveForm: {},
    popContent: {},
    flag: false,
    // 世界模块状态管理（持久化）
    worldState: {
      feedLikes: {}, // 动态点赞状态 {feedId: {isLiked: boolean, likeCount: number}}
      cardLikes: {}, // 日卡点赞状态 {cardId: {isLiked: boolean, likeCount: number}}
      feedFavorites: {}, // 动态收藏状态 {feedId: boolean}
      cardFavorites: {}, // 日卡收藏状态 {cardId: boolean}
      commentLikes: {}, // 评论点赞状态 {commentId: {isLiked: boolean, likeCount: number}}
      lastSyncTime: 0 // 最后同步时间
    },
    // 活动模块状态管理
    activityState: {
      enrollments: {}, // 活动报名状态 {activityId: {isEnrolled: boolean, orderId: string, payStatus: number}}
      favorites: {}, // 活动收藏状态 {activityId: boolean}
      likes: {}, // 活动点赞状态 {activityId: {isLiked: boolean, likeCount: number}}
      shares: {}, // 活动分享状态 {activityId: {shareCount: number, lastShareTime: timestamp}}
      drafts: {}, // 活动发布草稿 {draftId: {formData: object, lastSaved: timestamp}}
      cache: {}, // 活动详情缓存 {activityId: {data: object, cacheTime: timestamp}}
      listCache: {} // 活动列表缓存 {cacheKey: {data: array, cacheTime: timestamp}}
    }
  };
};
export const actions = {
  // 获取视口高度
  changeWindowHeight(windowHeight) {
    this.windowHeight = windowHeight;
  },
  // 改变用户信息
  changeUserInfo(userInfo) {
    this.userInfo = userInfo;
  },

  // 清除用户信息
  clearUserInfo() {
    this.userInfo = {};
    console.log('Store中的用户信息已清除');
  },

  // 更新用户信息（兼容updateUserInfo调用）
  updateUserInfo(userInfo) {
    this.userInfo = userInfo;
    console.log('Store中的用户信息已更新');
  },
  // 改变APP配置信息
  changeConfig(e) {
    this.config = e;
  },
  // 改变pid
  changePid(e) {
    this.pid = e;
  },
  // 改变等级配置
  changeLevel(e) {
    this.level = e;
  },
  changeAddr(e) {
    this.addr = e;
  },
  changeActiveTypeList(e) {
    this.activeTypeList = e;
  },
  editActive(e, go) {
    this.edit = e;
    if (go) navto(`/pages/bundle/index/addActive?type=edit`);
  },
  changeGoods(e, vip) {
    this.goodsList = e;
    if (e)
      navto(
        vip
          ? `/pages/bundle/common/confirmOrder?vip=${vip}`
          : `/pages/bundle/common/confirmOrder`
      );
  },
  setGoodInfo(e, is_pingjia, vip) {
    this.goodInfo = e;
    if (e) {
      if (is_pingjia) navto(`/pages/bundle/user/evaluate`);
      else
        navto(
          vip
            ? `/pages/bundle/user/myOrderInfo?vip=${vip}`
            : `/pages/bundle/user/myOrderInfo`
        );
    }
  },
  setActiveForm(e, title, id) {
    this.activeForm = e;
    navto(`/pages/bundle/index/moreActiveList?title=${title}&id=${id || 0}`);
  },
  setAddActiveFomr(e) {
    this.addActiveForm = e;
  },
  setPopContent(e) {
    this.popContent = e;
    navto("/components/modal", "rel");
  },

  // 世界模块状态管理actions
  // 更新动态点赞状态
  updateFeedLike(feedId, isLiked, likeCount) {
    this.worldState.feedLikes[feedId] = {
      isLiked: isLiked,
      likeCount: likeCount,
      timestamp: Date.now()
    };
    // 持久化到本地存储
    this.saveWorldStateToLocal();
  },

  // 更新日卡点赞状态
  updateCardLike(cardId, isLiked, likeCount) {
    this.worldState.cardLikes[cardId] = {
      isLiked: isLiked,
      likeCount: likeCount,
      timestamp: Date.now()
    };
    this.saveWorldStateToLocal();

    // 触发状态同步事件
    this.notifyStateChange('cardLike', { cardId, isLiked, likeCount });
  },

  // 更新动态收藏状态
  updateFeedFavorite(feedId, isFavorited) {
    this.worldState.feedFavorites[feedId] = {
      isFavorited: isFavorited,
      timestamp: Date.now()
    };
    this.saveWorldStateToLocal();
  },

  // 更新日卡收藏状态
  updateCardFavorite(cardId, isFavorited) {
    this.worldState.cardFavorites[cardId] = {
      isFavorited: isFavorited,
      timestamp: Date.now()
    };
    this.saveWorldStateToLocal();
  },

  // 获取动态点赞状态
  getFeedLikeState(feedId) {
    return this.worldState.feedLikes[feedId] || null;
  },

  // 获取日卡点赞状态
  getCardLikeState(cardId) {
    return this.worldState.cardLikes[cardId] || null;
  },

  // 获取动态收藏状态
  getFeedFavoriteState(feedId) {
    const state = this.worldState.feedFavorites[feedId];
    return state ? state.isFavorited : false;
  },

  // 获取日卡收藏状态
  getCardFavoriteState(cardId) {
    const state = this.worldState.cardFavorites[cardId];
    return state ? state.isFavorited : false;
  },

  // 状态同步通知机制
  notifyStateChange(type, data) {
    // 使用uni.$emit触发全局事件
    uni.$emit('stateChange', { type, data, timestamp: Date.now() });
  },

  // 批量更新状态（减少存储操作）
  batchUpdateStates(updates) {
    updates.forEach(update => {
      const { type, id, data } = update;
      switch (type) {
        case 'cardLike':
          this.worldState.cardLikes[id] = data;
          break;
        case 'feedLike':
          this.worldState.feedLikes[id] = data;
          break;
        case 'cardFavorite':
          this.worldState.cardFavorites[id] = data;
          break;
        case 'feedFavorite':
          this.worldState.feedFavorites[id] = data;
          break;
      }
    });

    // 只保存一次到本地存储
    this.saveWorldStateToLocal();

    // 触发批量同步事件
    this.notifyStateChange('batchUpdate', updates);
  },

  // 状态一致性检查
  validateStateConsistency() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理过期状态
    Object.keys(this.worldState.cardLikes).forEach(cardId => {
      if (now - this.worldState.cardLikes[cardId].timestamp > maxAge) {
        delete this.worldState.cardLikes[cardId];
      }
    });

    Object.keys(this.worldState.feedLikes).forEach(feedId => {
      if (now - this.worldState.feedLikes[feedId].timestamp > maxAge) {
        delete this.worldState.feedLikes[feedId];
      }
    });

    this.saveWorldStateToLocal();
  },

  // 保存世界模块状态到本地存储
  saveWorldStateToLocal() {
    try {
      uni.setStorageSync('worldState', JSON.stringify(this.worldState));
    } catch (error) {
      console.error('保存世界状态失败:', error);
    }
  },

  // 从本地存储加载世界模块状态
  loadWorldStateFromLocal() {
    try {
      const savedState = uni.getStorageSync('worldState');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        // 清理过期数据（超过24小时）
        const now = Date.now();
        const expireTime = 24 * 60 * 60 * 1000; // 24小时

        Object.keys(parsedState.feedLikes || {}).forEach(feedId => {
          if (now - (parsedState.feedLikes[feedId].timestamp || 0) > expireTime) {
            delete parsedState.feedLikes[feedId];
          }
        });

        Object.keys(parsedState.cardLikes || {}).forEach(cardId => {
          if (now - (parsedState.cardLikes[cardId].timestamp || 0) > expireTime) {
            delete parsedState.cardLikes[cardId];
          }
        });

        this.worldState = { ...this.worldState, ...parsedState };
      }
    } catch (error) {
      console.error('加载世界状态失败:', error);
    }
  },

  // 更新评论点赞状态
  updateCommentLike(commentId, isLiked, likeCount) {
    this.worldState.commentLikes[commentId] = {
      isLiked: isLiked,
      likeCount: likeCount
    };
  },

  // 获取评论点赞状态
  getCommentLikeState(commentId) {
    return this.worldState.commentLikes[commentId] || null;
  },

  // 活动模块状态管理actions
  // 更新活动报名状态
  updateActivityEnrollment(activityId, isEnrolled, orderId = null, payStatus = 0) {
    this.activityState.enrollments[activityId] = {
      isEnrolled: isEnrolled,
      orderId: orderId,
      payStatus: payStatus,
      updateTime: Date.now()
    };
    // 同步到本地存储
    this.saveActivityStateToLocal();
  },

  // 更新活动收藏状态
  updateActivityFavorite(activityId, isFavorited) {
    this.activityState.favorites[activityId] = isFavorited;
    this.saveActivityStateToLocal();
  },

  // 更新活动点赞状态
  updateActivityLike(activityId, isLiked, likeCount) {
    this.activityState.likes[activityId] = {
      isLiked: isLiked,
      likeCount: likeCount,
      updateTime: Date.now()
    };
    this.saveActivityStateToLocal();
  },

  // 更新活动分享状态
  updateActivityShare(activityId, shareCount = 0) {
    this.activityState.shares[activityId] = {
      shareCount: shareCount,
      lastShareTime: Date.now()
    };
    this.saveActivityStateToLocal();
  },

  // 保存活动草稿
  saveActivityDraft(draftId, formData) {
    this.activityState.drafts[draftId] = {
      formData: formData,
      lastSaved: Date.now()
    };
    this.saveActivityStateToLocal();
  },

  // 删除活动草稿
  removeActivityDraft(draftId) {
    delete this.activityState.drafts[draftId];
    this.saveActivityStateToLocal();
  },

  // 缓存活动详情
  cacheActivityDetail(activityId, data) {
    this.activityState.cache[activityId] = {
      data: data,
      cacheTime: Date.now()
    };
  },

  // 缓存活动列表
  cacheActivityList(cacheKey, data) {
    this.activityState.listCache[cacheKey] = {
      data: data,
      cacheTime: Date.now()
    };
  },

  // 获取活动报名状态
  getActivityEnrollmentState(activityId) {
    return this.activityState.enrollments[activityId] || null;
  },

  // 获取活动收藏状态
  getActivityFavoriteState(activityId) {
    return this.activityState.favorites[activityId] || false;
  },

  // 获取活动点赞状态
  getActivityLikeState(activityId) {
    return this.activityState.likes[activityId] || null;
  },

  // 获取活动分享状态
  getActivityShareState(activityId) {
    return this.activityState.shares[activityId] || null;
  },

  // 获取活动草稿
  getActivityDraft(draftId) {
    return this.activityState.drafts[draftId] || null;
  },

  // 获取缓存的活动详情
  getCachedActivityDetail(activityId, maxAge = 5 * 60 * 1000) { // 默认5分钟缓存
    const cached = this.activityState.cache[activityId];
    if (cached && (Date.now() - cached.cacheTime) < maxAge) {
      return cached.data;
    }
    return null;
  },

  // 获取缓存的活动列表
  getCachedActivityList(cacheKey, maxAge = 2 * 60 * 1000) { // 默认2分钟缓存
    const cached = this.activityState.listCache[cacheKey];
    if (cached && (Date.now() - cached.cacheTime) < maxAge) {
      return cached.data;
    }
    return null;
  },

  // 保存活动状态到本地存储
  saveActivityStateToLocal() {
    try {
      const stateToSave = {
        enrollments: this.activityState.enrollments,
        favorites: this.activityState.favorites,
        likes: this.activityState.likes,
        shares: this.activityState.shares,
        drafts: this.activityState.drafts
      };
      uni.setStorageSync('activityState', stateToSave);
    } catch (error) {
      console.error('保存活动状态到本地存储失败:', error);
    }
  },

  // 从本地存储恢复活动状态
  loadActivityStateFromLocal() {
    try {
      const savedState = uni.getStorageSync('activityState');
      if (savedState) {
        this.activityState.enrollments = savedState.enrollments || {};
        this.activityState.favorites = savedState.favorites || {};
        this.activityState.likes = savedState.likes || {};
        this.activityState.shares = savedState.shares || {};
        this.activityState.drafts = savedState.drafts || {};
      }
    } catch (error) {
      console.error('从本地存储恢复活动状态失败:', error);
    }
  },

  // 清理过期的缓存数据 - 修复：增强缓存清理机制
  cleanExpiredActivityCache() {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30分钟
    let cleanedCount = 0;

    // 清理活动详情缓存
    Object.keys(this.activityState.cache).forEach(activityId => {
      if (now - this.activityState.cache[activityId].cacheTime > maxAge) {
        delete this.activityState.cache[activityId];
        cleanedCount++;
      }
    });

    // 清理活动列表缓存
    Object.keys(this.activityState.listCache).forEach(cacheKey => {
      if (now - this.activityState.listCache[cacheKey].cacheTime > maxAge) {
        delete this.activityState.listCache[cacheKey];
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个过期缓存项`);
    }
  },

  // 启动自动缓存清理定时器
  startCacheCleanupTimer() {
    // 每10分钟清理一次过期缓存
    setInterval(() => {
      this.cleanExpiredActivityCache();
      this.cleanExpiredOfflineData();
    }, 10 * 60 * 1000);
    console.log('自动缓存清理定时器已启动');
  },

  // 网络状态管理
  networkState: {
    isOnline: true,
    lastOnlineTime: Date.now(),
    offlineQueue: [], // 离线操作队列
    retryCount: 0,
    maxRetries: 3
  },

  // 更新网络状态
  updateNetworkStatus(isOnline) {
    const wasOffline = !this.networkState.isOnline;
    this.networkState.isOnline = isOnline;

    if (isOnline) {
      this.networkState.lastOnlineTime = Date.now();
      if (wasOffline) {
        console.log('网络已恢复，处理离线队列');
        this.processOfflineQueue();
      }
    } else {
      console.log('网络已断开，启用离线模式');
    }
  },

  // 添加离线操作到队列
  addToOfflineQueue(operation) {
    this.networkState.offlineQueue.push({
      ...operation,
      timestamp: Date.now(),
      retryCount: 0
    });
    console.log('操作已添加到离线队列:', operation.type);
  },

  // 处理离线队列
  async processOfflineQueue() {
    if (!this.networkState.isOnline || this.networkState.offlineQueue.length === 0) {
      return;
    }

    const queue = [...this.networkState.offlineQueue];
    this.networkState.offlineQueue = [];

    for (const operation of queue) {
      try {
        await this.executeOfflineOperation(operation);
        console.log('离线操作执行成功:', operation.type);
      } catch (error) {
        console.error('离线操作执行失败:', operation.type, error);

        // 重试机制
        if (operation.retryCount < this.networkState.maxRetries) {
          operation.retryCount++;
          this.networkState.offlineQueue.push(operation);
        } else {
          console.error('离线操作最终失败，已放弃:', operation.type);
        }
      }
    }
  },

  // 执行离线操作
  async executeOfflineOperation(operation) {
    switch (operation.type) {
      case 'activity_like':
        // 重新执行点赞操作
        const likeApi = operation.data.isLiked
          ? huodongzan_add({ huodong_id: operation.data.activityId })
          : huodongzan_del({ huodong_ids: operation.data.activityId });
        await likeApi;
        break;

      case 'activity_favorite':
        // 重新执行收藏操作
        const favoriteApi = operation.data.isFavorited
          ? huodongshoucang_add({ huodong_id: operation.data.activityId })
          : huodongshoucang_del({ huodong_ids: operation.data.activityId });
        await favoriteApi;
        break;

      case 'activity_share':
        // 重新执行分享统计
        await userfenxiang_event({
          type: 2,
          item_id: operation.data.activityId,
          event_type: operation.data.eventType
        });
        break;

      case 'draft_save':
        // 重新保存草稿到服务器
        // 这里可以添加服务器草稿同步逻辑
        console.log('草稿同步到服务器:', operation.data.draftId);
        break;

      default:
        console.warn('未知的离线操作类型:', operation.type);
    }
  },

  // 获取离线缓存数据
  getOfflineData(key) {
    try {
      const offlineData = uni.getStorageSync('offlineData') || {};
      return offlineData[key] || null;
    } catch (error) {
      console.error('获取离线数据失败:', error);
      return null;
    }
  },

  // 保存离线缓存数据
  saveOfflineData(key, data) {
    try {
      const offlineData = uni.getStorageSync('offlineData') || {};
      offlineData[key] = {
        data: data,
        timestamp: Date.now()
      };
      uni.setStorageSync('offlineData', offlineData);
    } catch (error) {
      console.error('保存离线数据失败:', error);
    }
  },

  // 清理过期的离线数据
  cleanExpiredOfflineData() {
    try {
      const offlineData = uni.getStorageSync('offlineData') || {};
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      Object.keys(offlineData).forEach(key => {
        if (now - offlineData[key].timestamp > maxAge) {
          delete offlineData[key];
        }
      });

      uni.setStorageSync('offlineData', offlineData);
    } catch (error) {
      console.error('清理离线数据失败:', error);
    }
  },



  // 估算内存使用量
  estimateMemoryUsage() {
    let estimatedSize = 0;

    // 估算状态管理中的数据大小
    try {
      const stateString = JSON.stringify(this.$state);
      estimatedSize += stateString.length / 1024 / 1024; // 转换为MB
    } catch (e) {
      console.warn('无法估算状态大小:', e);
    }

    // 估算本地存储大小
    try {
      const storageKeys = ['activityState', 'offlineData', 'errorLog'];
      storageKeys.forEach(key => {
        const data = uni.getStorageSync(key);
        if (data) {
          const dataString = JSON.stringify(data);
          estimatedSize += dataString.length / 1024 / 1024;
        }
      });
    } catch (e) {
      console.warn('无法估算存储大小:', e);
    }

    return Math.round(estimatedSize * 100) / 100; // 保留两位小数
  },

  // 触发内存清理 - 修复：添加自动缓存清理定时器
  triggerMemoryCleanup() {
    console.log('开始内存清理...');

    // 清理过期缓存
    this.cleanExpiredActivityCache();
    this.cleanExpiredOfflineData();



    console.log('内存清理完成');
  },

  // 用户行为分析
  behaviorAnalytics: {
    // 用户偏好分析
    userPreferences: {
      activityTypes: {}, // 活动类型偏好
      timeSlots: {}, // 时间段偏好
      locations: {}, // 地点偏好
      priceRanges: {} // 价格区间偏好
    },

    // 用户行为模式
    behaviorPatterns: {
      sessionDuration: [], // 会话时长
      pageViews: {}, // 页面浏览次数
      clickPatterns: [], // 点击模式
      searchKeywords: {} // 搜索关键词
    },

    // 记录活动浏览行为
    recordActivityView: (activity) => {
      const analytics = this.behaviorAnalytics;

      // 记录活动类型偏好
      if (activity.type_name) {
        analytics.userPreferences.activityTypes[activity.type_name] =
          (analytics.userPreferences.activityTypes[activity.type_name] || 0) + 1;
      }

      // 记录地点偏好
      if (activity.shi) {
        analytics.userPreferences.locations[activity.shi] =
          (analytics.userPreferences.locations[activity.shi] || 0) + 1;
      }

      // 记录时间段偏好
      if (activity.start_time) {
        const hour = new Date(activity.start_time).getHours();
        const timeSlot = this.getTimeSlot(hour);
        analytics.userPreferences.timeSlots[timeSlot] =
          (analytics.userPreferences.timeSlots[timeSlot] || 0) + 1;
      }

      // 记录价格区间偏好
      if (activity.money !== undefined) {
        const priceRange = this.getPriceRange(activity.money);
        analytics.userPreferences.priceRanges[priceRange] =
          (analytics.userPreferences.priceRanges[priceRange] || 0) + 1;
      }

      console.log('记录活动浏览行为:', activity.name);
    },

    // 记录搜索行为
    recordSearchBehavior: (keyword, results) => {
      const analytics = this.behaviorAnalytics;

      if (keyword && keyword.trim()) {
        analytics.behaviorPatterns.searchKeywords[keyword] = {
          count: (analytics.behaviorPatterns.searchKeywords[keyword]?.count || 0) + 1,
          resultCount: results?.length || 0,
          lastSearchTime: Date.now()
        };
      }

      console.log('记录搜索行为:', keyword, '结果数量:', results?.length || 0);
    },

    // 记录页面访问
    recordPageVisit: (pageName, duration = 0) => {
      const analytics = this.behaviorAnalytics;

      analytics.behaviorPatterns.pageViews[pageName] =
        (analytics.behaviorPatterns.pageViews[pageName] || 0) + 1;

      if (duration > 0) {
        analytics.behaviorPatterns.sessionDuration.push({
          page: pageName,
          duration: duration,
          timestamp: Date.now()
        });

        // 只保留最近100条记录
        if (analytics.behaviorPatterns.sessionDuration.length > 100) {
          analytics.behaviorPatterns.sessionDuration.shift();
        }
      }

      console.log('记录页面访问:', pageName, '停留时间:', duration);
    },

    // 记录点击行为
    recordClickBehavior: (element, context) => {
      const analytics = this.behaviorAnalytics;

      analytics.behaviorPatterns.clickPatterns.push({
        element: element,
        context: context,
        timestamp: Date.now()
      });

      // 只保留最近200次点击记录
      if (analytics.behaviorPatterns.clickPatterns.length > 200) {
        analytics.behaviorPatterns.clickPatterns.shift();
      }

      console.log('记录点击行为:', element, context);
    },

    // 获取用户偏好推荐
    getUserRecommendations: () => {
      const preferences = this.behaviorAnalytics.userPreferences;

      // 分析最喜欢的活动类型
      const favoriteActivityType = this.getTopPreference(preferences.activityTypes);

      // 分析最喜欢的地点
      const favoriteLocation = this.getTopPreference(preferences.locations);

      // 分析最喜欢的时间段
      const favoriteTimeSlot = this.getTopPreference(preferences.timeSlots);

      // 分析最喜欢的价格区间
      const favoritePriceRange = this.getTopPreference(preferences.priceRanges);

      return {
        activityType: favoriteActivityType,
        location: favoriteLocation,
        timeSlot: favoriteTimeSlot,
        priceRange: favoritePriceRange,
        confidence: this.calculateConfidence(preferences)
      };
    },

    // 获取行为分析报告
    getBehaviorReport: () => {
      const patterns = this.behaviorAnalytics.behaviorPatterns;
      const preferences = this.behaviorAnalytics.userPreferences;

      // 计算平均会话时长
      const avgSessionDuration = patterns.sessionDuration.length > 0
        ? patterns.sessionDuration.reduce((sum, session) => sum + session.duration, 0) / patterns.sessionDuration.length
        : 0;

      // 获取最常访问的页面
      const mostVisitedPage = this.getTopPreference(patterns.pageViews);

      // 获取最常搜索的关键词
      const topSearchKeywords = Object.entries(patterns.searchKeywords)
        .sort(([,a], [,b]) => b.count - a.count)
        .slice(0, 5)
        .map(([keyword, data]) => ({ keyword, count: data.count }));

      return {
        avgSessionDuration: Math.round(avgSessionDuration),
        mostVisitedPage: mostVisitedPage,
        topSearchKeywords: topSearchKeywords,
        totalClicks: patterns.clickPatterns.length,
        userPreferences: this.getUserRecommendations(),
        reportTime: Date.now()
      };
    }
  },

  // 辅助函数：获取时间段
  getTimeSlot(hour) {
    if (hour >= 6 && hour < 12) return '上午';
    if (hour >= 12 && hour < 18) return '下午';
    if (hour >= 18 && hour < 24) return '晚上';
    return '深夜';
  },

  // 辅助函数：获取价格区间
  getPriceRange(price) {
    if (price === 0) return '免费';
    if (price <= 50) return '0-50元';
    if (price <= 100) return '50-100元';
    if (price <= 200) return '100-200元';
    return '200元以上';
  },

  // 辅助函数：获取最高偏好
  getTopPreference(preferences) {
    const entries = Object.entries(preferences);
    if (entries.length === 0) return null;

    return entries.reduce((max, current) =>
      current[1] > max[1] ? current : max
    )[0];
  },

  // 辅助函数：计算置信度
  calculateConfidence(preferences) {
    const totalInteractions = Object.values(preferences.activityTypes).reduce((sum, count) => sum + count, 0);

    if (totalInteractions < 5) return 'low';
    if (totalInteractions < 20) return 'medium';
    return 'high';
  }
};
