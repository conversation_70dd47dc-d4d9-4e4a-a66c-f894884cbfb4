
//初始化form
var layui_form;
layui.use('form', function(){
  layui_form = layui.form;
});
//打印
function doPrint(el){
	var bak = $("body").html();
	$(el + " .laytable-cell-checkbox").parent("td").remove();
	$(el + " .layui-table-col-special").parent("td").remove();
	$(el + " .layui-table-col-special").remove();
	$("body").html($(el).html());   
	window.print();  	
	$("body").html(bak);
}
//导出
function daochu(){
	$("#daochu").val("1");
	$("#search").submit();
}
function submit_(){
	$("#daochu").val("0");
	$("#search").submit();
}
//分页
function change_page_number(select_name){
	$("#search > input[name='page']").val($(select_name).val());
	$("#search").submit();
}
function change_page_size(new_page_size){
	$("#search > input[name='page_size']").val(new_page_size);
	$("#search").submit();
}
//确认
function confirm_(url,msg=""){
	if(msg == "")msg = "确定操作？";
	layui.layer.confirm(msg,{title:"提示"},function(index){
		layui.layer.close(index);
		window.location.href = url;
	});
}
//
$(document).ready(function(){
	//table 设置高度
	var table_height = $(document).height() - $(".layui-header").outerHeight(true) - $(".table-search-fieldset").outerHeight(true);
	if(table_height < 200)table_height = 200;
	$(".table_height").css({height:table_height});
	//
	//选中/全选 全选input 加入属性 quanxuan=name[]
	$(".layui-form-checkbox").on("click",function(){
		//
		var mark = $(this).prev(":checkbox");
		var mark_id = $(mark).prop("id");
		if(mark_id != "" && mark_id.indexOf("quanxuan_") === 0){
			var quanxuan_name = mark_id.substring(9);
			if(quanxuan_name != ""){
				var check = $(mark).prop("checked");
				if(check){
					$("input[name='"+quanxuan_name+"']").prop("checked", true);
					$("input[name='"+quanxuan_name+"']").next(".layui-form-checkbox").addClass("layui-form-checked");
				}else{
					$("input[name='"+quanxuan_name+"']").prop("checked", false);
					$("input[name='"+quanxuan_name+"']").next(".layui-form-checkbox").removeClass("layui-form-checked");
				}		
			}
		}
		
	});
	//设置select默认值
	$("select").each(function(k,v){
		var val = $(v).attr("value");
		var options = $(v)[0].options;
		for(var i=0;i<options.length;i++){
			if(options[i].value == val){
				$(v).val(val);
				break;
			}
		}
	});
	layui_form.render("select");
	//触发原标签change事件
	layui_form.on('select', function(data){
		$(data.elem).change();
	});
	//
});

$(function(){
	//全局点击事件
	$(document).bind("click",function(e){
		var target  = $(e.target);
		if(target.closest(".layui-nav-item").length == 0){
			$(".layui-nav-child").removeClass("layui-show");
		}
	});
	//ajax事件 loading
	var loadIndex = '';
	$(document).ajaxSend(function(e,xhr,opt){
		loadIndex = layui.layer.load(0, {shade: [0.1, '#fff']});
	});
	$(document).ajaxStop(function(e,xhr,opt){
		layui.layer.close(loadIndex);
	});
	//
});
//操作cookie
function setCookie(name,value,seconds){
	var exp = new Date();
	exp.setTime(exp.getTime() + seconds);
	document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString() + ";path=/";
}
function getCookie(name){
	var arr,reg = new RegExp("(^| )"+name+"=([^;]*)(;|$)");
	if(arr=document.cookie.match(reg)){
		return unescape(arr[2]);
	}else{
		return null;
	}
}
function delCookie(name){
	var exp = new Date();
	exp.setTime(exp.getTime() - 1);
	var cval = getCookie(name);
	if(cval != null){
		document.cookie= name + "="+cval+";expires="+exp.toGMTString() + ";path=/";
	}
}
//
function zoom_show_callback(obj){
	
	//console.log($(obj).attr("src"));
	$(".table_height_").css({"overflow":"unset"});
	$(obj).parent(".zoom-img-wrap").parent(".layui-table-cell").css({"overflow":"unset","z-index":"6666"});
}
function zoom_close_callback(obj){
	//console.log(2);
	$(".table_height_").css({"overflow":"hidden"});
	$(obj).parent(".layui-table-cell").css({"overflow":"hidden","z-index":"666"});
}
//
function Base64() {

    // private property
    _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    // public method for encoding
    this.encode = function (input) {
        var output = "";
        var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        var i = 0;
        input = _utf8_encode(input);
        while (i < input.length) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }
            output = output +
                _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
                _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
        }
        return output;
    }

    // public method for decoding
    this.decode = function (input) {
        var output = "";
        var chr1, chr2, chr3;
        var enc1, enc2, enc3, enc4;
        var i = 0;
        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
        while (i < input.length) {
            enc1 = _keyStr.indexOf(input.charAt(i++));
            enc2 = _keyStr.indexOf(input.charAt(i++));
            enc3 = _keyStr.indexOf(input.charAt(i++));
            enc4 = _keyStr.indexOf(input.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = _utf8_decode(output);
        return output;
    }

    // private method for UTF-8 encoding
    _utf8_encode = function (string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }

        }
        return utftext;
    }

    // private method for UTF-8 decoding
    _utf8_decode = function (utftext) {
        var string = "";
        var i = 0;
        var c = c1 = c2 = 0;
        while (i < utftext.length) {
            c = utftext.charCodeAt(i);
            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            } else if ((c > 191) && (c < 224)) {
                c2 = utftext.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            } else {
                c2 = utftext.charCodeAt(i + 1);
                c3 = utftext.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }
        return string;
    }
}
/*
navigator.geolocation.getCurrentPosition(function(position){
	if(position && position.coords.latitude && position.coords.longitude){
		var wgs84_lat = position.coords.latitude;
		var wgs84_lng = position.coords.longitude;
		if(wgs84_lng && wgs84_lat){
			//WGS84坐标(GPS坐标)
			document.getElementById("wgs84").innerHTML = wgs84_lng + "," + wgs84_lat;
			//gcj20坐标
			var gcj = WGS84ToGCJ(wgs84_lng,wgs84_lat);
			document.getElementById("gaode").innerHTML = gcj.lng + "," + gcj.lat;
			//bd09坐标
			var bd = gcj2bd(gcj.lng,gcj.lat);
			document.getElementById("baidu").innerHTML = bd.lng + "," + bd.lat;
			//
		}
	}
}, function(error){
	if(error && error.message){
		document.getElementById("position").innerHTML = error.message;
	}
});
*/
//WGS84转GCJ02 坐标(高德、腾讯)
function WGS84ToGCJ(lng,lat){
    var pi = 3.14159265358979324
    var ee = 0.00669342162296594323
    var a = 6378245.0
	var x = lng-105.0;
	var y = lat-35.0;
	var dLat = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x)) + (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0 + (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0 + (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
	var dLon = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x)) + (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0 + (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0 + (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
    let radLat = lat / 180.0 * pi;
    var magic = Math.sin(radLat);
    magic = 1 - ee * magic * magic;
    var sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((a * (1-ee)) / (magic * sqrtMagic) * pi);  
    dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi); 
    var mgLat = parseFloat(lat) + parseFloat(dLat);  
    var mgLon = parseFloat(lng) + parseFloat(dLon); 
    return {lng:mgLon,lat:mgLat};
};
//GCJ02转BD09坐标(百度)
function gcj2bd(lng,lat) {
	var x = lng, y = lat;
	var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * Math.PI);
	var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * Math.PI);
	var tempLon = z * Math.cos(theta) + 0.0065;
	var tempLat = z * Math.sin(theta) + 0.006;
    return {lng:tempLon,lat:tempLat};
}