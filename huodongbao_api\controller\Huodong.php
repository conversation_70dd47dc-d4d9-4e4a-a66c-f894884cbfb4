<?php

namespace controller;

use core\Controller;
use core\Db;

/*
 * @className 活动
*/

class Huodong extends Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 统一错误处理方法
     */
    private function handleError($message, $logMessage = null)
    {
        if ($logMessage) {
            $this->exception_log($logMessage);
        }
        return ["status" => "error", "msg" => $message];
    }

    /**
     * 验证位置数据的公共方法
     * @param int $is_online 是否线上活动
     * @param int $sheng_id 省份ID
     * @param int $shi_id 城市ID
     * @param int $qu_id 区县ID
     * @param string $addr 详细地址
     * @param float $lng 经度
     * @param float $lat 纬度
     * @return array 验证结果
     */
    private function validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat)
    {
        // 初始化位置变量
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;

        if ($is_online == 0) {
            // 线下活动需要验证位置信息
            if (
                empty($sheng_id) || !check($sheng_id, "intgt0") ||
                empty($shi_id) || !check($shi_id, "intgt0") ||
                empty($qu_id) || !check($qu_id, "intgt0") ||
                empty($addr) ||
                empty($lng) || !is_numeric($lng) || $lng <= 0 ||
                empty($lat) || !is_numeric($lat) || $lat <= 0
            ) {
                return $this->handleError("参数错误(位置)");
            }

            // 获取省份信息
            $sheng = Db()->table("china")->where("id={$sheng_id} AND deep=1")->getColumn("name");
            if (empty($sheng)) {
                return $this->handleError("获取省份信息失败");
            }

            // 获取城市信息
            $shi = Db()->table("china")->where("id={$shi_id} AND pid={$sheng_id} AND deep=2")->getColumn("name");
            if (empty($shi)) {
                return $this->handleError("获取市信息失败");
            }

            // 获取区县信息
            $qu = Db()->table("china")->where("id={$qu_id} AND pid={$shi_id} AND deep=3")->getColumn("name");
            if (empty($qu)) {
                return $this->handleError("获取县区信息失败");
            }
        } else {
            // 线上活动设置默认值
            $sheng_id = 0;
            $shi_id = 0;
            $qu_id = 0;
            $addr = '';
            $lng = 0;
            $lat = 0;
        }

        return [
            "status" => "ok",
            "data" => [
                "sheng" => $sheng,
                "shi" => $shi,
                "qu" => $qu,
                "sheng_id" => $sheng_id,
                "shi_id" => $shi_id,
                "qu_id" => $qu_id,
                "addr" => $addr,
                "lng" => $lng,
                "lat" => $lat
            ]
        ];
    }

    /*
	* @apiName 活动分类
	* @method get_type
	* @GET
	* @return {"status":"ok","data":[{"id":1,"name":"分类1","icon":"http:\/\/************\/test.jpg"},{"id":2,"name":"分类2","icon":"http:\/\/************\/test.jpg"},{"id":3,"name":"分类3","icon":"http:\/\/************\/test.jpg"}]}
	*/
    public function get_type()
    {

        $data = \core\Cache::getCache("huodong_get_type");
        if (!empty($data)) return ["status" => "ok", "data" => $data];
        dbConn();
        $where = "1";
        $data = Db()->table("huodong_type")->where($where)->fetchAll();
        foreach ($data as &$item) {
            $item['icon'] = strip_tags($item['icon']); // 去除 HTML 标签
        }
        if (empty($data)) {
            return ["status" => "empty"];
        } else {
            //\core\Cache::getCache("huodong_get_type",$data,600);
            return ["status" => "ok", "data" => $data];
        }
    }

    /*
	* @apiName 添加活动信息
	* @method add_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param name string 名称
	* @param title string 标题
	* @param type_id string 分类编号
	* @param img_url string 列表图
	* @param money string 费用
	* @param pay_type string 收费方式:1=线上,2=线下
	* @param contents string 介绍
	* @param liucheng json 流程json['111','222']
	* @param guige string 规格
	* @param num string 人数
	* @param baoming_start_time string 报名开始时间
	* @param baoming_end_time string 报名结束时间
	* @param start_time string 活动开始时间
	* @param end_time string 活动结束时间
	* @param sheng_id string 省编号
	* @param shi_id string 市编号
	* @param qu_id string 县区编号
	* @param addr string 具体位置
	* @param lng string 地图坐标经度
	* @param lat string 地图坐标纬度
	* @param yongjin_bili string 邀请佣金比例，5即5%
	* @param imgs_url string 图片集逗号隔开
	* @param is_choujiang string 是否抽奖:1=是,0=否
	* @param choujiang_huiyuan_num string 抽奖会员需达到数量
	* @param choujiang_zhongjiang_num string 抽奖中奖人数
	* @param choujiang_zhekou string 中奖折扣:0-99,0=免单,5=0.5折,60=6折
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人电话
	* @param lianxi_qrcode string 联系客服微信二维码地址
	* @param is_online string 是否线上:1=是,0=否
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function add_huodong(
        $uid,
        $token,
        $name,
        $title,
        $type_id,
        $img_url,
        $money,
        $contents,
        $num,
        $start_time,
        $sheng_id,
        $shi_id,
        $qu_id,
        $addr,
        $lng,
        $lat,
        $guige = "",
        $imgs_url = "",
        $is_choujiang = 0,
        $choujiang_huiyuan_num = 1,
        $choujiang_zhongjiang_num = 1,
        $choujiang_zhekou = 0,
        $lianxi_name = "",
        $lianxi_mobile = "",
        $lianxi_qrcode = "",
        $qun_qrcode = "",
        $pay_type = 1,
        $is_online = 0,
        $member_money = 0.00,
        $yongjin_bili = 0  // 添加佣金比例参数，默认为0（不使用佣金功能）
    )
    {
        $is_online = (int)$is_online; // Cast to integer

        // Basic parameter checks (non-location)
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($name) ||
            empty($title) ||
            empty($img_url) ||
            !check($img_url, "url") ||
            !is_numeric($money) ||
            !is_numeric($member_money) ||
            empty($contents) ||
            empty($num) ||
            !check($num, "intgt0") ||
            empty($type_id) ||
            !check($type_id, "intgt0") ||
            empty($start_time) ||
            !check($start_time, "datetime") ||
            empty($lianxi_name) ||
            empty($lianxi_qrcode) || !check($lianxi_qrcode, "url") ||
            empty($qun_qrcode) || !check($qun_qrcode, "url") ||
            !in_array($is_online, [0, 1])
        ) {
            return $this->handleError("参数错误(基础)", "活动发布参数验证失败 - uid: {$uid}");
        }

        // Initialize location variables
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;

        // Location specific checks and lookups for offline events
        $locationResult = $this->validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat);
        if ($locationResult['status'] !== 'ok') {
            return $locationResult;
        }

        // Extract validated location data
        $sheng = $locationResult['data']['sheng'];
        $shi = $locationResult['data']['shi'];
        $qu = $locationResult['data']['qu'];
        $sheng_id = $locationResult['data']['sheng_id'];
        $shi_id = $locationResult['data']['shi_id'];
        $qu_id = $locationResult['data']['qu_id'];
        $addr = $locationResult['data']['addr'];
        $lng = $locationResult['data']['lng'];
        $lat = $locationResult['data']['lat'];

        //
        $imgs_arr = [];
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return $this->handleError("参数错误");
                }
            }
        }
        //
        $uid = (int)$uid;
        $pay_type = (int)$pay_type;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        //
        // Check user role type
        $user_role_type = Db()->table("user")->where("uid={$uid}")->getColumn("role_type");
        if ($user_role_type !== 0) {
            $money = 0.00;        // Force fees to 0 for non-admin users
            $member_money = 0.00;
        }

        // 用户角色说明：
        // role_type = 0: 管理员
        // role_type = 1: 分会长
        // role_type = 2: 普通用户
        //
        dbConn();
        $user_info = Db()->table("user")->select("uid,mobile")->where("uid={$uid}")->fetch();


        $data = [
            "uid" => $uid,
            "money" => number_format($money, 2, ".", ""),
            "member_money" => number_format($member_money, 2, ".", ""),
            "lng" => number_format($lng, 6, ".", ""),
            "lat" => number_format($lat, 6, ".", ""),
            "type_id" => intval($type_id),
            "num" => intval($num),
            "sheng_id" => intval($sheng_id),
            "shi_id" => intval($shi_id),
            "qu_id" => intval($qu_id),
            "name" => ":name",
            "title" => ":title",
            "img_url" => ":img_url",
            "contents" => ":contents",
            "addr" => ":addr",
            "guige" => ":guige",
            "sheng" => ":sheng",
            "shi" => ":shi",
            "qu" => ":qu",
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "lianxi_qrcode" => ":lianxi_qrcode",
            "qun_qrcode" => ":qun_qrcode",
            "start_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            "end_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            // 根据用户角色设置审核状态：
            // 管理员(role_type=0): 直接通过审核 status=1
            // 分会长(role_type=1): 直接通过审核 status=1
            // 普通用户(role_type=2): 需要审核 status=0
            "status" => ($user_role_type == 2) ? 1 : 1, //测试写死生产要改成($user_role_type == 2) ? 0 : 1
            "is_online" => (int)$is_online,
        ];

        // 时间验证逻辑已移除，如需要可重新添加
        $check_type = Db()->table("huodong_type")->where("id={$data['type_id']}")->fetch();
        if (empty($check_type)) {
            return $this->handleError("活动分类不存在");
        }
        //

        //
        $prepareParam = [
            ":name" => htmlspecialchars(trim($name)),
            ":title" => htmlspecialchars(trim($title)),
            ":img_url" => htmlspecialchars(trim($img_url)),
            ":contents" => htmlspecialchars(trim($contents)),
            ":addr" => htmlspecialchars(trim($addr)),
            ":guige" => htmlspecialchars(trim($guige)),
            ":sheng" => htmlspecialchars(trim($sheng)),
            ":shi" => htmlspecialchars(trim($shi)),
            ":qu" => htmlspecialchars(trim($qu)),
            ":lianxi_name" => htmlspecialchars(trim($lianxi_name)),
            ":lianxi_mobile" => htmlspecialchars(trim($lianxi_mobile)),
            ":lianxi_qrcode" => htmlspecialchars(trim($lianxi_qrcode)),
            ":qun_qrcode" => htmlspecialchars(trim($qun_qrcode)),
//            ":liucheng" => json_encode(json_decode(stripslashes(trim($liucheng, '"')), true), JSON_UNESCAPED_UNICODE),
        ];

        dbConn();
        try {
            Db()->table("huodong")->prepareParam($prepareParam)->insert($data);
            $insert_id = Db()->insertId();
            //
            if (!empty($imgs_arr)) {
                foreach ($imgs_arr as $img) {
                    Db()->table("huodong_img")->prepareParam([":img_url" => $img])->insert(["huodong_id" => $insert_id, "img_url" => ":img_url"]);
                }
            }
            //
            return ["status" => "ok", "msg" => "操作成功,审核中"];
        } catch (\Exception $e) {
            return $this->handleError("系统繁忙，请稍后再试", $e->getMessage());
        }
    }

    /*
	* @apiName 修改活动信息
	* @method update_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param name string 名称
	* @param title string 标题
	* @param type_id string 分类编号
	* @param img_url string 列表图
	* @param money string 费用
	* @param pay_type string 收费方式:1=线上,2=线下
	* @param contents string 介绍
	* @param liucheng json 流程json
	* @param guige string 规格
	* @param num string 人数
	* @param baoming_start_time string 报名开始时间
	* @param baoming_end_time string 报名结束时间
	* @param start_time string 活动开始时间
	* @param end_time string 活动结束时间
	* @param sheng_id string 省编号
	* @param shi_id string 市编号
	* @param qu_id string 县区编号
	* @param addr string 具体位置
	* @param lng string 地图坐标经度
	* @param lat string 地图坐标纬度
	* @param yongjin_bili string 邀请佣金比例，5即5%
	* @param imgs_url string 图片集逗号隔开
	* @param is_choujiang string 是否抽奖:1=是,0=否
	* @param choujiang_huiyuan_num string 抽奖会员需达到数量
	* @param choujiang_zhongjiang_num string 抽奖中奖人数
	* @param choujiang_zhekou string 中奖折扣:0-99,0=免单,5=0.5折,60=6折
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人电话
	* @param lianxi_qrcode string 联系客服微信二维码地址
	* @param is_online string 是否线上:1=是,0=否
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function update_huodong(
        $uid,
        $token,
        $huodong_id,
        $name,
        $title,
        $type_id,
        $img_url,
        $money,
        $contents,
        $liucheng,
        $num,
        $start_time,
        $sheng_id,
        $shi_id,
        $qu_id,
        $addr,
        $lng,
        $lat,
        $yongjin_bili,
        $guige = "",
        $imgs_url = "",
        $is_choujiang = 0,
        $choujiang_huiyuan_num = 1,
        $choujiang_zhongjiang_num = 1,
        $choujiang_zhekou = 0,
        $lianxi_name = "",
        $lianxi_mobile = "",
        $lianxi_qrcode = "",
        $pay_type = 1,
        $is_online = 0,
        $member_money = 0.00
    )
    {
        $is_online = (int)$is_online; // Cast to integer

        // Basic parameter checks (non-location)
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($huodong_id) || !check($huodong_id, "intgt0") ||
            empty($name) ||
            empty($title) ||
            empty($img_url) ||
            !check($img_url, "url") ||
            !is_numeric($money) ||
            !is_numeric($member_money) ||
            empty($contents) ||
            empty($num) ||
            !check($num, "intgt0") ||
            empty($type_id) ||
            !check($type_id, "intgt0") ||
            empty($start_time) ||
            !check($start_time, "datetime") ||
            empty($lianxi_name) ||
            empty($lianxi_qrcode) || !check($lianxi_qrcode, "url") ||
            !in_array($is_online, [0, 1])
        ) return ["status" => "error", "msg" => "参数错误(基础)"];

        // Initialize location variables
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;


        // Location specific checks and lookups for offline events
        $locationResult = $this->validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat);
        if ($locationResult['status'] !== 'ok') {
            return $locationResult;
        }

        // Extract validated location data
        $sheng = $locationResult['data']['sheng'];
        $shi = $locationResult['data']['shi'];
        $qu = $locationResult['data']['qu'];
        $sheng_id = $locationResult['data']['sheng_id'];
        $shi_id = $locationResult['data']['shi_id'];
        $qu_id = $locationResult['data']['qu_id'];
        $addr = $locationResult['data']['addr'];
        $lng = $locationResult['data']['lng'];
        $lat = $locationResult['data']['lat'];

        //
        $imgs_arr = [];
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return ["status" => "error", "msg" => "参数错误"];
                }
            }
        }
        //
        $uid = (int)$uid;
        $pay_type = (int)$pay_type;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        // Check user role type
        $user_role_type = Db()->table("user")->where("uid={$uid}")->getColumn("role_type");
        if ($user_role_type !== 0) {
            $money = 0.00;        // Force fees to 0 for non-admin users
            $member_money = 0.00;
        }

        $huodong_id = (int)$huodong_id;
        dbConn();
        //
        $huodong_info = Db()->table("huodong")->select("id,baoming_num,status")->where("id={$huodong_id} AND uid={$uid}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['baoming_num'] > 0) {
            return ["status" => "error", "msg" => "活动信息已有人报名，不支持修改"];
        }
        /*
		if($huodong_info['status'] != 0){
			return ["status"=>"error","msg"=>"活动信息已不支持修改"];
		}
		*/
        $config = $this->get_config(["huodong_choujiang_start_time", "huodong_jiesuan_days"]);
        if (
            empty($config) ||
            !isset($config['huodong_choujiang_start_time']) ||
            !isset($config['huodong_jiesuan_days']) ||
            !check($config['huodong_choujiang_start_time'], "intgt0") ||
            !check($config['huodong_jiesuan_days'], "intgt0")
        ) {
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试:c"];
        }
        $huodong_choujiang_start_time = intval($config['huodong_choujiang_start_time']);
        $huodong_jiesuan_days = intval($config['huodong_jiesuan_days']);
        //
        $data = [
            "money" => number_format($money, 2, ".", ""),
            "member_money" => number_format($member_money, 2, ".", ""),
            "lng" => number_format($lng, 6, ".", ""),
            "lat" => number_format($lat, 6, ".", ""),
            "pay_type" => intval($pay_type),
            "type_id" => intval($type_id),
            "num" => intval($num),
            "yongjin_bili" => intval($yongjin_bili),
            "sheng_id" => intval($sheng_id),
            "shi_id" => intval($shi_id),
            "qu_id" => intval($qu_id),
            "name" => ":name",
            "title" => ":title",
            "img_url" => ":img_url",
            "contents" => ":contents",
            "liucheng" => ":liucheng",
            "addr" => ":addr",
            "guige" => ":guige",
            "sheng" => ":sheng",
            "shi" => ":shi",
            "qu" => ":qu",
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "lianxi_qrcode" => ":lianxi_qrcode",
            "qun_qrcode" => ":qun_qrcode",
            "start_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            "end_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            "time" => DATETIME,
            "status" => 0,
            "is_online" => (int)$is_online,
        ];
        if ($pay_type == 1) {
            $data['jiesuan_status'] = 0;
            $data['jiesuan_time'] = date("Y-m-d H:i:s", strtotime("+{$huodong_jiesuan_days} days", strtotime($data['end_time'])));
        } else {
            $data['jiesuan_status'] = 2;
        }
        //
        /*if (strtotime($data['baoming_start_time']) >= strtotime($data['baoming_end_time'])) {
            return ["status" => "error", "msg" => "报名开始和结束时间错误"];
        }
        if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
            return ["status" => "error", "msg" => "活动开始和结束时间错误"];
        }
        if (strtotime($data['baoming_end_time']) >= strtotime($data['start_time'])) {
            return ["status" => "error", "msg" => "活动时间和报名时间冲突"];
        }*/
        $check_type = Db()->table("huodong_type")->where("id={$data['type_id']}")->fetch();
        if (empty($check_type)) {
            return ["status" => "error", "msg" => "活动分类不存在"];
        }
        //
        // 注释掉佣金验证逻辑 - 活动发布功能现在不需要佣金逻辑
        // if ($pay_type == 2 && $data['yongjin_bili'] > 0) {
        //     return ["status" => "error", "msg" => "线下收费不支持佣金"];
        // }
        //
        $prepareParam = [
            ":name" => htmlspecialchars(trim($name)),
            ":title" => htmlspecialchars(trim($title)),
            ":img_url" => htmlspecialchars(trim($img_url)),
            ":contents" => htmlspecialchars(trim($contents)),
            ":addr" => htmlspecialchars(trim($addr)),
            ":guige" => htmlspecialchars(trim($guige)),
            ":sheng" => htmlspecialchars(trim($sheng)),
            ":shi" => htmlspecialchars(trim($shi)),
            ":qu" => htmlspecialchars(trim($qu)),
            ":lianxi_name" => htmlspecialchars(trim($lianxi_name)),
            ":lianxi_mobile" => htmlspecialchars(trim($lianxi_mobile)),
            ":lianxi_qrcode" => htmlspecialchars(trim($lianxi_qrcode)),
            ":liucheng" => json_encode(json_decode(stripslashes(trim($liucheng, '"')), true), JSON_UNESCAPED_UNICODE),
        ];
        if (is_null($prepareParam[':liucheng']) || $prepareParam[':liucheng'] == "null" || $prepareParam[':liucheng'] == "") {
            return ["status" => "error", "msg" => "流程参数错误"];
        }
        //
        $is_choujiang = intval($is_choujiang);
        $choujiang_huiyuan_num = intval($choujiang_huiyuan_num);
        $choujiang_zhongjiang_num = intval($choujiang_zhongjiang_num);
        $choujiang_zhekou = intval($choujiang_zhekou);
        if ($pay_type == 2 && $is_choujiang == 1) {
            return ["status" => "error", "msg" => "线下收费不支持抽奖"];
        }
        if ($is_choujiang == 1) {
            $data['choujiang_status'] = 0;
            $data['choujiang_config_json'] = json_encode([
                "huiyuan_num" => $choujiang_huiyuan_num,
                "zhongjiang_num" => $choujiang_zhongjiang_num,
                "zhekou" => $choujiang_zhekou,
            ]);
            $data['choujiang_time'] = date("Y-m-d H:i:s", strtotime("+{$huodong_choujiang_start_time} minute", strtotime($data['start_time'])));
            //
            // 注释掉佣金相关的抽奖验证逻辑 - 活动发布功能现在不需要佣金逻辑
            // if ($data['yongjin_bili'] > 0 && $choujiang_zhekou == 0) {
            //     return ["status" => "error", "msg" => "佣金和免单不能同时存在"];
            // }
            // if ($data['yongjin_bili'] + (100 - $choujiang_zhekou) > 100) {
            //     return ["status" => "error", "msg" => "佣金或抽奖比例过高"];
            // }
            //
        } else {
            $data['choujiang_status'] = 2;
            $data["choujiang_config_json"] = json_encode(new \stdClass());
        }
        //
        dbConn();
        try {
            $rowCount = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->prepareParam($prepareParam)->update($data);
            //
            Db()->table("huodong_img")->where("huodong_id={$huodong_id}")->del();
            //
            if (!empty($imgs_arr)) {
                foreach ($imgs_arr as $img) {
                    Db()->table("huodong_img")->prepareParam([":img_url" => $img])->insert(["huodong_id" => $huodong_id, "img_url" => ":img_url"]);
                }
            }
            //
            $this->user_log($uid, "编辑活动信息【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功,审核中"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消活动
	* @method cancel_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function cancel_huodong($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            !check($huodong_id, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id,baoming_num,status")->where("id={$huodong_id} AND uid={$uid}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['baoming_num'] > 0) {
            return ["status" => "error", "msg" => "活动信息已有人报名，不支持取消"];
        }
        /*
		if($huodong_info['status'] != 0){
			return ["status"=>"error","msg"=>"活动信息已不支持取消"];
		}
		*/
        // 0=>"审核中",1=>"审核通过",2=>"审核未通过",3=>"取消"
        // 这里改为：所有状态下均可取消
        try {
            // 获取活动信息
            $huodong_info = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->fetch();
            if (empty($huodong_info)) {
                return ["status" => "error", "msg" => "活动不存在"];
            }

            $result = !Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->update(["status" => 3]);
            if ($result) {
                throw new \Exception("操作失败");
            }

            // 获取所有已报名的用户，发送取消通知
            $baoming_users = Db()->table("huodong_baoming_order")
                ->select("uid")
                ->where("huodong_id={$huodong_id} AND status=1")
                ->fetchAll();

            if (!empty($baoming_users)) {
                foreach ($baoming_users as $user) {
                    $this->create_notification(
                        $user['uid'],
                        "activity_cancelled",
                        "活动取消通知",
                        "您报名的活动「{$huodong_info['title']}」已被主办方取消",
                        $huodong_id
                    );
                }
            }

            // 给活动发布者发送通知
            $this->create_notification(
                $uid,
                "activity_cancelled",
                "活动取消成功",
                "您的活动「{$huodong_info['title']}」已成功取消",
                $huodong_id
            );

            $this->user_log($uid, "取消活动【{$huodong_id}】");

            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动列表
	* @method get_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param type_id string 分类(0=全部)
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param is_tuijian string 是否推荐:1=是,0=否(可选)
	* @param keyword string 关键词(可选)
	* @param shi_id string 市级编号
	* @param qu_id string 区级编号(可选)
	* @param sort string 排序:1=默认,2=距离,3=人气(可选)
	* @param lng string 用户经度坐标(可选)
	* @param lat string 用户纬度坐标(可选)
	* @param baoming_status string 报名状态:1=未开始,2=报名中,3=报名已结束(可选)
	* @param huodong_status string 活动状态:1=未开始,2=进行中,3=已结束(可选)
	* @param baoming_date string 可报名日期(可选)
	* @param huodong_date string 活动日期(可选)
    * @param list_type string 列表类型:1=全部活动,2=线上活动,3=历史活动(可选)
	* @return {"status":"ok","data":[{"id":2,"uid":2,"name":"活动3","title":"活动标题","img_url":"http:\/\/************\/test.jpg","money":"100.00","guige":"规格1111122","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-18 11:30:00","baoming_num":0,"date":"2023-11-18","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园11","yongjin_bili":10,"distance":"0米","user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"}}],"count":1}
	*/
    public function get_list($uid = 0, $token = "", $shi_id = 0, $qu_id = 0, $page = 1, $page_size = 20, $type_id = 0, $is_tuijian = "ALL", $keyword = "", $sort = 1, $lng = 0, $lat = 0, $baoming_status = "ALL", $huodong_status = "ALL", $baoming_date = "", $huodong_date = "", $list_type = 1)
    {
        if (
            empty($page) ||
            !check($page, "intgt0") ||
            empty($page_size) ||
            !check($page_size, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];

        $page = (int)$page;
        $page_size = (int)$page_size;
        $type_id = (int)$type_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;
        $sort = (int)$sort;
        $lng = (float)$lng;
        $lat = (float)$lat;
        $list_type = (int)$list_type;

        dbConn();

        // 基础查询条件
        $where = "1 AND status=1";
        if ($type_id > 0) $where .= " AND type_id={$type_id}";
        if (!empty($keyword)) $where .= " AND (name LIKE '%{$keyword}%' OR title LIKE '%{$keyword}%')";

        // 根据list_type添加筛选条件
        switch ($list_type) {
            case 1: // 全部活动 - 显示本城市线下活动 + 所有线上活动
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time >= '{$today}'";

                // 城市筛选：本城市的线下活动 OR 所有线上活动
                if ($shi_id > 0) {
                    $city_condition = "(shi_id={$shi_id}";
                    if ($qu_id > 0) {
                        $city_condition .= " AND qu_id={$qu_id}";
                    }
                    $city_condition .= ") OR is_online = 1";
                    $where .= " AND ({$city_condition})";
                }
                break;

            case 2: // 线上活动 - 只显示线上活动，不受城市限制
                $where .= " AND is_online = 1";
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time >= '{$today}'";
                break;

            case 3: // 历史活动 - 历史活动按原逻辑，可以有城市筛选
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time <= '{$today}'";

                // 历史活动的城市筛选
                if ($shi_id > 0) $where .= " AND shi_id={$shi_id}";
                if ($qu_id > 0) $where .= " AND qu_id={$qu_id}";
                break;
        }

        // 报名状态筛选
        /*if ($baoming_status !== "ALL") {
            switch ($baoming_status) {
                case "1": // 未开始
                    $where .= " AND baoming_start_time > NOW()";
                    break;
                case "2": // 报名中
                    $where .= " AND baoming_start_time <= NOW() AND baoming_end_time >= NOW()";
                    break;
                case "3": // 报名已结束
                    $where .= " AND baoming_end_time < NOW()";
                    break;
            }
        }*/

        // 活动状态筛选
        if ($huodong_status !== "ALL") {
            switch ($huodong_status) {
                case "1": // 未开始
                    $where .= " AND CONCAT(start_time, ' ', start_time) > NOW()";
                    break;
                case "2": // 进行中
                    $where .= " AND CONCAT(start_time, ' ', start_time) <= NOW() AND CONCAT(start_time, ' ', end_time) >= NOW()";
                    break;
                case "3": // 已结束
                    $where .= " AND CONCAT(start_time, ' ', end_time) < NOW()";
                    break;
            }
        }

        // 日期筛选
        /*if (!empty($baoming_date)) {
            $where .= " AND DATE(baoming_start_time) <= '{$baoming_date}' AND DATE(baoming_end_time) >= '{$baoming_date}'";
        }*/
        if (!empty($huodong_date)) {
            $where .= " AND DATE(start_time) = '{$huodong_date}'";
        }

        // 排序
        $order = " start_time ";

        // 历史活动按开始时间倒序排列
        if ($list_type == 3) {
            $order = " start_time DESC ";
        }

        /* switch ($sort) {
             case 2: // 距离
                 if ($lng > 0 && $lat > 0) {
                     $order = "SQRT(POW(lng-{$lng},2)+POW(lat-{$lat},2)) ASC, id DESC";
                 } else {
                     $order = "id DESC";
                 }
                 break;
             case 3: // 人气
                 $order = "baoming_num DESC, id DESC";
                 break;
             default: // 默认
                 $order = "id DESC";
                 break;
         }*/

        // 查询数据
        $data = Db()->table("huodong")
            ->select("id,uid,name,title,img_url,money,member_money,is_online,guige,num,baoming_start_time,baoming_end_time,baoming_num,start_time,end_time,sheng,shi,qu,addr,yongjin_bili,lng,lat,pay_type")
            ->where($where)
            ->order($order)
            ->page($page, $page_size);
        if (empty($data)) {
            return ["status" => "empty", "msg" => "暂无数据"];
        }



        // 优化：批量获取用户信息，避免N+1查询问题
        $user_ids = array_unique(array_column($data, 'uid'));
        $users = [];
        if (!empty($user_ids)) {
            $user_list = Db()->table("user")
                ->select("uid,nickname,avatar,sex,is_huiyuan,mobile")
                ->where("uid IN (" . implode(',', $user_ids) . ")")
                ->fetchAll();
            foreach ($user_list as $user) {
                $users[$user['uid']] = $user;
            }
        }

        // 处理数据
        foreach ($data as &$row) {
            // 使用预加载的用户信息
            $row['user'] = isset($users[$row['uid']]) ? $users[$row['uid']] : new \stdClass();

            // 计算距离
            if ($lng > 0 && $lat > 0 && $row['lng'] > 0 && $row['lat'] > 0) {
                $distance = sqrt(pow($row['lng'] - $lng, 2) + pow($row['lat'] - $lat, 2)) * 111000;
                if ($distance < 1000) {
                    $row['distance'] = round($distance) . "米";
                } else {
                    $row['distance'] = round($distance / 1000, 1) . "公里";
                }
            } else {
                $row['distance'] = "0米";
            }
        }

        return ["status" => "ok", "data" => $data, "count" => \core\Page::$count];
    }

    /*
	* @apiName 获取和我相关的活动列表
	* @method get_my_list
	* @POST
	* @param type string 类型:1=发布的,2=报名的,3=收藏的,4=评价的,5=分享的
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":2,"huodong_id":2,"contents":"评价内容","imgs_url":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"time":"2023-11-18 13:21:00","huodong_info":{"id":2,"uid":2,"name":"活动3","title":"活动标题","img_url":"http:\/\/************\/test.jpg","guige":"规格1111122","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-19 11:30:00","date":"2023-11-19","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园11","yongjin_bili":10,"status":1,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}},"user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"}}]}
	*/
    public function get_my_list($uid, $token, $page = 1, $page_size = 20, $type = 1)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($page) ||
            !check($page, "intgt0") ||
            empty($page_size) ||
            !check($page_size, "intgt0") ||
            !check($type, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $page = (int)$page;
        $page_size = (int)$page_size;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        if ($type == 1) {
            $data = Db()->table("huodong")->select("id as huodong_id,jiesuan_status,jiesuan_money,jiesuan_yongjin,jiesuan_choujiang,is_online,money,member_money")->where("uid={$uid}")->order("pinned_at DESC,id DESC")->page($page, $page_size);
        } else if ($type == 2) {
            $data = Db()->table("huodong_baoming_order")->select("id,order_id,money,yongjin_money,pay_type,status,huodong_id,time")->where("uid={$uid}")->order("id DESC")->page($page, $page_size);
        } else if ($type == 3) {
            $data = Db()->table("huodong_shoucang")->select("id,huodong_id,time")->where("uid={$uid}")->order("id DESC")->page($page, $page_size);
        } else if ($type == 4) {
            $data = Db()->table("huodong_pingjia")->select("id,huodong_id,contents,imgs_url,time")->where("uid={$uid}")->order("id DESC")->page($page, $page_size);
        } else if ($type == 5) {
            $data = Db()->table("user_fenxiang_log")->select("id,item_id AS huodong_id,time")->where("uid={$uid} AND type=2")->order("id DESC")->page($page, $page_size);
        } else {
            return ["status" => "error", "msg" => "参数错误"];
        }
        if (empty($data)) {
            return ["status" => "empty", "msg" => "资源不存在"];
        }
        foreach ($data as &$row) {
            //
            if (isset($row['imgs_url'])) {
                $row['imgs_url'] = !empty($row['imgs_url']) ? explode("|", trim($row['imgs_url'], "|")) : [];
            }
            //
            $huodong_info = Db()->table("huodong")->select("id,uid,name,title,img_url,type_id,guige,money,member_money,is_online,pay_type,num,baoming_num,baoming_start_time,baoming_end_time,start_time,end_time,sheng,shi,qu,addr,yongjin_bili,status,liucheng,contents,lat,lng,sheng_id,shi_id,qu_id,choujiang_status,choujiang_config_json,lianxi_name,lianxi_mobile,lianxi_qrcode,pinned_at")->where("id={$row['huodong_id']}")->fetch(); // Added member_money, is_online
            if (!empty($huodong_info)) {
                $user = Db()->table("user")->select("uid,nickname,avatar,sex,is_huiyuan,mobile")->where("uid={$huodong_info['uid']}")->fetch();
                $huodong_info['user'] = $user ?: new \stdClass();
                $huodong_info['liucheng'] = json_decode($huodong_info['liucheng'], true);
                $huodong_info['choujiang_config'] = json_decode($huodong_info['choujiang_config_json'], true);
            }
            $row['huodong_info'] = $huodong_info ?: new \stdClass();
            //
        }
        return ["status" => "ok", "data" => $data];
    }

    /*
	* @apiName 获取活动详情
	* @method get_info
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","data":{"id":3,"uid":2,"name":"活动2","title":"活动标题","img_url":"http:\/\/************\/test.jpg","money":"100.00","contents":"活动介绍","liucheng":["流程1","流程2"],"guige":"规格1111","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-18 11:00:00","baoming_num":0,"date":"2023-11-18","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园","yongjin_bili":5,"time":"2023-11-18 09:49:52","pingjia_times":0,"user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"},"imgs":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"is_shoucang":0,"is_zan":0}}
	*/
    public function get_info($uid, $token, $huodong_id)
    {
        if (!check($huodong_id, "intgt0")) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        // 修复：允许未登录用户访问活动详情，uid可以为0或空
        // 只验证huodong_id是必需的
        $huodong_id = (int)$huodong_id;
        $where = "id={$huodong_id}";
        $uid = (int)$uid; // 未登录时uid为0
        dbConn();

        $data = Db()->table("huodong")->select(
            "id,uid,name,title,type_id,img_url,money,member_money,is_online,pay_type,
            contents,liucheng,guige,num,baoming_start_time,baoming_end_time,
            baoming_num,start_time,end_time,sheng,shi,qu,addr,lng,lat,
            yongjin_bili,time,pingjia_times,status,choujiang_status,
            choujiang_config_json,lianxi_name,lianxi_mobile,lianxi_qrcode,qun_qrcode,
            jiesuan_status,jiesuan_money,jiesuan_yongjin,jiesuan_choujiang,
            sheng_id,shi_id,qu_id,pinned_at"
        )->where($where)->fetch();

        if (empty($data)) {
            return ["status" => "empty", "msg" => "资源不存在"];
        }
        // 判断是否为真实登录用户（uid > 0 且有有效token）
        $isLoggedInUser = $uid > 0 && !empty($token) && strlen($token) == 32;
        $authSuccess = false;

        if ($isLoggedInUser) {
            // 对于已登录用户，尝试进行token验证
            $authSuccess = $this->auth($uid, $token);
        }

        if ($authSuccess) {
            // 认证成功，获取用户相关状态
            // 获取当前用户的收藏情况
            $shoucang = Db()->table("huodong_shoucang")->where("uid={$uid} AND huodong_id={$huodong_id}")->fetch();
            $data['is_shoucang'] = $shoucang ? 1 : 0;

            // 获取赞的情况
            $zan = Db()->table("huodong_zan")->where("uid={$uid} AND huodong_id={$huodong_id}")->fetch();
            $data['is_zan'] = $zan ? 1 : 0;

            // 获取当前用户报名的情况
            $baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")->fetch();
            $data['baoming_order'] = $baoming_order ?: new \stdClass();
        } else {
            // 未登录或认证失败，使用默认值（不阻断请求）
            $data['is_shoucang'] = 0;
            $data['is_zan'] = 0;
            $data['baoming_order'] = new \stdClass();
        }

        //
        $user = Db()->table("user")->select("uid,nickname,avatar,sex,is_huiyuan,mobile")->where("uid={$data['uid']}")->fetch();
        $data['user'] = $user ?: new \stdClass();

        //
        $imgs = Db()->table("huodong_img")->select("img_url")->where("huodong_id={$data['id']}")->fetchAll();
        $data['imgs'] = $imgs ? array_column($imgs, "img_url") : [];

        // 处理过去内容
        $data['contents'] = htmlspecialchars_decode($data['contents']);

        // 在 get_info 方法中加入用户报名查询（仅在认证成功时）
        if ($authSuccess && $uid) {
            $baoming_info = Db()->table("huodong_baoming_order")
                ->select("id,order_id,status,time")
                ->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")
                ->fetch();
            if ($baoming_info) {
                // 确保报名订单信息被添加到返回数据中
                $data['baoming_order'] = $baoming_info;
            }
        }
        //
        return ["status" => "ok", "data" => $data];
    }

    /*
	* @apiName 报名下单
	* @method add_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param is_choujiang string 是否参与抽奖:1=是,0=否
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人手机号
	* @param lianxi_sex string 联系人性别
	* @return {"status":"ok","msg":"操作成功","order_id":"20231118113306655667","money":"100.00"}
	*/
    public function add_baoming($uid, $token, $huodong_id, $is_choujiang, $lianxi_name, $lianxi_mobile, $lianxi_sex = 0)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0") ||
            !in_array($is_choujiang, [0, 1]) ||
            empty($lianxi_name) ||
            empty($lianxi_mobile) ||
            !check($lianxi_mobile, "mobile") ||
            !in_array($lianxi_sex, [0, 1, 2])
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $is_choujiang = (int)$is_choujiang;
        $lianxi_sex = (int)$lianxi_sex;

        dbConn();

        // 查询用户信息，包含会员状态
        $user_info = Db()->table("user")->select("uid, mobile, is_huiyuan")->where("uid={$uid}")->fetch();



        // 获取审核通过的指定 id 的活动。
        $huodong_info = Db()->table("huodong")->where("id={$huodong_id} AND status=1")->fetch();

        if (empty($huodong_info)) {
            return $this->handleError("活动不存在");
        }

        // 检查活动的开始时间、结束时间，人数等信息
        if (strtotime($huodong_info['start_time']) < time()) {
            return $this->handleError("活动报名已结束");
        }
        if ($huodong_info['baoming_num'] >= $huodong_info['num']) {
            return $this->handleError("活动报名人数已满");
        }

        // 检查当前用户是否报名过该活动 (查找未支付或已支付的订单)
        $baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status IN (0, 1)")->fetch();
        if (!empty($baoming_order)) {
            if($baoming_order['status'] == 0){
                // 检查订单创建时间，如果超过30分钟未支付，则自动取消
                $order_time = strtotime($baoming_order['time']);
                $current_time = time();
                $time_diff = $current_time - $order_time;

                // 如果订单超过30分钟未支付，自动取消
                if ($time_diff > 1800) { // 1800秒 = 30分钟
                    // 更新订单状态为已取消
                    Db()->table("huodong_baoming_order")->where("id={$baoming_order['id']}")->update(["status" => 2]);
                    // 返回新的报名请求
                    return ["status" => "ok", "msg" => "之前的未支付订单已过期，可以重新报名"];
                } else {
                    // 未过期的未支付订单
                    return ["status" => "error", "msg" => "您有待支付的报名订单，请先完成支付或取消", "order_id" => $baoming_order['order_id']]; // 可选返回order_id
                }
            } else {
                 return ["status" => "error", "msg" => "您已成功报名该活动"];
            }
        }

        // 根据会员状态计算支付金额
        $pay_money = 0;
        if ($user_info['is_huiyuan'] == 1) {
            $pay_money = $huodong_info['member_money'];
        } else {
            $pay_money = $huodong_info['money'];
        }
        $pay_money = number_format($pay_money, 2, ".", "");

        // 检查是否需要支付 (免费或线下支付)
        $nopay = false;
        // 使用计算出的 $pay_money 判断
        if (bccomp($pay_money, "0.00", 2) <= 0 || $huodong_info['pay_type'] == 2) {
            $nopay = true;
            // 如果是免费或线下，确保金额为0
            $pay_money = number_format(0, 2, ".", "");
        }
        $order_id = date("YmdHis") . makeCode(6);
        $data = [
            "order_id" => $order_id,
            "huodong_id" => $huodong_id,
            "uid" => $uid,
            "money" => $pay_money, // 存储计算出的金额
            "lianxi_sex" => $lianxi_sex,
            "yongjin_money" => 0,
            "is_choujiang" => 0,
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "status" => 0, // 默认状态为未支付
            "pay_time" => null, // 默认支付时间为空
            "pay_type" => 0, // 默认支付方式
        ];

        if ($nopay) {
            $data['pay_time'] = DATETIME;
            $data['pay_type'] = 3;
            $data['status'] = 1; // 免费或线下，直接设为已支付
        }

        // 佣金
        // 如果佣金比例存在（大于0）且活动需要付款。
        /*  if ($huodong_info['yongjin_bili'] > 0 && bccomp($huodong_info['money'], "0.00", 2) > 0) {
              // 获取平台需要获取的比例（20%）
              $huodong_yongjin_pingtai_bili = $this->get_config("huodong_yongjin_pingtai_bili");
              if ($huodong_yongjin_pingtai_bili === false) {
                  return ["status" => "error", "msg" => "系统配置参数错误"];
              }

              $huodong_yongjin_pingtai_bili = intval($huodong_yongjin_pingtai_bili);
              $yongjin_zong = number_format($huodong_info['money'] * $huodong_info['yongjin_bili'] / 100, 2, ".", "");
              // 用户佣金
              $yongjin_money = number_format($yongjin_zong * (1 - $huodong_yongjin_pingtai_bili / 100), 2, ".", "");

              // 活动发布者粉丝报名，去掉平台抽成（逻辑）
              // uid：关注者，to_uid：被关注者
              $check = Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$huodong_info['uid']}")->fetch();
              if(!empty($check)){
                  // 关注的关系存在, 则用户的佣金为 100%，不需要减扣平台的比例。
                  $yongjin_money = number_format($yongjin_zong * (1 - 0 / 100), 2, ".", "");
              }

              if (bccomp($yongjin_money, "0.00", 2) > 0) {

                  // 获取当前想要报名用户的父亲uid
                  $p_uid = Db()->table("user")->where("uid={$uid}")->getColumn("p_uid");

                  // 多级获取父亲 User
                  if (!empty($p_uid)) {
                      $c_p_uid = $p_uid;
                      while (true) {
                          if (empty($c_p_uid)) {
                              break;
                          }

                          // 获取父亲 User
                          $c_p_user = Db()->table("user")->select("uid,p_uid,is_huiyuan")->where("uid={$c_p_uid}")->fetch();

                          if (!empty($c_p_user) && $c_p_user['is_huiyuan'] == 1) {
                              $data['yongjin_zong'] = $yongjin_zong;
                              $data['yongjin_money'] = $yongjin_money;
                              $data['yongjin_uid'] = $c_p_uid;
                              break;
                          }
                          if (empty($c_p_user) || empty($c_p_user['p_uid'])) {
                              break;
                          }
                          $c_p_uid = $c_p_user['p_uid'];
                      }
                  }
              }
          }

          //是否参与抽奖
          if ($huodong_info['choujiang_status'] == 0 && $is_choujiang == 1) {
              $is_huiyuan = Db()->table("user")->where("uid={$uid}")->getColumn("is_huiyuan");
              if ($is_huiyuan == 1) {
                  $huodong_choujiang_year_zhongjiang_num = $this->get_config('huodong_choujiang_year_zhongjiang_num');
                  $limit_time = date("Y-m-d H:i:s", strtotime("-1 year"));
                  $zhongjian_count = Db()->table("huodong_zhongjiang_log")->where("uid={$uid} AND time>'{$limit_time}'")->count();
                  if ($zhongjian_count < $huodong_choujiang_year_zhongjiang_num) {
                      $data['is_choujiang'] = 1;
                  }
              }
          }
        */
        //
        $prepareParam = [
            ":lianxi_name" => $lianxi_name,
            ":lianxi_mobile" => $lianxi_mobile,
        ];
        dbConn();
        Db::begin();
        try {
            //
            Db()->table("huodong_baoming_order")->prepareParam($prepareParam)->insert($data);
            $insert_id = Db()->insertId();
            //
            // 只有在免费或线下支付($nopay=true, status=1)时才增加报名人数
            if ($nopay) {
                $sql = "UPDATE `huodong` SET baoming_num=baoming_num+1 WHERE id={$huodong_id} AND baoming_num<num";
                $rowCount = Db::_exec($sql);
                if (empty($rowCount)) {
                    throw new \Exception("更新已报名人数失败：{$huodong_id}");
                }
            }
            // 如果是免费活动或线下支付，直接创建报名成功通知
            if ($nopay) {
                $this->create_notification($uid, "activity_registration", "活动报名成功", "您已成功报名活动：{$huodong_info['name']}", $huodong_id);
            }
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功", "order_id" => $order_id, "money" => $pay_money];
        } catch (\Exception $e) {
            Db::rollback();
            return $this->handleError("系统繁忙，请稍后再试", $e->getMessage());
        }
    }


    /**
     * @apiName 活动核销
     * @method hexiao_baoming
     * @POST
     * @param $uid
     * @param $token
     * @param $baoming_uid
     * @param $huodong_id
     * @param $id
     * @param $order_id
     * @return void
     */
    public function hexiao_baoming($uid, $token, $baoming_uid, $huodong_id, $order_id, $id)
    {
        // 1. 通过报名用户的 uid, huodong_id, 查询到报名订单
        // 2. 设置订单的状态为已核销
        // 3. 完成核销
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")
            ->prepareParam([":order_id" => $order_id])
            ->where("order_id=:order_id AND uid={$baoming_uid}")
            ->fetch();

        // status
        // 状态:0=未支付,1=已支付,2=已取消,3=退款中,4=退款成功,5=退款失败
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }
        // 必须要已支付才能算报名成功
        if ($baoming_order['status'] != 1) {
            return ["status" => "error", "msg" => "报名状态异常"];
        }

        $huodong_info = Db()->table("huodong")
            ->where("id={$baoming_order['huodong_id']}")
            ->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }

        try {
            $data = [
                "status_hexiao"=>":status_hexiao"
            ];
            $prepareParam = [
                ":status_hexiao"=>1
            ];
            $res = Db()->table("huodong_baoming_order")
                ->where("order_id={$order_id} AND uid={$baoming_uid}")
                ->prepareParam($prepareParam)
                ->update($data);
            // {
            //    "status": "ok",
            //    "msg": "核销成功",
            //    "data": 1
            // }
            return ["status" => "ok", "msg" => "核销成功", "data"=>$res];
        }catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消报名
	* @method cancel_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param order_id string 报名订单编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function cancel_baoming($uid, $token, $order_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")->prepareParam([":order_id" => $order_id])->where("order_id=:order_id AND uid={$uid}")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }
        if ($baoming_order['status'] != 1) {
            return ["status" => "error", "msg" => "报名状态异常"];
        }
        $huodong_info = Db()->table("huodong")->where("id={$baoming_order['huodong_id']}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['status'] != 1 || !in_array($huodong_info['jiesuan_status'], [0, 2])) {
            return ["status" => "error", "msg" => "活动状态已不支持取消报名"];
        }
        if (time() > strtotime($huodong_info['start_time'])) {
            return ["status" => "error", "msg" => "活动报名已截止,不支持取消"];
        }
        Db::begin();
        try {
            //
            $sql = "UPDATE `huodong_baoming_order` SET `status`=3 WHERE id={$baoming_order['id']} AND uid={$uid} AND status=1";
            $rowCount = Db()->_exec($sql);
            if (empty($rowCount)) {
                throw new \Exception("修改报名订单信息失败");
            }
            //
            $sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`-1 WHERE id={$baoming_order['huodong_id']}";
            Db::_exec($sql);
            //
            $baoming_money = $baoming_order['money'];
            if (bccomp($baoming_money, "0.00", 2) > 0) {
                //
                if ($baoming_order['pay_type'] == 1) {//微信支付
                    $baoming_money_fen = intval($baoming_money * 100);
                    $res = \model\Wechat::tuikuan($baoming_money_fen, $baoming_money_fen, $baoming_order['order_id'], "取消报名");
                    if ($res['status'] == "ok") {
                        if ($res['data'] == "success") {
                            $sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE id={$baoming_order['id']}";
                            Db()->_exec($sql);
                        }
                    } else {
                        throw new \Exception($res['msg']);
                    }
                } else if ($baoming_order['pay_type'] == 2) {//余额支付
                    //
                    $sql = "UPDATE `user` SET `money`=`money`+{$baoming_money} WHERE uid={$baoming_order['uid']}";
                    $rowCount = Db()->_exec($sql);
                    if (empty($rowCount)) {
                        throw new \Exception("余额通道退款失败");
                    }
                    //
                    $shengyu = Db()->table("user")->where("uid={$baoming_order['uid']}")->getColumn("money");
                    $zhangdan = [
                        "uid" => $baoming_order['uid'],
                        "money" => $baoming_money,
                        "type" => 9,
                        "shengyu" => $shengyu,
                        "msg" => "取消报名退费:{$baoming_order['order_id']}",
                    ];
                    Db()->table("user_zhangdan")->insert($zhangdan);
                    //
                    $sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE id={$baoming_order['id']}";
                    Db()->_exec($sql);
                    //
                } else {
                    throw new \Exception("退款时未找到原支付方式");
                }
            } else {
                $sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE id={$baoming_order['id']}";
                Db()->_exec($sql);
            }
            // 创建取消报名通知
            $this->create_notification(
                $uid,
                "registration_cancelled",
                "取消报名成功",
                "您已成功取消活动「{$huodong_info['title']}」的报名",
                $baoming_order['huodong_id']
            );

            // 通知活动发布者
            $this->create_notification(
                $huodong_info['uid'],
                "registration_cancelled",
                "有用户取消报名",
                "用户取消了活动「{$huodong_info['title']}」的报名",
                $baoming_order['huodong_id']
            );

            $this->user_log($uid, "取消报名【{$baoming_order['order_id']}】");

            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }


    /*
	* @apiName 删除报名信息
	* @method cancel_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param order_id string 报名订单编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function delete_baoming($uid, $token, $order_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")->prepareParam([":order_id" => $order_id])->where("order_id=:order_id AND uid={$uid}")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }

        Db::begin();
        try {
            //
            $sql = "UPDATE `huodong_baoming_order` SET `status`=6 WHERE id={$baoming_order['id']} AND uid={$uid} AND status=1";
            $rowCount = Db()->_exec($sql);
            if (empty($rowCount)) {
                throw new \Exception("删除报名订单信息失败");
            }
            //
            $sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`-1 WHERE id={$baoming_order['huodong_id']}";
            Db::_exec($sql);

            $this->user_log($uid, "报名失败删除报名信息【{$baoming_order['order_id']}】");
            //
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动报名列表
	* @method get_baoming_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":4,"order_id":"20231118130603127893","uid":2,"money":"100.00","yongjin_money":"10.00","time":"2023-11-18 13:06:03","pay_time":null,"status":1,"is_jiesuan":0,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}},{"id":3,"order_id":"20231118130602506823","uid":2,"money":"100.00","yongjin_money":"10.00","time":"2023-11-18 13:06:02","pay_time":null,"status":1,"is_jiesuan":0,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}}]}
	*/
    public function get_baoming_list($uid, $token, $huodong_id, $page = 1, $page_size = 20)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($page) ||
            !check($page, "intgt0") ||
            empty($page_size) ||
            !check($page_size, "intgt0") ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $huodong_info = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "empty", "msg" => "活动信息未找到"];
        }
        $data = Db()->table("huodong_baoming_order")->select("id,order_id,uid,money,yongjin_money,time,pay_time,status,is_choujiang,is_jiesuan,lianxi_name,lianxi_mobile,lianxi_sex")->where("huodong_id={$huodong_id} AND status=1")->order("id DESC")->page($page, $page_size);
        if (empty($data)) {
            return ["status" => "empty", "msg" => "资源不存在"];
        }
        foreach ($data as &$row) {
            $user = Db()->table("user")->select("uid,avatar,nickname,mobile,sex")->where("uid={$row['uid']}")->fetch();
            $row['user'] = $user ?: new \stdClass();
            //
        }
        return ["status" => "ok", "data" => $data];
    }

    /*
	* @apiName 获取活动报名列表公开
	* @method get_baoming_list_public
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":4,"uid":2,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test"}}]}
	*/
    public function get_baoming_list_public($uid, $token, $huodong_id, $page = 1, $page_size = 20)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($page) ||
            !check($page, "intgt0") ||
            empty($page_size) ||
            !check($page_size, "intgt0") ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $huodong_info = Db()->table("huodong")->where("id={$huodong_id}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "empty", "msg" => "活动信息未找到"];
        }
        $data = Db()->table("huodong_baoming_order")->select("id,uid,pay_time")->where("huodong_id={$huodong_id} AND status=1")->order("id DESC")->page($page, $page_size);
        if (empty($data)) {
            return ["status" => "empty", "msg" => "资源不存在"];
        }
        foreach ($data as &$row) {
            $user = Db()->table("user")->select("uid,avatar,nickname,sex")->where("uid={$row['uid']}")->fetch();
            $row['user'] = $user ?: new \stdClass();
            //
        }
        return ["status" => "ok", "data" => $data];
    }

    /*
	* @apiName 添加收藏
	* @method shoucang_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function shoucang_add($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        $data = [
            "uid" => $uid,
            "huodong_id" => $huodong_id,
        ];
        $prepareParam = [

        ];
        dbConn();
        try {
            Db()->table("huodong_shoucang")->prepareParam($prepareParam)->insert($data);
            //
            $this->user_log($uid, "收藏活动【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 删除收藏
	* @method shoucang_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param ids string 活动编号多个逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function shoucang_del($uid, $token, $ids)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $ids_arr = explode(",", $ids);
        foreach ($ids_arr as $id) {
            if (!check($id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }
        }
        $ids_str = implode(",", $ids_arr);
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        try {
            if (!Db()->table("huodong_shoucang")->where("uid={$uid} AND huodong_id IN ({$ids_str})")->del()) {
                throw new \Exception("操作失败");
            }
            //
            $this->user_log($uid, "删除活动收藏信息【{$ids_str}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 点赞
	* @method zan_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function zan_add($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        $data = [
            "uid" => $uid,
            "huodong_id" => $huodong_id,
        ];
        $prepareParam = [

        ];
        dbConn();
        try {
            Db()->table("huodong_zan")->prepareParam($prepareParam)->insert($data);
            //
            $this->user_log($uid, "点赞活动【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消点赞
	* @method zan_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_ids string 活动编号多个逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function zan_del($uid, $token, $huodong_ids)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $ids_arr = explode(",", $huodong_ids);
        foreach ($ids_arr as $id) {
            if (!check($id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }
        }
        $ids_str = implode(",", $ids_arr);
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        try {
            if (!Db()->table("huodong_zan")->where("huodong_id IN ({$ids_str}) AND uid={$uid}")->del()) {
                throw new \Exception("操作失败");
            }
            //
            $this->user_log($uid, "取消活动点赞【{$ids_str}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 添加评价
	* @method add_pingjia
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param contents string 内容
	* @param imgs_url string 图片地址,多个英文逗号隔开(可选)
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function add_pingjia($uid, $token, $huodong_id, $contents = "", $imgs_url = "")
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (empty($contents) && empty($imgs_url)) {
            return ["status" => "error", "msg" => "缺少提问内容"];
        }
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return ["status" => "error", "msg" => "参数错误"];
                }
            }
            $imgs_url = "|" . implode("|", $imgs_arr) . "|";
        }
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $contents = mb_substr(htmlspecialchars($contents), 0, 500);
        //
        dbConn();
        //
        $huodong_info = Db()->table("huodong")->select("id,start_time")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        /*if (strtotime("{$huodong_info['start_time']}") > _NOW_) {
            return ["status" => "error", "msg" => "活动还未开始"];
        }*/
        $baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "参与活动的人才能评价"];
        }
        /*if ($baoming_order['is_pingjia'] != 0) {
            return ["status" => "error", "msg" => "当前活动已评价过"];
        }*/
        //
        Db::begin();
        try {
            $data = [
                "uid" => $uid,
                "huodong_id" => $huodong_id,
                "imgs_url" => ":imgs_url",
                "contents" => ":contents",
            ];
            $prepareParam = [
                ":imgs_url" => $imgs_url,
                ":contents" => $contents,
            ];
            Db()->table("huodong_pingjia")->prepareParam($prepareParam)->insert($data);
            //
            $sql = "UPDATE `huodong` SET `pingjia_times`=`pingjia_times`+1 WHERE id={$huodong_id}";
            Db::_exec($sql);
            //
            $sql = "UPDATE `huodong_baoming_order` SET `is_pingjia`=1 WHERE id={$baoming_order['id']}";
            Db::_exec($sql);
            //
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Exception $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "操作失败"];
        }
    }

    /*
	* @apiName 获取评价
	* @method get_pingjia
	* @POST
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":2,"uid":2,"contents":"评价内容","imgs_url":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"time":"2023-11-18 13:21:00","user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","is_huiyuan":0}}],"count":1}
	*/
    public function get_pingjia($huodong_id, $page = 1, $page_size = 20)
    {
        if (
            !check($huodong_id, "intgt0") ||
            empty($page) ||
            !check($page, "intgt0") ||
            empty($page_size) ||
            !check($page_size, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        $huodong_id = (int)$huodong_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        //
        dbConn();
        $where = "`huodong_id`={$huodong_id}";
        //
        $data = Db()->table("huodong_pingjia")->select("id,uid,contents,imgs_url,time")->where($where)->order("id DESC")->page($page, $page_size);
        if (empty($data)) {
            return ["status" => "empty", "msg" => "暂无数据"];
        }
        foreach ($data as &$row) {
            //
            $user_info = Db()->table("user")->select("uid,avatar,nickname,is_huiyuan")->where("uid={$row['uid']}")->fetch();
            $row['user'] = $user_info ? $user_info : new \stdClass();
            //
            $row['imgs_url'] = !empty($row['imgs_url']) ? explode("|", trim($row['imgs_url'], "|")) : [];
            //
        }
        return ["status" => "ok", "data" => $data, "count" => \core\Page::$count];
    }

    /*
    * @apiName 活动签到
    * @method checkin
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param huodong_id string 活动编号
    * @param lat string 用户纬度（线下活动必填）
    * @param lng string 用户经度（线下活动必填）
    * @return {"status":"ok","msg":"签到成功","data":{"points_awarded":10}}
    */
    public function checkin($uid, $token, $huodong_id, $lat = null, $lng = null)
    {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            empty($huodong_id) || !check($huodong_id, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;

        // 验证用户登录
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        // 获取活动信息
        $huodong_info = Db()->table("huodong")
            ->select("id,name,start_time,end_time,is_online,lng,lat,addr")
            ->where("id={$huodong_id} AND status=1")
            ->fetch();

        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在或已下架"];
        }

        // 检查是否已经签到过
        $existing_checkin = Db()->table("huodong_checkin")
            ->where("uid={$uid} AND huodong_id={$huodong_id}")
            ->fetch();

        if (!empty($existing_checkin)) {
            return ["status" => "error", "msg" => "您已经签到过了"];
        }

        // 验证签到时间（活动开始前30分钟到开始后30分钟）
        $current_time = time();
        $start_time = strtotime($huodong_info['start_time']);
        $checkin_start = $start_time - (30 * 60); // 开始前30分钟
        $checkin_end = $start_time + (30 * 60);   // 开始后30分钟

        if ($current_time < $checkin_start) {
            return ["status" => "error", "msg" => "签到时间未到，请在活动开始前30分钟内签到"];
        }

        if ($current_time > $checkin_end) {
            return ["status" => "error", "msg" => "签到时间已过，签到时间为活动开始前后30分钟内"];
        }

        $distance = null;
        $checkin_lat = null;
        $checkin_lng = null;

        // 线下活动需要验证地理位置
        if ($huodong_info['is_online'] == 0) {
            if (empty($lat) || empty($lng) || !is_numeric($lat) || !is_numeric($lng)) {
                return ["status" => "error", "msg" => "线下活动签到需要提供位置信息"];
            }

            $checkin_lat = (float)$lat;
            $checkin_lng = (float)$lng;
            $activity_lat = (float)$huodong_info['lat'];
            $activity_lng = (float)$huodong_info['lng'];

            // 计算距离（使用Haversine公式）
            $distance = $this->calculateDistance($checkin_lat, $checkin_lng, $activity_lat, $activity_lng);

            // 验证距离是否在1000米内
            if ($distance > 1000) {
                return ["status" => "error", "msg" => "您距离活动地点太远，无法签到（需在1000米内）"];
            }
        }

        // 计算积分奖励
        $points_awarded = 10; // 基础签到积分

        try {
            // 开始事务
            Db()->beginTransaction();

            // 插入签到记录
            $checkin_data = [
                "uid" => $uid,
                "huodong_id" => $huodong_id,
                "checkin_time" => date("Y-m-d H:i:s"),
                "checkin_lat" => $checkin_lat,
                "checkin_lng" => $checkin_lng,
                "distance" => $distance,
                "points_awarded" => $points_awarded,
                "checkin_ip" => $this->getClientIP(),
                "status" => 1
            ];

            Db()->table("huodong_checkin")->insert($checkin_data);

            // 更新用户积分
            Db()->table("user")->where("uid={$uid}")->update([
                "points" => "points + {$points_awarded}"
            ]);

            // 获取更新后的积分余额
            $points_balance = Db()->table("user")->where("uid={$uid}")->getColumn("points");

            // 插入积分记录
            $points_log_data = [
                "uid" => $uid,
                "points_change" => $points_awarded,
                "points_balance" => $points_balance,
                "source_type" => "activity_checkin",
                "source_id" => $huodong_id,
                "description" => "活动签到：" . $huodong_info['name']
            ];

            Db()->table("user_points_log")->insert($points_log_data);

            // 提交事务
            Db()->commit();

            // 记录用户操作日志
            $this->user_log($uid, "活动签到【{$huodong_info['name']}】");

            return [
                "status" => "ok",
                "msg" => "签到成功",
                "data" => [
                    "points_awarded" => $points_awarded,
                    "points_balance" => $points_balance,
                    "distance" => $distance
                ]
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db()->rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "签到失败，请稍后重试"];
        }
    }

    /*
    * 计算两点间距离（米）
    * 使用Haversine公式
    */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371000; // 地球半径（米）

        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLatRad = deg2rad($lat2 - $lat1);
        $deltaLngRad = deg2rad($lng2 - $lng1);

        $a = sin($deltaLatRad / 2) * sin($deltaLatRad / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLngRad / 2) * sin($deltaLngRad / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c);
    }

    /*
    * 获取客户端IP地址
    */
    private function getClientIP()
    {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
    }

    public function _empty()
    {
        return ["status" => "error", "msg" => "URL error"];
    }

    function __destruct()
    {

    }

    public static function auth($uid, $token)
    {
        return parent::auth($uid, $token); // TODO: Change the autogenerated stub
    }

    public static function exception_log($msg)
    {
        return parent::exception_log($msg); // TODO: Change the autogenerated stub
    }

    /*
     * 创建通知的私有方法
     * @param int $uid 用户ID，如果为0则创建全局通知
     * @param string $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param int $related_id 关联ID
     */
    private function create_notification($uid, $type, $title, $content, $related_id = null) {
        try {
            $is_global = ($uid == 0) ? 1 : 0;
            $data = [
                "uid" => $uid,
                "type" => ":type",
                "title" => ":title",
                "content" => ":content",
                "related_id" => $related_id,
                "is_read" => 0,
                "is_global" => $is_global
            ];
            $prepareParam = [
                ":type" => htmlspecialchars($type),
                ":title" => htmlspecialchars($title),
                ":content" => htmlspecialchars($content)
            ];

            Db()->table("user_notifications")
                ->prepareParam($prepareParam)
                ->insert($data);

        } catch (\Exception $e) {
            $this->exception_log("创建通知失败：" . $e->getMessage());
        }
    }
}
