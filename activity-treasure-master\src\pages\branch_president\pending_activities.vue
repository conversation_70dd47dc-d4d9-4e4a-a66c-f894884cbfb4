<template>
  <view class="page">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="待审核活动"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <!-- 活动列表 -->
      <view class="activity-list" v-if="activityList.length > 0">
        <view
          class="activity-item"
          v-for="activity in activityList"
          :key="activity.id"
          @click="viewActivityDetail(activity)"
        >
          <view class="activity-header">
            <image 
              class="activity-image"
              :src="activity.img_url || '/static/default-activity.png'"
              mode="aspectFill"
            />
            <view class="activity-info">
              <view class="activity-name">{{ activity.name }}</view>
              <view class="activity-title">{{ activity.title }}</view>
              <view class="activity-meta">
                <text class="organizer">组织者：{{ activity.organizer_name }}</text>
                <text class="create-time">{{ formatTime(activity.create_time) }}</text>
              </view>
            </view>
          </view>
          
          <view class="activity-actions" @click.stop>
            <u-button
              type="success"
              size="small"
              :loading="activity.reviewing"
              @click.stop="reviewActivity(activity, 1)"
              customStyle="background: #6AC086; border: none; border-radius: 30rpx; margin-right: 20rpx;"
            >
              通过
            </u-button>
            <u-button
              type="error"
              size="small"
              :loading="activity.reviewing"
              @click.stop="showRejectModal(activity)"
              customStyle="background: #ff4757; border: none; border-radius: 30rpx;"
            >
              拒绝
            </u-button>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <u-empty
          mode="list"
          text="暂无待审核活动"
          textColor="#999999"
          textSize="28"
        ></u-empty>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && !loading">
        <u-button
          type="primary"
          :loading="loadingMore"
          @click="loadMore"
          customStyle="background: #6AC086; border: none; border-radius: 50rpx;"
        >
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </u-button>
      </view>
    </view>
    
    <!-- 拒绝理由弹窗 -->
    <u-popup
      :show="showRejectPopup"
      mode="center"
      :round="20"
      :closeable="true"
      @close="closeRejectModal"
    >
      <view class="reject-modal">
        <view class="modal-title">拒绝理由</view>
        <u-textarea
          v-model="rejectReason"
          placeholder="请输入拒绝理由（可选）"
          maxlength="200"
          count
          height="120rpx"
        />
        <view class="modal-actions">
          <u-button
            type="info"
            @click="closeRejectModal"
            customStyle="margin-right: 20rpx; border-radius: 30rpx;"
          >
            取消
          </u-button>
          <u-button
            type="error"
            :loading="rejecting"
            @click="confirmReject"
            customStyle="background: #ff4757; border: none; border-radius: 30rpx;"
          >
            确认拒绝
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 加载状态 -->
    <u-loading-page 
      :loading="loading" 
      loading-text="加载中..."
      bg-color="#f8f9fa"
    ></u-loading-page>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { requireLogin } from "@/utils/auth";
import { branch_presidentpending_activities, branch_presidentreview_activity } from "@/api";
import { store } from "@/store";
// 🔧 P1-3修复：使用统一的权限检查工具函数
import { hasActivityReviewPermission, PERMISSION_TYPES } from "@/utils/permissions";

// 数据状态
const activityList = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 拒绝弹窗状态
const showRejectPopup = ref(false);
const rejectReason = ref('');
const rejecting = ref(false);
const currentRejectActivity = ref(null);

// 页面加载
onLoad(() => {
  if (!requireLogin()) {
    return;
  }

  // 🔧 P1-3修复：使用统一的权限检查工具函数
  if (!hasActivityReviewPermission()) {
    uni.showModal({
      title: '权限不足',
      content: '您没有活动审核权限，无法访问此页面',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
    return;
  }

  loadActivities();
});

onShow(() => {
  // 每次显示页面时刷新数据
  refreshData();
});

// 加载活动列表
const loadActivities = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1;
      hasMore.value = true;
      loading.value = true;
    } else if (!hasMore.value) {
      return;
    } else {
      // 第一次加载时也要设置loading状态
      loading.value = true;
    }
    
    const userInfo = store().$state.userInfo;

    // 验证用户登录状态
    if (!userInfo || !userInfo.uid || !userInfo.token) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('加载待审核活动，页码:', currentPage.value);

    const res = await branch_presidentpending_activities({
      uid: userInfo.uid,
      token: userInfo.token,
      page: currentPage.value,
      page_size: pageSize
    });

    console.log('待审核活动API响应:', res);
    
    if (res.status === 'ok') {
      const newActivities = res.data || [];
      
      // 为每个活动添加审核状态
      newActivities.forEach(activity => {
        activity.reviewing = false;
      });
      
      if (isRefresh) {
        activityList.value = newActivities;
      } else {
        activityList.value.push(...newActivities);
      }
      
      // 判断是否还有更多数据
      hasMore.value = newActivities.length === pageSize;
      
    } else if (res.status === 'empty') {
      if (isRefresh) {
        activityList.value = [];
      }
      hasMore.value = false;
    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: res.msg || '加载失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  loadActivities(true);
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadingMore.value = true;
    currentPage.value++;
    loadActivities();
  }
};

// 审核活动
const reviewActivity = async (activity, status) => {
  if (!activity || !activity.id) {
    uni.showToast({
      title: '活动信息错误',
      icon: 'none'
    });
    return;
  }

  try {
    activity.reviewing = true;

    const userInfo = store().$state.userInfo;
    if (!userInfo || !userInfo.uid || !userInfo.token) {
      uni.showToast({
        title: '用户信息错误，请重新登录',
        icon: 'none'
      });
      return;
    }

    const res = await branch_presidentreview_activity({
      uid: userInfo.uid,
      token: userInfo.token,
      huodong_id: activity.id,
      status: status,
      comment: status === 2 ? rejectReason.value : ''
    });

    console.log('审核活动API响应:', res);

    if (res.status === 'ok') {
      uni.showToast({
        title: status === 1 ? '审核通过' : '已拒绝',
        icon: 'success'
      });

      // 从列表中移除已审核的活动
      const index = activityList.value.findIndex(item => item.id === activity.id);
      if (index !== -1) {
        activityList.value.splice(index, 1);
      }

    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: res.msg || '审核失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('审核活动失败:', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    });
  } finally {
    activity.reviewing = false;
  }
};

// 显示拒绝弹窗
const showRejectModal = (activity) => {
  currentRejectActivity.value = activity;
  rejectReason.value = '';
  showRejectPopup.value = true;
};

// 关闭拒绝弹窗
const closeRejectModal = () => {
  showRejectPopup.value = false;
  currentRejectActivity.value = null;
  rejectReason.value = '';
};

// 确认拒绝
const confirmReject = () => {
  if (currentRejectActivity.value) {
    rejecting.value = true;
    reviewActivity(currentRejectActivity.value, 2).finally(() => {
      rejecting.value = false;
      closeRejectModal();
    });
  }
};

// 查看活动详情
const viewActivityDetail = (activity) => {
  uni.navigateTo({
    url: `/pages/bundle/index/activeInfo?id=${activity.id}`
  });
};

// 格式化时间（修复iOS兼容性问题）
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式兼容性：将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
  const iosCompatibleTimeStr = timeStr.replace(/-/g, '/');
  const date = new Date(iosCompatibleTimeStr);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return timeStr; // 如果日期无效，返回原始字符串
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) { // 1天内
    return Math.floor(diff / 3600000) + '小时前';
  } else {
    return date.toLocaleDateString();
  }
};
</script>

<style lang="scss" scoped>
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.activity-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.activity-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.activity-header {
  display: flex;
  margin-bottom: 20rpx;
}

.activity-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.organizer,
.create-time {
  font-size: 24rpx;
  color: #999999;
}

.activity-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.empty-state {
  margin-top: 100rpx;
}

.load-more {
  margin-top: 40rpx;
  text-align: center;
}

.reject-modal {
  padding: 40rpx;
  width: 600rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-actions {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}
</style>
