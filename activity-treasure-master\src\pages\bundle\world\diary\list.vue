<script setup>
import { ref, onMounted } from 'vue';
import { getDiaryList } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// 状态管理
const diaryList = ref([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);
const page = ref(1);
const pageSize = 10;

// 获取日记列表
const fetchDiaryList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return;

  loading.value = true;

  try {
    const currentPage = isRefresh ? 1 : page.value;
    const params = {
      page: currentPage,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      type: 'diary'
    };

    console.log('请求日记列表参数:', params);
    const res = await getDiaryList(params);
    console.log('日记列表API响应:', res);

    if (res.status === 'ok' && res.data) {
      const newList = res.data.list || [];
      console.log('获取到的日记列表:', newList);

      if (isRefresh) {
        diaryList.value = newList;
        page.value = 1;
      } else {
        diaryList.value = [...diaryList.value, ...newList];
      }

      hasMore.value = newList.length === pageSize;
      if (!isRefresh) page.value++;
    } else if (res.status === 'empty') {
      console.log('服务器返回空数据:', res.msg);
      if (isRefresh) {
        diaryList.value = [];
      }
      hasMore.value = false;
    } else {
      console.error('API返回错误:', res);
      uni.showToast({ title: res.msg || '获取日记列表失败', icon: 'none' });
    }
  } catch (error) {
    console.error('获取日记列表失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  fetchDiaryList(true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (hasMore.value && !loading.value) {
    fetchDiaryList();
  }
};

// 跳转到日记详情
const goToDiaryDetail = (diary) => {
  uni.navigateTo({
    url: `/pages/bundle/world/diary/detail?id=${diary.id}`
  });
};

// 跳转到写日记
const goToWriteDiary = () => {
  uni.navigateTo({
    url: '/pages/bundle/world/diary/post'
  });
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  let date;
  // 处理不同的时间格式
  if (typeof timeStr === 'string') {
    // 如果是字符串格式的时间戳或日期字符串
    if (/^\d+$/.test(timeStr)) {
      // 纯数字字符串，当作时间戳处理
      date = new Date(parseInt(timeStr) * 1000);
    } else {
      // 日期字符串
      date = new Date(timeStr);
    }
  } else if (typeof timeStr === 'number') {
    // 数字时间戳
    date = new Date(timeStr * 1000);
  } else {
    return '';
  }

  if (isNaN(date.getTime())) {
    console.warn('无效的时间格式:', timeStr);
    return '';
  }

  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;

  return date.toLocaleDateString();
};

onMounted(() => {
  fetchDiaryList();
});
</script>

<template>
  <view class="diary-list-page">
    <!-- 统一导航栏 -->
    <customNavbar title="我的日记">
      <template #right>
        <view class="write-btn" @click="goToWriteDiary">
          <u-icon name="plus" size="44rpx" color="#ffffff"></u-icon>
        </view>
      </template>
    </customNavbar>

    <!-- 日记列表 -->
    <scroll-view 
      scroll-y 
      class="diary-scroll"
      @scrolltolower="onLoadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="diary-list">
        <view 
          v-for="diary in diaryList" 
          :key="diary.id"
          class="diary-item"
          @click="goToDiaryDetail(diary)"
        >
          <view class="diary-content">
            <view class="diary-text">{{ diary.content }}</view>
            <view class="diary-meta">
              <text class="diary-time">{{ formatTime(diary.created_at) }}</text>
              <text class="diary-mood" v-if="diary.mood">{{ diary.mood }}</text>
            </view>
          </view>
          <image 
            v-if="diary.cover_image" 
            :src="diary.cover_image" 
            class="diary-cover"
            mode="aspectFill"
          ></image>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="load-status" v-if="loading && !refreshing">
        <u-loading-icon size="40rpx" color="#6AC086"></u-loading-icon>
        <text class="load-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view class="load-status" v-if="!hasMore && diaryList.length > 0">
        <text class="load-text">没有更多了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && diaryList.length === 0">
        <u-icon name="edit-pen" size="120rpx" color="#cccccc"></u-icon>
        <text class="empty-text">还没有日记，开始记录生活吧</text>
        <view class="empty-btn" @click="goToWriteDiary">
          <text class="btn-text">写日记</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
/* 统一设计变量 */
:root {
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --radius-card: 20rpx;
  --radius-button: 50rpx;
  --color-bg-page: #f8f9fa;
  --color-bg-card: #ffffff;
  --color-text-title: #333333;
  --color-text-body: #666666;
  --color-text-caption: #999999;
  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.diary-list-page {
  height: 100vh;
  background-color: var(--color-bg-page);
  display: flex;
  flex-direction: column;
}

.write-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.diary-scroll {
  flex: 1;
  padding: var(--spacing-lg);
}

.diary-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.diary-item {
  background-color: var(--color-bg-card);
  border-radius: var(--radius-card);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  display: flex;
  gap: var(--spacing-md);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.diary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.diary-text {
  font-size: 28rpx;
  color: var(--color-text-body);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.diary-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.diary-time {
  font-size: 24rpx;
  color: var(--color-text-caption);
}

.diary-mood {
  font-size: 24rpx;
  color: #6AC086;
  background-color: rgba(106, 192, 134, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.diary-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-card);
  flex-shrink: 0;
}

.load-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.load-text {
  font-size: 24rpx;
  color: var(--color-text-caption);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx var(--spacing-lg);
  gap: var(--spacing-lg);
}

.empty-text {
  font-size: 28rpx;
  color: var(--color-text-caption);
}

.empty-btn {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  padding: 24rpx 48rpx;
  box-shadow: var(--shadow-card);

  .btn-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 500;
  }
}
</style>
