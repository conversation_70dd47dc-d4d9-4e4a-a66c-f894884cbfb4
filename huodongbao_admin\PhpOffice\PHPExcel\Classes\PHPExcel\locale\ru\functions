##
## PHPExcel
##

## Copyright (c) 2006 - 2013 PHPExcel
##
## This library is free software; you can redistribute it and/or
## modify it under the terms of the GNU Lesser General Public
## License as published by the Free Software Foundation; either
## version 2.1 of the License, or (at your option) any later version.
##
## This library is distributed in the hope that it will be useful,
## but WITHOUT ANY WARRANTY; without even the implied warranty of
## MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
## Lesser General Public License for more details.
##
## You should have received a copy of the GNU Lesser General Public
## License along with this library; if not, write to the Free Software
## Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
##
## @category   PHPExcel
## @package    PHPExcel_Calculation
## @copyright  Copyright (c) 2006 - 2013 PHPExcel (http://www.codeplex.com/PHPExcel)
## @license    http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt	LGPL
## @version    ##VERSION##, ##DATE##
##
## Data in this file derived from information provided by web-junior (http://www.web-junior.net/)
##
##


##
##	Add-in and Automation functions				Функции надстроек и автоматизации
##
GETPIVOTDATA		= ПОЛУЧИТЬ.ДАННЫЕ.СВОДНОЙ.ТАБЛИЦЫ	##	Возвращает данные, хранящиеся в отчете сводной таблицы.


##
##	Cube functions						Функции Куб
##
CUBEKPIMEMBER		= КУБЭЛЕМЕНТКИП				##	Возвращает свойство ключевого индикатора производительности «(КИП)» и отображает имя «КИП» в ячейке. «КИП» представляет собой количественную величину, такую как ежемесячная валовая прибыль или ежеквартальная текучесть кадров, используемой для контроля эффективности работы организации.
CUBEMEMBER		= КУБЭЛЕМЕНТ				##	Возвращает элемент или кортеж из куба. Используется для проверки существования элемента или кортежа в кубе.
CUBEMEMBERPROPERTY	= КУБСВОЙСТВОЭЛЕМЕНТА			##	Возвращает значение свойства элемента из куба. Используется для проверки существования имени элемента в кубе и возвращает указанное свойство для этого элемента.
CUBERANKEDMEMBER	= КУБПОРЭЛЕМЕНТ				##	Возвращает n-ый или ранжированный элемент в множество. Используется для возвращения одного или нескольких элементов в множество, например, лучшего продавца или 10 лучших студентов.
CUBESET			= КУБМНОЖ				##	Определяет вычислительное множество элементов или кортежей, отправляя на сервер выражение, которое создает множество, а затем возвращает его в Microsoft Office Excel.
CUBESETCOUNT		= КУБЧИСЛОЭЛМНОЖ			##	Возвращает число элементов множества.
CUBEVALUE		= КУБЗНАЧЕНИЕ				##	Возвращает обобщенное значение из куба.


##
##	Database functions					Функции для работы с базами данных
##
DAVERAGE		= ДСРЗНАЧ				##	Возвращает среднее значение выбранных записей базы данных.
DCOUNT			= БСЧЁТ					##	Подсчитывает количество числовых ячеек в базе данных.
DCOUNTA			= БСЧЁТА				##	Подсчитывает количество непустых ячеек в базе данных.
DGET			= БИЗВЛЕЧЬ				##	Извлекает из базы данных одну запись, удовлетворяющую заданному условию.
DMAX			= ДМАКС					##	Возвращает максимальное значение среди выделенных записей базы данных.
DMIN			= ДМИН					##	Возвращает минимальное значение среди выделенных записей базы данных.
DPRODUCT		= БДПРОИЗВЕД				##	Перемножает значения определенного поля в записях базы данных, удовлетворяющих условию.
DSTDEV			= ДСТАНДОТКЛ				##	Оценивает стандартное отклонение по выборке для выделенных записей базы данных.
DSTDEVP			= ДСТАНДОТКЛП				##	Вычисляет стандартное отклонение по генеральной совокупности для выделенных записей базы данных
DSUM			= БДСУММ				##	Суммирует числа в поле для записей базы данных, удовлетворяющих условию.
DVAR			= БДДИСП				##	Оценивает дисперсию по выборке из выделенных записей базы данных
DVARP			= БДДИСПП				##	Вычисляет дисперсию по генеральной совокупности для выделенных записей базы данных


##
##	Date and time functions					Функции даты и времени
##
DATE			= ДАТА					##	Возвращает заданную дату в числовом формате.
DATEVALUE		= ДАТАЗНАЧ				##	Преобразует дату из текстового формата в числовой формат.
DAY			= ДЕНЬ					##	Преобразует дату в числовом формате в день месяца.
DAYS360			= ДНЕЙ360				##	Вычисляет количество дней между двумя датами на основе 360-дневного года.
EDATE			= ДАТАМЕС				##	Возвращает дату в числовом формате, отстоящую на заданное число месяцев вперед или назад от начальной даты.
EOMONTH			= КОНМЕСЯЦА				##	Возвращает дату в числовом формате для последнего дня месяца, отстоящего вперед или назад на заданное число месяцев.
HOUR			= ЧАС					##	Преобразует дату в числовом формате в часы.
MINUTE			= МИНУТЫ				##	Преобразует дату в числовом формате в минуты.
MONTH			= МЕСЯЦ					##	Преобразует дату в числовом формате в месяцы.
NETWORKDAYS		= ЧИСТРАБДНИ				##	Возвращает количество рабочих дней между двумя датами.
NOW			= ТДАТА					##	Возвращает текущую дату и время в числовом формате.
SECOND			= СЕКУНДЫ				##	Преобразует дату в числовом формате в секунды.
TIME			= ВРЕМЯ					##	Возвращает заданное время в числовом формате.
TIMEVALUE		= ВРЕМЗНАЧ				##	Преобразует время из текстового формата в числовой формат.
TODAY			= СЕГОДНЯ				##	Возвращает текущую дату в числовом формате.
WEEKDAY			= ДЕНЬНЕД				##	Преобразует дату в числовом формате в день недели.
WEEKNUM			= НОМНЕДЕЛИ				##	Преобразует числовое представление в число, которое указывает, на какую неделю года приходится указанная дата.
WORKDAY			= РАБДЕНЬ				##	Возвращает дату в числовом формате, отстоящую вперед или назад на заданное количество рабочих дней.
YEAR			= ГОД					##	Преобразует дату в числовом формате в год.
YEARFRAC		= ДОЛЯГОДА				##	Возвращает долю года, которую составляет количество дней между начальной и конечной датами.


##
##	Engineering functions					Инженерные функции
##
BESSELI			= БЕССЕЛЬ.I	 			##	Возвращает модифицированную функцию Бесселя In(x).
BESSELJ			= БЕССЕЛЬ.J				##	Возвращает функцию Бесселя Jn(x).
BESSELK			= БЕССЕЛЬ.K				##	Возвращает модифицированную функцию Бесселя Kn(x).
BESSELY			= БЕССЕЛЬ.Y				##	Возвращает функцию Бесселя Yn(x).
BIN2DEC			= ДВ.В.ДЕС				##	Преобразует двоичное число в десятичное.
BIN2HEX			= ДВ.В.ШЕСТН				##	Преобразует двоичное число в шестнадцатеричное.
BIN2OCT			= ДВ.В.ВОСЬМ				##	Преобразует двоичное число в восьмеричное.
COMPLEX			= КОМПЛЕКСН				##	Преобразует коэффициенты при вещественной и мнимой частях комплексного числа в комплексное число.
CONVERT			= ПРЕОБР				##	Преобразует число из одной системы единиц измерения в другую.
DEC2BIN			= ДЕС.В.ДВ				##	Преобразует десятичное число в двоичное.
DEC2HEX			= ДЕС.В.ШЕСТН				##	Преобразует десятичное число в шестнадцатеричное.
DEC2OCT			= ДЕС.В.ВОСЬМ				##	Преобразует десятичное число в восьмеричное.
DELTA			= ДЕЛЬТА				##	Проверяет равенство двух значений.
ERF			= ФОШ					##	Возвращает функцию ошибки.
ERFC			= ДФОШ					##	Возвращает дополнительную функцию ошибки.
GESTEP			= ПОРОГ					##	Проверяет, не превышает ли данное число порогового значения.
HEX2BIN			= ШЕСТН.В.ДВ				##	Преобразует шестнадцатеричное число в двоичное.
HEX2DEC			= ШЕСТН.В.ДЕС				##	Преобразует шестнадцатеричное число в десятичное.
HEX2OCT			= ШЕСТН.В.ВОСЬМ				##	Преобразует шестнадцатеричное число в восьмеричное.
IMABS			= МНИМ.ABS				##	Возвращает абсолютную величину (модуль) комплексного числа.
IMAGINARY		= МНИМ.ЧАСТЬ				##	Возвращает коэффициент при мнимой части комплексного числа.
IMARGUMENT		= МНИМ.АРГУМЕНТ				##	Возвращает значение аргумента комплексного числа (тета) — угол, выраженный в радианах.
IMCONJUGATE		= МНИМ.СОПРЯЖ				##	Возвращает комплексно-сопряженное комплексное число.
IMCOS			= МНИМ.COS				##	Возвращает косинус комплексного числа.
IMDIV			= МНИМ.ДЕЛ				##	Возвращает частное от деления двух комплексных чисел.
IMEXP			= МНИМ.EXP				##	Возвращает экспоненту комплексного числа.
IMLN			= МНИМ.LN				##	Возвращает натуральный логарифм комплексного числа.
IMLOG10			= МНИМ.LOG10				##	Возвращает обычный (десятичный) логарифм комплексного числа.
IMLOG2			= МНИМ.LOG2				##	Возвращает двоичный логарифм комплексного числа.
IMPOWER			= МНИМ.СТЕПЕНЬ				##	Возвращает комплексное число, возведенное в целую степень.
IMPRODUCT		= МНИМ.ПРОИЗВЕД				##	Возвращает произведение от 2 до 29 комплексных чисел.
IMREAL			= МНИМ.ВЕЩ				##	Возвращает коэффициент при вещественной части комплексного числа.
IMSIN			= МНИМ.SIN				##	Возвращает синус комплексного числа.
IMSQRT			= МНИМ.КОРЕНЬ				##	Возвращает значение квадратного корня из комплексного числа.
IMSUB			= МНИМ.РАЗН				##	Возвращает разность двух комплексных чисел.
IMSUM			= МНИМ.СУММ				##	Возвращает сумму комплексных чисел.
OCT2BIN			= ВОСЬМ.В.ДВ				##	Преобразует восьмеричное число в двоичное.
OCT2DEC			= ВОСЬМ.В.ДЕС				##	Преобразует восьмеричное число в десятичное.
OCT2HEX			= ВОСЬМ.В.ШЕСТН				##	Преобразует восьмеричное число в шестнадцатеричное.


##
##	Financial functions					Финансовые функции
##
ACCRINT			= НАКОПДОХОД				##	Возвращает накопленный процент по ценным бумагам с периодической выплатой процентов.
ACCRINTM		= НАКОПДОХОДПОГАШ			##	Возвращает накопленный процент по ценным бумагам, проценты по которым выплачиваются в срок погашения.
AMORDEGRC		= АМОРУМ				##	Возвращает величину амортизации для каждого периода, используя коэффициент амортизации.
AMORLINC		= АМОРУВ				##	Возвращает величину амортизации для каждого периода.
COUPDAYBS		= ДНЕЙКУПОНДО				##	Возвращает количество дней от начала действия купона до даты соглашения.
COUPDAYS		= ДНЕЙКУПОН				##	Возвращает число дней в периоде купона, содержащем дату соглашения.
COUPDAYSNC		= ДНЕЙКУПОНПОСЛЕ			##	Возвращает число дней от даты соглашения до срока следующего купона.
COUPNCD			= ДАТАКУПОНПОСЛЕ			##	Возвращает следующую дату купона после даты соглашения.
COUPNUM			= ЧИСЛКУПОН				##	Возвращает количество купонов, которые могут быть оплачены между датой соглашения и сроком вступления в силу.
COUPPCD			= ДАТАКУПОНДО				##	Возвращает предыдущую дату купона перед датой соглашения.
CUMIPMT			= ОБЩПЛАТ				##	Возвращает общую выплату, произведенную между двумя периодическими выплатами.
CUMPRINC		= ОБЩДОХОД				##	Возвращает общую выплату по займу между двумя периодами.
DB			= ФУО					##	Возвращает величину амортизации актива для заданного периода, рассчитанную методом фиксированного уменьшения остатка.
DDB			= ДДОБ					##	Возвращает величину амортизации актива за данный период, используя метод двойного уменьшения остатка или иной явно указанный метод.
DISC			= СКИДКА				##	Возвращает норму скидки для ценных бумаг.
DOLLARDE		= РУБЛЬ.ДЕС				##	Преобразует цену в рублях, выраженную в виде дроби, в цену в рублях, выраженную десятичным числом.
DOLLARFR		= РУБЛЬ.ДРОБЬ				##	Преобразует цену в рублях, выраженную десятичным числом, в цену в рублях, выраженную в виде дроби.
DURATION		= ДЛИТ					##	Возвращает ежегодную продолжительность действия ценных бумаг с периодическими выплатами по процентам.
EFFECT			= ЭФФЕКТ				##	Возвращает действующие ежегодные процентные ставки.
FV			= БС					##	Возвращает будущую стоимость инвестиции.
FVSCHEDULE		= БЗРАСПИС				##	Возвращает будущую стоимость первоначальной основной суммы после начисления ряда сложных процентов.
INTRATE			= ИНОРМА				##	Возвращает процентную ставку для полностью инвестированных ценных бумаг.
IPMT			= ПРПЛТ					##	Возвращает величину выплаты прибыли на вложения за данный период.
IRR			= ВСД					##	Возвращает внутреннюю ставку доходности для ряда потоков денежных средств.
ISPMT			= ПРОЦПЛАТ				##	Вычисляет выплаты за указанный период инвестиции.
MDURATION		= МДЛИТ					##	Возвращает модифицированную длительность Маколея для ценных бумаг с предполагаемой номинальной стоимостью 100 рублей.
MIRR			= МВСД					##	Возвращает внутреннюю ставку доходности, при которой положительные и отрицательные денежные потоки имеют разные значения ставки.
NOMINAL			= НОМИНАЛ				##	Возвращает номинальную годовую процентную ставку.
NPER			= КПЕР					##	Возвращает общее количество периодов выплаты для данного вклада.
NPV			= ЧПС					##	Возвращает чистую приведенную стоимость инвестиции, основанной на серии периодических денежных потоков и ставке дисконтирования.
ODDFPRICE		= ЦЕНАПЕРВНЕРЕГ				##	Возвращает цену за 100 рублей нарицательной стоимости ценных бумаг с нерегулярным первым периодом.
ODDFYIELD		= ДОХОДПЕРВНЕРЕГ			##	Возвращает доход по ценным бумагам с нерегулярным первым периодом.
ODDLPRICE		= ЦЕНАПОСЛНЕРЕГ				##	Возвращает цену за 100 рублей нарицательной стоимости ценных бумаг с нерегулярным последним периодом.
ODDLYIELD		= ДОХОДПОСЛНЕРЕГ			##	Возвращает доход по ценным бумагам с нерегулярным последним периодом.
PMT			= ПЛТ					##	Возвращает величину выплаты за один период аннуитета.
PPMT			= ОСПЛТ					##	Возвращает величину выплат в погашение основной суммы по инвестиции за заданный период.
PRICE			= ЦЕНА					##	Возвращает цену за 100 рублей нарицательной стоимости ценных бумаг, по которым производится периодическая выплата процентов.
PRICEDISC		= ЦЕНАСКИДКА				##	Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, на которые сделана скидка.
PRICEMAT		= ЦЕНАПОГАШ				##	Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, проценты по которым выплачиваются в срок погашения.
PV			= ПС					##	Возвращает приведенную (к текущему моменту) стоимость инвестиции.
RATE			= СТАВКА				##	Возвращает процентную ставку по аннуитету за один период.
RECEIVED		= ПОЛУЧЕНО				##	Возвращает сумму, полученную к сроку погашения полностью обеспеченных ценных бумаг.
SLN			= АПЛ					##	Возвращает величину линейной амортизации актива за один период.
SYD			= АСЧ					##	Возвращает величину амортизации актива за данный период, рассчитанную методом суммы годовых чисел.
TBILLEQ			= РАВНОКЧЕК				##	Возвращает эквивалентный облигации доход по казначейскому чеку.
TBILLPRICE		= ЦЕНАКЧЕК				##	Возвращает цену за 100 рублей нарицательной стоимости для казначейского чека.
TBILLYIELD		= ДОХОДКЧЕК				##	Возвращает доход по казначейскому чеку.
VDB			= ПУО					##	Возвращает величину амортизации актива для указанного или частичного периода при использовании метода сокращающегося баланса.
XIRR			= ЧИСТВНДОХ				##	Возвращает внутреннюю ставку доходности для графика денежных потоков, которые не обязательно носят периодический характер.
XNPV			= ЧИСТНЗ				##	Возвращает чистую приведенную стоимость для денежных потоков, которые не обязательно являются периодическими.
YIELD			= ДОХОД					##	Возвращает доход от ценных бумаг, по которым производятся периодические выплаты процентов.
YIELDDISC		= ДОХОДСКИДКА				##	Возвращает годовой доход по ценным бумагам, на которые сделана скидка (пример — казначейские чеки).
YIELDMAT		= ДОХОДПОГАШ				##	Возвращает годовой доход от ценных бумаг, проценты по которым выплачиваются в срок погашения.


##
##	Information functions					Информационные функции
##
CELL			= ЯЧЕЙКА				##	Возвращает информацию о формате, расположении или содержимом ячейки.
ERROR.TYPE		= ТИП.ОШИБКИ				##	Возвращает числовой код, соответствующий типу ошибки.
INFO			= ИНФОРМ				##	Возвращает информацию о текущей операционной среде.
ISBLANK			= ЕПУСТО				##	Возвращает значение ИСТИНА, если аргумент является ссылкой на пустую ячейку.
ISERR			= ЕОШ					##	Возвращает значение ИСТИНА, если аргумент ссылается на любое значение ошибки, кроме #Н/Д.
ISERROR			= ЕОШИБКА				##	Возвращает значение ИСТИНА, если аргумент ссылается на любое значение ошибки.
ISEVEN			= ЕЧЁТН					##	Возвращает значение ИСТИНА, если значение аргумента является четным числом.
ISLOGICAL		= ЕЛОГИЧ				##	Возвращает значение ИСТИНА, если аргумент ссылается на логическое значение.
ISNA			= ЕНД					##	Возвращает значение ИСТИНА, если аргумент ссылается на значение ошибки #Н/Д.
ISNONTEXT		= ЕНЕТЕКСТ				##	Возвращает значение ИСТИНА, если значение аргумента не является текстом.
ISNUMBER		= ЕЧИСЛО				##	Возвращает значение ИСТИНА, если аргумент ссылается на число.
ISODD			= ЕНЕЧЁТ				##	Возвращает значение ИСТИНА, если значение аргумента является нечетным числом.
ISREF			= ЕССЫЛКА				##	Возвращает значение ИСТИНА, если значение аргумента является ссылкой.
ISTEXT			= ЕТЕКСТ				##	Возвращает значение ИСТИНА, если значение аргумента является текстом.
N			= Ч					##	Возвращает значение, преобразованное в число.
NA			= НД					##	Возвращает значение ошибки #Н/Д.
TYPE			= ТИП					##	Возвращает число, обозначающее тип данных значения.


##
##	Logical functions					Логические функции
##
AND			= И					##	Renvoie VRAI si tous ses arguments sont VRAI.
FALSE			= ЛОЖЬ					##	Возвращает логическое значение ЛОЖЬ.
IF			= ЕСЛИ					##	Выполняет проверку условия.
IFERROR			= ЕСЛИОШИБКА				##	Возвращает введённое значение, если вычисление по формуле вызывает ошибку; в противном случае функция возвращает результат вычисления.
NOT			= НЕ					##	Меняет логическое значение своего аргумента на противоположное.
OR			= ИЛИ					##	Возвращает значение ИСТИНА, если хотя бы один аргумент имеет значение ИСТИНА.
TRUE			= ИСТИНА				##	Возвращает логическое значение ИСТИНА.


##
##	Lookup and reference functions				Функции ссылки и поиска
##
ADDRESS			= АДРЕС					##	Возвращает ссылку на отдельную ячейку листа в виде текста.
AREAS			= ОБЛАСТИ				##	Возвращает количество областей в ссылке.
CHOOSE			= ВЫБОР					##	Выбирает значение из списка значений по индексу.
COLUMN			= СТОЛБЕЦ				##	Возвращает номер столбца, на который указывает ссылка.
COLUMNS			= ЧИСЛСТОЛБ				##	Возвращает количество столбцов в ссылке.
HLOOKUP			= ГПР					##	Ищет в первой строке массива и возвращает значение отмеченной ячейки
HYPERLINK		= ГИПЕРССЫЛКА				##	Создает ссылку, открывающую документ, который находится на сервере сети, в интрасети или в Интернете.
INDEX			= ИНДЕКС				##	Использует индекс для выбора значения из ссылки или массива.
INDIRECT		= ДВССЫЛ				##	Возвращает ссылку, заданную текстовым значением.
LOOKUP			= ПРОСМОТР				##	Ищет значения в векторе или массиве.
MATCH			= ПОИСКПОЗ				##	Ищет значения в ссылке или массиве.
OFFSET			= СМЕЩ					##	Возвращает смещение ссылки относительно заданной ссылки.
ROW			= СТРОКА				##	Возвращает номер строки, определяемой ссылкой.
ROWS			= ЧСТРОК				##	Возвращает количество строк в ссылке.
RTD			= ДРВ					##	Извлекает данные реального времени из программ, поддерживающих автоматизацию COM (Программирование объектов. Стандартное средство для работы с объектами некоторого приложения из другого приложения или средства разработки. Программирование объектов (ранее называемое программированием OLE) является функцией модели COM (Component Object Model, модель компонентных объектов).).
TRANSPOSE		= ТРАНСП				##	Возвращает транспонированный массив.
VLOOKUP			= ВПР					##	Ищет значение в первом столбце массива и возвращает значение из ячейки в найденной строке и указанном столбце.


##
##	Math and trigonometry functions				Математические и тригонометрические функции
##
ABS			= ABS					##	Возвращает модуль (абсолютную величину) числа.
ACOS			= ACOS					##	Возвращает арккосинус числа.
ACOSH			= ACOSH					##	Возвращает гиперболический арккосинус числа.
ASIN			= ASIN					##	Возвращает арксинус числа.
ASINH			= ASINH					##	Возвращает гиперболический арксинус числа.
ATAN			= ATAN					##	Возвращает арктангенс числа.
ATAN2			= ATAN2					##	Возвращает арктангенс для заданных координат x и y.
ATANH			= ATANH					##	Возвращает гиперболический арктангенс числа.
CEILING			= ОКРВВЕРХ				##	Округляет число до ближайшего целого или до ближайшего кратного указанному значению.
COMBIN			= ЧИСЛКОМБ				##	Возвращает количество комбинаций для заданного числа объектов.
COS			= COS					##	Возвращает косинус числа.
COSH			= COSH					##	Возвращает гиперболический косинус числа.
DEGREES			= ГРАДУСЫ				##	Преобразует радианы в градусы.
EVEN			= ЧЁТН					##	Округляет число до ближайшего четного целого.
EXP			= EXP					##	Возвращает число e, возведенное в указанную степень.
FACT			= ФАКТР					##	Возвращает факториал числа.
FACTDOUBLE		= ДВФАКТР				##	Возвращает двойной факториал числа.
FLOOR			= ОКРВНИЗ				##	Округляет число до ближайшего меньшего по модулю значения.
GCD			= НОД					##	Возвращает наибольший общий делитель.
INT			= ЦЕЛОЕ					##	Округляет число до ближайшего меньшего целого.
LCM			= НОК					##	Возвращает наименьшее общее кратное.
LN			= LN					##	Возвращает натуральный логарифм числа.
LOG			= LOG					##	Возвращает логарифм числа по заданному основанию.
LOG10			= LOG10					##	Возвращает десятичный логарифм числа.
MDETERM			= МОПРЕД				##	Возвращает определитель матрицы массива.
MINVERSE		= МОБР					##	Возвращает обратную матрицу массива.
MMULT			= МУМНОЖ				##	Возвращает произведение матриц двух массивов.
MOD			= ОСТАТ					##	Возвращает остаток от деления.
MROUND			= ОКРУГЛТ				##	Возвращает число, округленное с требуемой точностью.
MULTINOMIAL		= МУЛЬТИНОМ				##	Возвращает мультиномиальный коэффициент множества чисел.
ODD			= НЕЧЁТ					##	Округляет число до ближайшего нечетного целого.
PI			= ПИ					##	Возвращает число пи.
POWER			= СТЕПЕНЬ				##	Возвращает результат возведения числа в степень.
PRODUCT			= ПРОИЗВЕД				##	Возвращает произведение аргументов.
QUOTIENT		= ЧАСТНОЕ				##	Возвращает целую часть частного при делении.
RADIANS			= РАДИАНЫ				##	Преобразует градусы в радианы.
RAND			= СЛЧИС					##	Возвращает случайное число в интервале от 0 до 1.
RANDBETWEEN		= СЛУЧМЕЖДУ				##	Возвращает случайное число в интервале между двумя заданными числами.
ROMAN			= РИМСКОЕ				##	Преобразует арабские цифры в римские в виде текста.
ROUND			= ОКРУГЛ				##	Округляет число до указанного количества десятичных разрядов.
ROUNDDOWN		= ОКРУГЛВНИЗ				##	Округляет число до ближайшего меньшего по модулю значения.
ROUNDUP			= ОКРУГЛВВЕРХ				##	Округляет число до ближайшего большего по модулю значения.
SERIESSUM		= РЯД.СУММ				##	Возвращает сумму степенного ряда, вычисленную по формуле.
SIGN			= ЗНАК					##	Возвращает знак числа.
SIN			= SIN					##	Возвращает синус заданного угла.
SINH			= SINH					##	Возвращает гиперболический синус числа.
SQRT			= КОРЕНЬ				##	Возвращает положительное значение квадратного корня.
SQRTPI			= КОРЕНЬПИ				##	Возвращает квадратный корень из значения выражения (число * ПИ).
SUBTOTAL		= ПРОМЕЖУТОЧНЫЕ.ИТОГИ			##	Возвращает промежуточный итог в списке или базе данных.
SUM			= СУММ					##	Суммирует аргументы.
SUMIF			= СУММЕСЛИ				##	Суммирует ячейки, удовлетворяющие заданному условию.
SUMIFS			= СУММЕСЛИМН				##	Суммирует диапазон ячеек, удовлетворяющих нескольким условиям. 
SUMPRODUCT		= СУММПРОИЗВ				##	Возвращает сумму произведений соответствующих элементов массивов.
SUMSQ			= СУММКВ				##	Возвращает сумму квадратов аргументов.
SUMX2MY2		= СУММРАЗНКВ				##	Возвращает сумму разностей квадратов соответствующих значений в двух массивах.
SUMX2PY2		= СУММСУММКВ				##	Возвращает сумму сумм квадратов соответствующих элементов двух массивов.
SUMXMY2			= СУММКВРАЗН				##	Возвращает сумму квадратов разностей соответствующих значений в двух массивах.
TAN			= TAN					##	Возвращает тангенс числа.
TANH			= TANH					##	Возвращает гиперболический тангенс числа.
TRUNC			= ОТБР					##	Отбрасывает дробную часть числа.


##
##	Statistical functions					Статистические функции
##
AVEDEV			= СРОТКЛ				##	Возвращает среднее арифметическое абсолютных значений отклонений точек данных от среднего.
AVERAGE			= СРЗНАЧ				##	Возвращает среднее арифметическое аргументов.
AVERAGEA		= СРЗНАЧА				##	Возвращает среднее арифметическое аргументов, включая числа, текст и логические значения.
AVERAGEIF		= СРЗНАЧЕСЛИ 				##	Возвращает среднее значение (среднее арифметическое) всех ячеек в диапазоне, которые удовлетворяют данному условию.
AVERAGEIFS		= СРЗНАЧЕСЛИМН 				##	Возвращает среднее значение (среднее арифметическое) всех ячеек, которые удовлетворяют нескольким условиям. 
BETADIST		= БЕТАРАСП				##	Возвращает интегральную функцию бета-распределения.
BETAINV			= БЕТАОБР				##	Возвращает обратную интегральную функцию указанного бета-распределения.
BINOMDIST		= БИНОМРАСП				##	Возвращает отдельное значение биномиального распределения.
CHIDIST			= ХИ2РАСП				##	Возвращает одностороннюю вероятность распределения хи-квадрат.
CHIINV			= ХИ2ОБР				##	Возвращает обратное значение односторонней вероятности распределения хи-квадрат.
CHITEST			= ХИ2ТЕСТ				##	Возвращает тест на независимость.
CONFIDENCE		= ДОВЕРИТ				##	Возвращает доверительный интервал для среднего значения по генеральной совокупности.
CORREL			= КОРРЕЛ				##	Возвращает коэффициент корреляции между двумя множествами данных.
COUNT			= СЧЁТ					##	Подсчитывает количество чисел в списке аргументов.
COUNTA			= СЧЁТЗ					##	Подсчитывает количество значений в списке аргументов.
COUNTBLANK		= СЧИТАТЬПУСТОТЫ			##	Подсчитывает количество пустых ячеек в диапазоне
COUNTIF			= СЧЁТЕСЛИ 				##	Подсчитывает количество ячеек в диапазоне, удовлетворяющих заданному условию
COUNTIFS		= СЧЁТЕСЛИМН				##	Подсчитывает количество ячеек внутри диапазона, удовлетворяющих нескольким условиям.
COVAR			= КОВАР					##	Возвращает ковариацию, среднее произведений парных отклонений
CRITBINOM		= КРИТБИНОМ				##	Возвращает наименьшее значение, для которого интегральное биномиальное распределение меньше или равно заданному критерию.
DEVSQ			= КВАДРОТКЛ				##	Возвращает сумму квадратов отклонений.
EXPONDIST		= ЭКСПРАСП				##	Возвращает экспоненциальное распределение.
FDIST			= FРАСП					##	Возвращает F-распределение вероятности.
FINV			= FРАСПОБР				##	Возвращает обратное значение для F-распределения вероятности.
FISHER			= ФИШЕР					##	Возвращает преобразование Фишера.
FISHERINV		= ФИШЕРОБР				##	Возвращает обратное преобразование Фишера.
FORECAST		= ПРЕДСКАЗ				##	Возвращает значение линейного тренда.
FREQUENCY		= ЧАСТОТА				##	Возвращает распределение частот в виде вертикального массива.
FTEST			= ФТЕСТ					##	Возвращает результат F-теста.
GAMMADIST		= ГАММАРАСП				##	Возвращает гамма-распределение.
GAMMAINV		= ГАММАОБР				##	Возвращает обратное гамма-распределение.
GAMMALN			= ГАММАНЛОГ				##	Возвращает натуральный логарифм гамма функции, Γ(x).
GEOMEAN			= СРГЕОМ				##	Возвращает среднее геометрическое.
GROWTH			= РОСТ					##	Возвращает значения в соответствии с экспоненциальным трендом.
HARMEAN			= СРГАРМ				##	Возвращает среднее гармоническое.
HYPGEOMDIST		= ГИПЕРГЕОМЕТ				##	Возвращает гипергеометрическое распределение.
INTERCEPT		= ОТРЕЗОК				##	Возвращает отрезок, отсекаемый на оси линией линейной регрессии.
KURT			= ЭКСЦЕСС				##	Возвращает эксцесс множества данных.
LARGE			= НАИБОЛЬШИЙ				##	Возвращает k-ое наибольшее значение в множестве данных.
LINEST			= ЛИНЕЙН				##	Возвращает параметры линейного тренда.
LOGEST			= ЛГРФПРИБЛ				##	Возвращает параметры экспоненциального тренда.
LOGINV			= ЛОГНОРМОБР				##	Возвращает обратное логарифмическое нормальное распределение.
LOGNORMDIST		= ЛОГНОРМРАСП				##	Возвращает интегральное логарифмическое нормальное распределение.
MAX			= МАКС					##	Возвращает наибольшее значение в списке аргументов.
MAXA			= МАКСА					##	Возвращает наибольшее значение в списке аргументов, включая числа, текст и логические значения.
MEDIAN			= МЕДИАНА				##	Возвращает медиану заданных чисел.
MIN			= МИН					##	Возвращает наименьшее значение в списке аргументов.
MINA			= МИНА					##	Возвращает наименьшее значение в списке аргументов, включая числа, текст и логические значения.
MODE			= МОДА					##	Возвращает значение моды множества данных.
NEGBINOMDIST		= ОТРБИНОМРАСП				##	Возвращает отрицательное биномиальное распределение.
NORMDIST		= НОРМРАСП				##	Возвращает нормальную функцию распределения.
NORMINV			= НОРМОБР				##	Возвращает обратное нормальное распределение.
NORMSDIST		= НОРМСТРАСП				##	Возвращает стандартное нормальное интегральное распределение.
NORMSINV		= НОРМСТОБР				##	Возвращает обратное значение стандартного нормального распределения.
PEARSON			= ПИРСОН				##	Возвращает коэффициент корреляции Пирсона.
PERCENTILE		= ПЕРСЕНТИЛЬ				##	Возвращает k-ую персентиль для значений диапазона.
PERCENTRANK		= ПРОЦЕНТРАНГ				##	Возвращает процентную норму значения в множестве данных.
PERMUT			= ПЕРЕСТ				##	Возвращает количество перестановок для заданного числа объектов.
POISSON			= ПУАССОН				##	Возвращает распределение Пуассона.
PROB			= ВЕРОЯТНОСТЬ				##	Возвращает вероятность того, что значение из диапазона находится внутри заданных пределов.
QUARTILE		= КВАРТИЛЬ				##	Возвращает квартиль множества данных.
RANK			= РАНГ					##	Возвращает ранг числа в списке чисел.
RSQ			= КВПИРСОН				##	Возвращает квадрат коэффициента корреляции Пирсона.
SKEW			= СКОС					##	Возвращает асимметрию распределения.
SLOPE			= НАКЛОН				##	Возвращает наклон линии линейной регрессии.
SMALL			= НАИМЕНЬШИЙ				##	Возвращает k-ое наименьшее значение в множестве данных.
STANDARDIZE		= НОРМАЛИЗАЦИЯ				##	Возвращает нормализованное значение.
STDEV			= СТАНДОТКЛОН				##	Оценивает стандартное отклонение по выборке.
STDEVA			= СТАНДОТКЛОНА				##	Оценивает стандартное отклонение по выборке, включая числа, текст и логические значения.
STDEVP			= СТАНДОТКЛОНП				##	Вычисляет стандартное отклонение по генеральной совокупности.
STDEVPA			= СТАНДОТКЛОНПА				##	Вычисляет стандартное отклонение по генеральной совокупности, включая числа, текст и логические значения.
STEYX			= СТОШYX				##	Возвращает стандартную ошибку предсказанных значений y для каждого значения x в регрессии.
TDIST			= СТЬЮДРАСП				##	Возвращает t-распределение Стьюдента.
TINV			= СТЬЮДРАСПОБР				##	Возвращает обратное t-распределение Стьюдента.
TREND			= ТЕНДЕНЦИЯ				##	Возвращает значения в соответствии с линейным трендом.
TRIMMEAN		= УРЕЗСРЕДНЕЕ				##	Возвращает среднее внутренности множества данных.
TTEST			= ТТЕСТ					##	Возвращает вероятность, соответствующую критерию Стьюдента.
VAR			= ДИСП					##	Оценивает дисперсию по выборке.
VARA			= ДИСПА					##	Оценивает дисперсию по выборке, включая числа, текст и логические значения.
VARP			= ДИСПР					##	Вычисляет дисперсию для генеральной совокупности.
VARPA			= ДИСПРА				##	Вычисляет дисперсию для генеральной совокупности, включая числа, текст и логические значения.
WEIBULL			= ВЕЙБУЛЛ				##	Возвращает распределение Вейбулла.
ZTEST			= ZТЕСТ					##	Возвращает двустороннее P-значение z-теста.


##
##	Text functions						Текстовые функции
##
ASC			= ASC					##	Для языков с двухбайтовыми наборами знаков (например, катакана) преобразует полноширинные (двухбайтовые) знаки в полуширинные (однобайтовые).
BAHTTEXT		= БАТТЕКСТ				##	Преобразует число в текст, используя денежный формат ß (БАТ).
CHAR			= СИМВОЛ				##	Возвращает знак с заданным кодом.
CLEAN			= ПЕЧСИМВ				##	Удаляет все непечатаемые знаки из текста.
CODE			= КОДСИМВ				##	Возвращает числовой код первого знака в текстовой строке.
CONCATENATE		= СЦЕПИТЬ				##	Объединяет несколько текстовых элементов в один.
DOLLAR			= РУБЛЬ					##	Преобразует число в текст, используя денежный формат.
EXACT			= СОВПАД				##	Проверяет идентичность двух текстовых значений.
FIND			= НАЙТИ					##	Ищет вхождения одного текстового значения в другом (с учетом регистра).
FINDB			= НАЙТИБ				##	Ищет вхождения одного текстового значения в другом (с учетом регистра).
FIXED			= ФИКСИРОВАННЫЙ				##	Форматирует число и преобразует его в текст с заданным числом десятичных знаков.
JIS			= JIS					##	Для языков с двухбайтовыми наборами знаков (например, катакана) преобразует полуширинные (однобайтовые) знаки в текстовой строке в полноширинные (двухбайтовые).
LEFT			= ЛЕВСИМВ				##	Возвращает крайние слева знаки текстового значения.
LEFTB			= ЛЕВБ					##	Возвращает крайние слева знаки текстового значения.
LEN			= ДЛСТР					##	Возвращает количество знаков в текстовой строке.
LENB			= ДЛИНБ					##	Возвращает количество знаков в текстовой строке.
LOWER			= СТРОЧН				##	Преобразует все буквы текста в строчные.
MID			= ПСТР					##	Возвращает заданное число знаков из строки текста, начиная с указанной позиции.
MIDB			= ПСТРБ					##	Возвращает заданное число знаков из строки текста, начиная с указанной позиции.
PHONETIC		= PHONETIC				##	Извлекает фонетические (фуригана) знаки из текстовой строки.
PROPER			= ПРОПНАЧ				##	Преобразует первую букву в каждом слове текста в прописную.
REPLACE			= ЗАМЕНИТЬ				##	Заменяет знаки в тексте.
REPLACEB		= ЗАМЕНИТЬБ				##	Заменяет знаки в тексте.
REPT			= ПОВТОР				##	Повторяет текст заданное число раз.
RIGHT			= ПРАВСИМВ				##	Возвращает крайние справа знаки текстовой строки.
RIGHTB			= ПРАВБ					##	Возвращает крайние справа знаки текстовой строки.
SEARCH			= ПОИСК					##	Ищет вхождения одного текстового значения в другом (без учета регистра).
SEARCHB			= ПОИСКБ				##	Ищет вхождения одного текстового значения в другом (без учета регистра).
SUBSTITUTE		= ПОДСТАВИТЬ				##	Заменяет в текстовой строке старый текст новым.
T			= Т					##	Преобразует аргументы в текст.
TEXT			= ТЕКСТ					##	Форматирует число и преобразует его в текст.
TRIM			= СЖПРОБЕЛЫ				##	Удаляет из текста пробелы.
UPPER			= ПРОПИСН				##	Преобразует все буквы текста в прописные.
VALUE			= ЗНАЧЕН				##	Преобразует текстовый аргумент в число.
