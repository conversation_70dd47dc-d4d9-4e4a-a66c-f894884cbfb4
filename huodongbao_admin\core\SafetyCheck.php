<?php
namespace core;
class SafetyCheck{
    /**
     * 过滤提交数据正则
     * @var array
     */
    protected static $filterUrl = [
        'xss' => "\\=\\+\\/v(?:8|9|\\+|\\/)|\\%0acontent\\-(?:id|location|type|transfer\\-encoding)",
    ];
 
    /**
     * 过滤提交数据正则
     * @var array
     */
    protected static $filterArgs = [
        'xss'   => "[\\'\\\"\\;\\*\\<\\>].*\\bon[a-zA-Z]{3,15}[\\s\\r\\n\\v\\f]*\\=|\\b(?:expression)\\(|\\<script[\\s\\\\\\/]|\\<\\!\\[cdata\\[|\\b(?:eval|alert|prompt|msgbox)\\s*\\(|url\\((?:\\#|data|javascript)",
        'sql'   => "[^\\{\\s]{1}(\\s|\\b)+(?:select\\b|update\\b|insert(?:(\\/\\*.*?\\*\\/)|(\\s)|(\\+))+into\\b).+?(?:from\\b|set\\b)|[^\\{\\s]{1}(\\s|\\b)+(?:create|delete|drop|truncate|rename|desc)(?:(\\/\\*.*?\\*\\/)|(\\s)|(\\+))+(?:table\\b|from\\b|database\\b)|into(?:(\\/\\*.*?\\*\\/)|\\s|\\+)+(?:dump|out)file\\b|\\bsleep\\([\\s]*[\\d]+[\\s]*\\)|benchmark\\(([^\\,]*)\\,([^\\,]*)\\)|(?:declare|set|select)\\b.*@|union\\b.*(?:select|all)\\b|(?:select|update|insert|create|delete|drop|grant|truncate|rename|exec|desc|from|table|database|set|where)\\b.*(charset|ascii|bin|char|uncompress|concat|concat_ws|conv|export_set|hex|instr|left|load_file|locate|mid|sub|substring|oct|reverse|right|unhex)\\(|(?:master\\.\\.sysdatabases|msysaccessobjects|msysqueries|sysmodules|mysql\\.db|sys\\.database_name|information_schema\\.|sysobjects|sp_makewebtask|xp_cmdshell|sp_oamethod|sp_addextendedproc|sp_oacreate|xp_regread|sys\\.dbms_export_extension)",
        'other' => "\\.\\.[\\\\\\/].*\\%00([^0-9a-fA-F]|$)|%00[\\'\\\"\\.]",
    ];
	
	private static $warning = [];
    /**
     * 数据过滤
     * @param $filterData
     * @param $filterArgs
     */
    protected static function filterData($filterData, $filterArgs){
        foreach ($filterData as $key => $value) {
            if (!is_array($key)) {
                self::filterCheck($key, $filterArgs);
            } else {
                self::filterData($key, $filterArgs);
            }
            if (!is_array($value)) {
                self::filterCheck($value, $filterArgs);
            } else {
                self::filterData($value, $filterArgs);
            }
        }
    }
 
    /**
     * 数据检查
     * @param $str
     * @param $filterArgs
     */
    protected static function filterCheck($str, $filterArgs){
        foreach ($filterArgs as $key => $value) {
            if (preg_match("/" . $value . "/is", $str) == 1 || preg_match("/" . $value . "/is", urlencode($str)) == 1) {
                //记录日志 - 信息拦截
				$log = "";
				$log .= "【拦截方式：".addslashes($key)."】\r\n";
				$log .= "匹配：".addslashes($str)."\r\n";
				$log .= "时间：".DATETIME."\r\n";
				$log .= "位置：".addslashes(\core\Route::$privileges)."\r\n";
				$log .= "IP：".addslashes(IP)."\r\n";
				$log .= "REQUEST_URI：".addslashes($_SERVER["REQUEST_URI"])."\r\n";
				//
				$header = "";
				foreach ($_SERVER as $name => $value){
					if(substr($name, 0, 5) == 'HTTP_'){
					   $header .= "\r\n\t[ " . str_replace('_', ' ', substr($name, 5)) . ": " . $value . " ] ";
					}
				}
				$get = "";
				if(!empty($_GET)){
					foreach($_GET as $gk=>$gv){
						$get .= "\r\n\t[ {$gk} = {$gv} ] ";
					}
				}
				$post = "";
				if(!empty($_POST)){
					foreach($_POST as $pk=>$pv){
						$post .= "\r\n\t[ {$pk} = {$pv} ] ";
					}
				}
				$file = "";
				if(!empty($_FILES)){
					$file = serialize($_FILES);
				}
				//
				$log .= "REQUEST_HEADER：".addslashes($header)."\r\n";
				$log .= "GET：".addslashes($get)."\r\n";
				$log .= "POST：".addslashes($post)."\r\n";
				$log .= "INPUT：".addslashes(\core\Route::$rawPost)."\r\n";
				$log .= "FILES：".addslashes($file)."\r\n";
				self::$warning[] = $log;
            }
        }
    }

    /**
     * 数据检查入口
     */
    public static function run(){
        $referer     = empty($_SERVER['HTTP_REFERER']) ? [] : [$_SERVER['HTTP_REFERER']];
        $queryString = empty($_SERVER["QUERY_STRING"]) ? [] : [$_SERVER["QUERY_STRING"]];
        $rawPost = empty(\core\Route::$rawPost) ? [] : [\core\Route::$rawPost];
        $params = empty(\core\Route::$params) ? [] : \core\Route::$params;
 
        self::filterData($queryString, self::$filterUrl);
        self::filterData($_GET, self::$filterArgs);
        self::filterData($_POST, self::$filterArgs);
        self::filterData($rawPost, self::$filterArgs);
        self::filterData($_COOKIE, self::$filterArgs);
        self::filterData($referer, self::$filterArgs);
        self::filterData($params, self::$filterArgs);
		
		if(empty(self::$warning))return true;
		
		foreach(self::$warning as $v){
			_log_($v);
		}
		\core\Error::securityCheck();
    }
}
