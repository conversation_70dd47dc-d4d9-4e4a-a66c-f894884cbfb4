/**
 * 🔧 P1-3修复：统一的前端权限检查工具函数
 * 确保各页面权限逻辑一致，避免重复代码
 */

import { store } from '@/store'

/**
 * 角色类型常量定义
 */
export const ROLE_TYPES = {
  ADMIN: '0',           // 管理员
  BRANCH_PRESIDENT: '1', // 分会长
  NORMAL_USER: '2',     // 普通用户
  VENUE_PROVIDER: '3',  // 场地与活动第三方
  CITY_PRESIDENT: '4',  // 城市分会长
  VENUE_ONLY: '5'       // 场地第三方-不管理分会
}

/**
 * 权限类型常量定义
 */
export const PERMISSION_TYPES = {
  ACTIVITY_INCOME: 'activity_income',     // 活动收入权限
  BRANCH_MANAGEMENT: 'branch_management', // 分会管理权限
  ACTIVITY_REVIEW: 'activity_review',     // 活动审核权限
  TRIAL_SHARE: 'trial_share',            // 体验券分享权限
  ACTIVITY_PUBLISH: 'activity_publish'    // 活动发布权限
}

/**
 * 权限配置映射表
 * 🔧 P1-3修复：与后端权限逻辑完全一致
 */
const PERMISSION_CONFIG = {
  // 活动收入权限：管理员(0)、场地与活动第三方(3)、城市分会长(4)、场地第三方-不管理分会(5)
  [PERMISSION_TYPES.ACTIVITY_INCOME]: [
    ROLE_TYPES.ADMIN,
    ROLE_TYPES.VENUE_PROVIDER,
    ROLE_TYPES.CITY_PRESIDENT,
    ROLE_TYPES.VENUE_ONLY
  ],
  
  // 分会管理权限：分会长(1)、场地与活动第三方(3)、城市分会长(4)
  [PERMISSION_TYPES.BRANCH_MANAGEMENT]: [
    ROLE_TYPES.BRANCH_PRESIDENT,
    ROLE_TYPES.VENUE_PROVIDER,
    ROLE_TYPES.CITY_PRESIDENT
  ],
  
  // 活动审核权限：分会长(1)、场地与活动第三方(3)、城市分会长(4)
  [PERMISSION_TYPES.ACTIVITY_REVIEW]: [
    ROLE_TYPES.BRANCH_PRESIDENT,
    ROLE_TYPES.VENUE_PROVIDER,
    ROLE_TYPES.CITY_PRESIDENT
  ],
  
  // 体验券分享权限：管理员(0)、分会长(1)、场地与活动第三方(3)、城市分会长(4)、场地第三方-不管理分会(5)
  [PERMISSION_TYPES.TRIAL_SHARE]: [
    ROLE_TYPES.ADMIN,
    ROLE_TYPES.BRANCH_PRESIDENT,
    ROLE_TYPES.VENUE_PROVIDER,
    ROLE_TYPES.CITY_PRESIDENT,
    ROLE_TYPES.VENUE_ONLY
  ],
  
  // 活动发布权限：管理员(0)、场地与活动第三方(3)、城市分会长(4)、场地第三方-不管理分会(5)
  [PERMISSION_TYPES.ACTIVITY_PUBLISH]: [
    ROLE_TYPES.ADMIN,
    ROLE_TYPES.VENUE_PROVIDER,
    ROLE_TYPES.CITY_PRESIDENT,
    ROLE_TYPES.VENUE_ONLY
  ]
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息对象
 */
export const getCurrentUser = () => {
  return store().$state.userInfo
}

/**
 * 获取当前用户角色类型
 * @returns {string} 角色类型字符串
 */
export const getCurrentUserRole = () => {
  const userInfo = getCurrentUser()
  if (!userInfo || userInfo.role_type === undefined || userInfo.role_type === null) {
    return ROLE_TYPES.NORMAL_USER // 默认为普通用户
  }
  
  // 确保类型安全，处理varchar类型
  return String(userInfo.role_type).trim()
}

/**
 * 检查用户是否有指定权限
 * @param {string} permissionType 权限类型
 * @param {Object} userInfo 用户信息（可选，默认使用当前用户）
 * @returns {boolean} 是否有权限
 */
export const checkPermission = (permissionType, userInfo = null) => {
  if (!userInfo) {
    userInfo = getCurrentUser()
  }
  
  if (!userInfo) {
    return false
  }
  
  const roleType = String(userInfo.role_type || ROLE_TYPES.NORMAL_USER).trim()
  const allowedRoles = PERMISSION_CONFIG[permissionType]
  
  if (!allowedRoles) {
    console.warn(`未知的权限类型: ${permissionType}`)
    return false
  }
  
  return allowedRoles.includes(roleType)
}

/**
 * 检查活动收入权限
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否有活动收入权限
 */
export const hasActivityIncomePermission = (userInfo = null) => {
  return checkPermission(PERMISSION_TYPES.ACTIVITY_INCOME, userInfo)
}

/**
 * 检查分会管理权限
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否有分会管理权限
 */
export const hasBranchManagementPermission = (userInfo = null) => {
  return checkPermission(PERMISSION_TYPES.BRANCH_MANAGEMENT, userInfo)
}

/**
 * 检查活动审核权限
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否有活动审核权限
 */
export const hasActivityReviewPermission = (userInfo = null) => {
  return checkPermission(PERMISSION_TYPES.ACTIVITY_REVIEW, userInfo)
}

/**
 * 检查体验券分享权限
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否有体验券分享权限
 */
export const hasTrialSharePermission = (userInfo = null) => {
  return checkPermission(PERMISSION_TYPES.TRIAL_SHARE, userInfo)
}

/**
 * 检查活动发布权限
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否有活动发布权限
 */
export const hasActivityPublishPermission = (userInfo = null) => {
  return checkPermission(PERMISSION_TYPES.ACTIVITY_PUBLISH, userInfo)
}

/**
 * 获取角色显示名称
 * @param {string} roleType 角色类型
 * @returns {string} 角色显示名称
 */
export const getRoleName = (roleType) => {
  const roleNames = {
    [ROLE_TYPES.ADMIN]: '管理员',
    [ROLE_TYPES.BRANCH_PRESIDENT]: '分会长',
    [ROLE_TYPES.NORMAL_USER]: '普通用户',
    [ROLE_TYPES.VENUE_PROVIDER]: '场地与活动第三方',
    [ROLE_TYPES.CITY_PRESIDENT]: '城市分会长',
    [ROLE_TYPES.VENUE_ONLY]: '场地第三方-不管理分会'
  }
  
  return roleNames[String(roleType).trim()] || '未知角色'
}

/**
 * 检查是否为分会长相关角色
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否为分会长相关角色
 */
export const isBranchPresidentRole = (userInfo = null) => {
  if (!userInfo) {
    userInfo = getCurrentUser()
  }
  
  if (!userInfo) {
    return false
  }
  
  const roleType = String(userInfo.role_type || ROLE_TYPES.NORMAL_USER).trim()
  return [ROLE_TYPES.BRANCH_PRESIDENT, ROLE_TYPES.CITY_PRESIDENT].includes(roleType)
}

/**
 * 检查是否为场地提供商角色
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否为场地提供商角色
 */
export const isVenueProviderRole = (userInfo = null) => {
  if (!userInfo) {
    userInfo = getCurrentUser()
  }
  
  if (!userInfo) {
    return false
  }
  
  const roleType = String(userInfo.role_type || ROLE_TYPES.NORMAL_USER).trim()
  return [ROLE_TYPES.VENUE_PROVIDER, ROLE_TYPES.VENUE_ONLY].includes(roleType)
}

/**
 * 权限检查装饰器（用于页面级权限控制）
 * @param {string} permissionType 权限类型
 * @param {Object} options 选项
 * @returns {Function} 装饰器函数
 */
export const requirePermission = (permissionType, options = {}) => {
  const {
    redirectTo = '/pages/bundle/common/login',
    showModal = true,
    modalTitle = '权限不足',
    modalContent = '您没有访问此页面的权限'
  } = options
  
  return (target) => {
    const originalOnLoad = target.onLoad || (() => {})
    
    target.onLoad = function(...args) {
      if (!checkPermission(permissionType)) {
        if (showModal) {
          uni.showModal({
            title: modalTitle,
            content: modalContent,
            showCancel: false,
            success: () => {
              uni.navigateBack()
            }
          })
        } else {
          uni.navigateTo({
            url: redirectTo
          })
        }
        return
      }
      
      originalOnLoad.apply(this, args)
    }
    
    return target
  }
}

/**
 * 批量权限检查
 * @param {Array} permissions 权限类型数组
 * @param {string} operator 操作符：'AND'（所有权限都需要）或'OR'（任一权限即可）
 * @param {Object} userInfo 用户信息（可选）
 * @returns {boolean} 是否满足权限要求
 */
export const checkMultiplePermissions = (permissions, operator = 'OR', userInfo = null) => {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return false
  }
  
  if (operator === 'AND') {
    return permissions.every(permission => checkPermission(permission, userInfo))
  } else {
    return permissions.some(permission => checkPermission(permission, userInfo))
  }
}

// 导出所有常量和函数
export default {
  ROLE_TYPES,
  PERMISSION_TYPES,
  getCurrentUser,
  getCurrentUserRole,
  checkPermission,
  hasActivityIncomePermission,
  hasBranchManagementPermission,
  hasActivityReviewPermission,
  hasTrialSharePermission,
  hasActivityPublishPermission,
  getRoleName,
  isBranchPresidentRole,
  isVenueProviderRole,
  requirePermission,
  checkMultiplePermissions
}
