/**
 * 主题样式
 * 定义应用的风格特性，包括柔和中性色系、纸张纹理背景等
 */

/* 主题颜色变量 - 统一使用设计令牌 */
$judu-primary: var(--color-primary, #6AC086); // 主色调：绿色
$judu-secondary: var(--color-text-secondary, #6c757d); // 次要色调
$judu-accent: var(--color-primary, #6AC086); // 强调色
$judu-dark: var(--color-text-primary, #212529); // 深色
$judu-text-primary: var(--color-text-primary, #333333); // 主要文字颜色
$judu-text-secondary: var(--color-text-secondary, #666666); // 次要文字颜色
$judu-text-light: #999999; // 浅色文字
$judu-border-color: var(--color-border, #dee2e6); // 边框颜色
$judu-background: var(--color-background, #f8f9fa); // 背景色：统一背景色
$judu-card-background: var(--color-surface, #ffffff); // 卡片背景色：白色
$judu-shadow-color: var(--color-shadow, rgba(0, 0, 0, 0.1)); // 阴影颜色
$judu-overlay-dark: rgba(0, 0, 0, 0.5); // 深色遮罩
$judu-overlay-light: rgba(255, 255, 255, 0.9); // 浅色遮罩
$judu-heart-color: var(--color-primary, #6AC086); // 点赞心形颜色

/* 动态列表颜色 */
$judu-feed-bg: #f5f5f7; // 动态列表背景色
$judu-feed-card-bg: #ffffff; // 动态卡片背景色
$judu-feed-divider: #eeeeee; // 动态分割线颜色
$judu-nav-active: #333333; // 导航激活颜色
$judu-nav-inactive: #999999; // 导航未激活颜色
$judu-tab-active-bg: #f0f0f0; // 标签激活背景色

/* 分享卡片颜色 */
$judu-share-bg-start: #e8e6f0; // 分享卡片背景渐变起始色
$judu-share-bg-end: #d8d6e8; // 分享卡片背景渐变结束色
$judu-share-watermark: rgba(255, 255, 255, 0.5); // 分享卡片水印颜色

/* 字体定义 */
$judu-font-serif: 'Noto Serif SC', 'Source Han Serif SC', 'Source Han Serif', serif; // 衬线字体
$judu-font-sans: 'Noto Sans SC', 'Source Han Sans SC', 'Source Han Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; // 无衬线字体

/* 圆角定义 */
$judu-border-radius-sm: 4px;
$judu-border-radius-md: 8px;
$judu-border-radius-lg: 12px;
$judu-border-radius-xl: 16px;
$judu-border-radius-circle: 50%;

/* 阴影定义 */
$judu-shadow-sm: 0 2px 8px $judu-shadow-color;
$judu-shadow-md: 0 4px 12px $judu-shadow-color;
$judu-shadow-lg: 0 8px 24px $judu-shadow-color;
$judu-text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

/* 动画时间 */
$judu-transition-fast: 0.2s;
$judu-transition-normal: 0.3s;
$judu-transition-slow: 0.5s;

/* 纸张纹理背景 */
@mixin judu-paper-texture {
  background-color: $judu-card-background;
  background-image: linear-gradient(to right, rgba(232, 230, 225, 0.1) 0%, rgba(232, 230, 225, 0.05) 50%, rgba(232, 230, 225, 0.1) 100%);
}

/* 日卡背景遮罩 */
@mixin judu-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $judu-overlay-dark;
  backdrop-filter: blur(3px);
}

/* 分享卡片背景 */
@mixin judu-share-card-bg {
  background: linear-gradient(to bottom, $judu-share-bg-start, $judu-share-bg-end);
  border-radius: $judu-border-radius-lg;
  padding: 30rpx;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

/* 动态卡片样式 */
@mixin judu-feed-card {
  background-color: $judu-feed-card-bg;
  border-radius: $judu-border-radius-md;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1px 3px $judu-shadow-color;
}

/* 卡片样式 */
@mixin judu-card {
  background-color: $judu-card-background;
  border-radius: $judu-border-radius-md;
  box-shadow: $judu-shadow-sm;
  transition: transform $judu-transition-normal, box-shadow $judu-transition-normal;

  &:hover, &:active {
    transform: translateY(-2px);
    box-shadow: $judu-shadow-md;
  }
}

/* 日期徽章样式 */
@mixin judu-date-badge {
  background-color: $judu-primary;
  color: $judu-text-primary;
  border-radius: $judu-border-radius-sm;
  padding: 4px 8px;
  font-family: $judu-font-serif;
  font-size: 14px;
  box-shadow: $judu-shadow-sm;
}

/* 分隔线 */
@mixin judu-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, $judu-border-color, transparent);
  margin: 16px 0;
}

/* 输入框毛边效果 */
@mixin judu-input {
  border: 1px solid $judu-border-color;
  border-radius: $judu-border-radius-sm;
  background-color: $judu-card-background;
  padding: 12px;
  font-family: $judu-font-serif;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border: 1px solid $judu-border-color;
    border-radius: $judu-border-radius-sm;
    pointer-events: none;
    clip-path: polygon(
      0% 0%, 100% 0%, 100% 2%, 0% 2%,
      0% 98%, 100% 98%, 100% 100%, 0% 100%
    );
  }
}

/* 水波纹效果 */
@mixin judu-ripple {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, $judu-accent 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
  }

  &:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
  }
}

/* 书籍翻页效果 */
@keyframes judu-page-turn {
  0% {
    transform: rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: rotateY(90deg);
    opacity: 0.5;
  }
  100% {
    transform: rotateY(0deg);
    opacity: 1;
  }
}

/* 墨水扩散动画 */
@keyframes judu-ink-spread {
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 渐显加载动画 */
@keyframes judu-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 羽毛飘落动画 */
@keyframes judu-feather-fall {
  0% {
    transform: translateY(-20px) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: translateY(0) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: translateY(20px) rotate(360deg);
    opacity: 0;
  }
}
