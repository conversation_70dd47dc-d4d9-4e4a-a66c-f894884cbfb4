<template>
  <view class="page branch-management">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="分会管理"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <!-- 分会信息卡片 -->
      <view class="branch-info-card">
        <view class="card-header">
          <view class="branch-name">{{ branchInfo.branch_name || '加载中...' }}</view>
          <view class="branch-location">{{ branchInfo.branch_location || '' }}</view>
        </view>
        <view class="stats-container">
          <view class="stat-item">
            <view class="stat-number">{{ stats.pending_activities || 0 }}</view>
            <view class="stat-label">待审核活动</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ stats.total_members || 0 }}</view>
            <view class="stat-label">总成员</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ stats.vip_members || 0 }}</view>
            <view class="stat-label">会员用户</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ stats.normal_members || 0 }}</view>
            <view class="stat-label">普通用户</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ stats.month_commission || '0.00' }}</view>
            <view class="stat-label">本月佣金</view>
          </view>
        </view>
      </view>
      
      <!-- 功能菜单 -->
      <view class="menu-container">
        <view class="menu-title">管理功能</view>
        <view class="menu-grid">
          <view 
            class="menu-item"
            @click="navto('/pages/branch_president/pending_activities')"
          >
            <view class="menu-icon">
              <u-icon name="list" size="40" color="#6AC086"></u-icon>
            </view>
            <view class="menu-text">待审核活动</view>
            <view class="menu-badge" v-if="stats.pending_activities > 0">
              {{ stats.pending_activities }}
            </view>
          </view>
          
          <view 
            class="menu-item"
            @click="navto('/pages/branch_president/commission')"
          >
            <view class="menu-icon">
              <u-icon name="rmb-circle" size="40" color="#6AC086"></u-icon>
            </view>
            <view class="menu-text">运营佣金</view>
          </view>
          
          <view 
            class="menu-item"
            @click="viewBranchMembers"
          >
            <view class="menu-icon">
              <u-icon name="account" size="40" color="#6AC086"></u-icon>
            </view>
            <view class="menu-text">分会成员</view>
          </view>
          
          <view 
            class="menu-item"
            @click="viewBranchActivities"
          >
            <view class="menu-icon">
              <u-icon name="calendar" size="40" color="#6AC086"></u-icon>
            </view>
            <view class="menu-text">分会活动</view>
          </view>
        </view>
      </view>
      
      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="section-title">快捷操作</view>
        <view class="action-list">
          <view class="action-item" @click="refreshData">
            <u-icon name="reload" size="32" color="#6AC086"></u-icon>
            <text class="action-text">刷新数据</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <u-loading-page 
      :loading="loading" 
      loading-text="加载中..."
      bg-color="#f8f9fa"
    ></u-loading-page>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";
import { branch_presidentpending_activities, branch_presidentget_stats } from "@/api";
import { store } from "@/store";
// 修复：使用统一的权限检查工具函数
import { hasBranchManagementPermission } from "@/utils/permissions";

// 分会信息
const branchInfo = reactive({
  branch_name: '',
  branch_location: '',
  branch_id: null
});

// 统计数据
const stats = reactive({
  pending_activities: 0,
  total_members: 0,
  normal_members: 0,
  vip_members: 0,
  month_commission: '0.00'
});

const loading = ref(true);

// 页面加载
onLoad(() => {
  if (!requireLogin()) {
    return;
  }
  
  // 修复：使用统一的权限检查工具函数
  if (!hasBranchManagementPermission()) {
    uni.showModal({
      title: '权限不足',
      content: '您没有分会管理权限，无法访问此页面',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
    return;
  }
  
  loadData();
});

onShow(() => {
  // 每次显示页面时刷新数据
  refreshData();
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    
    const userInfo = store().$state.userInfo;
    
    // 设置分会信息（从用户信息中获取）
    branchInfo.branch_name = userInfo.branch_name || '未知分会';
    branchInfo.branch_location = userInfo.branch_location || '';
    branchInfo.branch_id = userInfo.branch_id;
    
    // 获取待审核活动数量
    await loadPendingActivitiesCount();

    // 获取分会统计数据
    await loadBranchStats();
    
  } catch (error) {
    console.error('加载数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 获取待审核活动数量
const loadPendingActivitiesCount = async () => {
  try {
    const userInfo = store().$state.userInfo;
    const res = await branch_presidentpending_activities({
      uid: userInfo.uid,
      token: userInfo.token,
      page: 1,
      page_size: 1
    });
    
    if (res.status === 'ok') {
      stats.pending_activities = res.count || 0;
    }
  } catch (error) {
    console.error('获取待审核活动数量失败:', error);
  }
};

// 获取分会统计数据
const loadBranchStats = async () => {
  try {
    const userInfo = store().$state.userInfo;
    const res = await branch_presidentget_stats({
      uid: userInfo.uid,
      token: userInfo.token
    });

    if (res.status === 'ok') {
      stats.total_members = res.data.total_members || 0;
      stats.normal_members = res.data.normal_members || 0;
      stats.vip_members = res.data.vip_members || 0;
      stats.month_commission = res.data.month_commission || '0.00';
    }
  } catch (error) {
    console.error('获取分会统计数据失败:', error);
  }
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看分会成员
const viewBranchMembers = () => {
  uni.navigateTo({
    url: '/pages/branch_president/branch_members'
  });
};

// 查看分会活动
const viewBranchActivities = () => {
  uni.navigateTo({
    url: '/pages/branch_president/branch_activities'
  });
};
</script>

<style lang="scss" scoped>
@import '@/style/wcag-colors.scss';
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.branch-info-card {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: #ffffff;
}

.card-header {
  margin-bottom: 30rpx;
}

.branch-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.branch-location {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.95); /* 提高对比度，从opacity: 0.8改为更高对比度的颜色 */
}

.stats-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx 10rpx;
  background: rgba(106, 192, 134, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.1);
}

.stat-number {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
  color: #ffffff; /* 在绿色背景上使用白色，确保足够对比度 */
}

.stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9); /* 提高对比度，确保在绿色背景上可读 */
}

.menu-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a; /* 加深颜色，提高与白色背景的对比度 */
  margin-bottom: 30rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.menu-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.menu-icon {
  margin-bottom: 16rpx;
}

.menu-text {
  font-size: 26rpx;
  color: #1a1a1a; /* 加深颜色，提高对比度 */
  text-align: center;
}

.menu-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #ff4757;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.quick-actions {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a; /* 加深颜色，提高对比度 */
  margin-bottom: 30rpx;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.98);
  background: #e9ecef;
}

.action-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #1a1a1a; /* 加深颜色，提高对比度 */
}
</style>
