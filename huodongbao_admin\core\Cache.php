<?php
namespace core;
class Cache{
	static private $path = BASE_PATH . "cache/";

	public function __construct(){

	}
	static public function setCache($key,$contents,$expiration=600){
		$fileName = md5($key);
		return self::set($fileName,$contents,$expiration);
	}

	static public function getCache($key){
		$fileName = md5($key);
		return self::get($fileName);
	}

	//写入当前请求响应缓存
	static public function responseCache($contents,$expiration=600){
		$fileName = self::getResponseFileName();
		return self::set($fileName,$contents,$expiration);
	}

	//获取当前请求响应缓存
	static public function getResponseCache(){
		$fileName = self::getResponseFileName();
		return self::get($fileName);
	}

	//读取缓存
	static private function get($fileName){
		$filePath = self::$path.$fileName;
		if(!file_exists($filePath))return false;
		$data = unserialize(file_get_contents($filePath));
		if($data["expiration"] < _NOW_){
			@unlink($path.$fileName);
			return false;
		}
		return $data["contents"];
	}

	//写入缓存
	static private function set($fileName,$contents,$expiration){
		self::checkPath();
		$filePath = self::$path.$fileName;
		if(file_exists($filePath))@unlink($filePath);
		$data = ["expiration"=>_NOW_+$expiration,"contents"=>$contents];
		return file_put_contents($filePath,serialize($data));
	}

	//检查缓存目录
	static private function checkPath(){
		$path = self::$path;
		if(!is_dir($path))@mkdir($path, 0777, true);
	}

	//获取当前请求生成文件名
	static private function getResponseFileName(){
		$controllerName = \core\Route::$controller;
		$actionName = \core\Route::$action;
		$params = \core\Route::$params;
		$keys = $controllerName.$actionName;
		if(!empty($params)){
			ksort($params);
			foreach($params as $k=>$v){
				$keys .= $k;
				$keys .= $v;
			}
		}
		return md5($keys);
	}
	//删除所有缓存
	static public function clearCache(){
		return removeDir(self::$path);
	}
	public function __destruct(){

	}
}
