<?php
namespace controller;
use core\Controller;
use core\Db;
class Login extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	//登录界面
	public function login(){
		if(!empty($_SESSION['root_info'])){
			header("location:".url("index/index"));
		}
		return view(['login']);
	}
	
	//登录
	public function doLogin($username,$password,$google_auth_code){
		responseType("json");
		/*
		if(empty($_SESSION['authcode']) || $_SESSION['authcode'] != $_POST['authcode']){
			return ['status'=>"error","msg"=>"验证码错误"];
		}
		*/

		dbConn();
		//
		$pass_len = mb_strlen($password);
		$pass_show_len = 3;
		if($pass_len > $pass_show_len){
			$mask_xing_num = $pass_len - $pass_show_len;
			$pass_end = "";
			for($i=0;$i<$mask_xing_num;$i++){
				$pass_end .= "*";
			}
			$pass_end .= mb_substr($password, - $pass_show_len);
		}else{
			$pass_end = $password;
		}
		//查看IP错误日志
		$start_time = date("Y-m-d H:i:s",_NOW_ - 20 * 60);
		$error_count = Db()->table("ip_black")->where("ip='".IP."' AND type=1 AND `time`>'{$start_time}'")->count();
		if(!empty($error_count) && $error_count >= 15){
			$this->exception_log("IP登陆管理端错误过于频繁【 ".IP." 】登陆【{$username}】密码【{$pass_end}】");
			return ['status'=>"error","msg"=>"错误登陆过于频繁，请稍后再试"];
		}
		//
		//ip区域限制
		$allow_ip = [
			"127.0.0.1",
			"localhost",
		];
		if(!in_array(IP,$allow_ip)){
			//ip段检测
			$check = Db()->table("root_ip")->fetch();
			if(!empty($check)){
				$ip_id = Db()->table("root_ip")->where("ip=:ip")->prepareParam([":ip"=>IP])->getColumn("id");
				if(empty($ip_id)){
					$this->exception_log("IP地址【".IP."】管理端登录被限制，用户名【{$username}】，登陆密码【{$pass_end}】");
					return ['status'=>"error","msg"=>"您所在IP禁止登录"];					
				}else{
					Db()->table("root_ip")->where("id={$ip_id}")->update(["last_use_time"=>DATETIME]);
				}
			}
		}
		//
		try{
			$sql = "SELECT * FROM `root` WHERE r_name=:r_name LIMIT 1";
			$res = Db()->_fetch($sql,[":r_name"=>$username]);
			if(empty($res) || $res['password'] !== md5(config("sys.root_salt").$password)){
				throw new \Exception("用户名或密码错误");
			}
			//
			if(!empty($res['google_auth_key'])){
				if(empty($google_auth_code))throw new \Exception("设置Google验证码但未输入");
				if(!in_array(strlen($google_auth_code),[6,8]))throw new \Exception("Google验证码错误");
				if(!\lib\GoogleAuthenticator::verifyCode($res['google_auth_key'],$google_auth_code)){
					throw new \Exception("Google验证码输入错误");
				}
			}
			//
			$group = Db()->table("root_group")->where("id={$res['group_id']}")->select("privileges")->fetch();
			if(empty($group) || !isset($group['privileges']) || empty($group['privileges'])){
				throw new \Exception("权限获取失败");
			}
			$res["privileges"] = explode("|",$group['privileges']);
			$res['login_ip'] = IP;
			$_SESSION['root_info'] = $res;
			if(isset($_SESSION['authcode']))unset($_SESSION['authcode']);
			Db()->table("root")->where("rid={$res['rid']}")->update(['last_login_time'=>date("Y-m-d H:i:s")]);
			$this->root_log("登录成功");
			//一个账户不能同时多人登录
			$files = scandir(\core\Session::$session_path);
			foreach ($files as $file) {
				if($file == "." || $file == "..")continue;
				$data = unserialize(file_get_contents(\core\Session::$session_path . $file));
				if(isset($data['data']['root_info']['rid']) && $data['data']['root_info']['rid'] == $_SESSION['root_info']['rid']){
					@unlink(\core\Session::$session_path . $file);
					$this->root_log("其他设备的登录【{$data['data']['root_info']['login_ip']}】已被挤下线");
				}
			}
			//
			return ['status'=>"ok"];
		}catch(\Exception $e){
			$msg = $e->getMessage();
			$ip_black = [
				"ip"=>IP,
				"type"=>1,
				"msg"=>":msg",
			];
			Db()->table("ip_black")->prepareParam([":msg"=>"尝试登录用户名：".htmlspecialchars($username)])->insert($ip_black);
			$this->exception_log("登录失败-{$msg}，用户名【{$username}】，登陆密码【{$pass_end}】");
			return ['status'=>"error","msg"=>$msg];
		}
	}
	//验证码
	public function authcode(){
		$image = imagecreatetruecolor(100, 30);    //1>设置验证码图片大小的函数
		//5>设置验证码颜色 imagecolorallocate(int im, int red, int green, int blue);
		$bgcolor = imagecolorallocate($image,255,255,255); //#ffffff
		//6>区域填充 int imagefill(int im, int x, int y, int col) (x,y) 所在的区域着色,col 表示欲涂上的颜色
		imagefill($image, 0, 0, $bgcolor);
		//10>设置变量
		$captcha_code = "";

		//7>生成随机数字
		for($i=0;$i<4;$i++){
			//设置字体大小
			$fontsize = 6;    
			//设置字体颜色，随机颜色
			$fontcolor = imagecolorallocate($image, rand(0,120),rand(0,120), rand(0,120));      //0-120深颜色
			//设置数字
			$fontcontent = rand(0,9);
			//10>.=连续定义变量
			$captcha_code .= $fontcontent;  
			//设置坐标
			$x = ($i*100/4)+rand(5,10);
			$y = rand(5,10);

			imagestring($image,$fontsize,$x,$y,$fontcontent,$fontcolor);
		}
		//10>存到session
		$_SESSION['authcode'] = $captcha_code;
		//8>增加干扰元素，设置雪花点
		for($i=0;$i<200;$i++){
			//设置点的颜色，50-200颜色比数字浅，不干扰阅读
			$pointcolor = imagecolorallocate($image,rand(50,200), rand(50,200), rand(50,200));    
			//imagesetpixel — 画一个单一像素
			imagesetpixel($image, rand(1,99), rand(1,29), $pointcolor);
		}
		//9>增加干扰元素，设置横线
		for($i=0;$i<4;$i++){
			//设置线的颜色
			$linecolor = imagecolorallocate($image,rand(80,220), rand(80,220),rand(80,220));
			//设置线，两点一线
			imageline($image,rand(1,99), rand(1,29),rand(1,99), rand(1,29),$linecolor);
		}

		//2>设置头部，image/png
		header('Content-Type: image/png');
		//3>imagepng() 建立png图形函数
		imagepng($image);
		//4>imagedestroy() 结束图形函数 销毁$image
		imagedestroy($image);		
	}
	public function logout(){
		if(!empty($_SESSION['root_info'])){
			$this->root_log("退出登录");
			unset($_SESSION['root_info']);
		}
		header("location:".url("login/login"));
	}
	
	//add ip
	public function addip($token=""){
		dbConn();
		//token验证
		$allow_token = Db()->table("config")->where("`name`='root_ip_token'")->getColumn("val");
		if(empty($allow_token) || $token != $allow_token){
			$this->exception_log("IP地址【".IP."】尝试打开便捷IP段管理由于【Token错误】被限制");
			header("location:".url("login/login"));
			exit;
		}
		$result_msg = "";
		if(!empty($_POST['ip'])){
			$ip = htmlspecialchars(trim($_POST['ip']));
			$msg = isset($_POST['msg']) ? htmlspecialchars(trim($_POST['msg'])) : "";
			//
			$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : "";
			$user_agent_match = [];
			preg_match_all("/(?<=\()[^\)]+/",$user_agent, $user_agent_match);
			if(isset($user_agent_match[0][0])){
				$user_agent = $user_agent_match[0][0];
			}
			$msg .= "【{$user_agent}】";
			//
			if(check($ip,"ip")){
				if(!empty($msg)){
					$check = Db()->table("root_ip")->where("ip=:ip")->prepareParam([":ip"=>$ip])->fetch();
					if(!empty($check)){
						$result_msg = "IP地址【{$ip}】已存在";
					}else{
						$data = [
							"ip"=>":ip",
							"msg"=>":msg",
						];
						$prepareParam = [
							':ip'=>$ip,
							':msg'=>$msg,
						];
						try{
							if(Db()->table("root_ip")->prepareParam($prepareParam)->insert($data)){
								$result_msg = "成功添加IP：【{$ip}】";
								$msg = DATETIME . " : IP地址【".IP."】设备【{$user_agent}】成功添加允许管理员操作IP：【{$ip}】";
								_log_($msg);
							}else{
								$result_msg = "操作失败";
							}
						}catch(\Exception $e){
							$result_msg = $e->getMessage();
						}
					}
				}else{
					$result_msg = "备注不能为空";
				}
			}else{
				$result_msg = "IP地址【{$ip}】格式错误";
			}
		}
		$c_ip = IP;
		$html = 
<<<EOF
		<html>
		<head>
			<title>添加IP</title>
			<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0" name="viewport">
		</head>
		<body>
			<div style="margin-top:30px;padding:10px;">
				<form action="" method="post">
					<textarea id="ip" name="ip" style='width:100%;font-size:large;' rows=5 placeholder="IP"></textarea>
					<textarea id="msg" name="msg" style='width:100%;margin-top:10px;font-size:large;' rows=5 placeholder="备注"></textarea>
					<input type="hidden" name='token' value="{$token}"  />
					<p>当前IP：<span style="color:green;">{$c_ip}</span> &nbsp;&nbsp;<input type="button" value="填充" onclick="c_ip();" />&nbsp;&nbsp;<input type="reset" value="清空" /></p>
					<p><input type="submit" style="width:100%;margin-top:10px;font-size:large;"  /></p>
				</form>
				<p style="color:red" >{$result_msg}</p>
			</div>
		</body>
		<script>
		function c_ip(){
			document.getElementById("ip").value="{$c_ip}";
			document.getElementById("msg").value="临时IP";
		}
		</script>
		</html>
EOF;
		echo $html;
	}	
	
	public function show_google_code($key=""){
		
		if(empty($key)){
			header("location:".url("login/login"));
			exit;			
		}
		$code = \lib\GoogleAuthenticator::getCode($key);
		if(empty($code)){
			$code = (string)$code;
		}
		$asset_path = ASSET_PATH;
		$mask_key = substr($key,-4);
		$datetime = DATETIME;
		$html = 
<<<EOF
		<html>
		<head>
			<title>Google Code</title>
			<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0" name="viewport">
			<script src="{$asset_path}lib/jquery-3.4.1/jquery-3.4.1.min.js" charset="utf-8"></script>
			<script charset="utf-8" src="{$asset_path}js/clipboard.min.js"></script>
			
		</head>
		<body>
			<div style="margin-top:30px;padding:10px;">
				<p>时间【{$datetime}】</p>
				<p>密钥【******{$mask_key}】</p>
				<p>验证码<span style="color:red">【 {$code} 】</span></p>
				<p><input class="copy" id="copy_btn" style="cursor:pointer;font-size:18px;" data-clipboard-text="{$code}" type="button" value="复制" /></p>
				<p id="show_msg" style="display:none;color:gray;"></p>
			</div>
		</body>
	<script>
var clipboard = new ClipboardJS('.copy');
clipboard.on('success', function(e) {
    console.info('Action:', e.action);
    console.info('Text:', e.text);
    console.info('Trigger:', e.trigger);
    e.clearSelection();
	$("#copy_btn").hide();
	$("#show_msg").text("复制成功 ✓");
	$("#show_msg").show();
});

clipboard.on('error', function(e) {
    console.error('Action:', e.action);
    console.error('Trigger:', e.trigger);
	alert("复制失败");
});
</script>
		</html>
EOF;
		echo $html;		
	}
	
	public function root_msg_read($id){
		responseType("json");
		if(!check($id,"intgt0")){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$id = intval($id);
		dbConn();
		Db()->table("root_msg")->where("id={$id}")->update(["is_read"=>1,"read_time"=>DATETIME]);
		return ["status"=>"ok"];
	}
	
	public function _empty(){
		
	}
	function __destruct(){

	}
}
