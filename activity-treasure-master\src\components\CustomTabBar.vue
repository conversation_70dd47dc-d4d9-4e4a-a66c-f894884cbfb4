<script setup>
import { ref, computed, onMounted } from 'vue'

// 定义props
const props = defineProps({
  current: {
    type: Number,
    default: 0
  }
})

// 定义emits
const emit = defineEmits(['change'])

// 当前选中的tab索引
const currentIndex = ref(props.current)

// 导航栏配置
const tabList = [
  {
    pagePath: '/pages/index',
    iconPath: '/static/index.png',
    selectedIconPath: '/static/index.svg',
    text: '活动',
    index: 0
  },
  {
    pagePath: '/pages/world',
    iconPath: '/static/world.png',
    selectedIconPath: '/static/world.svg',
    text: '世界',
    index: 1
  },
  {
    pagePath: '/pages/addActive',
    iconPath: '/static/addActive.svg',
    selectedIconPath: '/static/addActive.svg',
    text: '发布',
    index: 2,
    isSpecial: true // 标记为特殊按钮（骑缝式）
  },
  {
    pagePath: '/pages/notifications',
    iconPath: '/static/notification.svg',
    selectedIconPath: '/static/notification-active.svg',
    text: '通知',
    index: 3
  },
  {
    pagePath: '/pages/my', // 个人中心页面路径
    iconPath: '/static/my.png',
    selectedIconPath: '/static/myd.png',
    text: '我的',
    index: 4
  }
]

// 获取当前页面路径并设置对应的tab
onMounted(() => {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const route = '/' + currentPage.route
    
    const tabIndex = tabList.findIndex(tab => tab.pagePath === route)
    if (tabIndex !== -1) {
      currentIndex.value = tabIndex
    }
  }
})

// 处理tab点击 - 优化：使用switchTab提升性能
const handleTabClick = (item, index) => {
  if (currentIndex.value === index) return

  // 添加点击动画反馈 - 微信小程序环境优化
  try {
    // 使用uni-app的方式添加点击反馈
    uni.vibrateShort({
      type: 'light'
    })
  } catch (e) {
    // 忽略震动错误
  }

  currentIndex.value = index
  emit('change', index)

  // 发布页面特殊处理 - 修复重复点击失效问题
  if (item.pagePath === '/pages/addActive') {
    // 直接跳转到会员验证页面，移除页面检查逻辑以确保每次都能正常跳转
    uni.navigateTo({
      url: '/pages/addActive',
      animationType: 'slide-in-bottom',
      animationDuration: 200,
      fail: (err) => {
        console.error('跳转发布页面失败:', err)
        // 降级处理：使用reLaunch
        uni.reLaunch({
          url: '/pages/addActive'
        })
      }
    })
  } else {
    // 其他页面使用redirectTo实现页面切换，避免reLaunch导致的状态丢失
    uni.redirectTo({
      url: item.pagePath,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        // 降级处理：使用navigateTo
        uni.navigateTo({
          url: item.pagePath,
          fail: (navErr) => {
            console.error('navigateTo也失败:', navErr)
            // 最后降级：使用reLaunch
            uni.reLaunch({
              url: item.pagePath,
              fail: (relaunchErr) => {
                console.error('reLaunch也失败:', relaunchErr)
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                })
              }
            })
          }
        })
      }
    })
  }
}

// 判断是否为当前选中的tab
const isActive = (index) => {
  return currentIndex.value === index
}
</script>

<template>
  <view class="custom-tab-bar">
    <!-- SVG波浪曲线背景 -->
    <!-- 动态波浪曲线背景 - 微信小程序兼容版 -->
    <svg class="wave-background" viewBox="0 0 375 120" preserveAspectRatio="none">
      <defs>
        <!-- 优化的渐变效果，融入主色调 -->
        <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
          <stop offset="30%" style="stop-color:#f8fdf9;stop-opacity:0.92" />
          <stop offset="70%" style="stop-color:#f5fcf7;stop-opacity:0.88" />
          <stop offset="100%" style="stop-color:#f0faf2;stop-opacity:0.85" />
        </linearGradient>
        <!-- 添加蒙版渐变 -->
        <linearGradient id="maskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#6AC086;stop-opacity:0.02" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.08" />
        </linearGradient>
      </defs>

      <!-- 简化的波浪路径：保持发布按钮凸起效果 -->
      <path
        d="M 0,80
           Q 75,75 150,70
           Q 187.5,45 225,70
           Q 300,75 375,80
           L 375,120
           L 0,120 Z"
        fill="url(#waveGradient)"
      />
    </svg>

    <!-- 导航栏背景蒙版层 -->
    <view class="tab-bar-background"></view>

    <!-- 额外的渐变蒙版层 -->
    <view class="tab-bar-mask"></view>
    
    <!-- 导航项容器 -->
    <view class="tab-bar-container">
      <view 
        v-for="(item, index) in tabList" 
        :key="index"
        class="tab-item"
        :class="{ 
          'tab-item-active': isActive(index),
          'tab-item-special': item.isSpecial 
        }"
        @click="handleTabClick(item, index)"
      >
        <!-- 统一的按钮样式，不区分特殊按钮 -->
        <image
          class="tab-icon"
          :src="isActive(index) ? item.selectedIconPath : item.iconPath"
          mode="aspectFit"
        />
        <text
          class="tab-text"
          :class="{ 'tab-text-active': isActive(index) }"
        >
          {{ item.text }}
        </text>
      </view>
    </view>
    
    <!-- 安全区域适配 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<style lang="scss" scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* 确保波浪背景正确显示 */
  overflow: visible;
}

/* 动态波浪曲线背景样式 - 优化版 */
.wave-background {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 160rpx; /* 增加高度，确保曲线完全显示 */
  z-index: 1;
  pointer-events: none;
  opacity: 1; /* 提高透明度，确保曲线可见 */
}

.tab-bar-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 160rpx; /* 增加高度确保完全覆盖 */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(25rpx);
  -webkit-backdrop-filter: blur(25rpx);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1),
              0 -8rpx 32rpx rgba(106, 192, 134, 0.08);
  z-index: 2;
  border-radius: 0;
  /* 添加柔和的上边缘渐变 */
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.85) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
}

.tab-bar-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 160rpx; /* 增加高度确保完全覆盖 */
  background: linear-gradient(
    to bottom,
    rgba(106, 192, 134, 0.02) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(248, 249, 250, 0.08) 100%
  );
  z-index: 2.5;
  pointer-events: none;
  /* 添加边缘模糊效果 */
  filter: blur(0.5rpx);
}

.tab-bar-container {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 16rpx 0 12rpx 0;
  background: transparent;
  position: relative;
  z-index: 4;
  pointer-events: auto;
  /* 确保内容在蒙版之上 */
  height: 160rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  position: relative;
  transition: all 0.3s ease;
}



.tab-icon {
  width: 48rpx;
  height: 48rpx;
  transition: all 0.3s ease;
}

/* 发布按钮特殊样式 */
.tab-item-special .tab-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: 50%;
  padding: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.3);
}

.tab-item-special .tab-text {
  margin-top: 12rpx;
  font-weight: 600;
}

.tab-text {
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
  transition: all 0.3s ease;
  line-height: 1;
}

.tab-text-active {
  color: #6AC086;
  font-weight: 600;
}

.tab-item-active .tab-icon {
  transform: scale(1.1);
}

/* 特殊按钮激活状态 */
.tab-item-special.tab-item-active .tab-icon {
  transform: scale(1.05);
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.4);
}

// 安全区域适配
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  position: relative;
  z-index: 3;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .tab-text {
    font-size: 22rpx;
  }
  
  .tab-icon {
    width: 44rpx;
    height: 44rpx;
  }
}
</style>
