<?php
/**
 * 测试世界模块API
 */

require_once 'core/init.php';

// 连接数据库
dbConn();

echo "<h2>世界模块API测试</h2>\n";

try {
    require_once 'controller/World.php';
    $worldController = new \controller\World();
    
    // 测试参数
    $testUid = 215;
    $testToken = '8bde525b2267e3b1ee716884a9a3b0a0';
    
    echo "<h3>测试参数</h3>\n";
    echo "<p>用户ID: " . $testUid . "</p>\n";
    echo "<p>Token: " . $testToken . "</p>\n";
    
    // 1. 测试获取日记列表
    echo "<h3>1. 测试获取日记列表 (get_feeds)</h3>\n";
    
    $diaryParams = [
        'page' => 1,
        'page_size' => 10,
        'uid' => $testUid,
        'token' => $testToken,
        'user_id' => 0,
        'filter_type' => 'latest',
        'type' => 'diary'
    ];
    
    echo "<p>请求参数: " . json_encode($diaryParams, JSON_UNESCAPED_UNICODE) . "</p>\n";
    
    $diaryResult = $worldController->get_feeds(
        $diaryParams['page'],
        $diaryParams['page_size'],
        $diaryParams['uid'],
        $diaryParams['token'],
        $diaryParams['user_id'],
        $diaryParams['filter_type'],
        $diaryParams['type']
    );
    
    echo "<p><strong>日记列表API响应:</strong></p>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . 
         htmlspecialchars(json_encode($diaryResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
         "</pre>\n";
    
    // 2. 测试获取摘录列表
    echo "<h3>2. 测试获取摘录列表 (get_quotes)</h3>\n";
    
    $quoteParams = [
        'page' => 1,
        'page_size' => 20,
        'uid' => $testUid,
        'token' => $testToken
    ];
    
    echo "<p>请求参数: " . json_encode($quoteParams, JSON_UNESCAPED_UNICODE) . "</p>\n";
    
    $quoteResult = $worldController->get_quotes(
        $quoteParams['page'],
        $quoteParams['page_size'],
        $quoteParams['uid'],
        $quoteParams['token']
    );
    
    echo "<p><strong>摘录列表API响应:</strong></p>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . 
         htmlspecialchars(json_encode($quoteResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
         "</pre>\n";
    
    // 3. 测试发布动态
    echo "<h3>3. 测试发布动态 (publish_feed)</h3>\n";
    
    $publishParams = [
        'uid' => $testUid,
        'token' => $testToken,
        'content' => '这是一条API测试动态，时间：' . date('Y-m-d H:i:s'),
        'images' => [],
        'location' => '',
        'tags' => 'API测试',
        'privacy' => 'public',
        'type' => 'feed'
    ];
    
    echo "<p>请求参数: " . json_encode($publishParams, JSON_UNESCAPED_UNICODE) . "</p>\n";
    
    $publishResult = $worldController->publish_feed(
        $publishParams['uid'],
        $publishParams['token'],
        $publishParams['content'],
        $publishParams['images'],
        $publishParams['location'],
        $publishParams['tags'],
        $publishParams['privacy']
    );
    
    echo "<p><strong>发布动态API响应:</strong></p>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . 
         htmlspecialchars(json_encode($publishResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
         "</pre>\n";
    
    // 4. 测试发布摘录
    echo "<h3>4. 测试发布摘录 (create_quote)</h3>\n";
    
    $createQuoteParams = [
        'uid' => $testUid,
        'token' => $testToken,
        'content' => '测试是成功的第一步。—— API测试',
        'author' => 'API测试',
        'source' => '测试文档',
        'tags' => 'API,测试',
        'privacy' => 'public'
    ];
    
    echo "<p>请求参数: " . json_encode($createQuoteParams, JSON_UNESCAPED_UNICODE) . "</p>\n";
    
    $createQuoteResult = $worldController->create_quote(
        $createQuoteParams['uid'],
        $createQuoteParams['token'],
        $createQuoteParams['content'],
        $createQuoteParams['author'],
        $createQuoteParams['source'],
        $createQuoteParams['tags'],
        $createQuoteParams['privacy']
    );
    
    echo "<p><strong>发布摘录API响应:</strong></p>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . 
         htmlspecialchars(json_encode($createQuoteResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
         "</pre>\n";
    
    // 5. 总结测试结果
    echo "<h3>5. 测试结果总结</h3>\n";
    
    $results = [
        '日记列表API' => $diaryResult['status'] ?? 'unknown',
        '摘录列表API' => $quoteResult['status'] ?? 'unknown',
        '发布动态API' => $publishResult['status'] ?? 'unknown',
        '发布摘录API' => $createQuoteResult['status'] ?? 'unknown'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>API名称</th><th>状态</th><th>结果</th></tr>\n";
    
    foreach ($results as $apiName => $status) {
        $statusColor = $status === 'ok' ? 'green' : ($status === 'error' ? 'red' : 'orange');
        $statusText = $status === 'ok' ? '✅ 成功' : ($status === 'error' ? '❌ 失败' : '⚠️ 未知');
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($apiName) . "</td>";
        echo "<td style='color: " . $statusColor . ";'>" . htmlspecialchars($status) . "</td>";
        echo "<td style='color: " . $statusColor . ";'>" . $statusText . "</td>";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // 6. 问题诊断
    echo "<h3>6. 问题诊断</h3>\n";
    
    if ($diaryResult['status'] === 'empty' || $quoteResult['status'] === 'empty') {
        echo "<p style='color: orange;'>⚠️ 检测到空数据问题</p>\n";
        echo "<p><strong>可能原因:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>数据库中没有对应类型的数据</li>\n";
        echo "<li>用户权限问题</li>\n";
        echo "<li>数据筛选条件过于严格</li>\n";
        echo "</ul>\n";
        
        echo "<p><strong>建议解决方案:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>运行 quick_add_test_data.php 添加测试数据</li>\n";
        echo "<li>检查用户认证状态</li>\n";
        echo "<li>检查数据库连接</li>\n";
        echo "</ul>\n";
    }
    
    if ($diaryResult['status'] === 'ok' && $quoteResult['status'] === 'ok') {
        echo "<p style='color: green;'>✅ 所有API测试通过！小程序应该能正常显示数据了。</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ API测试失败: " . $e->getMessage() . "</p>\n";
    echo "<p>错误详情: " . $e->getTraceAsString() . "</p>\n";
}

echo "<h3>测试完成</h3>\n";
echo "<p>请根据以上测试结果进行相应的修复操作。</p>\n";
?>
