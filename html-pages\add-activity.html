<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="活动宝 - 添加活动">
    <title>活动宝 - 添加活动</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 页面特定样式 */
        .page-header {
            background: var(--color-surface);
            border-bottom: 0.05rem solid var(--color-border-light);
            padding: var(--spacing-md) var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            position: sticky;
            top: 0;
            z-index: var(--z-index-sticky);
        }
        
        .back-btn {
            color: var(--color-text-primary);
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-xs);
            transition: all var(--transition-fast);
        }
        
        .back-btn:hover {
            background: var(--color-gray-100);
        }
        
        .page-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
        }
        
        .form-container {
            padding: var(--spacing-lg);
        }
        
        .upload-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .upload-area {
            border: 0.11rem dashed var(--color-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-xl);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            background: var(--color-gray-50);
        }
        
        .upload-area:hover {
            border-color: var(--color-primary);
            background: var(--color-primary-lighter);
            transform: scale(1.02);
        }
        
        .upload-area.dragover {
            border-color: var(--color-primary);
            background: var(--color-primary-lighter);
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 2.67rem; /* 50rpx */
            color: var(--color-text-tertiary);
            margin-bottom: var(--spacing-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-md);
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-xs);
        }
        
        .upload-hint {
            font-size: var(--font-size-sm);
            color: var(--color-text-tertiary);
        }
        
        .preview-image {
            width: 100%;
            max-width: 8.53rem; /* 160rpx */
            height: 4.8rem; /* 90rpx */
            object-fit: cover;
            border-radius: var(--radius-sm);
            margin-top: var(--spacing-sm);
        }
        
        .section-header {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-xs);
            border-bottom: 0.05rem solid var(--color-border-light);
            text-align: center;
        }
        
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .form-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 0.05rem solid var(--color-border-light);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-md);
            color: var(--color-text-primary);
            background: var(--color-surface);
            transition: all var(--transition-fast);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 0.11rem rgba(92, 184, 92, 0.1);
            transform: translateY(-0.05rem);
        }
        
        .form-textarea {
            min-height: 3.2rem; /* 60rpx */
            resize: vertical;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1rem;
            padding-right: 2.5rem;
        }
        
        .radio-group {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-sm);
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            cursor: pointer;
        }
        
        .radio-input {
            width: 1.07rem; /* 20rpx */
            height: 1.07rem;
            border: 0.11rem solid var(--color-border);
            border-radius: var(--radius-round);
            position: relative;
            cursor: pointer;
        }
        
        .radio-input:checked {
            border-color: var(--color-primary);
            background: var(--color-primary);
        }
        
        .radio-input:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0.32rem; /* 6rpx */
            height: 0.32rem;
            background: white;
            border-radius: var(--radius-round);
        }
        
        .radio-label {
            font-size: var(--font-size-md);
            color: var(--color-text-primary);
            cursor: pointer;
        }
        
        .submit-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--color-surface);
            border-top: 0.05rem solid var(--color-border-light);
            padding: var(--spacing-md) var(--spacing-lg);
            padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
        }
        
        .submit-btn {
            width: 100%;
            height: 2.67rem; /* 50rpx */
            background: var(--color-text-secondary);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .submit-btn.active {
            background: var(--color-primary);
        }
        
        .submit-btn:hover.active {
            background: var(--color-primary-dark);
            transform: translateY(-0.05rem);
            box-shadow: var(--shadow-md);
        }
        
        .main-content {
            padding-bottom: 5.33rem; /* 为提交按钮留空间 */
        }
        
        .required {
            color: var(--color-error);
        }
        
        .form-hint {
            font-size: var(--font-size-sm);
            color: var(--color-text-tertiary);
            margin-top: 0.27rem; /* 5rpx */
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- 页面头部 -->
        <header class="page-header">
            <div class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </div>
            <h1 class="page-title">添加活动</h1>
        </header>
        
        <main class="main-content">
            <form class="form-container" id="activity-form">
                <!-- 上传封面 -->
                <div class="upload-section">
                    <div class="upload-area" id="upload-area">
                        <div class="upload-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="upload-text">上传活动封面图</div>
                        <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 16:9</div>
                        <input type="file" id="cover-input" accept="image/*" style="display: none;">
                    </div>
                </div>
                
                <!-- 基本信息 -->
                <div class="section-header text-hierarchy-h2">活动名称、理由及活动简介</div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-title">
                        活动标题：<span class="required">*</span>
                    </label>
                    <input type="text" id="activity-title" class="form-input" placeholder="请输入活动标题" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-type">
                        活动类型：<span class="required">*</span>
                    </label>
                    <select id="activity-type" class="form-input form-select" required>
                        <option value="">请选择活动类型</option>
                        <option value="outdoor">户外运动</option>
                        <option value="culture">文化艺术</option>
                        <option value="business">商务会议</option>
                        <option value="social">社交聚会</option>
                        <option value="education">教育培训</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">活动性质：</label>
                    <div class="radio-group">
                        <label class="radio-item">
                            <input type="radio" name="activity-nature" value="offline" class="radio-input" checked>
                            <span class="radio-label">线下</span>
                        </label>
                        <label class="radio-item">
                            <input type="radio" name="activity-nature" value="online" class="radio-input">
                            <span class="radio-label">线上</span>
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-location">
                        活动地点：<span class="required">*</span>
                    </label>
                    <input type="text" id="activity-location" class="form-input" placeholder="请选择活动地点" required>
                    <div class="form-hint">点击选择具体地址</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-time">
                        活动时间：<span class="required">*</span>
                    </label>
                    <input type="datetime-local" id="activity-time" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-participants">
                        参与人数：
                    </label>
                    <input type="number" id="activity-participants" class="form-input" placeholder="请输入参与人数" min="1">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-fee">
                        活动费用：
                    </label>
                    <input type="number" id="activity-fee" class="form-input" placeholder="请输入活动费用（元）" min="0" step="0.01">
                    <div class="form-hint">填写0表示免费活动</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="activity-description">
                        活动描述：<span class="required">*</span>
                    </label>
                    <textarea id="activity-description" class="form-input form-textarea" placeholder="请详细描述活动内容、流程、注意事项等" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="contact-name">
                        联系人姓名：<span class="required">*</span>
                    </label>
                    <input type="text" id="contact-name" class="form-input" placeholder="请输入联系人姓名" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="contact-phone">
                        联系人电话：<span class="required">*</span>
                    </label>
                    <input type="tel" id="contact-phone" class="form-input" placeholder="请输入联系人电话" required>
                </div>
            </form>
        </main>
        
        <!-- 提交按钮 -->
        <div class="submit-section">
            <button type="submit" class="submit-btn" id="submit-btn" form="activity-form">
                下一页
            </button>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('activity-form');
            const submitBtn = document.getElementById('submit-btn');
            const uploadArea = document.getElementById('upload-area');
            const coverInput = document.getElementById('cover-input');
            
            // 表单验证
            function validateForm() {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                    }
                });
                
                submitBtn.classList.toggle('active', isValid);
                return isValid;
            }
            
            // 监听表单变化
            form.addEventListener('input', validateForm);
            form.addEventListener('change', validateForm);
            
            // 初始验证
            validateForm();
            
            // 文件上传处理
            uploadArea.addEventListener('click', function() {
                coverInput.click();
            });
            
            coverInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadArea.innerHTML = `
                            <img src="${e.target.result}" alt="预览图" class="preview-image">
                            <div class="upload-text" style="margin-top: var(--spacing-sm);">点击重新上传</div>
                        `;
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0 && files[0].type.startsWith('image/')) {
                    coverInput.files = files;
                    coverInput.dispatchEvent(new Event('change'));
                }
            });
            
            // 表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!validateForm()) {
                    alert('请填写所有必填项');
                    return;
                }
                
                // 模拟提交
                submitBtn.textContent = '提交中...';
                submitBtn.disabled = true;
                
                setTimeout(() => {
                    alert('活动创建成功！');
                    // 这里可以添加跳转逻辑
                    window.location.href = 'index.html';
                }, 2000);
            });
            
            // 地点选择
            const locationInput = document.getElementById('activity-location');
            locationInput.addEventListener('click', function() {
                // 这里可以集成地图选择组件
                console.log('选择地点');
            });
            
            // 活动性质切换
            const natureRadios = document.querySelectorAll('input[name="activity-nature"]');
            const locationGroup = locationInput.closest('.form-group');
            
            natureRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'online') {
                        locationInput.placeholder = '请输入线上活动链接';
                        locationGroup.querySelector('.form-hint').textContent = '如：腾讯会议、钉钉会议等';
                    } else {
                        locationInput.placeholder = '请选择活动地点';
                        locationGroup.querySelector('.form-hint').textContent = '点击选择具体地址';
                    }
                });
            });
        });
    </script>
</body>
</html>
