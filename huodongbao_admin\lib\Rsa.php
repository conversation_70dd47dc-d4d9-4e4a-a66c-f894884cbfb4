<?php
namespace lib;
class Rsa{
	
	static private $public_key = null;//公钥
	static private $private_key = null;//私钥
	
	public function __construct(){
		
	}
	
	static public function create_rsa_key($openssl_cnf_path=""){
		//配置信息
		$config = [
			'digest_alg' => 'sha512',
			'private_key_bits' => 4096,//指定多少位来生成私钥
			'private_key_type' => OPENSSL_KEYTYPE_RSA
		];
		if(!empty($openssl_cnf_path)){
			$config['config'] = $openssl_cnf_path;
		}else{
			if(stripos(PHP_OS,"win") !== false){
				$configFile = php_ini_loaded_file();
				if($configFile){
					$binaryDir = dirname($configFile);
				}else{
					$binaryDir = PHP_BINDIR;
				}
				$config_path = $binaryDir . "\\extras\\ssl\\openssl.cnf";
				$config['config'] = $config_path;
			}else{
				$config['config'] = "/etc/pki/tls/openssl.cnf";
			}
		}
		if(!file_exists($config['config'])){
			return false;
		}
		$res = openssl_pkey_new($config);
		if(!$res)return false;
		openssl_pkey_export($res, $private_key, null, $config);
		$details = openssl_pkey_get_details($res);
		if(!$details)return false;
		$public_key = $details['key'];
		if(empty($public_key) || empty($private_key))return false;
		return ['public_key' => $public_key, 'private_key' => $private_key];
	}
	
	//设置公钥
	static public function set_public_key($public_key,$is_file = true){
		if($is_file){//文件形式
			if(!file_exists($public_key))return false;
			$public_key = openssl_pkey_get_public(file_get_contents($public_key));	
		}else{
			//$public_key = "-----BEGIN PUBLIC KEY-----\n" . wordwrap($public_key, 64, "\n", true) . "\n-----END PUBLIC KEY-----";	
		}
		if(empty($public_key))return false;
		self::$public_key = $public_key;
		return true;
	}
	
	//设置私钥
	static public function set_private_key($private_key,$is_file = true){
		if($is_file){//文件形式
			if(!file_exists($private_key))return false;
			$private_key = openssl_pkey_get_private(file_get_contents($private_key));
		}else{
			//$private_key = "-----BEGIN PRIVATE KEY-----\n" . wordwrap($private_key, 64, "\n", true) . "\n-----END PRIVATE KEY-----";
		}
		if(empty($private_key))return false;
		self::$private_key = $private_key;
		return true;
	}
	
	//获取公钥
	static public function get_public_key(){
		return self::$public_key;
	}
	
	//获取私钥
	static public function get_private_key(){
		return self::$private_key;
	}	
	
	//公钥加密
	static public function encrypt($str){
		if(empty(self::$public_key))return false;
		openssl_public_encrypt($str,$encrypted, self::$public_key);
		$encrypted = base64_encode($encrypted);
		if(is_resource(self::$public_key)) openssl_free_key(self::$public_key);
		return $encrypted ?: false;
	}
	
	// 私钥解密
	static public function decrypt($encrypt_str){
		if(empty(self::$private_key))return false;
		openssl_private_decrypt(base64_decode($encrypt_str), $decrypted, self::$private_key);
		if(is_resource(self::$private_key))openssl_free_key(self::$private_key);
		return $decrypted ?: false;
	}
	
	//私钥签名
	static public function get_sign($str){
		if(empty(self::$private_key))return false;
		openssl_sign($str, $signature, self::$private_key,OPENSSL_ALGO_SHA256);
		$sign = base64_encode($signature);
		if(is_resource(self::$private_key))openssl_free_key(self::$private_key);
		return $sign ?: false;
	}
	
	//公钥验签
	static public function verify_sign($str,$sign){
		if(empty(self::$public_key))return false;
		$result = openssl_verify($str, base64_decode($sign), self::$public_key,OPENSSL_ALGO_SHA256);//如果签名正确返回 1, 签名错误返回 0, 内部发生错误则返回-1
		if(is_resource(self::$public_key))openssl_free_key(self::$public_key);
		return $result === 1;
	}
	
	public function __destruct(){
		
	}
}

/*
Rsa::set_public_key($public_key,false);
Rsa::set_private_key($private_key,false);

dump(Rsa::get_public_key());
dump(Rsa::get_private_key());

$encrypt = Rsa::encrypt("abc");
dump($encrypt);
$decrypt = Rsa::decrypt($encrypt);
dump($decrypt);

$sign = Rsa::get_sign("efd");
dump($sign);
$verifySign = Rsa::verify_sign("efd",$sign);
dump($verifySign);
*/