<fieldset class="table-search-fieldset">
    <legend>分会长申请详情</legend>
    <div style="margin: 20px;">
        <div class="layui-row">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">申请信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                                <tr>
                                    <td width="120"><strong>申请ID：</strong></td>
                                    <td><?php echo $application['id']; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>申请人：</strong></td>
                                    <td>
                                        <?php echo htmlspecialchars($application['user_nickname']); ?>
                                        <?php if($application['user_avatar']): ?>
                                        <img src="<?php echo $application['user_avatar']; ?>" style="width: 30px; height: 30px; border-radius: 50%; margin-left: 10px; vertical-align: middle;">
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>手机号：</strong></td>
                                    <td><?php echo htmlspecialchars($application['user_mobile']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>分会名称：</strong></td>
                                    <td><?php echo htmlspecialchars($application['branch_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>分会地区：</strong></td>
                                    <td><?php echo htmlspecialchars($application['branch_location']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>申请时间：</strong></td>
                                    <td><?php echo $application['application_time']; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>申请状态：</strong></td>
                                    <td>
                                        <?php 
                                        switch($application['status']) {
                                            case 0:
                                                echo '<span class="layui-badge layui-bg-orange">待审核</span>';
                                                break;
                                            case 1:
                                                echo '<span class="layui-badge layui-bg-green">已通过</span>';
                                                break;
                                            case 2:
                                                echo '<span class="layui-badge">已拒绝</span>';
                                                break;
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">分会描述</div>
                    <div class="layui-card-body">
                        <div style="padding: 10px; background: #f8f8f8; border-radius: 4px; min-height: 80px;">
                            <?php echo nl2br(htmlspecialchars($application['branch_description'] ?: '暂无描述')); ?>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">申请理由</div>
                    <div class="layui-card-body">
                        <div style="padding: 10px; background: #f8f8f8; border-radius: 4px; min-height: 100px;">
                            <?php echo nl2br(htmlspecialchars($application['application_reason'])); ?>
                        </div>
                    </div>
                </div>
                
                <?php if($application['status'] != 0): ?>
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">审核信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                                <tr>
                                    <td width="120"><strong>审核时间：</strong></td>
                                    <td><?php echo $application['review_time'] ?: '未审核'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>审核意见：</strong></td>
                                    <td>
                                        <div style="padding: 10px; background: #f8f8f8; border-radius: 4px; min-height: 60px;">
                                            <?php echo nl2br(htmlspecialchars($application['review_comment'] ?: '无审核意见')); ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php if($application['status'] == 1 && $application['created_branch_id']): ?>
                                <tr>
                                    <td><strong>创建分会ID：</strong></td>
                                    <td><?php echo $application['created_branch_id']; ?></td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">操作</div>
                    <div class="layui-card-body">
                        <div style="text-align: center;">
                            <a href="/branchpresident/applications" class="layui-btn layui-btn-primary">返回列表</a>
                            
                            <?php if($application['status'] == 0): ?>
                            <br><br>
                            <button class="layui-btn layui-btn-normal" onclick="reviewApplication(<?php echo $application['id']; ?>, 1)">
                                <i class="layui-icon layui-icon-ok"></i> 通过申请
                            </button>
                            <br><br>
                            <button class="layui-btn layui-btn-danger" onclick="reviewApplication(<?php echo $application['id']; ?>, 2)">
                                <i class="layui-icon layui-icon-close"></i> 拒绝申请
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">申请人信息</div>
                    <div class="layui-card-body">
                        <div style="text-align: center;">
                            <?php if($application['user_avatar']): ?>
                            <img src="<?php echo $application['user_avatar']; ?>" style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 10px;">
                            <br>
                            <?php endif; ?>
                            <strong><?php echo htmlspecialchars($application['user_nickname']); ?></strong>
                            <br>
                            <span style="color: #999;"><?php echo htmlspecialchars($application['user_mobile']); ?></span>
                            <br><br>
                            <a href="/user/edit?uid=<?php echo $application['user_id']; ?>" class="layui-btn layui-btn-xs">查看用户详情</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</fieldset>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 审核申请
    window.reviewApplication = function(id, status) {
        var title = status == 1 ? '通过申请' : '拒绝申请';
        var content = '<div style="padding: 20px;">';
        content += '<div class="layui-form-item">';
        content += '<label class="layui-form-label">审核意见：</label>';
        content += '<div class="layui-input-block">';
        content += '<textarea id="review_comment" placeholder="请输入审核意见（可选）" class="layui-textarea" style="height: 100px;"></textarea>';
        content += '</div>';
        content += '</div>';
        content += '</div>';
        
        layer.open({
            type: 1,
            title: title,
            content: content,
            area: ['500px', '300px'],
            btn: ['确认', '取消'],
            yes: function(index, layero) {
                var comment = $('#review_comment').val();
                
                // 显示加载状态
                layer.load(2);
                
                // 提交审核
                $.post('/branchpresident/review_application', {
                    id: id,
                    status: status,
                    comment: comment
                }, function(response) {
                    layer.closeAll('loading');
                    layer.close(index);
                    
                    if(response.indexOf('成功') > -1) {
                        layer.msg('审核完成', {icon: 1}, function() {
                            window.location.reload();
                        });
                    } else {
                        layer.msg('审核失败：' + response, {icon: 2});
                    }
                }).fail(function() {
                    layer.closeAll('loading');
                    layer.close(index);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                });
            }
        });
    };
});
</script>

<style>
.layui-card {
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}
.layui-card-header {
    background-color: #f8f8f8;
    font-weight: bold;
}
</style>
