<?php
namespace core;
class Session{
	static public $session_name = null;
	static public $session_path =  BASE_PATH . "session" . DS;
	public function __construct(){
		
	}
	
	static public function init(){
		if(!is_dir(Session::$session_path))@mkdir(Session::$session_path, 0777, true);
		$GLOBALS["_SESSION"] = [];
		if(isset($_COOKIE[config("sys.session_name")])){
			Session::$session_name = $_COOKIE[config("sys.session_name")];
			$session_data = Session::get();
			if($session_data){
				$GLOBALS["_SESSION"] = $session_data;
			}
		}
	}
	
	static public function save(){
		if(!is_dir(Session::$session_path))@mkdir(Session::$session_path, 0777, true);
		if(!empty($GLOBALS["_SESSION"])){
			Session::send(config("sys.session_expire"));
			$data = [
				"deadline"=>date("Y-m-d H:i:s",time()+config("sys.session_expire")),
				"data"=>$GLOBALS["_SESSION"],
			];
			return file_put_contents(Session::$session_path . Session::$session_name,serialize($data));
		}else{
			Session::send(-1);
			if(file_exists(Session::$session_path . Session::$session_name))@unlink(Session::$session_path . Session::$session_name);
			return true;
		}
	}
	
	static public function distroy(){
		if(empty(Session::$session_name))return false;
		return @unlink($session_path . Session::$session_name);
	}
	
	static public function clear(){
		$files = scandir(Session::$session_path);
		foreach ($files as $file) {
			if($file == "." || $file == "..")continue;
			$data = unserialize(file_get_contents(Session::$session_path . $file));
			if(!isset($data['deadline'])){
				@unlink(Session::$session_path . $file);
			}else{
				$dateline = strtotime($data['deadline']);
				if($dateline < _NOW_){
					@unlink(Session::$session_path . $file);
				}
			}
		}
	}
	
	static private function get(){
		if(empty(Session::$session_name))return false;
		$files = scandir(Session::$session_path);
		foreach ($files as $file) {
			if($file == Session::$session_name){
				$data = unserialize(file_get_contents(Session::$session_path . Session::$session_name));
				$dateline = strtotime($data['deadline']);
				if($dateline < _NOW_){
					return false;
				}
				return $data['data'];
			}
		}
		return false;
	}
	
	static private function send($expire){
		if(empty(Session::$session_name)){
			$session_name = md5(uniqid());
			Session::$session_name = $session_name;
		}
		$expire = time() + $expire;
		return setcookie(config("sys.session_name"),Session::$session_name,$expire,"/",config("sys.session_domain"),config("sys.session_secure"),true);
	}
	
	public function __destruct(){

	}
}
