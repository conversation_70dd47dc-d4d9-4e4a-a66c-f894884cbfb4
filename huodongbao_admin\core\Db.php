<?php
namespace core;
use core\config;
use \PDO;
class Db{
	static private $_instance = null;
	static protected $connects = [];
	static protected $connectCurrent = null;
	static public $lastSql = null;
	protected $table = "";
	protected $select = "*";
	protected $where = "";
	protected $order = "";
	protected $limit = "";
	protected $group = "";
	protected $having = "";
	protected $prepareParam = [];
	
	
	public function __construct(){
		
	}
	
	static public function getInstance(){
		if(self::$_instance instanceof self){
			return self::$_instance;
		}
		self::$_instance = new self();
		return self::$_instance;
	}
	
	static public function connect($useConfig="",$reConn=false){
		$config = Config::getDatabaseConfig();
		if(empty($config) || !is_array($config) || count($config) == 0)throw new \Exception("数据库配置为空");
		if(empty($useConfig)){
			if(count($config) > 1)throw new \Exception("未选择数据库配置");
			$config_keys = array_keys($config);
			$useConfig = $config_keys[0];
		}
		if(!isset($config[$useConfig]))throw new \Exception("数据库配置不存在");
		if(isset(self::$connects[$useConfig]) && !$reConn){
			self::$connectCurrent = self::$connects[$useConfig];
			return self::$connects[$useConfig];
		}
		$configInfo = $config[$useConfig];
		try{
			$conn = new PDO("{$configInfo['type']}:host={$configInfo['host']}:{$configInfo['port']};dbname={$configInfo['database']}",$configInfo['user'],$configInfo['password'],array(PDO::ATTR_AUTOCOMMIT=>1,PDO::ATTR_PERSISTENT => true));
			self::$connects[$useConfig] = $conn;
			self::$connects[$useConfig]->query("set names {$configInfo['charset']}");
			self::$connects[$useConfig]->setAttribute(PDO::ATTR_ERRMODE,  PDO::ERRMODE_EXCEPTION);
			self::$connectCurrent = self::$connects[$useConfig];
		}catch(PDOException $e){
			_log_($e->getMessage());
			throw new \Exception("数据库连接失败");
		}
		return self::$connects[$useConfig];
	}
	
	static public function query($sql){
		self::$lastSql = $sql;
		return self::$connectCurrent->query($sql);
	}
	
	//完整sql查询
	
	static public function _fetchAll($sql,$prepareParam=[]){
		if(empty($prepareParam)){
			self::$lastSql = $sql;
			$result = self::$connectCurrent->query($sql);
		}else{
			self::$lastSql = ["sql"=>$sql,["prepareParam"=>$prepareParam]];
			$result = self::$connectCurrent->prepare($sql);
			foreach($prepareParam as $k=>$v){
				if(is_int($v)){
					$result->bindValue($k,$v,PDO::PARAM_INT);
				}else{
					$result->bindValue($k,$v,PDO::PARAM_STR);
				}
			}
			$result->execute();
		}
		if($result){
			$result->setFetchMode(PDO::FETCH_ASSOC);
			return $result->fetchAll();
		}else{
			return false;
		}
	}
	
	static public function _fetch($sql,$prepareParam=[]){
		if(empty($prepareParam)){
			self::$lastSql = $sql;
			$result = self::$connectCurrent->query($sql);
		}else{
			self::$lastSql = ["sql"=>$sql,["prepareParam"=>$prepareParam]];
			$result = self::$connectCurrent->prepare($sql);
			foreach($prepareParam as $k=>$v){
				if(is_int($v)){
					$result->bindValue($k,$v,PDO::PARAM_INT);
				}else{
					$result->bindValue($k,$v,PDO::PARAM_STR);
				}
			}
			$result->execute();
		}
		if($result){
			$result->setFetchMode(PDO::FETCH_ASSOC);
			return $result->fetch();
		}else{
			return false;
		}
	}
	
	//完整sql写入
	
	static public function _exec($sql,$prepareParam=[]){//echo $sql;
		if(empty($prepareParam)){
			self::$lastSql = $sql;
			return self::$connectCurrent->exec($sql);
		}else{
			self::$lastSql = ["sql"=>$sql,["prepareParam"=>$prepareParam]];
			$result = self::$connectCurrent->prepare($sql);
			foreach($prepareParam as $k=>$v){
				if(is_int($v)){
					$result->bindValue($k,$v,PDO::PARAM_INT);
				}else{
					$result->bindValue($k,$v,PDO::PARAM_STR);
				}
			}
			$result->execute();
			return $result->rowCount();
		}
	}
	
	//链式操作
	
	public function table($table){
		$this->table = strpos($table,".") === false ? '`'.$table.'`' : $table;
		return $this;
	}
	
	public function select($select="*"){
		$this->select = $select;
		return $this;
	}
	
	public function where($where=''){
		$this->where = " WHERE ".$where;
		return $this;
	}
	
	public function order($order){
		$this->order = " ORDER BY ".$order;
		return $this;
	}
	public function group($group){
		$this->group = " GROUP BY ".$group;
		return $this;
	}
	public function having($having){
		$this->having = " HAVING ".$having;
		return $this;
	}	
	public function limit($limit=20,$start=0){
		$this->limit = " LIMIT ".$start.",".$limit;
		return $this;
	}
	
	public function page($page=1,$size=10,$pageParams=null){
		$page = (int)$page;
		$page = $page>0 ? $page : 1;
		$start = $size * ($page - 1);
		$this->limit = " LIMIT ".$start.",".$size;
		//设置page类参数
		if(empty($this->group)){
			$sql = "SELECT count(1) as count FROM ".$this->table.$this->where;
			$res = $this->_fetch($sql,$this->prepareParam);
		}else{
			$res = ["count"=>0];
		}
		\core\Page::$count= isset($res["count"]) ? $res["count"] : 0;
		\core\Page::$pageSize=$size;
		\core\page::$pageParams = $pageParams;
		//
		return $this->fetchAll();
	}
	
	public function prepareParam($prepareParam = []){
		$this->prepareParam = $prepareParam;
		return $this;
	}
	
	public function fetch(){
		if(empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = "SELECT ".$this->select." FROM ".$this->table." ".$this->where." ".$this->order." LIMIT 1";//echo $sql."<br />";exit;
		$prepareParam = $this->prepareParam;
		$this->reset();
		return $this->_fetch($sql,$prepareParam);
	}
	
	public function fetchAll(){
		if(empty($this->table))die("error db:fetchAll参数错误");
		$sql = "SELECT ".$this->select." FROM ".$this->table." ".$this->where." ".$this->group." ".$this->having." ".$this->order." ".$this->limit;//echo $sql."<br />";
		$prepareParam = $this->prepareParam;
		$this->reset();
		return $this->_fetchAll($sql,$prepareParam);
	}
	
	public function getColumn($column,$default = false){
		$column = addslashes($column);
		if(empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = "SELECT `".$column."` FROM ".$this->table." ".$this->where." ".$this->order." LIMIT 1";//echo $sql."<br />";exit;
		$prepareParam = $this->prepareParam;
		$this->reset();
		$res = $this->_fetch($sql,$prepareParam);
		if($res && isset($res[$column]))return $res[$column];
		return $default;
	}
	
	public function insert(Array $data,$type=false){
		if(empty($data) || !is_array($data) || empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$keys = implode("`,`",array_keys($data));
		//$values = implode("','",array_values($data));
		$values_arr = array_values($data);
		$values = '';
		$prepareParam = $this->prepareParam;
		foreach($values_arr as $v){
			if(!empty($prepareParam) && isset($prepareParam[$v])){
				$values .= $v.",";
			}else{
				$values .= "'".addslashes($v)."',";
			}
		}
		$values = substr($values,0,-1);
		$sql = ($type ? "REPLACE" : "INSERT") .' INTO '.$this->table.' (`'.$keys.'`) VALUES ('.$values.')';//echo $sql."<br />";
		$this->reset();
		return $this->_exec($sql,$prepareParam);
	}
	
	public function del(){
		if(empty($this->where) || empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = 'DELETE FROM '.$this->table.$this->where;
		$prepareParam = $this->prepareParam;
		$this->reset();
		return $this->_exec($sql,$prepareParam);
	}
	
	public function update(Array $data){//test($data);test($where);
		if(empty($data) || !is_array($data) || empty($this->where) || empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$str = "";
		$prepareParam = $this->prepareParam;
		foreach($data as $k=>$v){
			if(!empty($prepareParam) && isset($prepareParam[$v])){
				$str .= $k.'='.$v.',';
			}else{
				$str .= $k.'='.'\''.addslashes($v).'\',';
			}
		}
		$str = substr($str,0,-1);
		$sql = 'UPDATE '.$this->table.' SET '.$str.$this->where; //dump($sql."<br />");dump($prepareParam);exit;
		$this->reset();
		return $this->_exec($sql,$prepareParam);
	}

	public function sum($column,$default=0){
		if(empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = "SELECT SUM(`".$column."`) AS s FROM ".$this->table." ".$this->where." LIMIT 1";//echo $sql."<br />";exit;
		$prepareParam = $this->prepareParam;
		$this->reset();
		$res = $this->_fetch($sql,$prepareParam);
		if($res && isset($res['s']))return $res['s'];
		return $default;
	}

	public function count($default=0){
		if(empty($this->table))throw new \Exception("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = "SELECT COUNT(1) AS c FROM ".$this->table." ".$this->where." LIMIT 1";//echo $sql."<br />";exit;
		$prepareParam = $this->prepareParam;
		$this->reset();
		$res = $this->_fetch($sql,$prepareParam);
		if($res && isset($res['c']))return $res['c'];
		return $default;
	}
	
	public function reset(){
		$this->limit = "";
		$this->order = "";
		$this->group = "";
		$this->having = "";
		$this->where = "";
		$this->table = "";
		$this->select = "*";
		$this->prepareParam = [];
	}
	
	//事务处理
	
	static public function begin(){
		self::$connectCurrent->beginTransaction();
	}
	
	static public function commit(){
		self::$connectCurrent->commit();
	}
	
	static public function rollback(){
		self::$connectCurrent->rollback();
	}
	
	//锁处理
	
	static public function lock(array $tables){
		if(empty($tables) || !is_array($tables))die("文件" . __FILE__ . "第" .__LINE__ . "行参数错误");
		$sql = "lock tables ";
		foreach($tables as $table=>$type){
			$sql .= $table." ".$type.",";
		}
		$sql = substr($sql,0,-1);
		return self::query($sql);
	}
	
	static public function unlock(){
		return self::query('unlock tables');
	}
	
	//预处理方法
	
	static public function prepare($sql){
		return self::$connectCurrent->prepare($sql, array(PDO::ATTR_CURSOR => PDO::CURSOR_FWDONLY));
	}
	
	static public function sthFetch($sth){
		if($sth){
			$sth->setFetchMode(PDO::FETCH_ASSOC);
			return $sth->fetch();
		}else{
			return false;
		}
	}
	
	static public function sthFetchAll($sth){
		if($sth){
			$sth->setFetchMode(PDO::FETCH_ASSOC);
			return $sth->fetchAll();
		}else{
			return false;
		}
	}
	//
	
	//获取最后插入id
	
	static public function insertId(){
		return self::$connectCurrent->lastInsertId();
	}
	
	//加引号
	
	static public function quote($var=''){
		if(!self::ping())throw new \Exception("数据库连接不可用");
		return self::$connectCurrent->quote($var);
	}
	
	//连接可用性检测
	
	static public function ping(){
		if(self::$connectCurrent === null)return false;
		try{
			self::$connectCurrent->getAttribute(\PDO::ATTR_SERVER_INFO);
		}catch(\Exception $e){
			//if(stripos($e->getMessage(), "MySQL server has gone away") !== false){}
			return false;
		}
		return true;
	}
	//
	public function __destruct(){

	}
}
