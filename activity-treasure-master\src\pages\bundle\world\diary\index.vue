<script setup>
import { ref, onMounted } from 'vue';
import { getDiaries } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';

// 状态管理
const diaries = ref([]);
const loading = ref(true);
const refreshing = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 10;

// 加载日记列表
const loadDiaries = async (page = 1, isRefresh = false) => {
  try {
    if (isRefresh) {
      refreshing.value = true;
      currentPage.value = 1;
    } else if (page > 1) {
      loadingMore.value = true;
    } else {
      loading.value = true;
    }

    const res = await getDiaries({
      page: page,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      type: 'diary' // 只获取日记类型
    });

    if (res.status === 'ok') {
      const newDiaries = res.data.list || [];
      
      if (isRefresh || page === 1) {
        diaries.value = newDiaries;
      } else {
        diaries.value = [...diaries.value, ...newDiaries];
      }
      
      hasMore.value = newDiaries.length === pageSize;
      currentPage.value = page;
    } else if (res.status === 'empty') {
      if (isRefresh || page === 1) {
        diaries.value = [];
      }
      hasMore.value = false;
    }
  } catch (error) {
    console.error('加载日记失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
    loadingMore.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  loadDiaries(1, true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadDiaries(currentPage.value + 1);
  }
};

// 查看日记详情
const viewDiary = (diary) => {
  navto(`/pages/bundle/world/detail/index?type=diary&id=${diary.id}`);
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - time;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDiaries();
});
</script>

<template>
  <view class="diary-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 日记列表 -->
    <scroll-view 
      v-else
      class="diary-scroll"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <!-- 空状态 -->
      <view v-if="diaries.length === 0" class="empty-container">
        <image src="/static/empty.png" class="empty-image" mode="aspectFit"></image>
        <text class="empty-text">还没有日记哦</text>
        <text class="empty-desc">记录生活的点点滴滴</text>
      </view>

      <!-- 日记列表 -->
      <view v-else class="diary-list">
        <view 
          v-for="diary in diaries" 
          :key="diary.id" 
          class="diary-item"
          @click="viewDiary(diary)"
        >
          <!-- 用户信息 -->
          <view class="diary-header">
            <image 
              :src="diary.user?.avatar_url || '/static/default-avatar.png'" 
              class="user-avatar"
              mode="aspectFill"
            ></image>
            <view class="user-info">
              <text class="user-nickname">{{ diary.user?.nickname || '匿名用户' }}</text>
              <text class="diary-time">{{ formatTime(diary.created_at) }}</text>
            </view>
            <!-- 私密标识 -->
            <view v-if="diary.privacy === 'private'" class="privacy-badge">
              <u-icon name="lock" size="12" color="#999"></u-icon>
              <text class="privacy-text">私密</text>
            </view>
          </view>

          <!-- 日记内容 -->
          <view class="diary-content">
            <text class="diary-text">{{ diary.content }}</text>
          </view>

          <!-- 图片展示 -->
          <view v-if="diary.images && diary.images.length > 0" class="diary-images">
            <image 
              v-for="(img, index) in diary.images.slice(0, 3)" 
              :key="index"
              :src="img"
              class="diary-image"
              mode="aspectFill"
            ></image>
            <view v-if="diary.images.length > 3" class="more-images">
              <text class="more-text">+{{ diary.images.length - 3 }}</text>
            </view>
          </view>

          <!-- 位置信息 -->
          <view v-if="diary.location" class="diary-location">
            <u-icon name="map" size="12" color="#999"></u-icon>
            <text class="location-text">{{ JSON.parse(diary.location).name }}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="circle" size="20" color="#6AC086"></u-loading-icon>
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="!hasMore && diaries.length > 0" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.diary-container {
  height: 100%;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.diary-scroll {
  height: 100%;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .empty-desc {
    font-size: 28rpx;
    color: #999;
  }
}

.diary-list {
  padding: 20rpx;
}

.diary-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.diary-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }
  
  .user-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .diary-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .privacy-badge {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    
    .privacy-text {
      font-size: 20rpx;
      color: #999;
      margin-left: 8rpx;
    }
  }
}

.diary-content {
  margin-bottom: 24rpx;
  
  .diary-text {
    font-size: 30rpx;
    line-height: 1.6;
    color: #333;
  }
}

.diary-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
  
  .diary-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
  }
  
  .more-images {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .more-text {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

.diary-location {
  display: flex;
  align-items: center;
  
  .location-text {
    font-size: 24rpx;
    color: #999;
    margin-left: 8rpx;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-more-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
