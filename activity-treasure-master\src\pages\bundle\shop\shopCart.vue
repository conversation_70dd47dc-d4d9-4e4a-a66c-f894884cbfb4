<script setup>
import { goodsget_car_list, goodsadd_car, goodsdel_car } from "@/api";
import { reactive, ref, nextTick } from "vue";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto } from "@/utils";

const sumPrice = ref("0.00");
const list = ref([]); // 数据列表
const checkboxValue = ref([]); // 选中的列表
const options = ref([
  {
    text: "删除",
    style: {
      backgroundColor: "#ff6e16",
    },
    show: false,
  },
]);
const checkBox = ref([]); // 全选是否选中

onPullDownRefresh(() => {
  setTimeout(function () {
    get();
    uni.stopPullDownRefresh();
  }, 1000);
});
onLoad(async () => {});
onShow(async () => {
  checkBox.value = [];
  checkboxValue.value = [];
  get();
});

// 获取总价格
const getAllMoney = () => {
  sumPrice.value = 0;
  list.value.map((val) => {
    if (checkboxValue.value.indexOf(val.id) != -1) sumPrice.value += val.price * val.num;
  });
  sumPrice.value = sumPrice.value.toFixed(2);
};

// 获取列表
const get = async () => {
  const res = await goodsget_car_list();
  if (res != "n") list.value = res.data;
};

// 选择商品
const checkboxChange = (e) => {
  checkboxValue.value = e;
  if (checkboxValue.value.length > 0) {
    getAllMoney();
    if ((checkboxValue.value.length = list.value.length)) checkBox.value = ["checked"];
  } else {
    sumPrice.value = 0;
    checkBox.value = [];
  }
};

// 减少商品数量
const del = async (val, i) => {
  if (val.num > 1) {
    val.num--;
    const res = await goodsadd_car({
      goods_id: val.goods_id,
      guige_id: val.guige_id,
      num: val.num,
    });
    getAllMoney();
  } else uni.$u.toast("不可低于一件");
};

// 增加商品数量
const add = async (val, i) => {
  val.num++;
  const res = await goodsadd_car({
    goods_id: val.goods_id,
    guige_id: val.guige_id,
    num: val.num,
  });
  getAllMoney();
};

// 删除商品
const deleteGoods = async (e) => {
  const res = await goodsdel_car({ ids: e ? e : checkboxValue.value.join(",") });
  if (res.status === "ok") {
    list.value = [];
    get();
  }
  if (list.value.length === 0) checkBox.value = [];
  getAllMoney();
};

//全选
const changeAll = (e) => {
  sumPrice.value = 0;
  checkBox.value = checkBox.value?.length > 0 ? ["checked"] : [];
  if (e.length) list.value.map((val) => checkboxValue.value.push(val.id));
  else checkboxValue.value = [];
  getAllMoney();
};

//跳转确认订单页面
const toConfirmation = () => {
  let goodsList = [];
  list.value.map((val) => {
    if (checkboxValue.value.includes(val.id))
      goodsList.push({
        goods_id: val.goods_id,
        guige_id: val.guige_id,
        num: val.num,
        money: (Math.round(val.guige_info.price * 100) * val.num) / 100,
        img: val.goods_info.img_url,
        name: val.goods_info.name,
        guige: val.guige_info.guige,
        is_shiyong: val.guige_info.is_shiyong,
      });
  });
  if (goodsList.length < 1) return uni.$u.toast("请选择商品结算");
  store().changeGoods(goodsList);
};
</script>

<template>
  <view class="page p30">
    <u-empty :show="list.length === 0" mode="car" margin-top="300rpx"> </u-empty>
    <u-swipe-action class="mt30" :autoClose="true">
      <u-checkbox-group
        v-model="checkboxValue"
        placement="column"
        shape="circle"
        activeColor="#ff6e16"
        @change="checkboxChange"
      >
        <view class="mb20 ma w690" v-for="(val, i) in list" :key="i">
          <u-swipe-action-item
            :options="options"
            v-if="val.guige_info.guige"
            :autoClose="true"
            @click="deleteGoods(val.id)"
          >
            <view
              class="df p20"
              @click="
                navto(
                  `/pages/bundle/shop/goodInfo?id=${val.id}&guige=${JSON.stringify(
                    val.guige_info.guige
                  )}`
                )
              "
            >
              <u-checkbox :customStyle="{ marginBottom: '8px' }" :name="val.id">
              </u-checkbox>
              <u-image
                radius="12"
                width="165rpx"
                height="165rpx"
                :src="val.goods_info.img_url"
              ></u-image>
              <view class="ml30 f1 df fdc jcsb">
                <view class="x28">{{ val.goods_info.name }}</view>
                <view class="df aic">
                  <view
                    class="x24 c69 h2"
                    v-for="(item, index) in val.guige_info?.guige"
                    :key="index"
                  >
                    {{ index }}: {{ item }};
                  </view>
                </view>
                <view class="df aic jcsb">
                  <u-text
                    mode="price"
                    color="#333"
                    size="24rpx"
                    :text="val.price"
                  ></u-text>
                  <u-number-box integer>
                    <template #minus>
                      <view class="minus" @click="del(val, i)">
                        <u-icon
                          class="border r50"
                          style="padding: 5rpx; border-color: #ff6e16"
                          :name="`${store().$state.url}del.png`"
                          color="#ff6e16"
                          size="44rpx"
                        ></u-icon>
                      </view>
                    </template>
                    <template #input>
                      <text style="width: 50px; text-align: center" class="input">
                        {{ val.num }}
                      </text>
                    </template>
                    <template #plus>
                      <view class="plus" @click="add(val, i)">
                        <u-icon
                          class="ball r50"
                          style="padding: 5rpx"
                          :name="`${store().$state.url}add.png`"
                          color="#fff"
                          size="44rpx"
                        ></u-icon>
                      </view>
                    </template>
                  </u-number-box>
                </view>
              </view>
            </view>
          </u-swipe-action-item>
        </view>
      </u-checkbox-group>
    </u-swipe-action>
    <view class="pfx z20 df aic jcsb p20 b6f w" style="bottom: 100rpx; left: 0">
      <u-checkbox-group
        shape="circle"
        placement="column"
        @change="changeAll"
        :disabled="list.length === 0"
        v-model="checkBox"
      >
        <u-checkbox active-color="#ff6e16" label="全选" name="checked"></u-checkbox>
      </u-checkbox-group>
      <view class="ml20 call" v-if="checkboxValue.length" @click="deleteGoods(0)">
        删除
      </view>
      <view class="f1 tar mr40">合计：￥{{ sumPrice }}</view>
      <u-button
        color="linear-gradient(142deg, #8efffe 0%, #c6e538 100%)"
        shape="circle"
        :customStyle="{ width: '160rpx', color: '#333' }"
        text="支付"
        @click="toConfirmation"
      ></u-button>
    </view>
  </view>
</template>

<style lang="scss"></style>
