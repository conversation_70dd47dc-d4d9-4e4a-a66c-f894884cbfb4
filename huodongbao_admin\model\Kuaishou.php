<?php
namespace model;
use core\Db;
class Kuaishou{
	
	public function __construct(){
		
	}
	
	//小程序登录
	static public function getOpenid($code="",$debug=false){
		$appid = config("app.kuaishou_appid");
		$appsecret = config("app.kuaishou_app_secret");
		$url = "https://open.kuaishou.com/oauth2/mp/code2session";
		$data = [
			"app_id"=>$appid,
			"app_secret"=>$appsecret,
			"js_code"=>$code,
		];
		$res = curl($url,$data,false);
		$data = json_decode($res['body'],true);
		if(isset($data['result']) && $data['result'] == 1 && isset($data['open_id']) && !empty($data['open_id'])){
			$data = [
				"openid"=>$data['open_id'],
				"unionid"=>$data['open_id'],
			];
			return $data;
		}else{
			if($debug === false)return false;
			return $res;
		}
	}

	//获取接口调用的凭证
	static public function getAccessToken($is_online=false){
		//
		if(empty($is_online)){
			$kuaishou_access_token = \core\Cache::getCache("kuaishou_access_token");
			if(!empty($kuaishou_access_token))return $kuaishou_access_token;
		}
		//
		$appid = config("app.kuaishou_appid");
		$appsecret = config("app.kuaishou_app_secret");
		//
		$url = "https://open.kuaishou.com/oauth2/access_token";
		$params = [
			"grant_type"=>"client_credentials",
			"app_id"=>$appid,
			"app_secret"=>$appsecret,
		];
		$res = curl($url,$params,false);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(isset($data['result']) && $data['result'] == 1 && isset($data['access_token']) && !empty($data['access_token']) && isset($data['expires_in'])){
			//\core\Cache::setCache("kuaishou_access_token",$data['access_token'],$data['expires_in'] - 10);
			return $data['access_token'];
		}else{
			return false;
		}
	}
	
	//担保支付下单
	static public function danbao_pay($money_fen,$out_order_no,$open_id,$subject="商品描述",$goods_num=1){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		//
		$appid = config("app.kuaishou_appid");
		//
		$url = "https://open.kuaishou.com/openapi/mp/developer/epay/create_order?app_id={$appid}&access_token={$access_token}";
		$data = [
			"app_id"=>$appid,//小程序APPID
			"out_order_no"=>$out_order_no,//商户系统内部订单号
			"open_id"=>$open_id,//快手用户在当前小程序的open_id
			"total_amount"=>$money_fen,//用户支付金额，单位为[分]。不允许传非整数的数值。
			"subject"=>$subject,//商品描述。 长度限制不超过 128 字节
			"detail"=>$subject,//商品详情
			"type"=>1731,//商品类型，不同商品类目的编号见 https://mp.kuaishou.com/docs/operate/platformAgreement/epayServiceCharge.html
			"expire_time"=>30 * 60,//订单过期时间，单位秒，300s - 172800s
			"notify_url"=>HTTP_HOST . "/pay/kaishou_pay_notify",//通知URL必须为直接可访问的URL，不允许携带查询串
		];
		if(check($goods_num,"intgt0") && $goods_num > 1){
			//$data['multi_copies_goods_info'] = '[{"copies":'.$goods_num.'}]';
		}
		$data['sign'] = self::getRequestSign($data);
		unset($data['app_id']);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			isset($data['result']) &&
			$data['result'] == 1 &&
			isset($data['order_info']['order_no']) && 
			!empty($data['order_info']['order_no']) &&
			isset($data['order_info']['order_info_token']) &&
			!empty($data['order_info']['order_info_token'])
		){
			$data = [
				"order_no"=>$data['order_info']['order_no'],//签名后的订单信息
				"order_info_token"=>$data['order_info']['order_info_token'],//抖音侧唯一订单号
			];
			return ["status"=>"ok","data"=>$data];
		}
		$msg = isset($data['result']) ? $data['result'] : "下单失败";
		$msg .= isset($data['error_msg']) ? ":【".$data['error_msg']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//担保支付-订单支付结果查询
	static public function query_order($out_order_no){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		//
		$appid = config("app.kuaishou_appid");
		//
		$url = "https://open.kuaishou.com/openapi/mp/developer/epay/query_order?app_id={$appid}&access_token={$access_token}";
		$data = [
			"app_id"=>$appid,//小程序APPID
			"out_order_no"=>$out_order_no,//商户系统内部订单号
		];
		$data['sign'] = self::getRequestSign($data);
		unset($data['app_id']);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			isset($data['result']) &&
			$data['result'] == 1 &&
			isset($data['payment_info']['pay_status']) && 
			$data['payment_info']['pay_status'] == "SUCCESS"
		){
			return ["status"=>"ok","data"=>$data];
		}
		return false;
	}

	//担保支付-发起退款
	static public function danbao_pay_tuikuan($refund_amount,$out_order_no,$reason="协商退款",$goods_num=1){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		//
		$appid = config("app.kuaishou_appid");
		//
		$url = "https://open.kuaishou.com/openapi/mp/developer/epay/apply_refund?app_id={$appid}&access_token={$access_token}";
		$data = [
			"app_id"=>$appid,//小程序APPID
			"out_order_no"=>$out_order_no,//商户订单号
			"out_refund_no"=>$out_order_no,//开发者的退款单号
			"refund_amount"=>$refund_amount,//用户退款金额，单位为分
			"reason"=>$reason,//退款理由
			"notify_url"=>"https://api.benbene.cn/pay/kuaishou_pay_tuikuan_notify",//商户自定义回调地址，必须以 https 开头，支持 443 端口
		];
		if(check($goods_num,"intgt0") && $goods_num > 1){
			//$data['multi_copies_goods_info'] = '[{"copies":'.$goods_num.'}]';
		}
		$data['sign'] = self::getRequestSign($data);
		unset($data['app_id']);
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			isset($data['result']) &&
			$data['result'] == 1 &&
			isset($data['refund_no']) && 
			!empty($data['refund_no'])
		){
			return ["status"=>"ok","data"=>$data['refund_no']];
		}
		$msg = isset($data['result']) ? $data['result'] : "退款受理失败";
		$msg .= isset($data['error_msg']) ? ":【".$data['error_msg']."】" : "";
		return ["status"=>"error","msg"=>$msg];
	}
	
	//担保支付订单同步推送
	static public function danbao_pay_push($open_id,$order_id,$create_time,$status,$product_cover_img_id){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		//
		$appid = config("app.kuaishou_appid");
		//
		$url = "https://open.kuaishou.com/openapi/mp/developer/order/v1/report?app_id={$appid}&access_token={$access_token}";
		$data = [
			"out_biz_order_no"=>$order_id,
			"out_order_no"=>$order_id,
			"open_id"=>$open_id,
			"order_create_time"=>$create_time,//订单创建时间，需小于等于当前时间 时间戳13位单位：ms
			"order_status"=>$status,//订单状态 1待支付2支付成功3已取消4退款中5退款失败6退款成功10待使用虚拟类商品11已使用虚拟类商品状态12待发货实物13部分发货实物14待收货实物15交易成功实物
			"order_path"=>"/pages/bundle/myOrder/myOrder",//订单path，用于跳转到小程序内订单详情页，需要存在于小程序最新版本的包中
			"product_cover_img_id"=>$product_cover_img_id,//商品图对应的imgId（通过文件上传接口获取），要求长宽比1:1，最大不超过10MB。需与用户下单时的商品图保持一致
		];
		$res = curl($url,$data,true);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(!empty($data) && isset($data['result']) && $data['result'] == 1){
			return ["status"=>"ok"];
		}
		$msg = isset($data['result']) ? $data['result'] : "快手担保支付同步推送订单失败";
		$msg .= isset($data['error_msg']) ? ":".$data['error_msg'] : "";
		return ["status"=>"error","msg"=>$msg];
	}

	//通过url进行图片上传担保支付订单同步推送需要的图片
	static public function uploadWithUrl($img_url){
		$access_token = self::getAccessToken();
		if(empty($access_token)){
			$access_token = self::getAccessToken(true);
			if(empty($access_token)){
				return ["status"=>"error","msg"=>"获取access_token失败"];
			}
		}
		//
		$appid = config("app.kuaishou_appid");
		//
		$url = "https://open.kuaishou.com/openapi/mp/developer/file//img/uploadWithUrl?app_id={$appid}&access_token={$access_token}&url=".urlencode($img_url);
		$res = curl($url);
		$data = json_decode($res['body'],true);
		//dump($data);
		if(
			!empty($data) && 
			isset($data['result']) && 
			$data['result'] == 1 &&
			isset($data['data']['imgId']) &&
			!empty($data['data']['imgId'])
		){
			return $data['data']['imgId'];
		}
		return false;
	}
	
	//获取担保支付请求签名
    static public  function getRequestSign($params){
        ksort($params);
		$sign_str = "";
        foreach ($params as $k => $v) {
           $sign_str .= $k."=".$v . "&";
        }
		$appsecret = config("app.kuaishou_app_secret");
        return md5(mb_substr($sign_str,0,-1) . $appsecret);
    }
	
	//验证担保支付回调签名
    static public function verifyNotifySign(){
		if(!isset($_SERVER['HTTP_KWAISIGN'])){
			return false;
		}
		$input = file_get_contents("php://input");
		$appsecret = config("app.kuaishou_app_secret");
		$sign = md5($input . $appsecret);
		if($_SERVER['HTTP_KWAISIGN'] === $sign){
			return true;
		}
		return false;
    }
	
	public function _empty(){
		
	}

	function __destruct(){

	}
}
