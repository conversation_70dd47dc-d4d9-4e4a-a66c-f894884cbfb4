<script setup>
import { watch, ref, reactive } from "vue";
import { useradd_addr, userdel_addr } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import china from "@/utils/china.js";
import { back } from "@/utils";

const edit = ref(false);
const id = ref("");
const username = ref("");
const mobile = ref("");
const addr = ref("");
const is_default = ref(false);
const columns = ref([[], [], []]);
const name = ref("");
const sheng_id = ref("");
const shi_id = ref("");
const qu_id = ref("");
const show = ref(false);
const defaultIndex = ref([0, 0, 0]);

onLoad((e) => {
  if (e.info) {
    const info = JSON.parse(e.info);
    id.value = info.id;
    username.value = info.username;
    mobile.value = info.mobile;
    addr.value = info.addr;
    is_default.value = info.is_default == 1 ? true : false;
    edit.value = info.edit;
    name.value = `${info.sheng}-${info.shi}-${info.qu}`;
    sheng_id.value = info.sheng_id;
    shi_id.value = info.shi_id;
    qu_id.value = info.qu_id;
  }
  china.map((val) => {
    columns.value[0].push({ name: val.name, id: val.id });
  });
  columns.value[1] = china[0].children;
  columns.value[2] = china[0].children[0].children;
});

const confirm = (e) => {
  sheng_id.value = e.value[0].id;
  shi_id.value = e.value[1].id;
  qu_id.value = e.value[2].id;
  defaultIndex.value = e.indexs;
  name.value = `${e.value[0].name}-${e.value[1].name}-${e.value[2].name}`;
  show.value = false;
};
const change = (e) => {
  switch (e.columnIndex) {
    case 0:
      columns.value[1] = china[e.indexs[0]].children;
      columns.value[2] = china[e.indexs[0]].children[e.indexs[1]].children;
      break;
    case 1:
      columns.value[2] = china[e.indexs[0]].children[e.indexs[1]].children;
      break;
    case 2:
      break;
  }
};
const isDefaultChange = (e) => {
  is_default.value = e;
};
const del = () => {
  uni.showModal({
    title: "删除提示",
    content: "你将删除这个收货地址",
    success: async (res) => {
      const e = await userdel_addr({ ids: id.value });
      if (e.status === "ok") {
        uni.navigateBack();
      }
    },
  });
};
const submit = async () => {
  let data = {
    username: username.value,
    mobile: mobile.value,
    sheng_id: sheng_id.value,
    shi_id: shi_id.value,
    qu_id: qu_id.value,
    addr: addr.value,
    is_default: is_default.value ? 1 : 0,
  };
  if (!data.username) {
    uni.showToast({ title: "请输入收件人姓名", icon: "none" });
    return;
  }
  if (!data.mobile) {
    uni.showToast({ title: "请输入收件人电话号码", icon: "none" });
    return;
  }
  if (!data.sheng_id) {
    uni.showToast({ title: "请选择收件地址", icon: "none" });
    return;
  }
  if (!data.addr) {
    uni.showToast({ title: "请输入收件人详细地址", icon: "none" });
    return;
  }
  uni.showLoading({
    title: "正在提交",
  });
  const res = await useradd_addr(data);
  if (res.status === "ok") {
    if (edit.value) {
      const e = await userdel_addr({ ids: id.value });
      if (e.status === "ok") {
        uni.hideLoading();
        back({ tip: "提交成功，3秒后返回上一级页面" });
      }
    } else {
      uni.hideLoading();
      uni.navigateBack();
    }
  }
};
</script>
<template>
  <view class="page b6f">
    <view class="df aic pt40 pb40 pl20 pr20 borderBottom">
      <view class="w200">收件人</view>
      <u-input placeholder="请输入收件人姓名" type="text" v-model="username" />
    </view>
    <view class="df aic pt40 pb40 pl20 pr20 borderBottom">
      <view class="w200">电话号码</view>
      <u-input placeholder="请输入收件人电话号码" type="text" v-model="mobile" />
    </view>
    <view class="df aic pt40 pb40 pl20 pr20 borderBottom">
      <view class="w200">所在地区</view>
      <view class="ml10 f1 xl2" @click="show = true">
        {{ name ? name : "请选择地址" }}
      </view>
    </view>
    <view class="df aic pt40 pb40 pl20 pr20 borderBottom">
      <view class="w200">详细地址</view>
      <u-textarea
        v-model="addr"
        :autoHeight="true"
        placeholder="输入详细地址"
      ></u-textarea>
    </view>
    <view class="df aic pt40 pb40 pl20 pr20 borderBottom">
      <view class="w200 f1">设置默认地址</view>
      <u-switch v-model="is_default" @change="isDefaultChange" activeColor="#EF662D" />
    </view>
    <view class="df aic jcc mt50 xl4 call" v-if="edit" @tap="del"> 删除收货地址 </view>
    <view class="pfx bottom0 b6f">
      <u-button
        color="#FAD000"
        text="提交"
        :customStyle="{
          width: '750rpx',
          height: '98rpx',
          color: '#000',
          fontSize: '34rpx',
        }"
        @click="submit"
      ></u-button>
      <u-safe-bottom></u-safe-bottom>
    </view>
    <u-picker
      :defaultIndex="defaultIndex"
      :show="show"
      :columns="columns"
      keyName="name"
      @confirm="confirm"
      @change="change"
      :closeOnClickOverlay="true"
      @close="show = false"
      @cancel="show = false"
    ></u-picker>
  </view>
</template>

<style scoped lang="less"></style>
