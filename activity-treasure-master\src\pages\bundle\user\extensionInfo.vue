<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import { userget_xiaji_user } from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto } from "@/utils";
import { getItem } from "@/utils";

const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");
const current = ref(0);
const form = ref({
  to_uid: null,
});

onLoad((e) => {
  form.value.to_uid = e.id;
});
onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  userget_xiaji_user({ page: mescroll.num, page_size: mescroll.size, ...form.value })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};
</script>
<template>
  <view class="page">
    <mescroll-uni
      class="list"
      :height="height"
      :up="{
        page: {
          num: 0,
          size: 20,
          time: null,
        },
      }"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @topclick="$event.scrollTo(0)"
    >
      <view class="p30 df aic borderBottom" v-for="(val, i) in goods" :key="i">
        <u-avatar size="110rpx" mode="aspectFill" :src="val.avatar"></u-avatar>
        <view class="pa" style="top: 115rpx; left: 47rpx" v-if="val.is_huiyuan">
          <u-image
            width="76rpx"
            height="34rpx"
            :src="`${store().$state.url}vip.png`"
          ></u-image>
        </view>
        <u-text
          margin="10rpx"
          size="30rpx"
          bold
          color="#414141"
          :text="val.nickname"
          word-wrap="anywhere"
        >
        </u-text>
        <u-text
          margin="10rpx"
          size="28rpx"
          color="#414141"
          :text="`ID：${val.uid}`"
          word-wrap="anywhere"
        >
        </u-text>
        <view
          class="m10 df aic jcc x24 c6f r20"
          style="padding: 5rpx 10rpx"
          :style="{ background: `${val.sex == 2 ? '#ffa0e8' : '#9eb6fa'}` }"
        >
          <u-icon
            color="#fff"
            :name="val.sex == 0 ? '' : val.sex == 1 ? 'man' : 'woman'"
            size="12"
            :label="val.sex == 0 ? '未知' : val.sex == 1 ? '男' : '女'"
            labelSize="18rpx"
            labelColor="#fff"
            top="2rpx"
          ></u-icon>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>

<style scoped lang="less"></style>
