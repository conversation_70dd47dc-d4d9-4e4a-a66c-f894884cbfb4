<template>
  <view class="page branch-management">
    <myTitle
      bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
      height="200rpx"
      title="分会活动管理"
      color="#ffffff"
      :blod="true"
    ></myTitle>
    
    <view class="content-container">
      <!-- 统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <view class="stats-number">{{ totalActivities }}</view>
          <view class="stats-label">总活动数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ ongoingActivities }}</view>
          <view class="stats-label">进行中</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ completedActivities }}</view>
          <view class="stats-label">已完成</view>
        </view>
      </view>
      
      <!-- 筛选器 -->
      <view class="filter-container">
        <u-button
          :type="filterStatus === 'all' ? 'primary' : 'info'"
          size="mini"
          @click="setFilter('all')"
          customStyle="border-radius: 30rpx; margin-right: 15rpx; width: 140rpx; height: 60rpx; font-size: 24rpx;"
          :customTextStyle="filterStatus === 'all' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          全部活动
        </u-button>
        <u-button
          :type="filterStatus === 'ongoing' ? 'primary' : 'info'"
          size="mini"
          @click="setFilter('ongoing')"
          customStyle="border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;"
          :customTextStyle="filterStatus === 'ongoing' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          进行中
        </u-button>
        <u-button
          :type="filterStatus === 'completed' ? 'primary' : 'info'"
          size="mini"
          @click="setFilter('completed')"
          customStyle="border-radius: 30rpx; margin-right: 15rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;"
          :customTextStyle="filterStatus === 'completed' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          已完成
        </u-button>
        <u-button
          :type="filterStatus === 'cancelled' ? 'primary' : 'info'"
          size="mini"
          @click="setFilter('cancelled')"
          customStyle="border-radius: 30rpx; width: 120rpx; height: 60rpx; font-size: 24rpx;"
          :customTextStyle="filterStatus === 'cancelled' ? 'color: #ffffff' : 'color: #6AC086'"
        >
          已取消
        </u-button>
      </view>
      
      <!-- 活动列表 -->
      <view class="activity-list" v-if="activityList.length > 0">
        <view 
          class="activity-item"
          v-for="activity in activityList"
          :key="activity.id"
          @click="viewActivityDetail(activity)"
        >
          <view class="activity-header">
            <image 
              class="activity-image"
              :src="activity.img_url || '/static/default-activity.png'"
              mode="aspectFill"
            />
            <view class="activity-status">
              <u-tag
                :text="getStatusText(activity)"
                :type="getStatusType(activity)"
                size="mini"
              ></u-tag>
            </view>
          </view>
          
          <view class="activity-content">
            <view class="activity-name">{{ activity.name }}</view>
            <view class="activity-title">{{ activity.title }}</view>
            
            <view class="activity-meta">
              <view class="meta-item">
                <u-icon name="clock" color="#999" size="24"></u-icon>
                <text>{{ formatTime(activity.start_time) }}</text>
              </view>
              <view class="meta-item">
                <u-icon name="account" color="#999" size="24"></u-icon>
                <text>{{ activity.signup_count || 0 }}人参与</text>
              </view>
            </view>
            
            <view class="activity-organizer">
              <text>组织者：{{ activity.organizer_name }}</text>
            </view>
          </view>
          
          <view class="activity-actions">
            <u-icon name="arrow-right" color="#999" size="24"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <u-empty
          mode="list"
          text="暂无活动数据"
          textColor="#999999"
          textSize="28"
        ></u-empty>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && activityList.length > 0">
        <u-loadmore 
          :status="loadStatus"
          @loadmore="loadMore"
        ></u-loadmore>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { store } from '@/store';
import { branch_presidentget_activities } from '@/api';

// 响应式数据
const loading = ref(false);
const activityList = ref([]);
const filterStatus = ref('all');
const currentPage = ref(1);
const hasMore = ref(true);
const loadStatus = ref('loadmore');
const pageSize = 20;

// 统计数据
const stats = reactive({
  total: 0,
  ongoing: 0,
  completed: 0,
  cancelled: 0
});

// 计算属性
const totalActivities = computed(() => stats.total);
const ongoingActivities = computed(() => stats.ongoing);
const completedActivities = computed(() => stats.completed);

// 页面加载
onLoad(() => {
  loadActivities(true);
});

// 下拉刷新
onPullDownRefresh(() => {
  loadActivities(true);
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});

// 上拉加载更多
onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    loadMore();
  }
});

// 加载活动列表
const loadActivities = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1;
      hasMore.value = true;
      loading.value = true;
      loadStatus.value = 'loading';
    } else if (!hasMore.value) {
      return;
    }
    
    const userInfo = store().$state.userInfo;
    const res = await branch_presidentget_activities({
      uid: userInfo.uid,
      token: userInfo.token,
      page: currentPage.value,
      page_size: pageSize,
      status: filterStatus.value
    });
    
    if (res.status === 'ok') {
      const newActivities = res.data || [];
      
      if (isRefresh) {
        activityList.value = newActivities;
      } else {
        activityList.value.push(...newActivities);
      }
      
      // 更新统计数据
      if (res.stats) {
        stats.total = res.stats.total || 0;
        stats.ongoing = res.stats.ongoing || 0;
        stats.completed = res.stats.completed || 0;
        stats.cancelled = res.stats.cancelled || 0;
      }
      
      // 判断是否还有更多数据
      hasMore.value = newActivities.length === pageSize;
      currentPage.value++;
      
      loadStatus.value = hasMore.value ? 'loadmore' : 'nomore';
      
    } else if (res.status === 'relogin') {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
    } else {
      uni.showToast({
        title: res.msg || '加载失败',
        icon: 'none'
      });
      loadStatus.value = 'loadmore';
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
    loadStatus.value = 'loadmore';
  } finally {
    loading.value = false;
  }
};

// 设置筛选器
const setFilter = (status) => {
  filterStatus.value = status;
  loadActivities(true);
};

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadStatus.value = 'loading';
    loadActivities(false);
  }
};

// 查看活动详情
const viewActivityDetail = (activity) => {
  uni.navigateTo({
    url: `/pages/bundle/index/activeInfo?id=${activity.id}`
  });
};

// 获取状态文本
const getStatusText = (activity) => {
  // 使用后端返回的display_status或根据activity数据判断
  const status = activity.display_status || activity.status;
  const statusMap = {
    1: '进行中',
    2: '已完成',
    3: '已取消'
  };
  return statusMap[status] || '未知';
};

// 获取状态类型
const getStatusType = (activity) => {
  const status = activity.display_status || activity.status;
  const typeMap = {
    1: 'success',
    2: 'info',
    3: 'error'
  };
  return typeMap[status] || 'info';
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp || timestamp === 0) return '时间待定';

  // 确保timestamp是数字类型
  const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
  if (isNaN(ts)) return '时间格式错误';

  const date = new Date(ts * 1000);
  if (isNaN(date.getTime())) return '时间格式错误';

  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
@import '@/style/wcag-colors.scss';
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

.content-container {
  padding: 30rpx;
}

.stats-card {
  display: flex;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(106, 192, 134, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
  
  .stats-number {
    font-size: 48rpx;
    font-weight: bold;
    color: #6AC086;
    margin-bottom: 10rpx;
  }
  
  .stats-label {
    font-size: 24rpx;
    color: #666666; /* 提高对比度，从#999改为#666666 */
  }
}

.filter-container {
  display: flex;
  margin-bottom: 30rpx;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  overflow-x: auto;
  padding: 10rpx 0;
}

.activity-list {
  .activity-item {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    
    .activity-header {
      position: relative;
      height: 300rpx;
      
      .activity-image {
        width: 100%;
        height: 100%;
      }
      
      .activity-status {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
      }
    }
    
    .activity-content {
      padding: 30rpx;
      
      .activity-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #1a1a1a; /* 提高对比度 */
        margin-bottom: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .activity-title {
        font-size: 28rpx;
        color: #4a4a4a; /* 提高对比度，从#666改为#4a4a4a */
        margin-bottom: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .activity-meta {
        display: flex;
        margin-bottom: 20rpx;
        
        .meta-item {
          display: flex;
          align-items: center;
          margin-right: 40rpx;
          font-size: 24rpx;
          color: #666666; /* 提高对比度，从#999改为#666666 */

          text {
            margin-left: 10rpx;
          }
        }
      }

      .activity-organizer {
        font-size: 24rpx;
        color: #666666; /* 提高对比度，从#999改为#666666 */
      }
    }
    
    .activity-actions {
      position: absolute;
      top: 50%;
      right: 30rpx;
      transform: translateY(-50%);
    }
  }
}

.empty-state {
  padding: 100rpx 0;
}

.load-more {
  padding: 30rpx 0;
}
</style>
