<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad, onShareAppMessage, onShareTimeline, onBackPress } from '@dcloudio/uni-app';
import { getCardDetail, likeCard, favoriteCard, commentCard, getCardComments, deleteComment } from '@/api/index.js';
import { store } from '@/store';
import { saveImageToAlbum } from '@/utils/painterShare';
import SharePopup from '@/components/share-popup/share-popup.vue';
import { hideShareMenu, isShareMenuShow } from '@/utils/uniShareConfig';
import { requireLogin } from '@/utils/auth';

// 添加调试日志
console.log('日卡详情页面加载');

// --- 状态变量 ---
const cardDetail = ref(null); // 日卡详情数据
const isLoading = ref(true); // 加载状态
const showComments = ref(false); // 是否显示评论区
const commentText = ref(''); // 评论文本
const comments = ref([]); // 评论列表
const commentsLoading = ref(false); // 评论加载状态
const commentsPage = ref(1); // 评论页码
const hasMoreComments = ref(true); // 是否有更多评论
const commentsRefreshing = ref(false); // 评论下拉刷新状态
const replyTo = ref(null); // 回复对象
const sortType = ref('latest'); // 评论排序方式: 'latest'最新 或 'hot'最热

const showShareImagePreview = ref(false); // 是否显示分享图片预览
const shareImagePath = ref(''); // 分享图片路径

// 分享相关状态
const showSharePopup = ref(false);
const currentShareData = ref(null);

// --- 计算属性 ---
const backgroundStyle = computed(() => {
  if (!cardDetail.value?.background_image_url) return {};
  return {
    backgroundImage: `url(${cardDetail.value.background_image_url})`
  };
});

const commentPlaceholder = computed(() => {
  return replyTo.value ? `回复 ${replyTo.value.nickname}` : '写下你的评论...';
});

// --- 生命周期钩子 ---
onLoad(async (options) => {
  const cardId = options?.cardId;
  if (!cardId) {
    uni.showToast({ title: '无法加载日卡详情', icon: 'none' });
    uni.navigateBack();
    return;
  }

  try {
    isLoading.value = true;
    await loadCardDetail(cardId);

    // 显示评论区 - 移到加载完成后执行
    if (options?.showComments === 'true') {
      showComments.value = true;
      // 直接加载评论
      loadComments(true);
    }

    // 如果是从分享按钮跳转过来，自动打开分享弹窗
    if (options?.share === 'true') {
      // 延迟一下，确保页面已完全加载
      setTimeout(() => {
        handleShareFromUtils();
      }, 500);
    }
  } catch (error) {
    console.error('加载日卡详情失败:', error);
    uni.showToast({ title: '加载失败，请重试', icon: 'none' });
  } finally {
    isLoading.value = false;
  }
});

onMounted(() => {
  // 预加载评论数据
  if (cardDetail.value?.id) {
    loadComments(true);
  }
});

// 分享配置
onShareAppMessage(() => {
  try {
    if (!cardDetail.value) {
      console.warn('日卡信息未加载完成，使用默认分享信息');
      return {
        title: '分享一张精美日卡',
        path: '/pages/bundle/world/card/index',
        imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
      };
    }

    return {
      title: cardDetail.value.description ?
        (cardDetail.value.description.length > 30 ?
          cardDetail.value.description.substring(0, 30) + '...' :
          cardDetail.value.description) :
        '分享一张精美日卡',
      path: `/pages/bundle/world/card/detail?cardId=${cardDetail.value.id}`,
      imageUrl: cardDetail.value.background_image_url || store().$state.config?.img_config?.app_logo?.val || ''
    };
  } catch (error) {
    console.error('日卡分享配置失败:', error);
    return {
      title: '分享一张精美日卡',
      path: '/pages/bundle/world/card/index',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  }
});

onShareTimeline(() => {
  if (!cardDetail.value) return {};
  return {
    title: cardDetail.value.description.substring(0, 30) + '...',
    query: `cardId=${cardDetail.value.id}`,
    imageUrl: cardDetail.value.background_image_url
  };
});

// 处理返回按键，确保uni-share菜单能正确关闭
onBackPress(({ from }) => {
  console.log('返回按键触发，来源:', from);
  if (from === 'backbutton' && isShareMenuShow()) {
    hideShareMenu();
    return true; // 阻止默认返回行为
  }
  return false; // 允许默认返回行为
});

// --- 方法 ---
// 加载日卡详情
const loadCardDetail = async (cardId) => {
  try {
    const res = await getCardDetail({
      id: cardId,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      cardDetail.value = res.data;
    } else {
      throw new Error(res.msg || '获取日卡详情失败');
    }
  } catch (error) {
    console.error('Error loading card detail:', error);
    throw error;
  }
};

// 加载评论
const loadComments = async (reset = false) => {
  if (commentsLoading.value || (!hasMoreComments.value && !reset)) return;

  if (!cardDetail.value?.id) return;

  try {
    commentsLoading.value = true;

    if (reset) {
      commentsPage.value = 1;
      comments.value = [];
    }

    const res = await getCardComments({
      card_id: cardDetail.value.id,
      page: commentsPage.value,
      page_size: 10,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      sort_type: sortType.value
    });

    if (res.status === 'ok') {
      if (reset) {
        comments.value = res.data.list || [];
      } else {
        comments.value = [...comments.value, ...(res.data.list || [])];
      }

      hasMoreComments.value = (res.data.list || []).length === 10;
      commentsPage.value++;
    } else if (res.status === 'empty') {
      hasMoreComments.value = false;
    }
  } catch (error) {
    console.error('加载评论失败:', error);
    uni.showToast({ title: '加载评论失败', icon: 'none' });
  } finally {
    commentsLoading.value = false;
    commentsRefreshing.value = false; // 🔧 修复：确保下拉刷新状态重置
  }
};

// 🆕 新增：评论区下拉刷新
const onCommentsRefresh = () => {
  commentsRefreshing.value = true;
  loadComments(true);
};

// 处理点赞
const handleLike = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  if (!cardDetail.value) return;

  // 乐观更新UI
  const originalLikedState = cardDetail.value.isLiked;
  const originalLikeCount = cardDetail.value.likeCount;

  cardDetail.value.isLiked = !cardDetail.value.isLiked;
  cardDetail.value.likeCount += cardDetail.value.isLiked ? 1 : -1;

  try {
    console.log('发送点赞请求:', {
      id: cardDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    const res = await likeCard({
      id: cardDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    console.log('点赞响应:', res);

    if (res.status !== 'ok') {
      // 恢复原状态
      cardDetail.value.isLiked = originalLikedState;
      cardDetail.value.likeCount = originalLikeCount;
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    } else {
      uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
    }
  } catch (error) {
    // 恢复原状态
    cardDetail.value.isLiked = originalLikedState;
    cardDetail.value.likeCount = originalLikeCount;
    console.error('点赞失败:', error);
    uni.showToast({ title: '操作失败，请重试', icon: 'none' });
  }
};

// 处理收藏
const handleFavorite = async () => {
  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  if (!cardDetail.value) return;

  // 乐观更新UI
  const originalFavoritedState = cardDetail.value.isFavorited || false;
  cardDetail.value.isFavorited = !cardDetail.value.isFavorited;

  try {
    const res = await favoriteCard({
      id: cardDetail.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status !== 'ok') {
      // 恢复原状态
      cardDetail.value.isFavorited = originalFavoritedState;
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    } else {
      uni.showToast({ title: res.msg || '操作成功', icon: 'success' });
    }
  } catch (error) {
    // 恢复原状态
    cardDetail.value.isFavorited = originalFavoritedState;
    console.error('收藏失败:', error);
    uni.showToast({ title: '操作失败，请重试', icon: 'none' });
  }
};

// 切换评论区显示
const toggleComments = () => {
  showComments.value = !showComments.value;
  if (showComments.value) {
    loadComments(true);
  }
};

// 设置回复对象
const setReplyTo = (comment) => {
  replyTo.value = comment;
  // 聚焦输入框
  if (comment) {
    setTimeout(() => {
      uni.pageScrollTo({
        scrollTop: 9999,
        duration: 300
      });
    }, 100);
  }
};

// 提交评论
const submitComment = async () => {
  if (!commentText.value.trim()) {
    uni.showToast({ title: '评论内容不能为空', icon: 'none' });
    return;
  }

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再评论')) {
    return;
  }

  try {
    // 确保parent_id是数字类型或null，修复外键约束错误
    // 当parent_id为0时，将其设置为null以避免外键约束错误
    const parentId = replyTo.value?.id ? parseInt(replyTo.value.id) : null;

    console.log('发送评论请求:', {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      card_id: cardDetail.value.id,
      content: commentText.value,
      parent_id: parentId
    });

    const res = await commentCard({
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      card_id: cardDetail.value.id,
      content: commentText.value,
      parent_id: parentId // 确保是数字类型或null
    });

    console.log('评论响应:', res);

    if (res.status === 'ok') {
      uni.showToast({ title: '评论成功', icon: 'success' });
      commentText.value = '';
      replyTo.value = null;

      // 重新加载评论
      loadComments(true);

      // 更新评论计数
      cardDetail.value.commentCount = (cardDetail.value.commentCount || 0) + 1;
    } else {
      uni.showToast({ title: res.msg || '评论失败', icon: 'none' });
    }
  } catch (error) {
    console.error('提交评论失败:', error);
    uni.showToast({ title: '评论失败，请重试', icon: 'none' });
  }
};

// 改变评论排序方式
const changeSortType = (type) => {
  if (sortType.value === type) return;

  sortType.value = type;
  console.log('切换日卡评论排序方式为:', type);
  loadComments(true);
};

// 关闭页面
const closePage = () => {
  uni.navigateBack();
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题，将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss"
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const date = new Date(formattedTimeStr);
  const now = new Date();
  
  // 获取时间部分 (HH:mm:ss)
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const timeDetail = `${hours}:${minutes}:${seconds}`;
  
  // 判断是否是今天
  const isToday = date.setHours(0, 0, 0, 0) === now.setHours(0, 0, 0, 0);
  if (isToday) {
    return `今天 ${timeDetail}`;
  }
  
  // 判断是否是昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = date.setHours(0, 0, 0, 0) === yesterday.setHours(0, 0, 0, 0);
  if (isYesterday) {
    return `昨天 ${timeDetail}`;
  }
  
  // 获取完整日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // 返回完整的日期和时间
  return `${year}-${month}-${day} ${timeDetail}`;
};

// 处理分享按钮点击 - 使用share-popup组件
const handleShareClick = async () => {
  if (!cardDetail.value) {
    uni.showToast({
      title: '内容加载中，请稍后',
      icon: 'none'
    });
    return;
  }

  console.log('=== 日卡详情页分享按钮点击 ===');
  console.log('📤 开始分享日卡，数据:', cardDetail.value);

  try {
    // 设置分享数据
    currentShareData.value = {
      template: 'card',
      image: cardDetail.value.background_image_url,
      content: cardDetail.value.description,
      author: cardDetail.value.author,
      date: cardDetail.value.created_at ? new Date(cardDetail.value.created_at).toLocaleDateString() : null,
      backgroundImage: cardDetail.value.background_image_url,
      authorAvatar: cardDetail.value.author_avatar
    };

    console.log('📤 分享数据设置完成:', currentShareData.value);

    // 显示分享弹窗
    showSharePopup.value = true;
    console.log('📤 分享弹窗已显示');

  } catch (error) {
    console.error('❌ 分享处理失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  }
};

// 保持原有的handleShare函数用于兼容性
const handleShare = handleShareClick;

// 处理分享成功
const handleShareSuccess = (result) => {
  console.log('分享成功:', result);
  uni.showToast({
    title: '分享成功',
    icon: 'success'
  });
};

// 处理分享错误
const handleShareError = (error) => {
  console.error('分享失败:', error);
  uni.showToast({
    title: '分享失败',
    icon: 'none'
  });
};





// 分享给微信好友
const shareToWechatFriend = () => {
  // 在小程序环境中使用正确的分享方式
  // #ifdef MP-WEIXIN
  try {
    // 显示分享菜单，让用户选择分享方式
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage']
    });

    uni.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });
  } catch (error) {
    console.error('显示分享菜单失败:', error);
    uni.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
  // #endif

  // 在非微信小程序环境中
  // #ifndef MP-WEIXIN
  // 先生成分享图片
  shareAsImage(true).then(imagePath => {
    // 尝试使用系统分享
    if (uni.canIUse('shareWithSystem')) {
      uni.shareWithSystem({
        summary: cardDetail.value?.description || '分享一张日卡',
        imageUrl: imagePath || cardDetail.value?.background_image_url || '',
        href: `https://example.com/card?id=${cardDetail.value?.id}`,
        success() {
          console.log('分享成功');
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail(err) {
          console.log('分享失败:', err);
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 复制链接到剪贴板
      uni.setClipboardData({
        data: `https://example.com/card?id=${cardDetail.value?.id}`,
        success: function() {
          uni.showToast({
            title: '链接已复制，请粘贴分享',
            icon: 'none'
          });
        }
      });
    }
  }).catch(error => {
    console.error('生成分享图片失败:', error);
    uni.showToast({
      title: '分享失败',
      icon: 'none'
    });
  });
  // #endif
};

// 分享到朋友圈
const shareToWechatMoments = () => {
  // 在微信小程序环境中使用正确的分享方式
  // #ifdef MP-WEIXIN
  try {
    // 显示分享菜单，包含朋友圈选项
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    uni.showToast({
      title: '请点击右上角分享到朋友圈',
      icon: 'none'
    });
  } catch (error) {
    console.error('显示分享菜单失败:', error);
    uni.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
  // #endif

  // 在非微信小程序环境中，生成图片分享
  // #ifndef MP-WEIXIN
  shareAsImage();
  // #endif
};

// 生成分享图片
const shareAsImage = async (skipPreview = false) => {
  uni.showLoading({
    title: '生成分享图片中...'
  });

  try {
    // 使用Painter生成分享图片
    const imagePath = await generateShareImage({
      template: 'card',
      backgroundImage: cardDetail.value?.background_image_url,
      content: cardDetail.value?.description,
      author: cardDetail.value?.author,
      authorAvatar: cardDetail.value?.author_avatar,
      date: cardDetail.value?.created_at ? new Date(cardDetail.value.created_at).toLocaleDateString() : null,
      watermark: '分享自MindfulMeetUp & 小聚会'
    });

    uni.hideLoading();
    console.log('Painter生成日卡分享图片成功:', imagePath);

    if (skipPreview) {
      return imagePath; // 直接返回图片路径，不显示预览
    }

    // 在微信小程序中使用官方图片分享API
    // #ifdef MP-WEIXIN
    try {
      await wx.showShareImageMenu({
        path: imagePath
      });

      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('图片分享失败:', error);
      // 如果官方API失败，显示图片预览
      shareImagePath.value = imagePath;
      showShareImagePreview.value = true;
    }
    // #endif

    // 在非微信小程序环境中显示图片预览
    // #ifndef MP-WEIXIN
    shareImagePath.value = imagePath;
    showShareImagePreview.value = true;
    // #endif

    return imagePath;
  } catch (error) {
    uni.hideLoading();
    console.error('生成分享图片失败:', error);
    uni.showToast({
      title: '生成分享图片失败',
      icon: 'none'
    });
    throw error;
  }
};

// 保存分享图片
const saveShareImage = async (imagePath) => {
  try {
    await saveImageToAlbum(imagePath);
    uni.showToast({
      title: '图片已保存到相册',
      icon: 'success'
    });
  } catch (error) {
    console.error('保存图片失败:', error);

    // 检查是否是权限问题
    if (error.errMsg && error.errMsg.includes('authorize')) {
      uni.showModal({
        title: '提示',
        content: '需要授权访问相册才能保存图片',
        confirmText: '去授权',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting();
          }
        }
      });
    } else {
      uni.showToast({
        title: '保存图片失败',
        icon: 'none'
      });
    }
  }
};

// 添加检查用户是否为评论创建者的辅助函数
// 检查当前用户是否为评论创建者
const isCommentCreator = (comment) => {
  if (!comment || !store().$state.userInfo?.uid) return false;
  return comment.user_id === store().$state.userInfo.uid;
};

// 添加处理删除评论的方法
// 处理删除评论
const handleDeleteComment = (comment) => {
  if (!isCommentCreator(comment)) {
    uni.showToast({ title: '您无权删除此评论', icon: 'none' });
    return;
  }

  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这条评论吗？',
    confirmColor: '#6AC086',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' });
          
          const params = {
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token,
            comment_id: comment.id,
            comment_type: 'card'  // 这里明确指定为card类型评论
          };
          
          console.log('删除评论请求参数:', JSON.stringify(params));
          
          const result = await deleteComment(params);
          
          console.log('删除评论返回结果:', JSON.stringify(result));
          
          uni.hideLoading();
          
          if (result.status === 'ok') {
            uni.showToast({ title: '删除成功', icon: 'success' });
            // 从评论列表中移除该评论
            const index = comments.value.findIndex(item => item.id === comment.id);
            if (index !== -1) {
              comments.value.splice(index, 1);
            }
            // 更新评论计数
            if (cardDetail.value) {
              cardDetail.value.comment_count = Math.max(0, (cardDetail.value.comment_count || 0) - 1);
            }
          } else {
            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('删除评论出错:', error);
          uni.showToast({ title: '删除失败，请稍后重试', icon: 'none' });
        }
      }
    }
  });
};
</script>

<template>
  <view class="card-detail-container" :style="backgroundStyle" @click="closePage">
    <!-- 背景遮罩 -->
    <view class="card-background-overlay"></view>

    <!-- Canvas 2D API分享图片生成 -->
    <view class="hidden-canvas-container">
      <canvas
        type="2d"
        id="share-canvas"
        style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1334px;"
      ></canvas>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-state">
      <u-loading-icon mode="circle" size="30" color="#ffffff"></u-loading-icon>
    </view>

    <!-- 背景模糊图片 -->
    <view v-else-if="cardDetail" class="background-blur">
      <image
        :src="cardDetail.background_image_url || '/static/default-card-bg.jpg'"
        class="blur-image"
        mode="aspectFill"
      ></image>
      <view class="blur-overlay"></view>
    </view>

    <!-- 内容区域 -->
    <view v-if="cardDetail" class="card-content-wrapper" @click.stop>
      <!-- 日卡卡片 -->
      <view class="card-container">
        <!-- 上半部分：图片 -->
        <view class="card-image-section">
          <image
            :src="cardDetail.background_image_url || '/static/default-card-bg.jpg'"
            class="card-image"
            mode="aspectFill"
          ></image>
        </view>

        <!-- 下半部分：文字和作者 -->
        <view class="card-text-section">
          <view class="text-content">
            <text class="card-description">{{ cardDetail.description }}</text>
          </view>
          <view v-if="cardDetail.author" class="author-section">
            <text class="card-author">{{ cardDetail.author }}</text>
            <text v-if="cardDetail.source" class="card-source">《{{ cardDetail.source }}》</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="card-action-bar">
      <view class="action-icon" @click.stop="toggleComments">
        <u-icon name="chat" size="26" color="rgba(255, 255, 255, 0.9)"></u-icon>
        <text class="action-count">{{ cardDetail?.commentCount || '524' }}</text>
      </view>
      <view class="action-icon" @click.stop="handleLike">
        <u-icon :name="cardDetail?.isLiked ? 'heart-fill' : 'heart'" size="26" :color="cardDetail?.isLiked ? '#ff6b81' : 'rgba(255, 255, 255, 0.9)'"></u-icon>
        <text class="action-count" :class="{ 'liked': cardDetail?.isLiked }">{{ cardDetail?.likeCount || '1.5w' }}</text>
      </view>
      <view class="action-icon" @click.stop="handleFavorite">
        <u-icon :name="cardDetail?.isFavorited ? 'star-fill' : 'star'" size="26" :color="cardDetail?.isFavorited ? '#FFD700' : 'rgba(255, 255, 255, 0.9)'"></u-icon>
      </view>
      <!-- 分享按钮已注释，使用微信右上角分享 -->
      <!-- <view class="action-icon" @click.stop="handleShare">
        <u-icon name="share" size="26" color="rgba(255, 255, 255, 0.9)"></u-icon>
      </view> -->
    </view>



      <!-- 分享图片预览 -->
      <view v-if="showShareImagePreview" class="share-image-preview">
        <view class="share-image-container">
          <image :src="shareImagePath" mode="widthFix" class="share-preview-image"></image>
          <view class="share-preview-actions">
            <button class="share-action-btn" @click="shareToWechatFriend">发送给朋友</button>
            <button class="share-action-btn" @click="shareToWechatMoments">分享到朋友圈</button>
            <button class="share-action-btn" @click="saveShareImage(shareImagePath)">保存到相册</button>
            <button class="share-action-btn cancel" @click="showShareImagePreview = false">取消</button>
          </view>
        </view>
      </view>

      <!-- 评论区 -->
      <view v-if="showComments" class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 ({{ cardDetail?.commentCount || 0 }})</text>
          <view class="comment-sort-options">
            <text
              class="sort-option"
              :class="{ 'active': sortType === 'latest' }"
              @click="changeSortType('latest')"
            >最新</text>
            <text
              class="sort-option"
              :class="{ 'active': sortType === 'hot' }"
              @click="changeSortType('hot')"
            >最热</text>
          </view>
          <u-icon name="close" size="20" color="#999" @click="showComments = false"></u-icon>
        </view>

        <!-- 评论列表 -->
        <scroll-view
          class="comments-list"
          scroll-y
          @scrolltolower="loadComments"
          refresher-enabled
          :refresher-triggered="commentsRefreshing"
          @refresherrefresh="onCommentsRefresh"
        >
          <view v-if="comments.length === 0 && !commentsLoading" class="empty-comments">
            <text>暂无评论，快来发表第一条评论吧</text>
          </view>

          <view v-for="(comment, index) in comments" :key="comment.id" class="comment-item">
            <image class="comment-avatar" :src="comment.user?.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-nickname">{{ comment.user?.nickname || '用户' }}</text>
                <text class="comment-time">{{ formatTime(comment.created_at) }}</text>
              </view>
              <view class="comment-text">
                <text v-if="comment.parent_id" class="reply-target">回复 @{{ comment.reply_to?.nickname || '用户' }}：</text>
                <text>{{ comment.content }}</text>
              </view>
              <view class="comment-actions">
                <text class="reply-btn" @click="setReplyTo({id: comment.id, nickname: comment.user?.nickname || '用户'})">回复</text>
                <text v-if="isCommentCreator(comment)" class="delete-btn" @click="handleDeleteComment(comment)">删除</text>
              </view>
            </view>
          </view>

          <view v-if="commentsLoading" class="loading-more">
            <u-loading-icon mode="circle" size="24" color="#999"></u-loading-icon>
          </view>
        </scroll-view>

        <!-- 评论输入框 -->
        <view class="comment-input-area">
          <view v-if="replyTo" class="reply-info">
            <text>回复：{{ replyTo.nickname }}</text>
            <u-icon name="close" size="12" color="#999" @click="setReplyTo(null)"></u-icon>
          </view>
          <view class="input-wrapper">
            <input
              class="comment-input"
              v-model="commentText"
              :placeholder="commentPlaceholder"
              confirm-type="send"
              @confirm="submitComment"
            />
            <button class="send-btn" @click="submitComment">发送</button>
          </view>
        </view>
      </view>

    <!-- 关闭按钮 -->
    <view class="close-button" @click.stop="closePage">
      <u-icon name="close" color="rgba(255, 255, 255, 0.9)" size="20"></u-icon>
    </view>

    <!-- 分享弹窗 -->
    <share-popup
      :show="showSharePopup"
      title="分享日卡"
      :share-data="currentShareData"
      :show-member-invite="store().$state.userInfo?.role_type === 0 || store().$state.userInfo?.role_type === 1"
      @close="showSharePopup = false"
      @share-success="handleShareSuccess"
      @share-error="handleShareError"
    />

  </view>
</template>

<style lang="scss" scoped>
@import '@/style/judu-theme.scss';

.card-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: relative;
  background: #000000;
  overflow: hidden;
  animation: fadeIn 0.4s ease-out forwards;
}

.hidden-canvas-container {
  position: fixed;
  left: -9999px;
  top: -9999px;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
}

.card-background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 遮罩 */
  backdrop-filter: blur(2px);
}

/* 背景模糊效果 */
.background-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.blur-image {
  width: 100%;
  height: 100%;
  filter: blur(20rpx);
  transform: scale(1.1);
}

.blur-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}

.loading-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.card-content-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 5;
  padding: 60rpx 40rpx 160rpx 40rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

/* 新的卡片容器样式 */
.card-container {
  width: 85%;
  max-width: 640rpx;
  background: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  aspect-ratio: 3/4;
}

.card-image-section {
  width: 100%;
  flex: 1.2;
  position: relative;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-text-section {
  flex: 1;
  padding: 48rpx 40rpx 40rpx 40rpx;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.card-description {
  font-size: 32rpx;
  line-height: 1.8;
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  letter-spacing: 2rpx;
  text-align: left;
  font-weight: 400;
}

.author-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
  margin-top: 32rpx;
}

.card-author {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.card-source {
  font-size: 24rpx;
  color: #999999;
  opacity: 0.8;
}

.card-action-bar {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  width: auto;
  min-width: 400rpx;
  z-index: 10;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.action-icon {
  margin: 0 30rpx;
  position: relative;
  padding: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.9);
  }
}

.share-button {
  background: none;
  border: none;
}

.action-count {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  min-width: 30rpx;
  text-align: center;
  font-weight: 500;
}

.action-count.liked {
  color: #ff6b81;
}

.close-button {
  position: absolute;
  top: 60rpx;
  left: 40rpx;
  z-index: 10;
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.comments-section {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding: 30rpx;
  z-index: 20;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.comments-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.comment-sort-options {
  display: flex;
  gap: 20rpx;
}

.sort-option {
  font-size: 28rpx;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.sort-option.active {
  color: #007AFF;
  font-weight: bold;
}

.comments-list {
  flex: 1;
  max-height: 50vh;
}

.empty-comments {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.comment-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 1px solid #f0f0f0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.comment-nickname {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.reply-target {
  color: #6AC086; /* 绿色 */
  margin-right: 10rpx;
  font-weight: 500;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
}

.reply-btn {
  font-size: 24rpx;
  color: #6AC086; /* 绿色 */
  padding: 4rpx 10rpx;
}

.delete-btn {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 10rpx;
}

.loading-more {
  padding: 20rpx 0;
  text-align: center;
}

.comment-input-area {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.reply-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.input-wrapper {
  display: flex;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-btn {
  margin-left: 20rpx;
  background-color: #6AC086; /* 绿色 */
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.share-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

.share-image-container {
  width: 80%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-preview-image {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.share-preview-actions {
  width: 100%;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-action-btn {
  background-color: #6AC086;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.share-action-btn.cancel {
  background-color: #f5f5f5;
  color: #333;
}
</style>
