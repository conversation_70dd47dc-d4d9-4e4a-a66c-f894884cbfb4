//
var bak_document_title = document.title;
var notify = false;
var websock = false;
var socket = null;
var param_login = {"url":"login@rchat","nickname":rchat_nickname,"channel":rchat_channel};
var rchat_position = "-";
//消息通知
//
var audio_url = "/asset/audio/notice.wav";
var notice_audio = new Audio;
notice_audio.src = audio_url;
//
if (window.Notification) {
	if(Notification.permission === "granted"){
		notify = true;
	}else if(Notification.permission !== 'denied'){
		Notification.requestPermission(function (permission) {
			if(permission == "granted"){
				notify = true;
			}
		});
	}
    var popNotice = function(title,body) {
        var notification = new Notification(title, {
            body: body,
			tag: "rchat",
            icon: "/asset/images/rchat.jpg",
			renotify: true,
			silent: true,
			//requireInteraction:true,
			sound:audio_url,
        });
        
        notification.onclick = function() {
            notification.close();
			if(!$("#rchat_div").is(":visible")){
				$("#rchat_div").slideDown(function(){
					send_rchat_status();
				});
				$("#rchat_message_tips").hide();
				setCookie("rchat_div_is_show",1,24*3600);
			}
			document.title = bak_document_title;
            window.focus();
        }; 
    };
}
if(window.navigator.geolocation){
	navigator.geolocation.getCurrentPosition(function(position){
		if(position && position.coords.latitude && position.coords.longitude){
			var wgs84_lat = position.coords.latitude;
			var wgs84_lng = position.coords.longitude;
			if(wgs84_lng && wgs84_lat){
				//WGS84坐标(GPS坐标)
				//document.getElementById("wgs84").innerHTML = wgs84_lng + "," + wgs84_lat;
				//gcj20坐标
				var gcj = WGS84ToGCJ(wgs84_lng,wgs84_lat);
				//document.getElementById("gaode").innerHTML = gcj.lng + "," + gcj.lat;
				//bd09坐标
				var bd = gcj2bd(gcj.lng,gcj.lat);
				//document.getElementById("baidu").innerHTML = bd.lng + "," + bd.lat;
				//
				rchat_position = bd.lng.toFixed(4) + "," + bd.lat.toFixed(4);
				send_rchat_status();
			}
		}else{
			rchat_position = "...";
		}
	}, function(error){
		if(error && error.message){
			rchat_position = "-";
		}
	});
}
//  
window.onfocus = function () {
	if($("#rchat_div").is(":visible")){
		document.title = bak_document_title;
	}
};
//
function link(){
    if(websock)return true;
    socket = new WebSocket(rchat_websocket_url);
    socket.onopen=function(){
        websock = true;
        tips("连接成功...");
		if(window.Notification && Notification.permission !== "granted"){
			tips("请打开网站通知...");
		}
        send(param_login);
		send_rchat_status();
		$("#rchat_channel").css("color","green");
		$("#rchat_users_num").css("color","green");
		$("#rchat_users_num").text("-");
    }
    socket.onmessage=function(msg){
        receive(msg.data);
    }
    socket.onclose=function(){
        websock = false;
		$("#rchat_channel").css("color","gray");
		$("#rchat_users_num").css("color","gray");
        $("#rchat_users_num").text("-"); 
		tips("连接已断开...");
    }
}
function send_message(){
    var msg = $.trim($("#message_text").val());
    if(msg == ""){
        tips("消息不能为空");
        return false;
    }
    var data = {"url":"channel_send@rchat","msg":msg};
    if(send(data))$("#message_text").val("");
}
function send_rchat_status(){
	if(is_open_rchat && websock){
		var data = {"url":"set_rchat_status@rchat"};
		data.notify = window.Notification && Notification.permission === "granted" ? 1 : 0;
		data.rchat_is_show = $("#rchat_div").is(":visible") ? 1 : 0;
		data.user_agent = get_ua();
		data.position = rchat_position;
		send(data);	
	}
}
function get_users(){
	var data = {"url":"get_users@rchat"};
	send(data);
}

function receive(msg){
    var data = JSON.parse(eval(msg));
	//console.log(data);
    if(data.type == "undefined"){
        console.log("未知类型[ " + data.type +" ]");return false;
    }
	//
	if('num' in data){
		$("#rchat_users_num").text(data.num.channel);
	}
	//
    switch(data.type){
		case "relogin":
			tips("连接已断开，3秒钟后尝试连接...")
			setTimeout(function(){
				send(param_login);
			},3 * 1000);			
			break;
        case "online_notify":
            break;
        case "offline_notify":
            break;
        case "channel_message":
            data.msg = data.msg.replace(/\r\n/g,'<br/>');
            data.msg = data.msg.replace(/\n\r/g,'<br/>');
            data.msg = data.msg.replace(/\n/g,'<br/>');
			if(data.from != rchat_nickname){
				var str = '<li><div class="layim-chat-user"><cite style="font-size:large;">'+data.from+'<i style="font-size:small;">'+data.datetime+'</i></cite></div><div class="layim-chat-text">'+data.msg+'</div></li>';
				$("#rchat_contents_main").append(str);
				if(!$("#rchat_div").is(":visible")){
					$("#rchat_message_tips").show();
				}
				if(window.Notification && Notification.permission === "granted"){
					popNotice("您有新消息","来自 - " + data.from);
					notice_audio.play();
					if($(':focus').length==0) {
						document.title = "您有新消息";
					}
					
				}
			}else{
				var str = '<li class="layim-chat-mine"><div class="layim-chat-user"><cite style="font-size:large;"><i style="font-size:small;">'+data.datetime+'</i>'+data.from+'</cite></div><div class="layim-chat-text">'+data.msg+'</div></li>';
				$("#rchat_contents_main").append(str);
			}
            var ele = document.getElementById('rchat_contents_main_div');
            ele.scrollTop = ele.scrollHeight;
			//
            break;
        case "users_list":
            data.msg = data.msg.replace(/\r\n/g,'<br/>');
            data.msg = data.msg.replace(/\n\r/g,'<br/>');
            data.msg = data.msg.replace(/\n/g,'<br/>');
			var str = '<li class="layim-chat-system"><span>'+data.from+'<br />'+data.msg+'</span></li>';
            $("#rchat_contents_main").append(str);
            var ele = document.getElementById('rchat_contents_main_div');
            ele.scrollTop = ele.scrollHeight;
            //
            break;
        case "ping":
        case "message_notify":
            break;
        default:
            console.log("未知类型[ "+data.type+" ]");
    }
}
function send(obj){
    if(!websock){
        tips("您已掉线,准备尝试连接...");
        link();
        return false;
    }
    socket.send(JSON.stringify(obj));
    return true;
}
//
function tips(msg){
	var str = '<li class="layim-chat-system"><span><em>'+get_time()+"</em> "+msg+'<br /></span></li>';
	$("#rchat_contents_main").append(str);
	var ele = document.getElementById('rchat_contents_main_div');
	ele.scrollTop = ele.scrollHeight;
}
function toggle_rchat_div(){
	if($("#rchat_div").is(":visible")){
		$("#rchat_div").slideUp(function(){
			send_rchat_status();
		});
		setCookie("rchat_div_is_show",0,24*3600);
	}else{
		$("#rchat_div").slideDown(function(){
			send_rchat_status();
		});
		$("#rchat_message_tips").hide();
		document.title = bak_document_title;
		setCookie("rchat_div_is_show",1,24*3600);
	}
}
//
document.onkeydown = function(e){
    var theEvent = window.event || e;
    var code = theEvent.keyCode || theEvent.which || theEvent.charCode;
    if (code == 13) {
        send_message();
    }
}

function get_ua(){
	var ua = navigator.userAgent.toLowerCase();
	//
	var ua_os = "";
	if(ua.indexOf("windows") >=0){
		ua_os = "Windows";
	}else if(ua.indexOf("macintosh") >=0){
		ua_os = "Mac";
	}else if(ua.indexOf("iphone") >=0){
		ua_os = "IOS";
	}else if(ua.indexOf("ipad") >=0){
		ua_os = "iPad";
	}else if(ua.indexOf("android") >=0){
		ua_os = "Android";
	}else if(ua.indexOf("ubuntu") >=0){
		ua_os = "Ubuntu";
	}else if(ua.indexOf("freebsd") >=0){
		ua_os = "FreeBSD";
	}else if(ua.indexOf("linux") >=0){
		ua_os = "Linux";
	}else{
		ua_os = "Unknow";
	}
	//
	var ua_browser = "";
	if(ua.indexOf("micromessenger") >=0){
		ua_browser = "Weixin";
	}else if(ua.indexOf("qqbrowser") >=0){
		ua_browser = "QQBrowser";
	}else if(ua.indexOf("ucweb") >=0){
		ua_browser = "UCWEB";
	}else if(ua.indexOf("opera") >=0 || ua.indexOf("opr") >=0){
		ua_browser = "Opera";
	}else if(ua.indexOf("firefox") >=0){
		ua_browser = "Firefox";
	}else if(ua.indexOf("360se") >=0){
		ua_browser = "360SE";
	}else if(ua.indexOf("edg") >=0){
		ua_browser = "Edge";
	}else if(ua.indexOf("msie") >=0){
		ua_browser = "MSIE";
	}else if(ua.indexOf("chrome") >=0){
		ua_browser = "Chrome";
	}else if(ua.indexOf("safari") >=0){
		ua_browser = "Safari";
	}else if(ua.indexOf("applewebkit") >=0){
		ua_browser = "WebKit";
	}else{
		ua_browser = "Unknow";
	}
	//
	return ua_os + " - " + ua_browser;
}

function get_time(){
	var date = new Date();
	let year = date.getFullYear();
	let month = date.getMonth() + 1;
	let day = date.getDate();
	let hours = date.getHours();
	let minutes = date.getMinutes();
	let seconds = date.getSeconds();

	month = month < 10 ? "0" + month : month;
	day = day < 10 ? "0" + day : day;
	hours = hours < 10 ? "0" + hours : hours;
	minutes = minutes < 10 ? "0" + minutes : minutes;
	seconds = seconds < 10 ? "0" + seconds : seconds;

	return `${month}/${day} ${hours}:${minutes}:${seconds}`;
}
//
$(document).ready(function(){
	$("#rchat_channel").text(rchat_channel);
	$(".close_rchat").click(function(){
		toggle_rchat_div();
	});
	$("#toggle_rchat_div").click(function(){
		toggle_rchat_div();
	});
	$("#rchat_send").click(function(){
		send_message();
	});
	$('#rchat_contents_main').dblclick(function(){
		if(confirm("确定清空当前聊天内容?")){
			$("#rchat_contents_main").empty();
		}
	});
	if(is_open_rchat){
		//
		var rchat_div_is_show = getCookie("rchat_div_is_show");
		if(rchat_div_is_show == 1){
			$("#rchat_div").slideDown(function(){
				link();
			});
			setCookie("rchat_div_is_show",1,24*3600);
		}else{
			link();
		}
		//
		setInterval(function(){send({"url":"ping@server"});},10 * 1000); 
		//
	}else{
		$("#toggle_rchat_div").parent(".layui-nav-item").hide();
	}
});
