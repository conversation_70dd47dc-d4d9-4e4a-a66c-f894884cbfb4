<?php
namespace controller;
use core\Controller;
use core\Db;

/*
 * @className 分会长运营体系
 */
class Branchpresident extends Controller {

    public function __construct() {
        parent::__construct();
    }

    /*
     * @apiName 申请成为分会长
     * @method apply
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param branch_name string 分会名称
     * @param branch_description string 分会描述
     * @param branch_location string 分会地区
     * @param application_reason string 申请理由
     * @param nickname string 申请人昵称
     * @param wechat_qr_image string 微信二维码图片路径
     * @return {"status":"ok","msg":"申请提交成功，请等待管理员审核","data":{"application_id":123}}
     */
    public function apply($uid, $token, $branch_name, $branch_description, $branch_location, $application_reason, $nickname = '', $wechat_qr_image = '') {
        // 🔧 新增：参数验证包含昵称和微信二维码
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            empty($branch_name) || strlen($branch_name) < 2 || strlen($branch_name) > 100 ||
            empty($branch_location) || strlen($branch_location) > 200 ||
            empty($application_reason) || strlen($application_reason) < 50 || strlen($application_reason) > 500 ||
            empty($nickname) || strlen($nickname) < 2 || strlen($nickname) > 20 ||
            empty($wechat_qr_image)
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        // 使用事务和行锁防止并发问题
        Db()->begin();
        try {
            // 检查用户是否已经是分会长（使用参数化查询和行锁）
            $user_info = Db::_fetch("SELECT role_type,branch_id FROM user WHERE uid=? FOR UPDATE", [$uid]);
            if ($user_info && $user_info['role_type'] == '1') {
                throw new \Exception("您已经是分会长，无法重复申请");
            }

            // 检查是否有待审核的申请（使用参数化查询和行锁）
            $existing_application = Db::_fetch("SELECT id,status FROM branch_president_applications WHERE user_id=? AND status=0 FOR UPDATE", [$uid]);

            if (!empty($existing_application)) {
                throw new \Exception("您已有待审核的申请，请耐心等待");
            }

            // 检查分会名称是否重复
            $existing_branch = Db()->table("user_branch")
                ->select("branch_id")
                ->where("branch_name=:branch_name")
                ->prepareParam([":branch_name" => $branch_name])
                ->fetch();

            if (!empty($existing_branch)) {
                throw new \Exception("分会名称已存在，请选择其他名称");
            }

            $existing_application_name = Db()->table("branch_president_applications")
                ->select("id")
                ->where("branch_name=:branch_name AND status!=2")
                ->prepareParam([":branch_name" => $branch_name])
                ->fetch();

            if (!empty($existing_application_name)) {
                throw new \Exception("该分会名称已有人申请，请选择其他名称");
            }
            // 🔧 新增：插入申请记录包含昵称和微信二维码
            $prepareParam = [
                ":branch_name" => $branch_name,
                ":branch_description" => $branch_description,
                ":branch_location" => $branch_location,
                ":application_reason" => $application_reason,
                ":nickname" => $nickname,
                ":wechat_qr_image" => $wechat_qr_image
            ];

            $data = [
                "user_id" => $uid,
                "branch_name" => ":branch_name",
                "branch_description" => ":branch_description",
                "branch_location" => ":branch_location",
                "application_reason" => ":application_reason",
                "nickname" => ":nickname",
                "wechat_qr_image" => ":wechat_qr_image",
                "application_time" => date("Y-m-d H:i:s"),
                "status" => 0
            ];

            Db()->table("branch_president_applications")->prepareParam($prepareParam)->insert($data);
            $application_id = Db()->insertId();

            // 记录用户日志
            $this->user_log($uid, "申请成为分会长：{$branch_name}");

            Db()->commit();

            return [
                "status" => "ok",
                "msg" => "申请提交成功，请等待管理员审核",
                "data" => ["application_id" => $application_id]
            ];

        } catch (\Exception $e) {
            Db()->rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => $e->getMessage()];
        }
    }

    /*
     * @apiName 获取待审核活动列表
     * @method pending_activities
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param page string 页码,默认1
     * @param page_size string 每页多少条,默认20
     * @return {"status":"ok","data":[{"id":1,"name":"活动名称","title":"活动标题","img_url":"封面图","start_time":"开始时间","organizer_name":"组织者昵称"}],"count":10}
     */
    public function pending_activities($uid, $token, $page = 1, $page_size = 20) {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            !check($page, "intgt0") ||
            !check($page_size, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $page = (int)$page;
        $page_size = (int)$page_size;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        // 验证分会长身份（使用参数化查询）
        $user_info = Db()->table("user")
            ->select("role_type,branch_id")
            ->where("uid=:uid")
            ->prepareParam([":uid" => $uid])
            ->fetch();

        // 🆕 修改：支持新角色类型，允许branch_id为0（总会）的分会长
        $allowed_roles = ['1', '3', '4']; // 分会长、场地与活动第三方、城市分会长
        if (!in_array($user_info['role_type'], $allowed_roles) || $user_info['branch_id'] === null) {
            return ["status" => "error", "msg" => "您没有分会管理权限或未管理任何分会"];
        }

        $branch_id = (int)$user_info['branch_id'];

        try {

            // 查询本分会待审核活动（参考User.php的JOIN写法）
            // 计算LIMIT参数
            $start = ($page - 1) * $page_size;

            $fromClause = 'huodong h LEFT JOIN user u ON h.uid = u.uid';
            $selectFields = 'h.id, h.name, h.title, h.img_url, h.start_time, h.time as create_time, h.uid, u.nickname as organizer_name';

            $activities = Db()->table($fromClause)
                ->select($selectFields)
                ->where("u.branch_id = {$branch_id} AND (h.president_review_status IS NULL OR h.president_review_status = 0) AND h.status = 1")
                ->order("h.time DESC")
                ->limit($page_size, $start)
                ->fetchAll();

            // 获取总数用于分页
            $total_count = 0;
            try {
                $fromClause = 'huodong h LEFT JOIN user u ON h.uid = u.uid';
                $total_count = Db()->table($fromClause)
                    ->where("u.branch_id = {$branch_id} AND (h.president_review_status IS NULL OR h.president_review_status = 0) AND h.status = 1")
                    ->count();
            } catch (\Exception $e) {
                $this->exception_log("pending_activities: 获取总数失败: " . $e->getMessage());
            }

            if (empty($activities)) {
                $this->exception_log("pending_activities 返回空结果: total_count={$total_count}");
                return ["status" => "empty", "msg" => "暂无待审核活动", "total" => $total_count];
            }

            $this->exception_log("pending_activities 返回数据: count=" . count($activities));

            // 格式化数据
            foreach ($activities as &$activity) {
                $activity['id'] = (int)$activity['id'];
                $activity['start_time'] = $activity['start_time'] ?: '';
                $activity['create_time'] = $activity['create_time'] ?: '';
            }

            return [
                "status" => "ok",
                "data" => $activities,
                "total" => $total_count,
                "current_page" => $page,
                "page_size" => $page_size
            ];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * @apiName 审核活动
     * @method review_activity
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param huodong_id string 活动编号
     * @param status string 审核状态：1=通过，2=拒绝
     * @param comment string 审核意见（可选）
     * @return {"status":"ok","msg":"审核完成"}
     */
    public function review_activity($uid, $token, $huodong_id, $status, $comment = '') {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            empty($huodong_id) || !check($huodong_id, "intgt0") ||
            !in_array($status, [1, 2])
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $status = (int)$status;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        // 使用事务和行锁防止并发审核
        Db()->begin();
        try {
            // 验证分会长身份（使用参数化查询）
            $user_info = Db()->table("user")
                ->select("role_type,branch_id")
                ->where("uid=:uid")
                ->prepareParam([":uid" => $uid])
                ->fetch();

            // 记录用户信息
            $this->exception_log("review_activity 用户信息: " . json_encode($user_info));

            // 修复：支持新角色类型的活动审核权限（varchar类型）
            $allowed_roles = ['1', '3', '4']; // 分会长、场地与活动第三方、城市分会长
            if (!in_array($user_info['role_type'], $allowed_roles)) {
                throw new \Exception("您没有活动审核权限，role_type: {$user_info['role_type']}");
            }

            $branch_id = (int)$user_info['branch_id'];
            $this->exception_log("review_activity branch_id: {$branch_id}");

            // 验证活动是否属于本分会且需要审核（使用行锁防止并发）
            $sql = "SELECT h.id,h.name,h.uid,h.president_review_status,u.branch_id,u.nickname as organizer_name
                    FROM `huodong` h
                    LEFT JOIN `user` u ON h.uid = u.uid
                    WHERE h.id={$huodong_id} FOR UPDATE";

            // 记录SQL查询日志
            $this->exception_log("review_activity SQL查询: " . $sql);

            $activity_info = Db::_fetch($sql);

            // 记录查询结果
            $this->exception_log("review_activity 查询结果: " . json_encode($activity_info));

            if (empty($activity_info)) {
                throw new \Exception("活动不存在");
            }

            if ($activity_info['branch_id'] != $branch_id) {
                throw new \Exception("该活动不属于您管理的分会，活动分会ID: {$activity_info['branch_id']}, 用户分会ID: {$branch_id}");
            }

            // 再次检查审核状态（防止并发修改）
            if (!is_null($activity_info['president_review_status']) && $activity_info['president_review_status'] != 0) {
                throw new \Exception("该活动已经审核过了，当前状态: {$activity_info['president_review_status']}");
            }
            // 更新活动审核状态（使用参数化查询）
            $update_data = [
                "president_review_status" => $status,
                "president_review_time" => date("Y-m-d H:i:s"),
                "reviewed_by_president" => $uid
            ];

            if (!empty($comment)) {
                $update_data["president_review_comment"] = $comment;
            }

            Db()->table("huodong")
                ->where("id=:huodong_id")
                ->prepareParam([":huodong_id" => $huodong_id])
                ->update($update_data);

            // 记录用户日志
            $status_text = $status == 1 ? "通过" : "拒绝";
            $this->user_log($uid, "审核活动【{$activity_info['name']}】：{$status_text}");

            // 发送通知给活动发布者
            $this->create_notification(
                $activity_info['uid'],
                'activity_review',
                '活动审核结果',
                $status == 1
                    ? "您的活动【{$activity_info['name']}】已通过分会长审核"
                    : "您的活动【{$activity_info['name']}】未通过分会长审核" . (!empty($comment) ? "，原因：{$comment}" : ""),
                $huodong_id
            );

            Db()->commit();

            return [
                "status" => "ok",
                "msg" => "审核完成"
            ];

        } catch (\Exception $e) {
            Db()->rollback();
            $error_msg = "review_activity失败: " . $e->getMessage() . " | 参数: uid={$uid}, huodong_id={$huodong_id}, status={$status}";
            $this->exception_log($error_msg);
            return ["status" => "error", "msg" => $e->getMessage()];
        }
    }
    /*
     * @apiName 获取分会统计数据
     * @method get_stats
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @return {"status":"ok","data":{"total_members":10,"month_commission":"150.00","total_activities":5,"pending_activities":2}}
     */
    public function get_stats($uid, $token) {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        try {
            // 验证分会长身份
            $user_info = Db()->table("user")
                ->select("role_type,branch_id")
                ->where("uid=:uid")
                ->prepareParam([":uid" => $uid])
                ->fetch();

            if ($user_info['role_type'] != '1' || is_null($user_info['branch_id'])) {
                return ["status" => "error", "msg" => "您不是分会长或未管理任何分会"];
            }

            $branch_id = (int)$user_info['branch_id'];
            $current_month = date('Y-m');

            // 🔴 修复：实现分会统计数据获取
            $stats = [];

            // 1. 统计分会成员数量（按类型分别统计）
            // 统计普通用户数量（is_huiyuan=0）
            $stats['normal_members'] = Db()->table("user")
                ->where("branch_id={$branch_id} AND is_huiyuan=0")
                ->count();

            // 统计会员用户数量（is_huiyuan=1）
            $stats['vip_members'] = Db()->table("user")
                ->where("branch_id={$branch_id} AND is_huiyuan=1")
                ->count();

            // 统计总成员数量
            $stats['total_members'] = $stats['normal_members'] + $stats['vip_members'];

            // 2. 统计本月待结算运营佣金（根据time字段）
            $month_start = strtotime(date('Y-m-01 00:00:00'));
            $month_end = strtotime(date('Y-m-t 23:59:59'));
            $month_commission = Db()->table("user_yongjin_log")
                ->where("uid={$uid} AND commission_type='operation' AND status=0 AND time>=:month_start AND time<=:month_end")
                ->prepareParam([":month_start" => $month_start, ":month_end" => $month_end])
                ->sum("money");
            $stats['month_commission'] = number_format($month_commission ?: 0, 2, '.', '');

            // 3. 统计分会活动总数
            $fromClause = 'huodong h LEFT JOIN user u ON h.uid = u.uid';
            $stats['total_activities'] = Db()->table($fromClause)
                ->where("u.branch_id = {$branch_id}")
                ->count();

            // 4. 统计待审核活动数量
            $stats['pending_activities'] = Db()->table($fromClause)
                ->where("u.branch_id = {$branch_id} AND (h.president_review_status IS NULL OR h.president_review_status = 0) AND h.status != 3")
                ->count();

            // 5. 统计可提现佣金
            $available_commission = Db()->table("user_yongjin_log")
                ->where("uid=:uid AND commission_type='operation' AND status=1")
                ->prepareParam([":uid" => $uid])
                ->sum("money");
            $stats['available_commission'] = number_format($available_commission ?: 0, 2, '.', '');

            return [
                "status" => "ok",
                "data" => $stats
            ];

        } catch (\Exception $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * 创建通知的私有方法
     * @param int $uid 用户ID，如果为0则创建全局通知
     * @param string $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param int $related_id 关联ID
     */
    private function create_notification($uid, $type, $title, $content, $related_id = null) {
        try {
            $is_global = ($uid == 0) ? 1 : 0;
            $data = [
                "uid" => $uid,
                "type" => ":type",
                "title" => ":title",
                "content" => ":content",
                "related_id" => $related_id,
                "is_read" => 0,
                "is_global" => $is_global
            ];
            $prepareParam = [
                ":type" => htmlspecialchars($type),
                ":title" => htmlspecialchars($title),
                ":content" => htmlspecialchars($content)
            ];

            Db()->table("user_notifications")
                ->prepareParam($prepareParam)
                ->insert($data);

        } catch (\Exception $e) {
            $this->exception_log("创建通知失败：" . $e->getMessage());
        }
    }

    /*
     * @apiName 获取分会长佣金记录
     * @method get_commission
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param page string 页码,默认1
     * @param page_size string 每页多少条,默认20
     * @return {"status":"ok","data":[{"id":1,"money":"100.00","commission_type":"operation","status":1,"related_month":"2025-07","created_at":"2025-07-09 10:00:00"}],"total":10}
     */
    public function get_commission($uid, $token, $page = 1, $page_size = 20) {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            !check($page, "intgt0") ||
            !check($page_size, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $page = (int)$page;
        $page_size = (int)$page_size;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        try {
            // 记录请求参数
            $this->exception_log("get_commission 请求参数: uid={$uid}, page={$page}, page_size={$page_size}");

            // 验证分会长身份
            $user_info = Db()->table("user")
                ->select("role_type,branch_id")
                ->where("uid={$uid}")
                ->fetch();

            // 记录用户信息
            $this->exception_log("get_commission 用户信息: " . json_encode($user_info));

            if ($user_info['role_type'] != '1') {
                $this->exception_log("get_commission 权限验证失败: role_type={$user_info['role_type']}, branch_id={$user_info['branch_id']}");
                return ["status" => "error", "msg" => "您不是分会长"];
            }

            $branch_id = (int)$user_info['branch_id'];
            $this->exception_log("get_commission branch_id: {$branch_id}");

            // 计算LIMIT参数
            $start = ($page - 1) * $page_size;

            // 先查询该用户是否有任何佣金记录
            $all_records_count = Db()->table("user_yongjin_log")
                ->where("uid={$uid}")
                ->count();
            $this->exception_log("get_commission 用户{$uid}的所有佣金记录数: " . $all_records_count);

            // 查询佣金记录 - 先查询所有佣金记录，不限制commission_type
            $commission_records = Db()->table("user_yongjin_log")
                ->select("id,money,commission_type,status,time,remark")
                ->where("uid={$uid}")
                ->order("time DESC")
                ->limit($page_size, $start)
                ->fetchAll();

            // 记录查询结果
            $this->exception_log("get_commission 查询结果数量: " . count($commission_records));
            if (!empty($commission_records)) {
                $this->exception_log("get_commission 第一条记录: " . json_encode($commission_records[0]));
            }

            // 获取总数
            $total_count = Db()->table("user_yongjin_log")
                ->where("uid={$uid}")
                ->count();

            // 记录总数
            $this->exception_log("get_commission 总记录数: " . $total_count);

            if (empty($commission_records)) {
                $this->exception_log("get_commission 返回空结果: total_count={$total_count}");
                return ["status" => "empty", "msg" => "暂无佣金记录", "total" => $total_count];
            }

            $this->exception_log("get_commission 返回数据: count=" . count($commission_records));

            // 格式化数据
            foreach ($commission_records as &$record) {
                $record['money'] = number_format($record['money'], 2, '.', '');
                $record['status_text'] = $record['status'] == 0 ? '待结算' : ($record['status'] == 1 ? '可提现' : '已提现');
            }

            return [
                "status" => "ok",
                "data" => $commission_records,
                "total" => $total_count,
                "current_page" => $page,
                "page_size" => $page_size
            ];

        } catch (\Exception $e) {
            $error_msg = "get_commission错误: " . $e->getMessage() . " | 文件:" . $e->getFile() . " | 行号:" . $e->getLine() . " | 参数: uid={$uid}, page={$page}, page_size={$page_size}";
            $this->exception_log($error_msg);
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * @apiName 获取分会成员列表
     * @method get_members
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param keyword string 搜索关键词（可选）
     * @param page string 页码,默认1
     * @param page_size string 每页多少条,默认20
     * @return {"status":"ok","data":[{"uid":1,"nickname":"用户昵称","avatar":"头像","is_huiyuan":1,"join_time":"2025-07-09 10:00:00"}],"total":10}
     */
    public function get_members($uid, $token, $keyword = '', $page = 1, $page_size = 20) {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            !check($page, "intgt0") ||
            !check($page_size, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $page = (int)$page;
        $page_size = (int)$page_size;

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        try {
            // 记录请求参数
            $this->exception_log("get_members 请求参数: uid={$uid}, keyword='{$keyword}', page={$page}, page_size={$page_size}");

            // 验证分会长身份
            $user_info = Db()->table("user")
                ->select("role_type,branch_id")
                ->where("uid={$uid}")
                ->fetch();

            // 记录用户信息
            $this->exception_log("get_members 用户信息: " . json_encode($user_info));

            if ($user_info['role_type'] != '1') {
                $this->exception_log("get_members 权限验证失败: role_type={$user_info['role_type']}, branch_id={$user_info['branch_id']}");
                return ["status" => "error", "msg" => "您不是分会长"];
            }

            $branch_id = (int)$user_info['branch_id'];
            $this->exception_log("get_members branch_id: {$branch_id}");

            // 构建查询条件 - 添加is_dongjie=0确保不查询冻结用户
            $where = "branch_id={$branch_id} AND is_dongjie=0";
            if (!empty($keyword)) {
                $keyword = addslashes($keyword);
                $where .= " AND (nickname LIKE '%{$keyword}%' OR mobile LIKE '%{$keyword}%')";
            }

            // 记录查询条件
            $this->exception_log("get_members 查询条件: " . $where);

            // 计算LIMIT参数
            $start = ($page - 1) * $page_size;

            // 先查询该分会是否有任何成员
            $all_members_count = Db()->table("user")
                ->where("branch_id={$branch_id}")
                ->count();
            $this->exception_log("get_members 分会{$branch_id}的所有成员数: " . $all_members_count);

            // 查询分会成员
            $members = Db()->table("user")
                ->select("uid,nickname,avatar,is_huiyuan,reg_time as join_time,mobile")
                ->where($where)
                ->order("reg_time DESC")
                ->limit($page_size, $start)
                ->fetchAll();

            // 记录查询结果
            $this->exception_log("get_members 查询结果数量: " . count($members));
            if (!empty($members)) {
                $this->exception_log("get_members 第一条记录: " . json_encode($members[0]));
            }

            // 获取总数
            $total_count = Db()->table("user")
                ->where($where)
                ->count();

            // 记录总数
            $this->exception_log("get_members 总记录数: " . $total_count);

            if (empty($members)) {
                return ["status" => "empty", "msg" => "暂无成员数据", "total" => $total_count];
            }

            // 格式化数据
            foreach ($members as &$member) {
                $member['uid'] = (int)$member['uid'];
                $member['is_huiyuan'] = (int)$member['is_huiyuan'];
                $member['member_type'] = $member['is_huiyuan'] == 1 ? '会员用户' : '普通用户';
                // 隐藏手机号中间4位
                if (!empty($member['mobile'])) {
                    $member['mobile_masked'] = substr($member['mobile'], 0, 3) . '****' . substr($member['mobile'], -4);
                }
            }

            return [
                "status" => "ok",
                "data" => $members,
                "total" => $total_count,
                "current_page" => $page,
                "page_size" => $page_size
            ];

        } catch (\Exception $e) {
            $error_msg = "get_members错误: " . $e->getMessage() . " | 文件:" . $e->getFile() . " | 行号:" . $e->getLine() . " | 参数: uid={$uid}, keyword='{$keyword}', page={$page}, page_size={$page_size}";
            $this->exception_log($error_msg);
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * @apiName 获取分会活动列表
     * @method get_activities
     * @POST
     * @param uid string 用户编号
     * @param token string token
     * @param page string 页码,默认1
     * @param page_size string 每页多少条,默认20
     * @param status string 状态筛选,可选值：all=全部,ongoing=进行中,completed=已完成,cancelled=已取消
     * @return {"status":"ok","data":[{"id":1,"name":"活动名称","img_url":"封面图","start_time":"开始时间","status":1,"president_review_status":1}],"total":10,"stats":{"total":10,"ongoing":5,"completed":3,"cancelled":2}}
     */
    public function get_activities($uid, $token, $page = 1, $page_size = 20, $status = 'all') {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            !check($page, "intgt0") ||
            !check($page_size, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $page = (int)$page;
        $page_size = (int)$page_size;

        // 验证状态参数
        $valid_statuses = ['all', 'ongoing', 'completed', 'cancelled'];
        if (!in_array($status, $valid_statuses)) {
            $status = 'all';
        }

        // 用户认证验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        try {
            // 验证分会长身份
            $user_info = Db()->table("user")
                ->select("role_type,branch_id")
                ->where("uid={$uid}")
                ->fetch();

            // 修复：支持新角色类型的分会活动查看权限（int类型）
            $allowed_roles = [1, 3, 4]; // 分会长、场地与活动第三方、城市分会长
            if (!in_array($user_info['role_type'], $allowed_roles) || $user_info['branch_id'] === null) {
                return ["status" => "error", "msg" => "您没有分会管理权限或未管理任何分会"];
            }

            $branch_id = (int)$user_info['branch_id'];

            // 计算LIMIT参数
            $start = ($page - 1) * $page_size;

            // 构建WHERE条件
            $where_conditions = "u.branch_id = {$branch_id}";

            // 根据状态筛选添加条件
            if ($status !== 'all') {
                switch ($status) {
                    case 'ongoing':
                        // 进行中：状态为1且开始时间未到或正在进行
                        $where_conditions .= " AND h.status = 1 AND h.start_time > NOW()";
                        break;
                    case 'completed':
                        // 已完成：状态为2或开始时间已过的正常活动
                        $where_conditions .= " AND (h.status = 2 OR (h.status = 1 AND h.start_time <= NOW()))";
                        break;
                    case 'cancelled':
                        // 已取消：状态为3
                        $where_conditions .= " AND h.status = 3";
                        break;
                }
            }

            // 查询分会活动（使用JOIN查询）
            $fromClause = 'huodong h LEFT JOIN user u ON h.uid = u.uid';
            $selectFields = 'h.id, h.name, h.title, h.img_url, h.start_time, h.time as create_time, h.status, h.president_review_status, h.baoming_num as signup_count, u.nickname as organizer_name';

            $activities = Db()->table($fromClause)
                ->select($selectFields)
                ->where($where_conditions)
                ->order("h.time DESC")
                ->limit($page_size, $start)
                ->fetchAll();

            // 获取总数
            $total_count = Db()->table($fromClause)
                ->where($where_conditions)
                ->count();

            // 计算统计数据（仅在第一页时计算）
            $stats = null;
            if ($page == 1) {
                $stats = [
                    'total' => 0,
                    'ongoing' => 0,
                    'completed' => 0,
                    'cancelled' => 0
                ];

                // 统计总数
                $stats['total'] = Db()->table($fromClause)
                    ->where("u.branch_id = {$branch_id}")
                    ->count();

                // 统计进行中的活动
                $stats['ongoing'] = Db()->table($fromClause)
                    ->where("u.branch_id = {$branch_id} AND h.status = 1 AND h.start_time > NOW()")
                    ->count();

                // 统计已完成的活动
                $stats['completed'] = Db()->table($fromClause)
                    ->where("u.branch_id = {$branch_id} AND (h.status = 2 OR (h.status = 1 AND h.start_time <= NOW()))")
                    ->count();

                // 统计已取消的活动
                $stats['cancelled'] = Db()->table($fromClause)
                    ->where("u.branch_id = {$branch_id} AND h.status = 3")
                    ->count();
            }

            if (empty($activities)) {
                return ["status" => "empty", "msg" => "暂无活动数据", "total" => $total_count];
            }

            // 格式化数据
            foreach ($activities as &$activity) {
                $activity['id'] = (int)$activity['id'];
                $activity['status'] = (int)$activity['status'];
                $activity['president_review_status'] = (int)$activity['president_review_status'];
                $activity['signup_count'] = (int)$activity['signup_count'];

                // 确保时间字段格式正确 - 将datetime转换为时间戳
                $activity['start_time'] = $activity['start_time'] ? strtotime($activity['start_time']) : 0;
                $activity['create_time'] = $activity['create_time'] ?: '';

                // 状态文本（根据实际业务逻辑调整）
                if ($activity['status'] == 3) {
                    $activity['display_status'] = 3; // 已取消
                } elseif ($activity['status'] == 1 && $activity['start_time'] > time()) {
                    $activity['display_status'] = 1; // 进行中/未开始
                } else {
                    $activity['display_status'] = 2; // 已完成
                }
            }

            $result = [
                "status" => "ok",
                "data" => $activities,
                "total" => $total_count,
                "current_page" => $page,
                "page_size" => $page_size
            ];

            // 添加统计数据（仅在第一页时返回）
            if ($stats !== null) {
                $result['stats'] = $stats;
            }

            return $result;

        } catch (\Exception $e) {
            $this->exception_log("get_activities错误: " . $e->getMessage() . " 文件:" . $e->getFile() . " 行号:" . $e->getLine());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }
}
