<script setup>
import { watch, ref, reactive } from "vue";
import {
  configchongzhi_list,
  useradd_chongzhi_order,
  payweixin_pay,
  payget_weixinpay_sign,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto, getListHeight, pay, back } from "@/utils";

const list = ref([]);
const money = ref(0);
const index = ref(0);
const gapHeight = ref(0);

onLoad(async () => {
  const res = await configchongzhi_list();
  list.value = res.data;
  money.value = list.value[0].money;
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});

const blur = (e) => {
  // if (!uni.$u.test.digits(e)) {
  //   uni.$u.toast("请输入整数");
  //   money.value = 0;
  // }
};

const gpPay = async () => {
  if (store().$state.config?.config?.min_chongzhi_money?.val < money.value)
    return uni.$u.toast(
      `单次最少充值${store().$state.config?.config?.min_chongzhi_money?.val}元`
    );
  const chongzhiRes = await useradd_chongzhi_order({
    money: money.value,
  });
  if (chongzhiRes.status === "ok") {
    const wxRes = await payweixin_pay({
      type: 4,
      order_id: chongzhiRes.order_id,
      money: chongzhiRes.money,
    });
    if (wxRes.status === "ok") {
      const signRes = await payget_weixinpay_sign({ prepay_id: wxRes.prepay_id });
      const payRes = await pay(signRes);
      if (payRes.errMsg === "requestPayment:ok")
        back({ tip: "充值成功，即将返回上级页面", time: 1000 });
    }
  }
};
</script>
<template>
  <view class="page">
    <myTitle title="充值中心" img="topUpBg.png" height="420rpx"></myTitle>
    <view class="pa w690 left50 tl50" style="top: 178rpx">
      <u-text size="48rpx" :text="store().$state.userInfo?.money || 0"></u-text>
      <u-text size="26rpx" text="金币余额"></u-text>
      <u-text size="26rpx" text="如需充值更大金额，请联系客服"></u-text>
      <view class="mt40 py40 pl30 pr30 w b6f r20">
        <view class="df aic jcsb">
          <u-text
            align="left"
            margin="0 0 30rpx"
            size="22rpx"
            color="#818181"
            text="充值明细"
            @click="navto('/pages/bundle/user/topUpList')"
          ></u-text>
          <u-text
            align="right"
            margin="0 0 30rpx"
            size="22rpx"
            color="#818181"
            text="金币明细"
            @click="navto('/pages/bundle/user/coinInfo')"
          ></u-text>
        </view>
        <view class="df aic fw jcsb list">
          <view
            class="pr mb20 df fdc jcc aic r10"
            style="width: 193rpx; height: 220rpx"
            :style="{
              background: index === i ? 'rgba(255, 246, 240, 1)' : '#fff',
              border:
                index === i
                  ? '2rpx solid rgba(245,156,77,0.99)'
                  : '2rpx solid rgba(217, 217, 217, 0.99)',
            }"
            v-for="(val, i) in list"
            :key="i"
            @click="
              index = i;
              money = val?.money;
            "
          >
            <view>
              <u-text
                margin="0 0 20rpx"
                align="center"
                color="#FE6402"
                size="36rpx"
                bold
                lines="1"
                :text="`${val?.daozhang_money}金币`"
              ></u-text>
            </view>
            <view>
              <u-text
                mode="price"
                align="center"
                size="22rpx"
                bold
                lines="1"
                :text="val?.money"
              ></u-text>
            </view>
            <view class="pa bottom0 right0">
              <u-image
                width="73rpx"
                height="67rpx"
                :src="`${store().$state.url}topUpsCoin.png`"
                mode="scaleToFill"
              />
            </view>
          </view>
        </view>
        <u-text
          margin="0 0 52rpx"
          size="22rpx"
          color="#818181"
          text="自定义充值"
        ></u-text>
        <u-input
          v-model="money"
          type="number"
          suffix-icon="rmb"
          placeholder="请输入需要充值的金额（整数）"
          shape="circle"
          @blur="blur"
        ></u-input>
      </view>
    </view>
    <u-gap :height="gapHeight"></u-gap>
    <view class="pfx bottom0 w b6f bottomBox">
      <view class="df aic jcsb px10">
        <view class="ml50 df aic" style="color: #818181; size: 24rpx">
          应付：
          <u-text color="#FE6402" size="46rpx" mode="price" :text="money"></u-text>
        </view>
        <u-button
          color="linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%)"
          shape="circle"
          text="立即支付"
          :customStyle="{
            margin: '8rpx 0',
            width: '260rpx',
            height: '98rpx',
            color: '#333',
            fontSize: '30rpx',
          }"
          @click="gpPay"
        ></u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<style scoped lang="less">
.page {
  .list::after {
    content: "";
    width: 30%;
  }
}
</style>
