/**
 * 统一的登录状态验证工具
 * 解决登录状态验证不一致问题
 */

import { store } from '@/store'

// 防重复提示的全局状态
let isShowingReloginModal = false
let reloginModalTimer = null

/**
 * 检查用户是否已登录（增强版本，包含格式验证）
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  const userInfo = store().$state.userInfo

  // 基本存在性检查
  if (!userInfo || !userInfo.uid || !userInfo.token) {
    return false
  }

  // UID格式检查（必须是正整数）
  const uid = parseInt(userInfo.uid)
  if (isNaN(uid) || uid <= 0) {
    console.warn('登录状态检查失败: UID格式无效', userInfo.uid)
    return false
  }

  // Token格式检查（必须是32位字符串）
  if (typeof userInfo.token !== 'string' || userInfo.token.length !== 32) {
    console.warn('登录状态检查失败: Token格式无效', {
      tokenType: typeof userInfo.token,
      tokenLength: userInfo.token?.length
    })
    return false
  }

  // 检查是否是默认的无效token
  if (userInfo.token === "00000000000000000000000000000000") {
    console.warn('登录状态检查失败: 使用默认无效token')
    return false
  }

  return true
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息对象或null
 */
export const getCurrentUser = () => {
  const userInfo = store().$state.userInfo
  if (isLoggedIn()) {
    return userInfo
  }
  return null
}

/**
 * 获取用户UID
 * @returns {string|number|null} 用户UID或null
 */
export const getUserId = () => {
  const userInfo = getCurrentUser()
  return userInfo ? userInfo.uid : null
}

/**
 * 获取用户Token
 * @returns {string|null} 用户Token或null
 */
export const getUserToken = () => {
  const userInfo = getCurrentUser()
  return userInfo ? userInfo.token : null
}

/**
 * 检查token是否有效（基本格式检查）
 * @param {string} token 要检查的token
 * @returns {boolean} token是否有效
 */
export const isValidToken = (token) => {
  if (!token || typeof token !== 'string') {
    return false
  }

  // 检查token长度（通常token应该有一定长度）
  if (token.length < 10) {
    return false
  }

  // 检查是否是默认的无效token
  if (token === "00000000000000000000000000000000") {
    return false
  }

  return true
}

/**
 * 检查用户认证信息是否完整且有效
 * @returns {boolean} 认证信息是否有效
 */
export const isValidAuth = () => {
  const userInfo = store().$state.userInfo
  if (!userInfo || !userInfo.uid || !userInfo.token) {
    return false
  }

  // 检查uid是否有效
  const uid = parseInt(userInfo.uid)
  if (isNaN(uid) || uid <= 0) {
    return false
  }

  // 检查token是否有效
  return isValidToken(userInfo.token)
}

/**
 * 检查登录状态，如果未登录则跳转到登录页面
 * @param {string} returnUrl 登录成功后的返回地址（可选）
 * @param {string} message 提示信息（可选）
 * @returns {boolean} 是否已登录
 */
export const requireLogin = (returnUrl = '', message = '请先登录后再进行此操作') => {
  if (isLoggedIn()) {
    return true
  }

  // 如果没有提供返回地址，自动获取当前页面信息
  if (!returnUrl) {
    try {
      const currentPage = getCurrentPages()[getCurrentPages().length - 1]
      const currentRoute = currentPage.route
      const currentOptions = currentPage.options

      // 构建返回URL，避免空参数
      const params = Object.keys(currentOptions)
        .filter(key => currentOptions[key] !== undefined && currentOptions[key] !== '')
        .map(key => `${key}=${currentOptions[key]}`)
        .join('&')

      returnUrl = `/${currentRoute}${params ? '?' + params : ''}`
    } catch (error) {
      console.warn('获取当前页面信息失败:', error)
      returnUrl = '/pages/index'
    }
  }

  // 显示登录提示
  uni.showModal({
    title: '登录提示',
    content: message,
    confirmText: '去登录',
    cancelText: '取消',
    success: function (res) {
      if (res.confirm) {
        // 跳转到登录页面，优化URL编码处理
        try {
          const loginUrl = `/pages/bundle/common/login?returnUrl=${encodeURIComponent(returnUrl)}`
          console.log('跳转到登录页面:', loginUrl)
          uni.navigateTo({
            url: loginUrl
          })
        } catch (error) {
          console.error('登录页面跳转失败:', error)
          // 如果跳转失败，直接跳转到登录页面不带返回参数
          uni.navigateTo({
            url: '/pages/bundle/common/login'
          })
        }
      }
    }
  })

  return false
}

/**
 * 清除登录状态
 */
export const logout = () => {
  // 清除store中的用户信息
  store().clearUserInfo()
  
  // 清除本地存储
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('token')
  uni.removeStorageSync('uid')
  
  // 可以添加其他清理逻辑
  console.log('用户已退出登录')
}

/**
 * 更新用户信息到store和本地存储
 * @param {object} userInfo 用户信息
 */
export const updateUserInfo = (userInfo) => {
  if (!userInfo) return
  
  // 更新store
  store().updateUserInfo(userInfo)
  
  // 更新本地存储
  uni.setStorageSync('userInfo', userInfo)
  if (userInfo.token) {
    uni.setStorageSync('token', userInfo.token)
  }
  if (userInfo.uid) {
    uni.setStorageSync('uid', userInfo.uid)
  }
  
  console.log('用户信息已更新')
}

/**
 * 从本地存储恢复登录状态（增强版本，支持多重验证）
 */
export const restoreLoginState = () => {
  try {
    // 优先从store中获取用户信息
    const storeUserInfo = store().$state.userInfo
    if (storeUserInfo && storeUserInfo.uid && storeUserInfo.token) {
      console.log('从Store恢复登录状态')
      return true
    }

    // 如果store中没有，则从本地存储恢复
    const userInfo = uni.getStorageSync('userInfo')
    const token = uni.getStorageSync('token')
    const uid = uni.getStorageSync('uid')

    if (userInfo && token && uid) {
      // 确保userInfo包含必要的字段
      const completeUserInfo = {
        ...userInfo,
        token: token,
        uid: uid
      }

      // 验证恢复的数据格式
      if (isValidToken(token) && parseInt(uid) > 0) {
        // 更新到store
        store().updateUserInfo(completeUserInfo)
        console.log('从本地存储恢复登录状态')
        return true
      } else {
        console.warn('本地存储的登录信息格式无效，清理数据')
        // 清理无效的本地数据
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
        uni.removeStorageSync('uid')
      }
    }
  } catch (error) {
    console.warn('恢复登录状态失败:', error)
  }

  return false
}

/**
 * 验证token是否有效（通过调用后端API验证）
 * @returns {Promise<boolean>} token是否有效
 */
export const validateToken = async () => {
  const uid = getUserId()
  const token = getUserToken()

  if (!uid || !token) {
    console.log('Token验证失败: 缺少uid或token')
    return false
  }

  try {
    // 调用后端API验证token - 使用get_user_info作为验证方式
    const { userget_user_info } = await import('@/api')
    const result = await userget_user_info({ uid, token })

    if (result.status === 'ok') {
      console.log('Token验证成功')
      return true
    } else if (result.status === 'relogin') {
      console.log('Token验证失败: 需要重新登录')
      // 清理无效的本地状态
      logout()
      return false
    } else {
      console.log('Token验证失败:', result.msg)
      return false
    }
  } catch (error) {
    console.warn('Token验证异常:', error)
    return false
  }
}

/**
 * 增强的登录状态检查（包含服务器验证）
 * @param {boolean} skipServerValidation 是否跳过服务器验证（默认false）
 * @returns {Promise<boolean>} 是否已登录且token有效
 */
export const isLoggedInWithValidation = async (skipServerValidation = false) => {
  // 首先检查本地状态
  if (!isLoggedIn()) {
    return false
  }

  // 如果跳过服务器验证，只返回本地状态
  if (skipServerValidation) {
    return true
  }

  // 进行服务器验证
  return await validateToken()
}

/**
 * 检查并处理登录状态不一致问题
 * @returns {Promise<object>} 返回状态信息
 */
export const checkAndFixAuthState = async () => {
  const localLoggedIn = isLoggedIn()
  const serverValid = localLoggedIn ? await validateToken() : false

  const result = {
    localLoggedIn,
    serverValid,
    consistent: localLoggedIn === serverValid,
    action: 'none'
  }

  // 如果本地显示已登录但服务器验证失败，清理本地状态
  if (localLoggedIn && !serverValid) {
    console.log('检测到登录状态不一致，清理本地状态')
    logout()
    result.action = 'logout'
    result.consistent = true // 清理后状态一致
  }

  return result
}

/**
 * 快速检查登录状态一致性（不进行服务器验证）
 * @returns {object} 检查结果
 */
export const quickAuthCheck = () => {
  const localLoggedIn = isLoggedIn()
  const userInfo = store().$state.userInfo

  const result = {
    isValid: localLoggedIn,
    issues: [],
    userInfo: userInfo
  }

  if (!localLoggedIn && userInfo && (userInfo.uid || userInfo.token)) {
    result.issues.push('用户信息不完整或格式错误')
  }

  return result
}

/**
 * 获取API请求需要的认证参数（增强版本，确保参数稳定性）
 * @returns {object} 包含uid和token的对象，未登录时返回空对象
 */
export const getAuthParams = () => {
  try {
    // 先尝试恢复登录状态
    if (!isLoggedIn()) {
      restoreLoginState()
    }

    // 再次检查登录状态
    const authCheck = quickAuthCheck()
    if (!authCheck.isValid) {
      console.warn('获取认证参数失败，用户未正确登录:', authCheck.issues)
      return {}
    }

    const uid = getUserId()
    const token = getUserToken()

    // 双重验证：确保uid和token都有效
    if (!uid || !token || !isValidToken(token) || parseInt(uid) <= 0) {
      console.warn('认证参数无效:', { uid, tokenValid: isValidToken(token) })
      return {}
    }

    // 用户已登录，返回完整的认证参数
    return {
      uid: String(uid), // 确保uid为字符串格式
      token: String(token) // 确保token为字符串格式
    }
  } catch (error) {
    console.error('获取认证参数异常:', error)
    return {}
  }
}

/**
 * 统一处理API认证失败的情况（增强版本，防重复提示）
 * @param {object} apiResponse API响应对象
 * @param {string} context 调用上下文（用于日志）
 * @returns {boolean} 是否为认证失败
 */
export const handleAuthFailure = (apiResponse, context = '') => {
  if (apiResponse && apiResponse.status === 'relogin') {
    console.log(`API认证失败 [${context}]:`, apiResponse.msg)

    // 清理前端登录状态
    try {
      logout()
    } catch (error) {
      console.error('清理登录状态失败:', error)
    }

    // 防重复提示机制
    if (isShowingReloginModal) {
      console.log('重新登录提示已显示，跳过重复提示')
      return true
    }

    // 设置防重复标志
    isShowingReloginModal = true

    // 清除之前的定时器
    if (reloginModalTimer) {
      clearTimeout(reloginModalTimer)
    }

    // 提示用户重新登录
    uni.showModal({
      title: '登录提示',
      content: '登录状态已过期，请重新登录',
      confirmText: '去登录',
      cancelText: '取消',
      success: function (res) {
        // 重置防重复标志
        isShowingReloginModal = false

        if (res.confirm) {
          try {
            uni.navigateTo({
              url: '/pages/bundle/common/login'
            })
          } catch (error) {
            console.error('跳转登录页面失败:', error)
            // 如果跳转失败，尝试使用redirectTo
            uni.redirectTo({
              url: '/pages/bundle/common/login'
            })
          }
        }
      },
      fail: function() {
        // 如果弹窗失败，也要重置标志
        isShowingReloginModal = false
      }
    })

    // 设置超时重置标志（防止意外情况导致标志永远不重置）
    reloginModalTimer = setTimeout(() => {
      isShowingReloginModal = false
      console.log('重新登录提示标志已超时重置')
    }, 10000) // 10秒超时

    return true
  }
  return false
}

/**
 * 安全的API调用包装器，自动处理认证失败
 * @param {Function} apiCall API调用函数
 * @param {string} context 调用上下文
 * @returns {Promise} API调用结果
 */
export const safeApiCall = async (apiCall, context = '') => {
  try {
    const result = await apiCall()

    // 检查是否为认证失败
    if (handleAuthFailure(result, context)) {
      return null // 认证失败，返回null
    }

    return result
  } catch (error) {
    console.error(`API调用异常 [${context}]:`, error)
    throw error
  }
}

/**
 * 检查是否为指定用户
 * @param {string|number} targetUid 目标用户ID
 * @returns {boolean} 是否为指定用户
 */
export const isCurrentUser = (targetUid) => {
  const currentUid = getUserId()
  return currentUid && currentUid.toString() === targetUid.toString()
}

export default {
  isLoggedIn,
  getCurrentUser,
  getUserId,
  getUserToken,
  requireLogin,
  logout,
  updateUserInfo,
  restoreLoginState,
  validateToken,
  quickAuthCheck,
  getAuthParams,
  handleAuthFailure,
  safeApiCall,
  isCurrentUser
}
