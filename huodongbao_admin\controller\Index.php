<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 首页 
*/
class Index extends Controller{

	public function __construct(){
		parent::__construct();
		$this->auth();
	}

	/*
	* @name 首页统计
	* @method index
	*/		
	public function index(){
		dbConn();
		//header("location:".url("user/index"));;exit;
		//
		$index_tongji_days = Db()->table("config")->where("name='index_tongji_days'")->getColumn("val",90);
		$index_tongji_days = intval($index_tongji_days);
		//
		$result = [];
		$start_time = date("Y-m-d 00:00:00",strtotime("-{$index_tongji_days} days"));
		$end_time = date("Y-m-d H:i:s");
		//
		$sql = "SELECT date_format(`reg_time`,'%Y-%m-%d') as date_,count(1) as num FROM `user` WHERE `reg_time`>='{$start_time}' GROUP BY date_";
		$data = Db()->_fetchAll($sql);
		$result[] = ["title"=>"用户注册","name"=>"user_reg","type"=>"line","data"=>$data];
		//
		$zong_user = Db()->table("user")->count();
		$zong_user_huiyuan = Db()->table("user")->where("is_huiyuan=1")->count();
		$res = [["name"=>"非会员","value"=>$zong_user - $zong_user_huiyuan],["name"=>"会员","value"=>$zong_user_huiyuan]];
		$result[] = ["title"=>"用户会员比例","name"=>"user_huiyuan_bili","type"=>"pie","data"=>$res];
		//
		$sql = "SELECT date_format(`time`,'%Y-%m-%d') as date_,count(1) as num FROM `huodong` WHERE `time`>='{$start_time}' GROUP BY date_";
		$data = Db()->_fetchAll($sql);	
		$result[] = ["title"=>"活动发布数量","name"=>"huodong_num","type"=>"line","data"=>$data];
		//
		$sql = "SELECT date_format(`time`,'%Y-%m-%d') as date_,count(1) as num FROM `huodong_baoming_order` WHERE status IN (1) AND `time`>='{$start_time}' GROUP BY date_";
		$data = Db()->_fetchAll($sql);
		$result[] = ["title"=>"报名人数","name"=>"order_baoming","type"=>"line","data"=>$data];
		//
		$sql = "SELECT date_format(`create_time`,'%Y-%m-%d') as date_,count(1) as num FROM `goods_order` WHERE status IN (1,2,3) AND `create_time`>='{$start_time}' GROUP BY date_";
		$data = Db()->_fetchAll($sql);
		$result[] = ["title"=>"商城订单量","name"=>"goods_order_num","type"=>"line","data"=>$data];
		//
		assign("result",$result);
		//
		return view(['header','index/index','footer']);
	}

	public function _empty(){
		echo __CLASS__." -> _empty";
	}
	function __destruct(){

	}
}
